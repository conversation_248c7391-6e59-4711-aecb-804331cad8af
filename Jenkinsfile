pipeline {
    agent { label 'Second_Slave' }
    environment {
        NEXUS_COMMON_CREDS = credentials('0981d455-e100-4f93-9faf-151ac7e29d8a')
        NEXUS_URL = 'http://*************:8081'
    }
    options {
        buildDiscarder(logRotator(numToKeepStr: '5'))
	    office365ConnectorWebhooks([[
                name: '<PERSON>',
                notifyBackToNormal: true,
                notifyFailure: true,
                notifySuccess: true,
                notifyUnstable: true,
                url: "https://healsoftwareai.webhook.office.com/webhookb2/78345e71-2972-44c4-a270-fbae82662bf1@55dca2af-e23a-4402-b9a6-8833b28a02dc/JenkinsCI/7958868126734afeb78edb01dafdcc05/6fed72e3-b7dd-422f-9075-e6d96468feb0"
            ]]
        )
    }
    parameters {
        gitParameter branch: '', branchFilter: 'origin/(.*)', defaultValue: 'master', name: 'API_BRANCH', quickFilterEnabled: true, type: 'PT_BRANCH_TAG', useRepository: '.*appsone-api.git'
        gitParameter branch: '', branchFilter: 'origin/(.*)', defaultValue: 'develop', name: 'UI_BRANCH', quickFilterEnabled: true, type: 'PT_BRANCH_TAG', useRepository: '.*appsone-ui-5.0.git'
    }
    tools {
         nodejs 'NodeJs12'
          }
    stages {
        stage('Cleanup workspace'){
            steps{
                cleanWs()
            }
        }
        stage('Checkout') {
            parallel {
                stage('appsone-api checkout') {
                    steps {
                        script {
                            currentBuild.displayName = "#${BUILD_NUMBER}->${params.API_BRANCH}->${params.UI_BRANCH}"
                            currentBuild.description = "API branch: ${params.API_BRANCH} and UI branch: ${params.UI_BRANCH} is used for this build"
                        }
                        dir('heal-api') {
                            git branch: "${params.API_BRANCH}", url: 'https://<EMAIL>/appsone/appsone-api.git', credentialsId: "fd197b00-fd06-4632-a018-36134111e086"
                        }
                    }
                }
                stage('appsone-ui checkout') {
                    steps {
                        dir('heal-ui') {
                            git branch: "${params.UI_BRANCH}", url: 'https://<EMAIL>/appsone/appsone-ui-5.0.git', credentialsId: "fd197b00-fd06-4632-a018-36134111e086"
                        }
                    }
                }
            }
        }
        stage('Build UI') {
            steps {
                dir('heal-ui') {
                    sh 'npm install'
                    sh 'npm run build'
                }
            }
        }
        stage('Build tar & Sonarqube analysis') {
            steps {
                dir('heal-api') {
                    withSonarQubeEnv('sonarqube_40') {
			            sh 'mvn clean deploy -U'
                    }
                }
            }
        }
        stage('Copy ui to api') {
            steps {
                sh '''
                    mkdir -p build
                    mv heal-api/target/heal-ui-service*.tar.gz build/heal-ui-service.tar.gz
                    cd build/
                    tar -xzf heal-ui-service.tar.gz
                    rm -f heal-ui-service.tar.gz
                    mkdir -p heal-ui-service/ui/public/heal-ui-service/
                    cp -r ../heal-ui/dist/* heal-ui-service/ui/public/heal-ui-service/
                    tar -czf heal-ui-service.tar.gz heal-ui-service
                    rm -f ../heal-api/heal-ui-service.tar.gz
                    mv heal-ui-service.tar.gz ../heal-api/
                '''
            }
        }
        stage('Archive Builds') {
            steps {
                dir('heal-api') {
                    archiveArtifacts artifacts: 'heal-ui-service.tar.gz', fingerprint: true
                }
            }
        }
        stage('Docker build') {
            steps {
                dir('heal-api') {
                    sh "tar -xvf heal-ui-service.tar.gz"
                    script {
                        pom = readMavenPom file: 'pom.xml'
                        version = pom.version
                    }
                    echo "Building project in version: ${version}"
                    sh "docker build -t heal-ui-service:${version} ."
                }
            }
        }
        stage('Publish Docker Image') {
            steps {
                sh "docker save heal-ui-service:${version} > heal-ui-service_${version}.tar"
                sh "curl -v -u ${NEXUS_COMMON_CREDS} --upload-file heal-ui-service_${version}.tar ${NEXUS_URL}/nexus/repository/tls_docker_images/heal-ui-service_${version}.tar"
                sh "echo heal-ui-service_${version} > /tmp/heal-ui-service_version"
            }
        }
        stage('docker Vapt scan') {
            when {
                expression {
				 params.API_BRANCH == 'develop' && params.UI_BRANCH == 'develop'
                       }
                 }      
            steps {
                sh "docker scan heal-ui-service:${version} > heal-ui-service-scanreport.txt | echo"
                sh 'curl -v -u ${NEXUS_COMMON_CREDS} --upload-file heal-ui-service-scanreport.txt ${NEXUS_URL}/nexus/repository/Image_scan_report/heal-ui-service-scanreport.txt --progress-bar'
            }
             post {
                success {
                    emailext attachmentsPattern: 'heal-ui-service-scanreport.txt', body: 'Hi Team,<br><br> Please find the attached VAPT report fyr.<br><br>Kindly close it during next release.<br><br>Regards<br> Heal', subject: 'VAPT Report for heal-ui-service', to: '<EMAIL>'
                }
             }
        }

        stage('Cleanup') {
            steps {
                sh "docker rmi -f heal-ui-service:${version}"
                cleanWs()
            }
        }
    }
}
