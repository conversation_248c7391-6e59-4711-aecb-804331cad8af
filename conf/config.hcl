template {
    source      = "/etc/consul-template/templates/conf.properties.tmpl"
    destination = "/opt/heal-ui-service/config/conf.properties"
}

template {
    source      = "/etc/consul-template/templates/logback.xml.tmpl"
    destination = "/opt/heal-ui-service/config/logback.xml"
}

template {
    source      = "/etc/consul-template/templates/keycloak_details.json.tmpl"
    destination = "/opt/heal-ui-service/config/keycloak_details.json"
}

template {
    source      = "/etc/consul-template/templates/headers_details.json.tmpl"
    destination = "/opt/heal-ui-service/config/headers_details.json"
}