SELECT * FROM heal_schema.mst_producer;

 Set environment variables
export LD_LIBRARY_PATH=/opt/oracle/instantclient_19_23:$LD_LIBRARY_PATH

# Connect to Oracle database
/opt/oracle/instantclient_19_23/sqlplus healuser/Heal123#@//192.168.14.210:1521/orclpdb.localdomain


query 1:
select /*+RULE*/ count(1) as DB_SEQUENCE_CYCLIC from dba_sequences where cycle_flag='Y' and sequence_owner not in ('SYSTEM','SYS','DBSNMP') and (last_number-MIN_VALUE)/(max_value-min_value) > 100

query 2:
WITH t1 AS (SELECT DISTINCT sql_id, USER_ID FROM v$active_session_history WHERE sample_time BETWEEN to_char(to_timestamp(systimestamp - 5/1440,'DD-MON-YY HH24:MI:SS'),'DD-MON-YY HH.MI.SS.FF3 AM')  and to_char(systimestamp,'DD-MON-YY HH.MI.SS.FF3 AM')) SELECT u.USERNAME as PHY<PERSON>CALREADS_USER_ID, APPLICATION_WAIT_TIME AS PHYSICALREADS_APP_WAIT_TIME, SQL_ID AS PHYSICALREADS_SQL_ID, SQL_TEXT AS PHYSICALREADS_SQL_TEXT, DISK_READS_PER_EXEC AS PHYSICALREADS_DISK_READS_EXEC, BUFFER_GETS AS PHYSICALREADS_BUFFER_GETS, BUFFER_GETS_PER_EXECUTIONS AS PHYSICALREADS_BUFFER_GETS_EXEC, EXECUTIONS AS PHYSICALREADS_EXECUTIONS, DISK_READS AS PHYSICALREADS_DISK_READS, PARSE_CALLS AS PHYSICALREADS_PARSE_CALLS, SORTS AS PHYSICALREADS_SORTS, ROWS_PROCESSED AS PHYSICALREADS_ROWS_PROCESSED, HIT_RATIO AS PHYSICALREADS_HIT_RATIO, FIRST_LOAD_TIME AS PHYSICALREADS_FIRST_LOAD_TIME, SHARABLE_MEM AS PHYSICALREADS_SHARABLE_MEM, PERSISTENT_MEM AS PHYSICALREADS_PERSISTENT_MEM, RUNTIME_MEM AS PHYSICALREADS_RUNTIME_MEM, AVG_CPU_TIME AS PHYSICALREADS_AVG_CPU_TIME, AVG_ELAPSED_TIME AS PHYSICALREADS_AVG_ELAPSED_TIME, ADDRESS AS PHYSICALREADS_ADDRESS, HASH_VALUE AS PHYSICALREADS_HASH_VALUE, LASTACTIVETIME AS PHYSICALREADS_LASTACTIVETIME, FETCHES AS PHYSICALREADS_FETCHES, USERS_EXECUTING AS PHYSICALREADS_USERS_EXECUTING, DIRECT_WRITES AS PHYSICALREADS_DIRECT_WRITES, COMMAND_TYPE AS PHYSICALREADS_COMMAND_TYPE, OPTIMIZER_MODE AS PHYSICALREADS_OPTIMIZER_MODE FROM (SELECT a.OPTIMIZER_MODE, a.COMMAND_TYPE, a.DIRECT_WRITES, a.USERS_EXECUTING, a.FETCHES, a.APPLICATION_WAIT_TIME, a.SQL_TEXT, t1.USER_ID, a.SQL_ID, ROUND((a.DISK_READS / DECODE(a.EXECUTIONS, 0, 1, a.EXECUTIONS)), 2) DISK_READS_PER_EXEC, ROUND(a.BUFFER_GETS / DECODE(a.EXECUTIONS, 0, 1, a.EXECUTIONS)) BUFFER_GETS_PER_EXECUTIONS, a.DISK_READS, a.BUFFER_GETS, a.PARSE_CALLS, a.SORTS, a.EXECUTIONS, a.ROWS_PROCESSED, 100 - ROUND(100 * a.DISK_READS / GREATEST(a.BUFFER_GETS, 1), 2) HIT_RATIO, a.FIRST_LOAD_TIME, SHARABLE_MEM, PERSISTENT_MEM, RUNTIME_MEM, CPU_TIME AVG_CPU_TIME, ELAPSED_TIME AVG_ELAPSED_TIME, ADDRESS, HASH_VALUE, TO_CHAR(LAST_ACTIVE_TIME, 'YYYY-MM-DD HH24:MI:SS') AS LASTACTIVETIME, ROW_NUMBER() OVER (ORDER BY DISK_READS DESC) AS rnk FROM sys.V_$SQLAREA a, t1 WHERE a.sql_id = t1.sql_id AND a.PARSING_SCHEMA_NAME NOT IN ('appsone','SYS','PERFSTAT','DBSNMP') AND a.EXECUTIONS > 0 AND a.BUFFER_GETS / DECODE(a.EXECUTIONS, 0, 1, a.EXECUTIONS) > 1000 AND a.LAST_ACTIVE_TIME > (SYSDATE - INTERVAL '1' MINUTE)) main_query JOIN dba_users u ON main_query.USER_ID = u.USER_ID WHERE rnk <= 100
