<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="30 seconds">

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/tmp/logs/heal-ui-service.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</Pattern>
        </encoder>

        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <FileNamePattern>/tmp/logs/heal-ui-service.%i.log</FileNamePattern>
            <MinIndex>1</MinIndex>
            <MaxIndex>{{ key "service/uiservice/log/maxfiles"}}</MaxIndex>
        </rollingPolicy>

        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <MaxFileSize>{{ key "service/uiservice/log/maxfilesize"}}</MaxFileSize>
        </triggeringPolicy>
    </appender>

    <appender name="CORE_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/tmp/logs/heal-ui-service-core.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</Pattern>
        </encoder>

        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <FileNamePattern>/tmp/logs/heal-ui-service-core.%i.log</FileNamePattern>
            <MinIndex>1</MinIndex>
            <MaxIndex>{{ key "service/uiservice/log/root/maxfiles"}}</MaxIndex>
        </rollingPolicy>

        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <MaxFileSize>{{ key "service/uiservice/log/root/maxfilesize"}}</MaxFileSize>
        </triggeringPolicy>
    </appender>

    <appender name="STATS_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/tmp/logs/heal-ui-service-stats.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
           <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</Pattern>
        </encoder>

        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
           <FileNamePattern>/tmp/logs/heal-ui-service-stats.%i.log</FileNamePattern>
           <MinIndex>1</MinIndex>
           <MaxIndex>{{ key "service/uiservice/log/stats/maxfiles"}}</MaxIndex>
        </rollingPolicy>

        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
           <MaxFileSize>{{ key "service/uiservice/log/stats/maxfilesize"}}</MaxFileSize>
        </triggeringPolicy>
    </appender>

    <appender name="ERROR_LOGS_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/tmp/logs/heal-ui-service-errors.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
           <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</Pattern>
        </encoder>

        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
           <FileNamePattern>/tmp/logs/heal-ui-service-errors.%i.log</FileNamePattern>
           <MinIndex>1</MinIndex>
          <MaxIndex>{{ key "service/uiservice/log/maxfiles"}}</MaxIndex>
        </rollingPolicy>

        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
             <MaxFileSize>{{ key "service/uiservice/log/maxfilesize"}}</MaxFileSize>
        </triggeringPolicy>
    </appender>

    <logger name="com.appnomic.appsone.api" level="{{ key "service/uiservice/log/mode"}}" additivity="false">
        <appender-ref ref="FILE"/>
    </logger>

    <logger name="com.appnomic.appsone.api.cache.HealUICache" level="{{ key "service/uiservice/log/stats/mode"}}" additivity="false">
        <appender-ref ref="STATS_FILE"/>
    </logger>

    <logger name="com.appnomic.appsone.api.logging.PrintErrorLogs" level="ERROR" additivity="false">
        <appender-ref ref="ERROR_LOGS_FILE"/>
    </logger>

    <root level="{{ key "service/uiservice/log/root/mode"}}">
        <appender-ref ref="CORE_FILE"/>
    </root>
</configuration>
