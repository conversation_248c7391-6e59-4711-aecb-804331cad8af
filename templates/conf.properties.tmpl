#=====================================
# KEYCLOAK CONFIG
#KeyCloak parameters, these are used for session management
#=====================================
keycloak.ip={{ key "service/keycloak/hostname" }}
keycloak.port={{ key "service/keycloak/port/https" }}
keycloak.user={{ key "service/keycloak/username" }}
keycloak.pwd={{ key "service/keycloak/password" }}

#======================================
# Mysql Database Configuration
#=======================================
mysql.server.connection.url=jdbc:mysql://{{ key "service/perconadb/node1/ip" }}:{{ key "service/perconadb/node1/port" }}/appsone?{{ key "service/keycloak/jdbcparams" }}
mysql.database.username={{ key "service/perconadb/username" }}
mysql.database.password={{ key "service/perconadb/password_ui" }}
mysql.database.pool.size={{ key "service/uiservice/perconadb/poolsize" }}
mysql.database.pool.size.max={{ key "service/uiservice/perconadb/maxpoolsize" }}


timezone.offset={{ key "service/keycloak/timezoneoffset" }}

#=======================================
# Cache Service Configuration
#=======================================
# Cache refresh interval
cache.timeout = {{ key "service/uiservice/cacheTimeout"}}
http.handler.max.threads = {{ key "service/uiservice/http/handler/threads" }}

#=======================================
# Signal Close Interval
#=======================================
signal.close.window.time={{ key "service/pipeline/signalclosewindowtime" }}
show.application.sdm.topology={{key "service/uiservice/sdm/event"}}
signal.close.window.time.check.enabled={{ key "service/pipeline/signalclosewindowtime/check/enabled" }}

#======================================
# Signal description Configuration
#=======================================
signal.problem.description={{ key "service/uiservice/signal/problem/description"}}
signal.warning.description={{ key "service/uiservice/signal/warning/description"}}
#signal.info.description=

#======================================
# Signal description Configuration
#=======================================
opensearch.index.extension={{ key "service/uiservice/opensearch/index/extension" }}
forensic.lookahead.interval.minutes={{ key "service/uiservice/forensic/lookaheadintervalminute" }}
forensic.lookback.interval.minutes={{ key "service/uiservice/forensic/lookbackintervalminute" }}
forensic.level={{ key "service/uiservice/forensic/level" }}

instance.health.min={{ key "service/uiservice/instance/healthintervalminute" }}

keystore.file.path={{key "service/uiservice/keystore/path"}}
truststore.file.path={{key "service/uiservice/truststore/path"}}
static.files.path={{key "service/uiservice/frontend/path"}}
keystore.password={{key "service/uiservice/keystore/password"}}
truststore.password={{key "service/uiservice/truststore/password"}}

admin.user.identifier={{key "admin/user/identifier"}}
#=======================================
# Redis configuration
#=======================================
redis.hosts={{key "service/redis/nodes"}}
redis.ssl.enabled={{key "service/redis/sslenable"}}
redis.username={{key "service/redis/username"}}
redis.password={{key "service/redis/password/encrypted"}}
redis.cluster.mode= {{ key "service/redis/cluster/mode" }}
offset.from.gmt={{key "service/uiservice/offset/from/gmt"}}

health.metrics.scheduler.milliseconds={{key "service/uiservice/health/metrics/loginterval/milliseconds"}}
https.port={{key "service/uiservice/server/port"}}
html.encoder.enable={{key "service/uiservice/html/encoder/enable"}}
service.uiservice.txn.check.raw.index={{key "service/uiservice/txn/check/raw/index"}}
top.anomaly.count.from.opensearch={{key "service/uiservice/top/anomaly/count/from/opensearch"}}
cache.maximum.size={{key "service/uiservice/cache/max/size"}}
cache.timeout={{key "service/uiservice/cache/timeout/minutes"}}
cache.timeout.maintenance.data={{key "service/uiservice/cache/timeout/maintenance/data/minutes"}}
cache.refresh.scheduler.milliseconds={{key "service/uiservice/cache/refresh/scheduler/interval/milliseconds"}}

#======================================
# Reports
#======================================
report.data.service.url={{ key "report/data/service/url" }}
resend.mail.service.url={{ key "report/mail/service/url" }}
report.ui.service.output.path.container={{ key "service/uiservice/reports/output/path/container" }}

#=======================================
#JEAGER JIM TRACE CONFIG
#=======================================
jaeger.ip={{key "service/jaeger/ip"}}
jaeger.port={{key "service/jaeger/port"}}