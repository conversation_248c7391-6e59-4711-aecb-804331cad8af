Index: src/main/java/com/appnomic/appsone/api/beans/PairDataBean.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
--- src/main/java/com/appnomic/appsone/api/beans/PairDataBean.java	(date 1556613838000)
+++ src/main/java/com/appnomic/appsone/api/beans/PairDataBean.java	(date 1556613838000)
@@ -0,0 +1,18 @@
+package com.appnomic.appsone.api.beans;
+
+import lombok.Data;
+
+@Data
+public class PairDataBean {
+
+    private int id;
+
+    private int pairTypeId;
+
+    private String pairTypeName;
+
+    private String paramKey;
+
+    private String paramValue;
+
+}
Index: src/main/java/com/appnomic/appsone/api/dao/RulesDao.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
--- src/main/java/com/appnomic/appsone/api/dao/RulesDao.java	(date 1556624802000)
+++ src/main/java/com/appnomic/appsone/api/dao/RulesDao.java	(date 1556624802000)
@@ -0,0 +1,34 @@
+package com.appnomic.appsone.api.dao;
+
+import com.appnomic.appsone.api.beans.PairDataBean;
+import com.appnomic.appsone.api.beans.RegexTypeDetailBean;
+import com.appnomic.appsone.api.beans.RequestTypeDetailBean;
+import com.appnomic.appsone.api.beans.RulesBean;
+import org.skife.jdbi.v2.sqlobject.Bind;
+import org.skife.jdbi.v2.sqlobject.SqlQuery;
+import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
+import org.skife.jdbi.v2.sqlobject.stringtemplate.UseStringTemplate3StatementLocator;
+import org.skife.jdbi.v2.tweak.BeanMapperFactory;
+
+import java.util.List;
+
+@UseStringTemplate3StatementLocator
+public interface RulesDao {
+
+    @RegisterMapperFactory(BeanMapperFactory.class)
+    @SqlQuery("select r.id, r.name, r.is_enabled isEnabled, r.order, r.rule_type_id ruleTypeId from rules r where account_id = :accountId")
+    List<RulesBean> getRulesByAccountId(@Bind("accountId") Integer accountId);
+
+    @RegisterMapperFactory(BeanMapperFactory.class)
+    @SqlQuery("select id, initial_pattern initialPattern, last_pattern endPattern, length from tcp_patterns where rule_id = :ruleId")
+    RegexTypeDetailBean getRegexTypeDetail(@Bind("ruleId") Integer ruleId);
+
+    @RegisterMapperFactory(BeanMapperFactory.class)
+    @SqlQuery("select id, first_uri_segments firstSegment, last_uri_segments lastSegment, complete_uri completeURI, payload_type_id payloadTypeId from http_patterns where rule_id = :ruleId")
+    RequestTypeDetailBean getRequestTypeDetail(@Bind("ruleId") Integer ruleId);
+
+    @RegisterMapperFactory(BeanMapperFactory.class)
+    @SqlQuery("select id, pair_type_id pairTypeId, pair_key paramKey, pair_value paramValue from http_pair_data where rule_id = :ruleId and http_pattern_id = :httpPatternId")
+    List<PairDataBean> getPairDataList(@Bind("ruleId") Integer ruleId, @Bind("httpPatternId") Integer httpPatternId);
+
+}
Index: src/main/java/com/appnomic/appsone/api/service/mysql/MasterDataService.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
--- src/main/java/com/appnomic/appsone/api/service/mysql/MasterDataService.java	(revision 7fe603b6a08fd70888a9b7f87491895e1b52363d)
+++ src/main/java/com/appnomic/appsone/api/service/mysql/MasterDataService.java	(date 1556624568000)
@@ -4,6 +4,7 @@
 import com.appnomic.appsone.api.beans.ApplicationThresholdDetailsBean;
 import com.appnomic.appsone.api.common.Constants;
 import com.appnomic.appsone.api.dao.MasterDataDao;
+import com.appnomic.appsone.api.dao.RulesDao;
 import com.appnomic.appsone.api.manager.MySQLConnectionManager;
 import com.appnomic.appsone.api.pojo.*;
 import com.appnomic.appsone.api.util.ConfProperties;
@@ -24,6 +25,7 @@
 public class MasterDataService {
     private static final Logger logger = LoggerFactory.getLogger(MasterDataService.class);
     private static MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().getHandle().onDemand(MasterDataDao.class);
+    private static RulesDao rulesDao = MySQLConnectionManager.getInstance().getHandle().onDemand(RulesDao.class);
     private static String timezoneKey = ConfProperties.getString(Constants.TIMEZONE_TAG_DETAILS_IDETIFIER,
             Constants.TIMEZONE_TAG_DETAILS_IDETIFIER_DEFAULT);
     private static String accountTableRefName = ConfProperties.getString(Constants.ACCOUNT_TABLE_NAME_MYSQL,
@@ -505,4 +507,39 @@
         }
         return null;
     }
+
+    public static List<RulesBean> getRules(int accountId){
+        try{
+            List<RulesBean> rules = rulesDao.getRulesByAccountId(accountId);
+            for(RulesBean rule : rules){
+                rule.setRuleTypeName(getNameFromMSTSubType(rule.getRuleTypeId()));
+                RegexTypeDetailBean regexTypeDetailBean = rulesDao.getRegexTypeDetail(rule.getId());
+                RequestTypeDetailBean requestTypeDetailBean = rulesDao.getRequestTypeDetail(rule.getId());
+                if(requestTypeDetailBean != null){
+                    requestTypeDetailBean.setPayloadTypeName(
+                            getNameFromMSTSubType(requestTypeDetailBean.getPayloadTypeId())
+                    );
+                    List<PairDataBean> pairDataBeans = rulesDao.getPairDataList(rule.getId(), requestTypeDetailBean.getId());
+                    for(PairDataBean pairData : pairDataBeans){
+                        pairData.setPairTypeName(getNameFromMSTSubType(pairData.getPairTypeId()));
+                    }
+                    requestTypeDetailBean.setPairData(pairDataBeans);
+
+
+                }
+                rule.setRegexTypeDetails(regexTypeDetailBean);
+                rule.setRequestTypeDetails(requestTypeDetailBean);
+            }
+            return rules;
+        }catch (Exception e){
+            logger.error("Error occurred while fetching rules", e);
+        }
+
+        return null;
+    }
+
+    private static String getNameFromMSTSubType(Integer mstSubTypeId){
+        MasterSubTypeBean masterSubTypeBean = getMasterSubTypeDetailsForId(mstSubTypeId);
+        return masterSubTypeBean != null ? masterSubTypeBean.getName() : null;
+    }
 }
Index: src/main/java/com/appnomic/appsone/api/beans/RequestTypeDetailBean.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
--- src/main/java/com/appnomic/appsone/api/beans/RequestTypeDetailBean.java	(date *************)
+++ src/main/java/com/appnomic/appsone/api/beans/RequestTypeDetailBean.java	(date *************)
@@ -0,0 +1,23 @@
+package com.appnomic.appsone.api.beans;
+
+import lombok.Data;
+
+import java.util.List;
+
+@Data
+public class RequestTypeDetailBean {
+
+    private int id;
+
+    private String firstSegment;
+
+    private String lastSegment;
+
+    private String completeURI;
+
+    private int payloadTypeId;
+
+    private String payloadTypeName;
+
+    private List<PairDataBean> pairData;
+}
Index: src/main/java/com/appnomic/appsone/api/pojo/AccountConfiguration.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
--- src/main/java/com/appnomic/appsone/api/pojo/AccountConfiguration.java	(revision 7fe603b6a08fd70888a9b7f87491895e1b52363d)
+++ src/main/java/com/appnomic/appsone/api/pojo/AccountConfiguration.java	(date *************)
@@ -1,5 +1,6 @@
 package com.appnomic.appsone.api.pojo;
 
+import com.appnomic.appsone.api.beans.RulesBean;
 import lombok.Data;
 
 import java.util.List;
@@ -16,5 +17,6 @@
     private List<TransactionConfig> transactionConfigs;
     private List<CompInstanceClusterConfig> compInstanceDetail;
     private List<ComponentKpiDetail> componentDetail;
+    private List<RulesBean> rules;
 
 }
Index: src/main/java/com/appnomic/appsone/api/beans/RegexTypeDetailBean.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
--- src/main/java/com/appnomic/appsone/api/beans/RegexTypeDetailBean.java	(date *************)
+++ src/main/java/com/appnomic/appsone/api/beans/RegexTypeDetailBean.java	(date *************)
@@ -0,0 +1,15 @@
+package com.appnomic.appsone.api.beans;
+
+import lombok.Data;
+
+@Data
+public class RegexTypeDetailBean {
+
+    private int id;
+
+    private String initialPattern;
+
+    private String endPattern;
+
+    private int length;
+}
Index: src/main/java/com/appnomic/appsone/api/beans/RulesBean.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
--- src/main/java/com/appnomic/appsone/api/beans/RulesBean.java	(date 1556623995000)
+++ src/main/java/com/appnomic/appsone/api/beans/RulesBean.java	(date 1556623995000)
@@ -0,0 +1,30 @@
+package com.appnomic.appsone.api.beans;
+
+import lombok.Data;
+
+import java.util.List;
+import java.util.Map;
+
+@Data
+public class RulesBean {
+
+    private int id;
+
+    private String name;
+
+    private boolean isEnabled;
+
+    private int order;
+
+    private int ruleTypeId;
+
+    private String ruleTypeName;
+
+    private String txnGroupName;
+
+    private List<Map<String, String>> tags;
+
+    private RegexTypeDetailBean regexTypeDetails;
+
+    private RequestTypeDetailBean requestTypeDetails;
+}
Index: src/main/java/com/appnomic/appsone/api/service/ConfigurationDataService.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
--- src/main/java/com/appnomic/appsone/api/service/ConfigurationDataService.java	(revision 7fe603b6a08fd70888a9b7f87491895e1b52363d)
+++ src/main/java/com/appnomic/appsone/api/service/ConfigurationDataService.java	(date *************)
@@ -77,6 +77,9 @@
                 List<ComponentKpiDetail> componentKpiDetailList = getComponentKpiDetails(account.getAccountId());
                 accountConfiguration.setComponentDetail(componentKpiDetailList);
 
+                List<RulesBean> rulesBeanList = getRulesList(account.getAccountId(), allAccountDetails);
+                accountConfiguration.setRules(rulesBeanList);
+
                 accountConfigurations.add(accountConfiguration);
 
             }
@@ -388,6 +391,73 @@
         return agentConfigList;
     }
 
+    private static List<RulesBean> getRulesList(Integer accountId, AllAccountDetails allAccountDetails){
+
+        List<RulesBean> rules = new ArrayList<>();
+        List<TagMappingDetails> tagMappingDetailsList = allAccountDetails.getTagMappingDetailsList();
+        List<TagDetailsBean>  tagDetailsBeanList = allAccountDetails.getTagDetailsBeanList();
+        Predicate<TagMappingDetails> rulesPredicate = a -> a.getObjectRefTable().equalsIgnoreCase("rules");
+        List<TagMappingDetails> rulesTagMappingList = new ArrayList<>();
+        if (tagDetailsBeanList != null) {
+            rulesTagMappingList = tagMappingDetailsList.stream()
+                    .filter(rulesPredicate)
+                    .collect(Collectors.toList());
+        }
+
+        try {
+            rules = masterCache.getRules(accountId);
+
+            for(RulesBean rule : rules){
+
+                List<Map<String, String>> tags = new ArrayList<>();
+
+                rulesTagMappingList.stream()
+                        .filter(tag-> tag.getObjectId() == rule.getId())
+                        .forEach(tagMappingDetails -> {
+                            Map<String, String> tag = new HashMap<>();
+                            Optional<TagDetailsBean> findAny =  tagDetailsBeanList.stream()
+                                    .filter(tagDetailsBean -> tagDetailsBean.getId() == tagMappingDetails.getTagId())
+                                    .findAny();
+                            /**
+                             * If tag details is present then get the tags data
+                             */
+                            if (findAny.isPresent()) {
+                                TagDetailsBean tagDetailsBean = findAny.get();
+                                String refTable = tagDetailsBean.getRefTable();
+                                /**
+                                 * If reference table is null then add the key, value from tag mapping data
+                                 */
+                                if (refTable == null || refTable.equalsIgnoreCase("null") || refTable.length() < 1) {
+                                    tag.put("tagKey", tagMappingDetails.getTagKey());
+                                    tag.put("tagValue", tagMappingDetails.getTagValue());
+                                    tag.put("tagType", tagDetailsBean.getName());
+                                    tags.add(tag);
+                                }else {
+                                    /**
+                                     * If reference table is not null then get the key and value from table
+                                     */
+                                    TagPreDefinedData preDefinedData = TagsDataService.getTagData(refTable, tagMappingDetails.getTagValue(),
+                                            tagDetailsBean.getRefSelectColumnName(), tagDetailsBean.getRefWhereColumnName(), accountId);
+                                    if (preDefinedData == null) preDefinedData = TagsDataService.getTagData(refTable, tagMappingDetails.getTagKey(),
+                                            tagDetailsBean.getRefSelectColumnName(), tagDetailsBean.getRefWhereColumnName(), accountId);
+                                    if (preDefinedData != null) {
+                                        tag.put("tagKey", preDefinedData.getTagKey());
+                                        tag.put("tagValue", preDefinedData.getTagValue());
+                                        tag.put("tagType", tagDetailsBean.getName());
+                                        tags.add(tag);
+                                    }
+                                }
+                            }
+                        });
+                rule.setTags(tags);
+
+            }
+        }catch (Exception e){
+            log.error("Error occurred while getting rules", e);
+        }
+        return rules;
+    }
+
     private static void getTagsWithLayer() {
 
     }
Index: src/main/java/com/appnomic/appsone/api/cache/MasterCache.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
--- src/main/java/com/appnomic/appsone/api/cache/MasterCache.java	(revision 7fe603b6a08fd70888a9b7f87491895e1b52363d)
+++ src/main/java/com/appnomic/appsone/api/cache/MasterCache.java	(date *************)
@@ -708,6 +708,15 @@
                 }
             });
 
+    private LoadingCache<Integer, List<RulesBean>> rules = CacheBuilder.newBuilder()
+            .maximumSize(maxSize)
+            .expireAfterWrite(cacheTimeout, TimeUnit.MINUTES)
+            .build(new CacheLoader<Integer, List<RulesBean>>() {
+                @Override
+                public List<RulesBean> load(Integer accountId) throws Exception {
+                    return MasterDataService.getRules(accountId);
+                }
+            });
 
     private MasterCache() {
     }
@@ -1521,4 +1530,13 @@
         }
         return null;
     }
+
+    public List<RulesBean> getRules(Integer accountId) {
+        try {
+            return rules.get(accountId);
+        }catch (Exception e) {
+            log.error("Error occurred while getting rules ", e);
+        }
+        return null;
+    }
 }
