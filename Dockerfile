FROM healadmin/openjdk:11.0.24

RUN apt-get update && \
    apt-get upgrade -y && \
    apt-get -y install ca-certificates make openssl bash && \
    apt-get clean
ENV PATH "$PATH:/usr/local/ssl/bin"

ADD http://192.168.13.69:8081/nexus/repository/third-party/consul-template /usr/local/bin/consul-template

ADD ./conf /etc/consul-template/conf
ADD ./templates /etc/consul-template/templates

COPY ./heal-ui-service /opt/heal-ui-service/
COPY ./entrypoint.sh /opt/heal-ui-service/entrypoint.sh

RUN chmod +x /opt/heal-ui-service/entrypoint.sh /usr/local/bin/consul-template && \
    mkdir -p /tmp/logs

EXPOSE 8996
ENTRYPOINT ["/opt/heal-ui-service/entrypoint.sh"]