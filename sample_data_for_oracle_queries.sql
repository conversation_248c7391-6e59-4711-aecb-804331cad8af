-- Sample data for oracle_queries_for_time_series_data.txt

-- For Query 1: Create sequences with cycle_flag='Y'
CREATE SEQUENCE test_cyclic_seq1
  START WITH 1
  INCREMENT BY 1
  MINVALUE 1
  MAXVALUE 100
  CYCLE
  CACHE 20;

CREATE SEQUENCE test_cyclic_seq2
  START WITH 1
  INCREMENT BY 1
  MINVALUE 1
  MAXVALUE 50
  CYCLE
  CACHE 10;

-- Advance sequences to meet the condition (last_number-MIN_VALUE)/(max_value-min_value) > 10
DECLARE
  v_dummy NUMBER;
BEGIN
  -- Advance first sequence
  FOR i IN 1..950 LOOP
    SELECT test_cyclic_seq1.NEXTVAL INTO v_dummy FROM dual;
  END LOOP;
  
  -- Advance second sequence  
  FOR i IN 1..500 LOOP
    SELECT test_cyclic_seq2.NEXTVAL INTO v_dummy FROM dual;
  END LOOP;
END;
/

-- For Query 2: Create test user and execute some SQL to populate v$sqlarea and v$active_session_history
CREATE USER testuser IDENTIFIED BY testpass123;
GRANT CONNECT, RESOURCE TO testuser;

-- Create a test table to generate SQL activity
CREATE TABLE test_activity_table (
  id NUMBER PRIMARY KEY,
  name VARCHAR2(100),
  created_date DATE
);

-- Insert data to create buffer gets activity
INSERT INTO test_activity_table VALUES (1, 'Test Record 1', SYSDATE);
INSERT INTO test_activity_table VALUES (2, 'Test Record 2', SYSDATE);
INSERT INTO test_activity_table VALUES (3, 'Test Record 3', SYSDATE);
COMMIT;

-- Execute queries to populate v$sqlarea with high buffer gets
DECLARE
  v_count NUMBER;
BEGIN
  FOR i IN 1..100 LOOP
    SELECT COUNT(*) INTO v_count FROM test_activity_table WHERE id > 0;
  END LOOP;
END;
/

-- Note: v$active_session_history is populated automatically by Oracle
-- The queries should now return data when executed within the time window
