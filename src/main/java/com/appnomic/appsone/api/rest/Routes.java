package com.appnomic.appsone.api.rest;

import com.appnomic.appsone.api.beans.DateComponent;
import com.appnomic.appsone.api.beans.MaintenanceDetailsBean;
import com.appnomic.appsone.api.beans.MasterFeaturesBean;
import com.appnomic.appsone.api.beans.tfp.TFPTopNEvents;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.dao.mysql.entity.InstallationAttributeBean;
import com.appnomic.appsone.api.mobile.model.*;
import com.appnomic.appsone.api.mobile.service.*;
import com.appnomic.appsone.api.pojo.*;
import com.appnomic.appsone.api.pojo.applicationhealth.ApplicationHealthDetail;
import com.appnomic.appsone.api.pojo.heatmap.AnomalyDetailedCategory;
import com.appnomic.appsone.api.pojo.tfp.TFPAnomalyTransactionDetails;
import com.appnomic.appsone.api.pojo.tfp.TFPPerformanceDetails;
import com.appnomic.appsone.api.pojo.tfp.TFPServiceDetails;
import com.appnomic.appsone.api.reports.service.*;
import com.appnomic.appsone.api.service.*;
import com.appnomic.appsone.api.service.mysql.FileUploadDataService;
import com.appnomic.appsone.api.service.mysql.TransactionConfigService;
import com.appnomic.appsone.api.service.mysql.TransactionTagDataService;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.api.util.JsonTransformer;
import com.appnomic.appsone.api.util.UserValidationUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Spark;
import spark.servlet.SparkApplication;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static spark.Spark.*;

public class Routes implements SparkApplication {
    private static final Logger log = LoggerFactory.getLogger(Routes.class);
    private static final String API_INVOKED_ACCESS_LOG_STATEMENT = "Called API {} at time {}";

    @Override
    public void init() {
        String staticFileLocation = ConfProperties.getString(Constants.STATIC_FILES_LOCATIONS, Constants.STATIC_FILES_LOCATIONS_DEFAULT);
        int httpPort = ConfProperties.getInt(Constants.HTTPS_PORT_PROPERTY_NAME, Constants.HTTPS_PORT_DEFAULT);
        String keystoreFilePath = ConfProperties.getString(Constants.KEYSTORE_FILE_NAME, Constants.KEYSTORE_FILE_NAME_DEFAULT);
        String keystorePassword = ConfProperties.getString(Constants.KEYSTORE_PASSWORD, Constants.KEYSTORE_PASSWORD_DEFAULT);
        String truststoreFilePath = ConfProperties.getString(Constants.TRUSTSTORE_FILE_NAME, Constants.TRUSTSTORE_FILE_NAME_DEFAULT);
        String truststorePassword = ConfProperties.getString(Constants.TRUSTSTORE_PASSWORD, Constants.TRUSTSTORE_PASSWORD_DEFAULT);
        int maxThreads = ConfProperties.getInt(Constants.HTTP_HANDLER_MAX_THREADS, Constants.HTTP_HANDLER_MAX_THREADS_DEFAULT);

        port(httpPort);

        log.info("Truststore:{}, keystore:{}", truststoreFilePath, keystoreFilePath);
        /*if (!keystoreFilePath.trim().isEmpty()) {
            secure(keystoreFilePath, keystorePassword,
                    !truststoreFilePath.trim().isEmpty() ? truststoreFilePath : null,
                    !truststorePassword.trim().isEmpty() ? truststorePassword : null);
        } else {
            log.warn("SSL is disabled for heal-ui-service port.");
        }*/

        log.info("HEAL dashboard to start with {} port, threads: {}.", httpPort, maxThreads);
        threadPool(maxThreads);

        //staticFiles.location(staticFileLocation);
        staticFiles.header("Server", ConfProperties.getHeaderConfigurations().getOrDefault("Server", ""));
        Spark.init();

        //Getting allowed method calls from headers_details.json
        List<String> allowedMethodCalls = Arrays.stream(ConfProperties.getHeaderConfigurations()
                .getOrDefault("Access-Control-Allow-Methods", "GET,DELETE,PUT,POST")
                .split(","))
                .collect(Collectors.toList());

        KeyCloakAuthService.init();

        before((request, response) -> {
            log.debug("Before method is called............{}", request.pathInfo());
            HealUICache.INSTANCE.updateRequests(1);
            String requestMethod = request.requestMethod().toUpperCase();

            //Restricting the method calls that is not mentioned in headers_details.json
            if (!allowedMethodCalls.contains(requestMethod)) {
                HealUICache.INSTANCE.updateUnauthorizedRequests(1);
                ConfProperties.getHeaderConfigurations().forEach(response::header);
                halt(405, "Method Not Allowed");
            }

            if (request.pathInfo().contains("download")) {
                String authKey = request.cookie("token");
                boolean authenticated = KeyCloakAuthService.isValidKey(authKey);
                if (!authenticated) {
                    HealUICache.INSTANCE.updateUnauthorizedRequests(1);
                    ConfProperties.getHeaderConfigurations().forEach(response::header);
                    halt(401, "You are not welcome here");
                }
            } else if ((!request.pathInfo().contains("keycloak-settings")
                    && !request.requestMethod().equalsIgnoreCase("OPTIONS"))
                    && !request.pathInfo().contains("mlwbData")
                    && !request.pathInfo().contains("baseline-kpi-data")
                    && !request.pathInfo().contains("baseline-transaction-data")
                    && !request.pathInfo().contains("uploadSummaryData")
                    && !request.pathInfo().contains("xpTConfiguration")
                    && !request.pathInfo().contains("xptNotification")
                    && !request.pathInfo().contains("ts-data")) {
                String authKey = request.headers(Constants.AUTHORIZATION_HEADER);

                /*if (authKey == null || authKey.trim().isEmpty()) {
                    HealUICache.INSTANCE.updateUnauthorizedRequests(1);
                    ConfProperties.getHeaderConfigurations().forEach(response::header);
                    log.error("Request [{}] does not have authorization header", request.uri());
                    halt(401, "Authorization token is invalid");
                }

                boolean userVerified = CommonUtils.verifyUserStatus(request);
                if (!userVerified) {
                    HealUICache.INSTANCE.updateUnauthorizedRequests(1);
                    ConfProperties.getHeaderConfigurations().forEach(response::header);
                    halt(401, "You are dormant.");
                }

                boolean authenticated = KeyCloakAuthService.isValidKey(authKey);
                if (!authenticated) {
                    HealUICache.INSTANCE.updateUnauthorizedRequests(1);
                    ConfProperties.getHeaderConfigurations().forEach(response::header);
                    halt(401, "You are not welcome here");
                }

                boolean isUserValid = UserValidationUtil.verifyUserAccessibility(request);
                if (!isUserValid) {
                    HealUICache.INSTANCE.updateAccessDeniedRequests(1);
                    ConfProperties.getHeaderConfigurations().forEach(response::header);
                    halt(401, "You do not have access to the resource");
                }*/
            } else {
                HealUICache.INSTANCE.updateSkipValidationRequests(1);
                log.warn("JWT, user status and user accessibility validation skipped. " +
                        "Request details: {}, {}, {}", request.pathInfo(), request.requestMethod(), request.headers());
            }

        });

        after((request, response) -> ConfProperties.getHeaderConfigurations().forEach(response::header));

        options("*", (request, response) -> {
            response.status(200);
            return response;
        });

        get(Constants.UI_BASE_URL + "/keycloak-settings", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            KeycloakSettingsResponse keycloakSettingsResponse = ConfigurationDataService.getKeyCloakSettings();
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return keycloakSettingsResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/date-components", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), st);
            GenericResponse<List<DateComponent>> genericResponse = ConfigurationDataService.getDateTimeDropdownList(response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return genericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/features", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<List<MasterFeaturesBean>> genericResponse = ConfigurationDataService.getMasterFeatures(response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return genericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            String authKey = request.headers("Authorization");
            AccountResponse accountResponse = AccountService.getAccountList(authKey);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return accountResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/applications", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            AccountService accountService = new AccountService();
            GenericResponse<List<ApplicationDetail>> listGenericResponse = accountService.getApplicationList(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return listGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/topology", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            TopologyDetailsResponse topologyDetailsResponse = TopologyDetailsService.getTopologyDetails(request);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return topologyDetailsResponse;
        }, new JsonTransformer());

        //TODO: This API is commented out due to duplication. Will have to check this further. For the time being it is commented out.
        /*get(Constants.UI_BASE_URL + "/accounts/:identifier/applications/:appId/topology", (request, response) -> {
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            return TopologyDetailsService.getTopologyDetailsForApplication(request);
        }, new JsonTransformer());*/

        get(Constants.UI_BASE_URL + "/accounts/:identifier/services/:serviceId/applications", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<Set<Application>> genericResponse = new ServiceApplicationsService().getServiceApplications(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return genericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/applications/:applicationId/topology", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            TopologyDetailsResponse topologyDetailsResponse = ApplicationSDMService.getApplicationTopologyDetails(request);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return topologyDetailsResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/services/:serviceId/config", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            ClusterKpiResponse clusterKpiResponse = ClusterKpiMappingService.getClusterKpiList(request);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return clusterKpiResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/services/:serviceId/instances", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            InstanceDetailsResponse instanceDetailsResponse = ClusterKpiMappingService.getInstanceList(request);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return instanceDetailsResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/instances/:instanceId/attributes", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<Map<String, String>> mapGenericResponse = ClusterKpiMappingService.getAttributes(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return mapGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/entry-points", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            TransactionGroupResponse transactionGroupResponse = TransactionGroupService.getTxnGroupList(request);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return transactionGroupResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/services/:serviceId/instances/:instanceId/kpis/:kpi_id/groups/:group_id/ts-data", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            KpiDataService dataService = new KpiDataService();
            GenericResponse<UIData> coreNGKpiResponse = dataService.fetchKpiDataService(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return coreNGKpiResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/instances/:instanceId/kpis/:kpi_id/groups/:group_id/availability/ts-data",
                (request, response) -> {
                    long st = System.currentTimeMillis();
                    log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
                    AvailabilityKpiDataService availabilityKpiDataService = new AvailabilityKpiDataService();
                    GenericResponse<KpiDataPoints> genericResponse = availabilityKpiDataService.getAvailabilityKpiDataList(request, response);
                    HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
                    HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
                    return genericResponse;
                }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/services/:serviceId/instances/:instanceId/categories/:categoryId/kpis", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            KPIAnomalyService kpiAnomalyService = new KPIAnomalyService();
            GenericResponse genericResponse = kpiAnomalyService.getSortedKpiList(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return genericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/services/:serviceId/instances/:instanceId/availability/kpis",
                (request, response) -> {
                    long st = System.currentTimeMillis();
                    log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
                    AvailabilityKpiService availabilityKpiService = new AvailabilityKpiService();
                    GenericResponse<List<AvailabilityKpi>> availabilityKpiResponse = availabilityKpiService.getAvailabilityKpiList(request, response);
                    HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
                    HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
                    return availabilityKpiResponse;
                }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/anomalyForensicData", (request, response) -> {
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            return "Hello there, Im WIP...";
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/services/:serviceId/instances/:instanceId/" +
                "response-type/:response-type/transactions/:txnId/kpis/:kpi-identifier/ts-data", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            TransactionKPITimeSeriesDataService transactionKPITimeSeriesDataService = new TransactionKPITimeSeriesDataService();
            GenericResponse<UIData> genericResponse = transactionKPITimeSeriesDataService.getTransactionKpiTSData(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return genericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/controllers/:controllerId/instances/:instanceId/" +
                "response-type/:response-type/transactions/:txnId/kpis/ts-data", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            TransactionPerformanceDataService txnPerformanceService = new TransactionPerformanceDataService();
            GenericResponse<UIData> genericResponse = txnPerformanceService.getTransactionPerformanceData(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return genericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/parent-applications/:parentApplicationIdentifier/applications", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            ParentApplicationService parentApplicationService = new ParentApplicationService();
            GenericResponse<List<com.heal.configuration.pojos.Application>> listGenericResponse = parentApplicationService.getApplicationListMappedtoParentApplication(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return listGenericResponse;
        }, new JsonTransformer());
        /**
         * =====================MAINTENANCE WINDOW SERVICES===================================================================
         */
        get(Constants.UI_BASE_URL + "/accounts/:identifier/services/:serviceId/maintenance-details", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            MaintenanceWindowService maintenanceWindowService = new MaintenanceWindowService();
            GenericResponse<List<MaintenanceDetailsBean>> listGenericResponse = maintenanceWindowService.getMaintenanceDetails(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return listGenericResponse;
        }, new JsonTransformer());


        /**
         * =====================PROBLEM PAGE SERVICES===================================================================
         */

        get(Constants.UI_BASE_URL + "/accounts/:identifier/signals", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            SignalDataService signalDataService = new SignalDataService();
            GenericResponse<Set<SignalData>> setGenericResponse = signalDataService.getSignalList(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return setGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/services/:serviceId/signals", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            SignalDataService signalDataService = new SignalDataService();
            GenericResponse<Set<SignalData>> setGenericResponse = signalDataService.getSignalList(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return setGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/signals/:signalId", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            SignalDataService signalDataService = new SignalDataService();
            GenericResponse<Set<SignalData>> setGenericResponse = signalDataService.getSignalList(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return setGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/applications/:applicationId/signals", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            ApplicationWiseSignalsService applicationHealthDashboard = new ApplicationWiseSignalsService();
            GenericResponse<Set<SignalData>> setGenericResponse = applicationHealthDashboard.getApplicationWiseSignals(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return setGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/services/:serviceId/signals/:signalId/incidents", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            ProblemDetailService problemDetailService = new ProblemDetailService();
            GenericResponse<Map<String, Map<Long, List<AnomalyEventDetail>>>> mapGenericResponse = problemDetailService.getProblemDetail(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return mapGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/services/:serviceId/kpis", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<List<TFPTopNEvents>> listGenericResponse = new ProblemDetailService().getTopNEvents(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return listGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/signals/:signalId/rca-path", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            RCAPathService rcaPathService = new RCAPathService();
            GenericResponse genericResponse = rcaPathService.getRCAPathDetails(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return genericResponse;
        }, new JsonTransformer());

        /**
         * =============================================================================================================
         */

        get(Constants.UI_BASE_URL + "/accounts/:identifier/instances/:instanceId/kpis/:kpiId/watcher/ts-data", (request,
                                                                                                                response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            WatcherKpiService watcherKpiServiceCassandra = new WatcherKpiService();
            WatcherKpiDetailResponseObject watcherKpiDetailResponseObject = watcherKpiServiceCassandra.getWatcherKpiData(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return watcherKpiDetailResponseObject;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/controllers/:controllerId/instances/:instanceId/response-type" +
                "/:response-type/transactions/kpis/:kpi-identifier/ts-data", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            TransactionTimeSeriesDataService transactionTimeSeriesDataService = new TransactionTimeSeriesDataService();
            GenericResponse genericResponse = transactionTimeSeriesDataService.getTransactionTimeSeriesData(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);

            return genericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/controllers/:controllerId/instances/:instanceId" +
                "/response-type/:response-type/transactions/kpis/:kpi-identifier/nts-data", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            TransactionNonTimeSeriesDataService txnNtsDataServices = new TransactionNonTimeSeriesDataService();
            GenericResponse<Map<String, String>> genericResponse = txnNtsDataServices.getTransactionNtsData(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return genericResponse;
        }, new JsonTransformer());


        get(Constants.UI_BASE_URL + "/accounts/:identifier/controllers/:controllerId/instances/:instanceId" +
                "/response-type/:response-type/:topN/transactions", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            TransactionDetailDataService transactionDetailDataService = new TransactionDetailDataService();
            GenericResponse<TransactionSortedStats> genericResponse = transactionDetailDataService.getTransactionStatistics(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return genericResponse;
        }, new JsonTransformer());


        get(Constants.UI_BASE_URL + "/accounts/:identifier/services/:serviceId/instances/:instanceId/response-type/:responseType/transactions/:txnId/audit",
                (request, response) -> {
                    long st = System.currentTimeMillis();
                    log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
                    GenericResponse<List<TransactionAuditData>> listGenericResponse = new TransactionAuditDataService().getTransactionAuditDetails(request, response);
                    HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
                    HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
                    return listGenericResponse;
                }, new JsonTransformer());

        /**
         * ==================================TIMEZONE SERVICES======================================================
         */

        get(Constants.UI_BASE_URL + "/timezones", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse genericResponse = new TimezoneService().getAllTimezones(response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return genericResponse;
        }, new JsonTransformer());

        post(Constants.UI_BASE_URL + "/users/:userId/timezones", ((request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<String> stringGenericResponse = new TimezoneService().setUserPreferredTimezone(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return stringGenericResponse;

        }), new JsonTransformer());

        get(Constants.UI_BASE_URL + "/users/:userId/timezones", ((request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse genericResponse = new TimezoneService().getUserPreferredTimezone(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return genericResponse;
        }), new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/services/:serviceId/timezones", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse genericResponse = new TimezoneService().getServiceTimezones(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);

            return genericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/signals/:signalId/timezones", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse genericResponse = new TimezoneService().getSignalTimezones(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return genericResponse;
        }, new JsonTransformer());

        //=========================================XXX==============================================================

        /**
         * ==================Forensic APIs=======================================
         */

        get(Constants.UI_BASE_URL + "/accounts/:identifier/instances/:instanceId/categories/:categoryId/forensics/nts-data",
                ForensicServices::getForensicDetailsList,
                new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/instances/:instanceId/categories/:categoryId/forensics/download", (request,
                                                                                                                              response) -> {
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            ForensicServices forensicServices = new ForensicServices();
            forensicServices.downloadForensicDetails(request, response);
            return response;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/services/:serviceId/transactions/exists",
                ForensicServices::getConfigDetails,
                new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/instances/:instanceId/kpis/:kpiId/group-value/:group-value/forensics", (request,
                                                                                                                                   response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            ForensicServices forensicServices = new ForensicServices();
            GenericResponse<List<KpiAnomalyData>> listGenericResponse = forensicServices.forensicGridData(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return listGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/services/:serviceId/heat-map", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<List<AnomalyDetailedCategory>> genericResponse = new HeatMapService().getHeatMapData(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return genericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/instances/:instanceId/kpis/ts-data", (request,
                                                                                                 response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            ForensicKpiService kpiService = new ForensicKpiService();
            GenericResponse<LinkedHashMap<String, LinkedHashMap<String, List<Long>>>> genericResponse = kpiService.getKpiTimeSeries(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return genericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/instances/:instanceId/forensics/kpis/top-n-sql", (request,
                                                                                                             response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            ForensicKpiService kpiService = new ForensicKpiService();
            GenericResponse<ForensicKpiDataResponse> forensicKpiDataResponseGenericResponse = kpiService.getForensicKpiTopNSQL(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return forensicKpiDataResponseGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/application-health", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            ApplicationHealthService applicationHealthService = new ApplicationHealthService();
            GenericResponse<List<ApplicationHealthDetail>> listGenericResponse = applicationHealthService.getApplicationHealthStatus(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return listGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/transactions/:transaction_id/config", (
                request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            TransactionConfigService transactionConfigService = new TransactionConfigService(request, response);
            GenericResponse genericResponse = transactionConfigService.getTransactionConfig();
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return genericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/services/:serviceId/transactions/tags", (
                request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            TransactionTagDataService transactionTagDataService = new TransactionTagDataService();
            GenericResponse genericResponse = transactionTagDataService.getTransactionTagDetails(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return genericResponse;
        }, new JsonTransformer());

        /**
         * ==================User Preference APIs=======================================
         */

        get(Constants.UI_BASE_URL + "/accounts/:identifier/user-preferences", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            UserPreferenceService userService = new UserPreferenceService();
            GenericResponse genericResponse = userService.getPreferenceList(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return genericResponse;
        }, new JsonTransformer());

        post(Constants.UI_BASE_URL + "/accounts/:identifier/user-preferences", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            UserPreferenceService userService = new UserPreferenceService();
            GenericResponse genericResponse = userService.addPreference(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return genericResponse;
        }, new JsonTransformer());

        put(Constants.UI_BASE_URL + "/accounts/:identifier/user-preferences/:preferenceId", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            UserPreferenceService userService = new UserPreferenceService();
            GenericResponse genericResponse = userService.updatePreference(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);

            return genericResponse;
        }, new JsonTransformer());

        delete(Constants.UI_BASE_URL + "/accounts/:identifier/user-preferences/:preferenceId", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            UserPreferenceService userService = new UserPreferenceService();
            GenericResponse genericResponse = userService.deletePreference(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return genericResponse;
        }, new JsonTransformer());

        post(Constants.UI_BASE_URL + "/accounts/:identifier/users/:userId/notifications", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse genericResponse = NotificationService.saveNotifications(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return genericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/users/:userId/notifications", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse genericResponse = NotificationService.getNotifications(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return genericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/users/access-info", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<UserAccessibleActions> userAccessibleActionsGenericResponse = new UserAccessibleActionService().getUserAccessibilityDetails(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);

            return userAccessibleActionsGenericResponse;
        }, new JsonTransformer());

        /**
         * TFP API's
         */
        get(Constants.UI_BASE_URL + "/accounts/:identifier/applications/:applicationId/inbounds", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse genericResponse = new TransactionFlowPathService().getInbounds(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return genericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/applications/:applicationId/outbounds", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<List<TFPServiceDetails>> genericResponse = new TransactionFlowPathService().getOutbounds(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return genericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/services/:serviceId/kpis/performance/nts-data", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<TFPPerformanceDetails> genericResponse = new TransactionFlowPathService().getTfpPerformance(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return genericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/services/:serviceId/instances/maintenance", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse genericResponse = new InstanceDataService().getInstances(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return genericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/source-services/:srcserviceid/destination-services/:dstserviceid/transactions", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<List<TFPAnomalyTransactionDetails>> genericResponse = new TransactionFlowPathInboundOutboundService().getOutboundTransactions(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return genericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/services/:serviceId/transactions", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<List<TFPAnomalyTransactionDetails>> genericResponse = new TransactionService().getServiceAnomalyTransactions(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return genericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/components/:componentId/applications/:applicationId/instances", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<Set<ComponentInstancesResponse>> listGenericResponse = new ConfigWatchService().getComponentInstances(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return listGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/instances/config-watch-compare", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<List<InstanceWiseKpis>> listGenericResponse = new ConfigWatchService().getConfigCompare(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return listGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/services/:serviceId/clusters/:clusterId/instances/:instanceId/categories", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<List<CategoryEvents>> listGenericResponse = new InstanceCategoryDetailsService().getInstanceCategoryDetails(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return listGenericResponse;
        }, new JsonTransformer());
        get(Constants.UI_BASE_URL + "/accounts/:identifier/services/:serviceId/clusters/:clusterId/event-details", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<List<InstanceEvents>> listGenericResponse = new InstanceEventService().getInstanceEventsDetails(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return listGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/notification-preference-category", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<NotificationPreferences> preferences = new UserNotificationPreferenceService().getNotificationPreferences(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return preferences;
        }, new JsonTransformer());

        /**
         * ================================== Mobile Monitoring APIs (Start) ======================================================
         */

        // INFO : Mobile Monitoring Dashboard

        get(Constants.UI_BASE_URL + "/monitoring-applications", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<MonitoringApplication> monitoringApplicationGenericResponse = new MonitoringApplicationsService().getMonitoringApplications(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return monitoringApplicationGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/mobile-apps", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<Map<String, String>> mapGenericResponse = new MonitoringDashboardService().getMobileApps();
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return mapGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/app-os", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<List<String>> listGenericResponse = new MonitoringDashboardService().getAppOS(request);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return listGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/app-versions", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<List<String>> listGenericResponse = new MonitoringDashboardService().getAppVersions(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return listGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/top-error-files", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<List<TopErrorFile>> listGenericResponse = new MonitoringDashboardService().getTopErrorFiles(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return listGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/top-slow-pages", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<List<TopSlowPage>> listGenericResponse = new MonitoringDashboardService().getTopSlowPages(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return listGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/slow-pages-trend", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<Map<String, Long>> mapGenericResponse = new MonitoringDashboardService().getSlowPagesTrend(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return mapGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/error-files-trend", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<Map<String, Long>> mapGenericResponse = new MonitoringDashboardService().getErrorFilesTrend(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return mapGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/latest-crashed-app-versions", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<List<String>> listGenericResponse = new MonitoringDashboardService().getLatestCrashedAppVersions(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return listGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/version-crash-trend", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<Map<String, Map<String, Long>>> mapGenericResponse = new MonitoringDashboardService().getVersionCrashTrend(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return mapGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/usage-summary", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<UsageSummary> usageSummaryGenericResponse = new MonitoringDashboardService().getUsageSummary(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return usageSummaryGenericResponse;
        }, new JsonTransformer());

        // INFO : Crash Overview

        get(Constants.UI_BASE_URL + "/crash-overview", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<CrashOverview> crashOverviewGenericResponse = new CrashMonitoringService().getCrashOverview(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return crashOverviewGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/crash-overview/crash-trend", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<CrashTrend> crashTrendGenericResponse = new CrashMonitoringService().getCrashTrend(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return crashTrendGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/crash-overview/crash-free-users-trend", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<CrashFreeUsersTrend> crashFreeUsersTrendGenericResponse = new CrashMonitoringService().getCrashFreeUsersTrend(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return crashFreeUsersTrendGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/crash-overview/issues", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<List<Issue>> listGenericResponse = new CrashMonitoringService().getIssuesList(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return listGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/crash-overview/issues/:id/event-version-trend", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<Map<String, EventVersionTrend>> mapGenericResponse = new CrashMonitoringService().getEventVersionTrend(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return mapGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/crash-overview/issues/:id/stack-trace", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<StackTrace> stackTraceGenericResponse = new CrashMonitoringService().getCrashStackTrace(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return stackTraceGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/crash-overview/issues/:id/device-status", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<List<DeviceStatus>> listGenericResponse = new CrashMonitoringService().getDeviceStatus(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return listGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/crash-overview/issues/:id/devices", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<List<Devices>> listGenericResponse = new CrashMonitoringService().getDevices(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return listGenericResponse;
        }, new JsonTransformer());

        // INFO : Network Monitoring

        get(Constants.UI_BASE_URL + "/network-overview", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<List<NetworkRequest>> listGenericResponse = new NetworkMonitoringService().getNetworkRequests(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return listGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/network-overview/filters", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<Map<String, Set<String>>> mapGenericResponse = new NetworkMonitoringService().getNetworkRequestFilters(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return mapGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/network-overview/:request-name/response-time-trend", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<Map<String, BigDecimal>> mapGenericResponse = new NetworkMonitoringService().getResponseTimeTrend(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return mapGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/network-overview/:request-name/response-payload-size-trend", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<Map<String, BigDecimal>> mapGenericResponse = new NetworkMonitoringService().getResponsePayloadSizeTrend(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return mapGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/network-overview/:request-name/request-payload-size-trend", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<Map<String, BigDecimal>> mapGenericResponse = new NetworkMonitoringService().getRequestPayloadSizeTrend(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return mapGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/network-overview/:request-name/success-rate-trend", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<Map<String, BigDecimal>> mapGenericResponse = new NetworkMonitoringService().getSuccessRateTrend(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return mapGenericResponse;
        }, new JsonTransformer());

        // INFO : ANR

        get(Constants.UI_BASE_URL + "/anr-overview", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<ANROverview> anrOverviewGenericResponse = new ANRService().getANROverview(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return anrOverviewGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/anr-overview/details", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<List<ANRDetail>> listGenericResponse = new ANRService().getANRDetails(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return listGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/anr-overview/frozen-slow-frames-trend", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<FrozenSlowFramesTrend> frozenSlowFramesTrendGenericResponse = new ANRService().getFrozenSlowFramesTrend(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return frozenSlowFramesTrendGenericResponse;
        }, new JsonTransformer());

        // INFO : Usage Dashboard

        get(Constants.UI_BASE_URL + "/usage-dashboard/new-users-active-users-trend", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<NewUsersActiveUsersTrend> newUsersActiveUsersTrendGenericResponse = new UsageDashboardService().getNewUsersActiveUsersTrend(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return newUsersActiveUsersTrendGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/usage-dashboard/user-engagement-trend", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<Map<String, BigDecimal>> mapGenericResponse = new UsageDashboardService().getUserEngagementTrend(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return mapGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/usage-dashboard/user-actions", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<List<UserAction>> listGenericResponse = new UsageDashboardService().getUserActions(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return listGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/usage-dashboard/carrier-overview", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<List<CarrierOverview>> listGenericResponse = new UsageDashboardService().getCarrierOverview(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            return listGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/usage-dashboard/user-retention-trend", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<Map<String, BigDecimal>> mapGenericResponse = new UsageDashboardService().getUserRetentionTrend(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return mapGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/users-across-geo", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<List<GeoLocation>> listGenericResponse = new GeoLocationService().getGeoLocationsOverview(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return listGenericResponse;
        }, new JsonTransformer());

        /**
         * ================================== Mobile Monitoring APIs (End) ======================================================
         */

        /**
         * ================================== Forensic Notification APIs ======================================================
         */

        get(Constants.UI_BASE_URL + "/accounts/:identifier/users/:userId/notifications-forensic", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<ForensicNotificationConfiguration> forensicNotificationConfigurationGenericResponse = new GetForensicNotificationConfigurationsService().getForensicNotificationConfigurations(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return forensicNotificationConfigurationGenericResponse;
        }, new JsonTransformer());

        put(Constants.UI_BASE_URL + "/accounts/:identifier/users/:userId/notifications-forensic", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<String> stringGenericResponse = new UpdateForensicNotificationConfigurationService().updateForensicNotificationConfiguration(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return stringGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/applications/:applicationId/services", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<List<ServiceInstanceDetailsResponse>> listGenericResponse = new ApplicationDataService().getApplicationServiceDetails(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return listGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/applications/:applicationId/services/:serviceId/instances", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<List<ServiceInstanceDetailsResponse>> listGenericResponse = new ApplicationDataService().getApplicationServiceInstanceDetails(request, response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return listGenericResponse;
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/services/:serviceId/instanceHealth", (request, response) ->
                new GetServiceInstanceHealthService().getServiceInstanceHealth(request, response), new JsonTransformer());

        get(Constants.UI_BASE_URL + "/installation-attributes", (request, response) -> {
            long st = System.currentTimeMillis();
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            GenericResponse<List<InstallationAttributeBean>> listGenericResponse = FileUploadDataService.getInstallationAttributes(response);
            HealUICache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
            HealUICache.INSTANCE.addStatusCodes(response.status(), 1);
            return listGenericResponse;
        }, new JsonTransformer());

        /**
         * ================================== Solution Recommendation APIs ======================================================
         */

        get(Constants.UI_BASE_URL + "/accounts/:identifier/services/:serviceId/instanceDetails", (request, response) -> {
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            return new InstanceDataService().getInstancesByServiceId(request, response);
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/signalId/:signalId/solutions-feedback", (request, response) -> {
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            return new SolutionRecommendationService().getSolutionFeedback(request, response);
        }, new JsonTransformer());

        put(Constants.UI_BASE_URL + "/accounts/:identifier/signalId/:signalId/comment", (request, response) -> {
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            return new SolutionRecommendationService().takeSolutionCommentFeedback(request, response);
        }, new JsonTransformer());

        put(Constants.UI_BASE_URL + "/accounts/:identifier/signalId/:signalId/solutions-feedback", (request, response) -> {
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            return new SolutionRecommendationService().takeSolutionIsUsefulFeedback(request, response);
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/timeRanges", (request, response) -> {
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            return new TimeRangesService().getApplicableTimeRanges(request, response);
        }, new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/reports", (request, response) ->
                new GetReportsService().getReports(request, response), new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/reports/triggered", (request, response) ->
                new GetTriggeredReportsService().getTriggeredReports(request, response), new JsonTransformer());

        post(Constants.UI_BASE_URL + "/accounts/:identifier/reports/resend", (request, response) ->
                new ResendReportService().resendReport(request, response), new JsonTransformer());

        post(Constants.UI_BASE_URL + "/accounts/:identifier/reports/:reportId", (request, response) ->
                new GenerateReportService().triggerReport(request, response), new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/reports/:fileName/download", (request, response) ->
                new DownloadReportService().downloadReport(request, response), new JsonTransformer());

        post(Constants.UI_BASE_URL + "/accounts/:identifier/deleteReports", (request, response) ->
                new DeleteReportService().deleteReport(request, response), new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/applications/:applicationId/kpis", (request, response) ->
                new AccountApplicationKpiService().getAccountApplicationKpis(request, response), new JsonTransformer());

        get(Constants.UI_BASE_URL + "/accounts/:identifier/transactions/traces", (request, response) -> {
            log.trace(API_INVOKED_ACCESS_LOG_STATEMENT, request.pathInfo(), System.currentTimeMillis());
            return new JimTraceService().takeJaegerTraceForTransaction(request, response);
        });
    }


}
