package com.appnomic.appsone.api.common;

import com.appnomic.appsone.api.util.ConfProperties;
import org.apache.http.HttpStatus;

import java.util.Arrays;
import java.util.List;

public class Constants {

    private Constants() {

    }

    /**
     * Messages
     */
    public static final String INVALID_APPLICATION_ID_ERROR_MESSAGE = "Invalid application id provided";
    public static final String REQUEST_BODY_EMPTY_ERROR_MESSAGE = "Request body cannot be empty";
    public static final String REQUEST_BODY_INVALID_ERROR_MESSAGE = "Invalid data in request body";
    public static final String SUCCESS_MESSAGE = "SUCCESS";
    public static final String INVALID_FROM_TIME = "invalid fromTime provided.";
    public static final String INVALID_TO_TIME = "invalid toTime provided.";
    //Tag details name
    public static final String CONTROLLER_TAG = "Controller";
    public static final String DASHBOARD_UID_TAG = "DashboardUId";

    public static final String LAYER_TAG = "LayerName";
    public static final String LAYER_DEFAULT = "Type";
    public static final String TIME_ZONE_TAG = "Timezone";
    public static final String ENTRY_POINT = "EntryPoint";
    public static final String DEFAULT_ENTRY_POINT = "Type";
    public static final String UI_BASE_URL = "/heal-ui-service/v2.0/ui";
    public static final String REPLACE_UI_BASE_URL = "/heal-ui-service";
    public static final String CONF_PROPERTIES_FILE_NAME = "conf.properties";
    public static final String KEYCLOAK_SSO_CONF_FILE_NAME = "keycloak_details.json";
    public static final String HEADER_PROPERTIES_FILE_NAME = "headers_details.json";

    public static final String HTTPS_PORT_PROPERTY_NAME = "https.port";
    public static final String HTTPS_PORT_DEFAULT = "8996";
    public static final String INVOKED_METHOD = "Invoked method : ";

    public static final String MYSQL_DB_CONNECTION_URL_PROPERTY_NAME = "mysql.server.connection.url";
    public static final String MYSQL_DB_CONNECTION_URL_DEFAULT = "************************************************************************";

    public static final String MYSQL_DB_USERNAME_PROPERTY_NAME = "mysql.database.username";
    public static final String MYSQL_DB_USERNAME_PROPERTY_DEFAULT = "dbadmin";
    public static final String MYSQL_DB_AUTH_PROPERTY_NAME = "mysql.database.password";
    public static final String MYSQL_DB_AUTH_PROPERTY_DEFAULT = "cm9vdEAxMjM=";

    public static final String MYSQL_DB_POOL_SIZE_PROPERTY_NAME = "mysql.database.pool.size";
    public static final String MYSQL_DB_POOL_SIZE_DEFAULT = "50";

    public static final String MYSQL_DB_MAX_POOL_SIZE_PROPERTY_NAME = "mysql.database.pool.size.max";
    public static final String MYSQL_DB_MAX_POOL_SIZE_DEFAULT = "100";

    public static final String MYSQL_DB_DRIVER_NAME_PROPERTY_NAME = "mysql.driver.name";
    public static final String MYSQL_DB_DRIVER_NAME_DEFAULT = "com.mysql.cj.jdbc.Driver";

    public static final String H2_DB_DRIVER_NAME_PROPERT = "h2.driver.name";
    public static final String H2_DB_DRIVER_NAME_PROPERT_DEFAULT = "org.h2.Driver";

    public static final String COMPONENT_TYPE_DB_SERVER = "Database Server";

    public static final String THREAD_POOL_MIN_SIZE_NAME = "threadpool.min.size";
    public static final String THREAD_POOL_MIN_SIZE_DEFAULT_VALUE = "2";

    public static final String THREAD_POOL_MAX_SIZE_NAME = "threadpool.max.size";
    public static final String THREAD_POOL_MAX_SIZE_DEFAULT_VALUE = "10";

    public static final String DATE_TIME = "yyyy-MM-dd HH:mm:ss";

    //key cloak
    public static final String KEYCLOAK_IP = "keycloak.ip";
    public static final String KEYCLOAK_PORT = "keycloak.port";
    public static final String KEYCLOAK_USER = "keycloak.user";
    public static final String KEYCLOAK_AUTH = "keycloak.pwd";
    public static final String KEYCLOAK_CONNECTION_REFRESH = "keycloak.connection.refresh";
    public static final String KEYCLOAK_CONNECTION_REFRESH_DEFAULT = "30";
    public static final String USER_ACCOUNT_ACCESS_IDENTIFIER = "keycloak.account.identifier";
    public static final String USER_ACCOUNT_ACCESS_IDENTIFIER_DEFAULT = "Accounts";
    public static final String TIMEZONE_TAG_DETAILS_IDETIFIER = "timezone.tagdetail.key";
    public static final String TIMEZONE_TAG_DETAILS_IDETIFIER_DEFAULT = TIME_ZONE_TAG;
    public static final String ACCOUNT_TABLE_NAME_MYSQL = "account.table.name";
    public static final String ACCOUNT_TABLE_NAME_MYSQL_DEFAULT = "account";
    public static final String USER_ATTRIBUTES_TABLE_NAME_MYSQL = "user_attributes";

    public static final String REQUEST_THRESHOLD_PROPERTY = "request.threshold.seconds";
    public static final String REQUEST_THRESHOLD_PROPERTY_DEFAULT_VALUE = "3";

    // Cache Constants
    public static final String CACHE_MAXIMUM_SIZE_PROPERTY_NAME = "cache.maximum.size";
    public static final String CACHE_MAXIMUM_SIZE_DEFAULT_VALUE = "50000";
    public static final String CACHE_TIMEOUT_IN_MINUTES_PROPERTY_NAME = "cache.timeout";
    public static final String CACHE_TIMEOUT_IN_MINUTES_DEFAULT_VALUE = "30";
    public static final String CACHE_TIMEOUT_IN_MINUTES_MAINTENANCE_DATA_PROPERTY_NAME = "cache.timeout.maintenance.data";
    public static final String CACHE_TIMEOUT_IN_MINUTES_MAINTENANCE_DATA_DEFAULT_VALUE = "5";

    public static final String USER_CACHE_TIMEOUT_IN_MINUTES_PROPERTY_NAME = "user.cache.timeout";
    public static final String USER_CACHE_TIMEOUT_IN_MINUTES_DEFAULT_VALUE = "1";

    public static final String HEAL_METRICS_SCHEDULER_PROPERTY_NAME = "health.metrics.scheduler.milliseconds";
    public static final String HEAL_METRICS_SCHEDULER_PROPERTY_NAME_DEFAULT_VALUE = "10000";
    public static final String CACHE_REFRESH_SCHEDULER_PROPERTY_NAME = "cache.refresh.scheduler.milliseconds";
    public static final String CACHE_REFRESH_SCHEDULER_PROPERTY_NAME_DEFAULT_VALUE = "420000";

    public static final String TRANSACTION_RESPONSE_TYPE = "ResponseTimeType";
    public static final String ALL_TYPES = "ALL_TYPES";
    public static final String HOST_ADDRESS = "HostAddress";
    public static final String GLOBAL_ACCOUNT_ID = "global.account.id";
    public static final String GLOBAL_ACCOUNT_ID_DEFAULT = "1";
    public static final String GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT = "e573f852-5057-11e9-8fd2-b37b61e52317";
    public static final String CONTROLLER_TYPE_NAME_DEFAULT = "ControllerType";
    public static final String TRANSACTION_ATTRIBUTES_NAME = "transactionAttributes.type";
    public static final String TRANSACTION_ATTRIBUTES_NAME_DEFAULT = "Transaction_Attributes";
    public static final String TRANSACTION_ATTRIBUTE_TCP_TYPE = "transaction.attribute.tcp";
    public static final String TRANSACTION_ATTRIBUTE_TCP_TYPE_DEFAULT = "TCPData";
    public static final String TRANSACTION_ATTRIBUTE_QUERY_PARAMS_TYPE = "transaction.attribute.queryParams";
    public static final String TRANSACTION_ATTRIBUTE_QUERY_PARAMS_TYPE_DEFAULT = "QueryParams";
    public static final String SERVICES_CONTROLLER_TYPE = "Services";
    public static final String APPLICATION_CONTROLLER_TYPE = "Application";

    public static final String SYS_PROPERTIES_KPI_LIST = "system.property.kpis";
    public static final String SYS_PROPERTIES_KPI_LIST_DEFAULT = "SYSTEM_KEY_VALUE,MOUNT_POINT,MOUNT_PERMISSIONS,ENT_CPU,ENT_MEM";

    public static final int DEFAULT_ACCOUNT_ID = 1;
    public static final String HOST = "Host";
    public static final String COMP_INSTANCE_TABLE = "comp_instance";
    public static final String TRANSACTION_TABLE = "transaction";

    public static final String TRANSACTION_TYPE = "transaction.type.default";
    public static final String TRANSACTION_TYPE_DEFAULT = "DC";
    public static final String TRANSACTION_IDENTIFIER = "transaction.identifier";
    public static final String TRANSACTION_IDENTIFIER_DEFAULT = "Transaction";
    public static final String CLUSTER_IDENTIFIER = "cluster.identifier";
    public static final String CLUSTER_IDENTIFIER_DEFAULT = "Cluster";

    public static final String STATIC_FILES_LOCATIONS = "static.files.path";
    public static final String STATIC_FILES_LOCATIONS_DEFAULT = "/public";


    public static final String KEYSTORE_FILE_NAME = "keystore.file.path";
    public static final String KEYSTORE_FILE_NAME_DEFAULT = "/opt/heal-ui-service/config/appnomic-keystore.jks";

    public static final String KEYSTORE_PASSWORD = "keystore.password";
    public static final String KEYSTORE_PASSWORD_DEFAULT = "";

    public static final String TRUSTSTORE_FILE_NAME = "truststore.file.path";
    public static final String TRUSTSTORE_FILE_NAME_DEFAULT = "";

    public static final String TRUSTSTORE_PASSWORD = "truststore.password";
    public static final String TRUSTSTORE_PASSWORD_DEFAULT = "";

    public static final String OFFSET_FROM_GMT = "offset.from.gmt";
    public static final String OFFSET_FROM_GMT_DEFAULT = "19800000";

    public static final String TIMEZONE_OFFSET = "timezone.offset";
    public static final String TIMEZONE_OFFSET_DEFAULT = "19800000";
    public static final String GENERATED_TIME_STRING_FORMATTER = "generated.time.formatter.value";
    public static final String GENERATED_TIME_STRING_FORMATTER_DEFAULT = "yyyy-MM-dd HH:mm:00";

    public static final String HTTP_HANDLER_MAX_THREADS = "http.handler.max.threads";
    public static final String HTTP_HANDLER_MAX_THREADS_DEFAULT = "100";

    public static final String CONTROLLER = "controller";
    public static final String ALL_PROFILES = "ALL_PROFILES";

    public static final String KPI_UNIT_COUNT = "kpi.unit.count";
    public static final String KPI_UNIT_COUNT_DEFAULT = "Count";
    public static final String KPI_VALUE_PRECISION = "kpi.value.precision";
    public static final String KPI_VALUE_PRECISION_DEFAULT = "0";
    public static final String AVERAGE_ROLLUP_OPERATION = "Average";
    public static final String LAST_ROLLUP_OPERATION = "Last";
    public static final String MAX_ROLLUP_OPERATION = "Max";
    public static final String SUM_ROLLUP_OPERATION = "Sum";

    public static final String CONFIGWATCH_IDENTIFIER = "ConfigWatch";
    public static final String FILEWATCH_IDENTIFIER = "FileWatch";
    public static final String AVAILABILITY_KPI_IDENTIFIER = "Availability";
    public static final int CONFIG_WATCH_LOGICAL_CATEGORY_ID = -3;
    public static final int AVAILABILITY_LOGICAL_CATEGORY_ID = -1;
    public static final String KPI_CATEGORY_QUERY_DEEP_DIVE = "QueryDeepDive";
    public static final int SLOW_THRESHOLD_VALUE = 5000;
    public static final String CONFIG_WATCH_KEYS_SPLITTER = "keys.splitter";
    public static final String CONFIG_WATCH_KEYS_SPLITTER_DEFAULT = "!#@#!";
    public static final String CONFIG_WATCH_KEY_VALUE_SPLITTER = "key.value.splitter";
    public static final String CONFIG_WATCH_KEY_VALUE_SPLITTER_DEFAULT = "@#!#@";
    public static final String HIGH_THRESHOLD_IDENTIFIER_IDENTIFIER = "nor.high.identifier";
    public static final String HIGH_THRESHOLD_IDENTIFIER_IDENTIFIER_DEFAULT = "Upper";
    public static final String LOW_THRESHOLD_IDENTIFIER_IDENTIFIER = "nor.low.identifier";
    public static final String LOW_THRESHOLD_IDENTIFIER_IDENTIFIER_DEFAULT = "Lower";
    public static final String AGENT_TYPE = "Agent";
    public static final String COM_INSTANCE_TYPE = "instance";
    public static final String ALL = "All";
    public static final String COMPONENT = "Component";
    public static final String JIM_AGENT_TYPE = "JIMAgent";
    public static final String WORKLOAD_AGENT_TYPE = "Workload";

    public static final String AUTHORIZATION = "Authorization";

    public static final String MESSAGE_INVALID_TIME_RANGE = "Invalid Time Range";

    public static final String KAIROSDB_IP_PROP = "kairosdb.ip";
    public static final String KAIROSDB_IP_DEF = "127.0.0.1";
    public static final String KAIROSDB_PORT_PROP = "kairosdb.port";
    public static final String KAIROSDB_PORT_DEF = "8083";

    public static final String AGENT_TABLE = "agent";

    public static final int CATEGORY_DEEP_DIVE_ID = -2;
    public static final String CATEGORY_IDENTIFIER = "categoryIdentifier";

    public static final String QUERY_DEEP_DRIVE_SORT_KPI_PROPERTY = "query.deep.drive.sort.kpi";
    public static final String QUERY_DEEP_DRIVE_SQLID_KPI_PROPERTY = "query.deep.drive.sqlid.kpi";
    public static final String QUERY_DEEP_DRIVE_POPUP_KPIs_PROPERTY = "query.deep.drive.popup.kpis";
    public static final String QUERY_DEEP_DRIVE_SQLTEXT_KPI_PROPERTY = "query.deep.drive.sqlquery.kpi";

    // Constants for Oracle component to show for Query DeepDive details in UI
    public static final String ORA_QUERY_DEEP_DRIVE_SORT_KPI = "PHYSICALREADS_DISK_READS";
    public static final String ORA_QUERY_DEEP_DRIVE_SQLID_KPI = "PHYSICALREADS_SQL_ID";
    public static final String ORA_QUERY_DEEP_DRIVE_POPUP_KPIs = "PHYSICALREADS_FIRST_LOAD_TIME,PHYSICALREADS_LASTACTIVETIME,PHYSICALREADS_HASH_VALUE,PHYSICALREADS_ADDRESS,PHYSICALREADS_COMMAND_TYPE,PHYSICALREADS_OPTIMIZER_MODE,PHYSICALREADS_SQL_TEXT";
    public static final String ORA_QUERY_DEEP_DRIVE_SQLTEXT_KPI = "PHYSICALREADS_SQL_TEXT";

    // Constants for MSSQL component to show for Query DeepDive details in UI
    public static final String MSSQL_QUERY_DEEP_DRIVE_SORT_KPI = "MSSQL_AVG_CPU_TIME,MSSQL_TOTAL_CPU_TIME";
    public static final String MSSQL_QUERY_DEEP_DRIVE_SQLID_KPI = "MSSQL_OBJECT_ID";
    public static final String MSSQL_QUERY_DEEP_DRIVE_POPUP_KPIs = "MSSQL_LASTACTIVETIME,MSSQL_DB_NAME,MSSQL_HASH_VALUE,MSSQL_SQL_TEXT";
    public static final String MSSQL_QUERY_DEEP_DRIVE_SQLTEXT_KPI = "MSSQL_SQL_TEXT";

    // Constants for PostgresSQL component to show for Query DeepDive details in UI
    public static final String POSTGRES_QUERY_DEEP_DRIVE_SORT_KPI = "pg_avg_time";
    public static final String POSTGRES_QUERY_DEEP_DRIVE_SQLID_KPI = "pg_queryid";
    public static final String POSTGRES_QUERY_DEEP_DRIVE_POPUP_KPIs = "pg_calls,pg_rows,pg_blk_read_time,pg_blk_write_time,pg_query";
    public static final String POSTGRES_QUERY_DEEP_DRIVE_SQLTEXT_KPI = "pg_query";

    // Constants for other DB components to show for Query DeepDive details in UI
    public static final String QUERY_DEEP_DRIVE_SORT_KPI = "MYSQL_AVG_TIMER_WAIT";
    public static final String QUERY_DEEP_DRIVE_SQLID_KPI = "MYSQL_DIGEST";
    public static final String QUERY_DEEP_DRIVE_POPUP_KPIs = "MYSQL_LAST_SEEN,MYSQL_SCHEMA_NAME,MYSQL_DIGEST_TEXT,MYSQL_COUNT_START,MYSQL_QUANTILE_95,MYSQL_QUANTILE_99";
    public static final String QUERY_DEEP_DRIVE_SQLTEXT_KPI = "MYSQL_DIGEST_TEXT";

    public static final String EVENT = "event";
    public static final String SEGMENT = "xpt_Payee_Va";
    public static final String STATUS_TAG = "status";
    public static final String TRUE = "true";
    public static final String FALSE = "false";

    public static final String ICON_TITLE_SPLITTER = "icon.title.splitter";
    public static final String ICON_TITLE_SPLITTER_DEFAULT = "-";

    public static final String RCA_PATH_SEQUENCE_POSTION_KEY = "rca.path.sequence.position.key";
    public static final String RCA_PATH_SEQUENCE_POSTION_KEY_DEFAULT = "type";
    public static final String RCA_PATH_SEQUENCE_POSTION_VALUE = "rca.path.sequence.position.value";
    public static final String RCA_PATH_SEQUENCE_POSTION_VALUE_DEFAULT = "rcaPath";
    public static final String RCA_PATH_COMMONALITY_CONSTANT = "rca.commonality.factor";
    public static final String RCA_PATH_COMMONALITY_CONSTANT_DEFAULT = "1";
    public static final String RCA_PATH_COMMONALITY_LOGIC = "commonality.logic";
    public static final String RCA_PATH_COMMONALITY_LOGIC_DEFAULT = "1";

    public static final String CASSANDRA_AGGREGATION_OFFSET = "timeseries.aggregation.offset.minutes";
    public static final String CASSANDRA_AGGREGATION_OFFSET_DEFAULT = "0";

    public static final String NOTIFICATION_SERVICE_URL = "notification.service.url";
    public static final String NOTIFICATION_SERVICE_URL_DEF = "http://127.0.0.1:8888/notifications";

    public static final String CASSANDRA_ALL_IDENTIFIER = "ALL";

    public static final String SIGNAL_CLOSE_WINDOW_TIME = "signal.close.window.time";
    public static final String SIGNAL_CLOSE_WINDOW_DEFAULT_TIME = "15";

    public static final String SIGNAL_CLOSE_WINDOW_TIME_CHECK_ENABLED = "signal.close.window.time.check.enabled";
    public static final Boolean SIGNAL_CLOSE_WINDOW_TIME_CHECK_ENABLED_DEFAULT = true;
    public static final String SIGNAL_STATUS_CLOSED = "CLOSED";

    public static final String SHOW_SDM_TOPOLOGY = "show.application.sdm.topology";
    public static final String SHOW_SDM_TOPOLOGY_DEFAULT_VALUE = "signals";

    //Forensic Data
    public static final String EXTENSION_TYPE = "txt";
    public static final String COMMAND_OUTPUT_SPLITER = "#@#@#APPSONE_COMMAND_OUTPUT_SEPARATOR#@#@#\n";
    public static final String COMMAND_DATA_SPLITER = "#@#@#APPSONE_NEWLINE#@#@#";
    public static final String NAME_VALUE_SPLITER = ":";

    public static final String FORENSIC_COMMAND_DATA_LENGTH = "forensic.command.data.length";
    public static final String DEFAULT_FORENSIC_COMMAND_DATA_LENGTH = "1000";
    public static final String FORENSIC_ACTION_TRIGGER_TIME_LOOKAHEAD = "forensic.lookahead.interval.minutes";
    public static final String FORENSIC_ACTION_TRIGGER_TIME_LOOKAHEAD_DEFAULT = "10";
    public static final String FORENSIC_ACTION_TRIGGER_TIME_LOOKBACK = "forensic.lookback.interval.minutes";
    public static final String FORENSIC_ACTION_TRIGGER_TIME_LOOKBACK_DEFAULT = "10";
    public static final String NUMBER_OF_TOP_ANOMALIES_FROM_OPENSEARCH = "top.anomaly.count.from.opensearch";
    public static final String NUMBER_OF_TOP_ANOMALIES_FROM_OPENSEARCH_DEFAULT = "10";

    public static final String FORENSIC_ACTION_LEVEL = "forensic.level";
    public static final String FORENSIC_ACTION_LEVEL_DEFAULT = "category";

    public static final int STATUS_ACTIVE = 1;
    public static final int STATUS_INACTIVE = 0;

    /**
     * Return codes
     */
    public static final int SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE = HttpStatus.SC_BAD_REQUEST;
    public static final int INTERNAL_SERVER_ERROR_STATUS_CODE = HttpStatus.SC_INTERNAL_SERVER_ERROR;
    public static final int SUCCESS_STATUS_CODE = HttpStatus.SC_OK;

    /**
     * Request Params
     */
    public static final String REQUEST_PARAM_IDENTIFIER = ":identifier";
    public static final String REQUEST_PARAM_PREF_IDENTIFIER = ":preferenceId";
    public static final String AUTHORIZATION_HEADER = "Authorization";
    public static final String REQUEST_PARAM_TYPE = "type";
    public static final String REQUEST_PARAM_RESPONSE_TYPE = ":response-type";
    public static final String REQUEST_PARAM_FROM_TIME = "fromTime";
    public static final String REQUEST_PARAM_TO_TIME = "toTime";
    public static final String REQUEST_PARAM_CLUSTER_ID_UNDERSCORE = "cluster_id";
    public static final String REQUEST_PARAM_APPLICATION_ID = ":applicationId";
    public static final String REQUEST_QUERY_PARAM_APPLICATION_ID = "applicationId";
    public static final String REQUEST_PARAM_APPLICATION_SMALLID = ":applicationid";
    public static final String REQUEST_PARAM_PARENT_APPLICATION_ID = ":parentApplicationIdentifier";
    public static final String REQUEST_PARAM_SERVICE_ID = ":serviceId";
    public static final String REQUEST_PARAM_SRC_SERVICE_ID = ":srcserviceid";
    public static final String REQUEST_PARAM_DST_SERVICE_ID = ":dstserviceid";
    public static final String REQUEST_PARAM_POD_ID = ":podId";
    public static final String REQUEST_PARAM_NODE_ID = ":nodeId";
    public static final String REQUEST_PARAM_TXN_ID = ":txnId";
    public static final String REQUEST_PARAM_SIGNAL_ID = ":signalId";
    public static final String REQUEST_PARAM_COM_INSTANCE_ID = ":instanceId";
    public static final String REQUEST_PARAM_KPI_LIST = "componentKpi";
    public static final String REQUEST_PARAM_TXN_KPI_LIST = "componentKpi";
    public static final String REQUEST_PARAM_USER_ID = ":userId";
    public static final String REQUEST_PARAM_USERNAME = ":username";
    public static final String REQUEST_PARAM_COMPONENT_ID = ":componentId";
    public static final String REQUEST_PARAM_INSTANCE_IDS_LIST = "instanceIds";
    public static final String REQUEST_PARAM_DATE_LIST = "dates";
    public static final String REQUEST_TYPE_SERVICES = "services";
    public static final String REQUEST_PARAM_CLUSTER_ID = ":clusterId";
    public static final String REQUEST_SIGNAL_TYPE = "signalType";
    public static final String GET_APPLICATIONHEALTH_BY_PARENT = "GetApplicationHealthByParent";
    /**
     * Standard failure messages
     */
    public static final String MESSAGE_SUCCESS = "SUCCESS";
    public static final String MESSAGE_FAILURE = "FAILURE";
    public static final String MESSAGE_INVALID_PARAMETERS = "Invalid input parameter/s provided.";
    public static final String MESSAGE_INVALID_ACCOUNT = "Invalid account id provided.";
    public static final String MESSAGE_EMPTY_ACCOUNT = "Account identifier is null or empty";
    public static final String MESSAGE_EMPTY_PARENT_APPLICATION = "Parent Application identifier is null or empty";
    public static final String MESSAGE_INTERNAL_ERROR = "Internal server error occurred.";
    public static final String MESSAGE_INVALID_COMP_INSTANCE = "Invalid component instance id provided.";
    public static final String MESSAGE_INVALID_KPI = "Invalid component kpi id provided.";
    public static final String MESSAGE_INVALID_SERVICE = "Invalid service id provided";
    public static final String MESSAGE_INVALID_SRC_SERVICE = "Invalid source service id provided";
    public static final String MESSAGE_INVALID_DST_SERVICE = "Invalid destination service id provided";
    public static final String MESSAGE_INVALID_CLUSTER_ID = "Invalid cluster id provided";
    public static final String MESSAGE_INVALID_APPLICATION = "Invalid application id provided";
    public static final String MESSAGE_INVALID_POD_SERVICE = "Invalid Pod ID for this service";
    public static final String MESSAGE_EMPTY_SERVICE = "Service id is empty";
    public static final String MESSAGE_INVALID_TRANSACTION = "Invalid transaction id provided";
    public static final String MESSAGE_INVALID_NUMBER_FORMAT = "Invalid input received in place of numerical input";
    public static final String MESSAGE_INVALID_AUTH_TOKEN = "Invalid auth token received";
    public static final String MESSAGE_EMPTY_AUTH_TOKEN = "Auth token is empty";
    public static final String MESSAGE_INVALID_POD = "Invalid Pod Id received";
    public static final String MESSAGE_INVALID_NODE = "Invalid Node Id received";
    public static final String MESSAGE_EMPTY_POD = "Pod Id is empty";
    public static final String MESSAGE_EMPTY_NODE = "Node Id is empty";
    public static final String MESSAGE_NULL = "TFP anomaly transaction request object is received as null";
    public static final String MESSAGE_INVALID_USER_IDENTIFIER = "Invalid user identifier received";

    public static final String TRANSACTION_VOLUME = "TOTAL_VOLUME";
    public static final String TRANSACTION_RESPONSE_TIME = "RESPONSE_TIME";
    public static final String COMPONENT_KEY = "component";
    public static final String COMPONENT_TYPE_KEY = "type";
    public static final String COMPONENT_VERSION_KEY = "version";
    public static final String COMPONENT_ID_KEY = "Component Id";
    public static final String MIN_THRESHOLD = "Lower";
    public static final String MAX_THRESHOLD = "Upper";
    public static final String ENTRYPOINT_TAG_NAME_LITERAL = "EntryPoint";
    public static final String SUPPORTED_ROLLUP_LEVELS = "cassandra.rollup.level";
    public static final String SUPPORTED_ROLLUP_LEVELS_DEFAULT = "1440,60,30,15,1";

    public static final Integer SEVERITY_295 = 295;
    public static final Integer SEVERITY_296 = 296;

    public static final String PROBLEM_LITERAL = "problem";
    public static final String BATCH_JOB_LITERAL = "batch_job";

    public static final String POD = "Pod";
    public static final String ATTRIBUTE_STATUS = "status";
    public static final String ATTRIBUTE_UPTIME = "uptime";
    public static final String ATTRIBUTE_RESTARTS = "restarts";

    public static final String SERVICE_TYPE_TAG = "ServiceType";
    public static final String SERVICE_TYPE_DEFAULT = "Type";
    public static final String KUBERNETES = "Kubernetes";
    public static final String NODE_KPI_CPU_IDENTIFIER = "host_cpu_usage_current";
    public static final String NODE_KPI_MEM_IDENTIFIER = "host_mem_usage_current";
    public static final String NODE_KPI_POD_CNT_IDENTIFIER = "host_per_svc_pod_count";
    public static final String POD_KPI_CPU_IDENTIFIER = "pod_cpu_usage_current";
    public static final String POD_KPI_MEM_IDENTIFIER = "pod_mem_usage_current";

    /**
     * Client validation response messages
     */
    public static final String CLIENT_VALIDATION_MESSAGE_ACCOUNT_INVALID = "account id is null";
    public static final String CLIENT_VALIDATION_MESSAGE_COMP_INSTANCE_INVALID = "comp inst id is null";
    public static final String CLIENT_VALIDATION_MESSAGE_FROM_TIME_INBALID = "from time is null";
    public static final String CLIENT_VALIDATION_MESSAGE_TO_TIME_INVALID = "to time is null";

    public static final String ACCOUNT_IDENTIFIER = ":identifier";
    public static final String MST_TYPE_MAINTENANCE = "MaintenanceType";
    public static final String MST_TYPE_RECURRING = "RecurringType";
    public static final String MST_SUB_TYPE_SCHEDULED = "Scheduled";
    public static final String MST_SUB_TYPE_RECURRING = "Recurring";
    public static final String MST_SUB_TYPE_RECURRING_DAILY = "Daily";
    public static final String MST_SUB_TYPE_RECURRING_WEEKLY = "Weekly";
    public static final String MST_SUB_TYPE_RECURRING_MONTHLY = "Monthly";
    public static final String MST_SUB_TYPE_FORENSIC = "Forensic";
    public static final String MST_TYPE_CONFIG_WATCH = "ConfigWatch";
    public static final String MST_TYPE_FILE_WATCH = "FileWatch";
    public static final String MST_TYPE_NOTIFICATION_PREFERENCES = "NotificationPreferences";
    public static final String MAINTENANCE_ONGOING = "Ongoing";
    public static final String MAINTENANCE_UPCOMING = "Upcoming";
    public static final String MAINTENANCE_COMPLETED = "Completed";

    public static final String TFP_TRANSACTION_COUNT = "tfp.txn.count";
    public static final String TFP_TRANSACTION_COUNT_DEFAULT = "10";

    public static final String TFP_SERVICE = "Service";
    public static final String TFP_PEER_SERVICE = "PeerService";
    public static final String TFP_TXN_ID = "TxnId";
    public static final String TFP_TXN_DIRECTION = "TxnDirection";
    public static final String ELASTIC_TIMESTAMP = "@timestamp";
    public static final String TFP_TXN_VOLUME = "Volume";
    public static final String TFP_TXN_AVGRESPTIME = "AvgResponseTime";
    public static final String TFP_INDEX_PREFIX = "tfp-";
    public static final String TFP_TXN_RESPONSES_STATUS_TAG = "ResponseStatusTag";
    public static final String TFP_UNMATCHED_TXN = "unmatched";

    public static final String SIGNAL_PROBLEM_DESCRIPTION = "signal.problem.description";
    public static final String SIGNAL_PROBLEM_DESCRIPTION_DEFAULT = "Transactions at <entry_service_name> have been affected.";
    public static final String SIGNAL_WARNING_DESCRIPTION = "signal.warning.description";
    public static final String SIGNAL_WARNING_DESCRIPTION_DEFAULT = "Event(s) in <root_cause_service_list> root cause service(s) may impact transaction performance.";
    public static final String SIGNAL_INFO_DESCRIPTION = "signal.info.description";
    public static final String SIGNAL_INFO_DESCRIPTION_DEFAULT = "Info events generated for <affected_service_list>.";
    public static final String SIGNAL_BATCH_PROCESS_DESCRIPTION = "signal.batch.description";
    public static final String SIGNAL_BATCH_PROCESS_DESCRIPTION_DEFAULT = "Batch Job <job_id>, Event detected: <kpi>, Current Status: <current_batch_status>";

    public static final String AVG_RESPONSE_PERCENTILE_KPI_LITERAL = "RESPONSE_TIME_PERCENTILE";

    public static final Long MILLIS_THIRTY_MINUTES = 1800000L;
    public static final Long MILLIS_ONE_HOUR = 3600000L;
    public static final Long MILLIS_FOUR_HOURS = 14400000L;
    public static final Long MILLIS_TWELVE_HOURS = 43200000L;
    public static final Long MILLIS_TWENTY_FOUR_HOURS = 86400000L;
    public static final Long MILLIS_ONE_WEEK = 604800000L;
    public static final Long MILLIS_ONE_MONTH_28 = 2419200000L;
    public static final Long MILLIS_ONE_MONTH_29 = 2505600000L;
    public static final Long MILLIS_ONE_MONTH_30 = 2592000000L;
    public static final Long MILLIS_ONE_MONTH_31 = 2678400000L;

    public static final String BUCKET_THIRTY_MINUTE = "1 MINUTE";
    public static final String BUCKET_ONE_HOUR = "1 MINUTE";
    public static final String BUCKET_FOUR_HOURS = "15 MINUTE";
    public static final String BUCKET_TWELVE_HOURS = "30 MINUTE";
    public static final String BUCKET_TWENTY_FOUR_HOURS = "1 HOUR";
    public static final String BUCKET_ONE_WEEK = "1 DAY";
    public static final String BUCKET_ONE_MONTH = "1 DAY";

    public static final String ALL_APPLICATIONS = "all-applications";
    public static final String APP_NAME_SUFFIX = ".name";
    public static final String OS_LIST_SUFFIX = ".os";
    public static final String KEYPATH_SUFFIX = ".keyPath";
    public static final String CRASHLYTICS_SUFFIX = ".crashlytics";
    public static final String PERFMON_SUFFIX = ".perfmon";
    public static final String ANALYTICS_SUFFIX = ".analytics";
    public static final String APP_KEY_SUFFIX = ".appKey";

    public static final String CRASHLYTICS_ALIAS = "crashlytics";
    public static final String PERFMON_ALIAS = "perfmon";
    public static final String ANALYTICS_ALIAS = "analytics";

    public static final Long MINUTE = 60000L;
    public static final Long HOUR = 3600000L;
    public static final Long DAY = 86400000L;


    public static String getKeyPath(String appName) {
        return ConfProperties.getString(appName + KEYPATH_SUFFIX);
    }

    public static String getCrashlyticsTable(String appOSString) {
        return ConfProperties.getString(appOSString + CRASHLYTICS_SUFFIX);
    }

    public static String getPerfMonTable(String appOSString) {
        return ConfProperties.getString(appOSString + PERFMON_SUFFIX);
    }

    public static String getAnalyticsTable(String appOSString) {
        return ConfProperties.getString(appOSString + ANALYTICS_SUFFIX);
    }

    public static List<String> getOSListFromAppName(String appName) {
        return Arrays.asList(ConfProperties.getString(appName + OS_LIST_SUFFIX).split(","));
    }

    public static String getAppKey(String appOsString) {
        return ConfProperties.getString(appOsString + APP_KEY_SUFFIX);
    }

    public static final String RECENT_TXN_SHOW_INFO_CATEGORIES = "recent.transaction.show.info.category";
    public static final String RECENT_TXN_SHOW_INFO_CATEGORIES_DEFAULT = "0";

    public static final String INSTANCE_HEALTH_INTERVAL_PROP = "instance.health.min";
    public static final String INSTANCE_HEALTH_INTERVAL_MIN_DEFAULT = "15";

    public static final String OPENSEARCH_INDEX_EXTENSION_DEFAULT = "_ext";
    public static final String OPENSEARCH_INDEX_EXTENSION = "opensearch.index.extension";

    // Redis connectivity
    public static final String REDIS_HOSTS = "redis.hosts";
    public static final String REDIS_USERNAME = "redis.username";
    public static final String REDIS_PASSWORD = "redis.password";
    public static final String REDIS_CLUSTER_MODE = "redis.cluster.mode";
    public static final boolean REDIS_CLUSTER_MODE_DEFAULT = true;
    public static final String REDIS_SSL_ENABLED = "redis.ssl.enabled";
    public static final boolean REDIS_SSL_ENABLED_DEFAULT = true;
    public static final String COLON = ":";

    //Open Search Indexes
    public static final String INDEX_PREFIX_ANOMALIES = "heal_anomalies";
    public static final String INDEX_PREFIX_SIGNALS = "heal_signals";
    public static final String INDEX_PREFIX_SERVICE_MAINTENANCE_WINDOW = "heal_service_maintenance_data";
    public static final String INDEX_PREFIX_INSTANCE_MAINTENANCE_WINDOW = "heal_instance_maintenance_data";
    public static final String INDEX_PREFIX_INSTANCE_HEALTH = "heal_health_instance";
    public static final String INDEX_PREFIX_COLLATED_TRANSACTIONS = "heal_collated_txn";
    public static final String INDEX_PREFIX_RAW_TRANSACTIONS = "heal_raw_txn";
    public static final String INDEX_PREFIX_COLLATED_KPI = "heal_collated_kpi";
    public static final String INDEX_PREFIX_COLLATED_TRANSACTIONS_AGENT = "heal_collated_agent_txn";
    public static final String INDEX_PREFIX_FORENSICS = "heal_forensics";
    public static final String INDEX_PREFIX_INSTANCE_KPI_THRESHOLDS = "heal_instance_kpi_thresholds";
    public static final String INDEX_PREFIX_SERVICE_KPI_THRESHOLDS = "heal_service_kpi_thresholds";
    public static final String INDEX_PREFIX_TRANSACTION_KPI_THRESHOLDS = "heal_transaction_kpi_thresholds";
    public static final String INDEX_PREFIX_RAW_KPI = "heal_raw_kpi";
    public static final String INDEX_PREFIX_REPORTS = "heal_reports";
    public static final String UNKNOWN_SOURCE = "Unknown";

    public static final String WATCHER_VALUE_SPLITTER_DEFAULT = "@#!#@";

    public static final String ENCODE_ENABLED = "html.encoder.enable";
    public static final int ENCODE_ENABLED_DEFAULT = 1;

    public static final String CHECK_RAW_TRANSACTION = "service.uiservice.txn.check.raw.index";

    public static final String CHECK_RAW_TRANSACTION_DEFAULT = "1";

    public static final String REPORT_DATA_SERVICE_URL = "report.data.service.url";
    public static final String DEFAULT_REPORT_DATA_SERVICE_URL = "http://localhost:11190/triggerReport";
    public static final String RESEND_MAIL_DATA_SERVICE_URL = "resend.mail.service.url";
    public static final String DEFAULT_RESEND_MAIL_DATA_SERVICE_URL = "http://localhost:11190/sendMail";

    public static final String ADMIN_USER_IDENTIFIER = "admin.user.identifier";
    public static final String DEFAULT_ADMIN_USER_IDENTIFIER = "7640123a-fbde-4fe5-9812-581cd1e3a9c1";

    //Jaeger connectivity
    public static final String JAEGER_IP = "jaeger.ip";
    public static final String JAEGER_PORT = "jaeger.port";
    public static final String JAEGER_TRACE_DEFAULT_RECORD_LIMIT = "20";
    public static final String JAEGER_TRACE_DEFAULT_LOOKBACK_TIME = "custom";
    public static final String SERVICE_ID = "service";
    public static final String TRANSACTION_ID = "transaction";

    public static final String MICROSERVICE = "Microservice";
    public static final String TRACE_KEYWORD = "TRACE";
    public static final String TRACK_KEYWORD = "TRACK";


    public static final String RETAIN_LOCAL_CACHE_ANY_ERROR = "retain.local.cache.any.error";
    public static final String RETAIN_LOCAL_CACHE_ANY_ERROR_DEFAULT = "1";
}
