package com.appnomic.appsone.api.common.client;

import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.RequestException;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;

public interface ClientValidator<T> {

    UtilityBean<T> validate(RequestObject requestObject) throws ClientException;

    UtilityBean<T> extract(RequestObject requestObject) throws ClientException;

}