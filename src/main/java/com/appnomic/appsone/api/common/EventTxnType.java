package com.appnomic.appsone.api.common;

public enum EventTxnType {

    REQ("Request"),
    RESP("Response"),
    REQ_RESP("Request_Response");

    private String operation;

    EventTxnType(String operation) {
        this.operation = operation;
    }

    public String getOperation() {
        return operation;
    }

    @Override
    public String toString() {
        return operation;
    }
}
