package com.appnomic.appsone.api.common;

import lombok.Data;

@Data
public class GenericResponse<T> {
    private String message;
    private String responseStatus;
    protected T data;

    public GenericResponse<T> getFailureResponse(String message, String status, T data) {
        this.message = message;
        this.responseStatus = status;
        this.data = data;
        return this;
    }

    public GenericResponse<T> setMessage(String message) {
        this.message = message;
        return this;
    }

    public GenericResponse<T> setResponseStatus(String responseStatus) {
        this.responseStatus = responseStatus;
        return this;
    }

    public GenericResponse<T> setData(T data) {
        this.data = data;
        return this;
    }

    public static <T> GenericResponse<T> getInstance(String message, String responseStatus, T data){

        GenericResponse<T> response = new GenericResponse<>();
        response.setData(data);
        response.setMessage(message);
        response.setResponseStatus(responseStatus);
        return response;
    }

    public static <T> GenericResponse<T> getInstance(String message, String responseStatus){

        GenericResponse<T> response = new GenericResponse<>();
        response.setMessage(message);
        response.setResponseStatus(responseStatus);
        return response;
    }
}
