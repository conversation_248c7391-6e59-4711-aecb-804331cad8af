package com.appnomic.appsone.api.common.server.impl;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.common.server.ServerValidator;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.ServiceRepo;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class InstanceDataRequestServerValidator implements ServerValidator<UtilityBean> {
    private static final Logger LOGGER = LoggerFactory.getLogger(InstanceDataRequestServerValidator.class);

    @Override
    public UtilityBean validate(UtilityBean utilityBean) throws ServerException {

        AccountRepo accountRepo = new AccountRepo();
        ServiceRepo serviceRepo = new ServiceRepo();
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if(userId == null){
            throw new ServerException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }
        utilityBean.setUserId(userId);

        com.heal.configuration.pojos.Account account = accountRepo.getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
        }
        utilityBean.setAccount(account);

        BasicEntity basicEntity = serviceRepo.getAllServices(account.getIdentifier()).stream().filter(s -> s.getId() == utilityBean.getServiceId()).findAny().orElse(null);
        if( basicEntity == null ) {
            LOGGER.error("Invalid service id: {}",utilityBean.getServiceId());
            throw new ServerException(UIMessages.ERROR_INVALID_SERVICE_ID);
        }

        Service service = HealUICache.INSTANCE.getServiceDetails(account.getIdentifier(), basicEntity.getIdentifier());
        if( service == null ) {
            LOGGER.error("Invalid service id: {}, identifier:{}",utilityBean.getServiceId(), basicEntity.getIdentifier());
            throw new ServerException(UIMessages.ERROR_INVALID_SERVICE_ID);
        }
        utilityBean.setService(service);

        LOGGER.debug("All input parameters are valid.");
        return utilityBean;
    }
}
