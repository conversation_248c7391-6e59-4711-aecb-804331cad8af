package com.appnomic.appsone.api.common.client.impl;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.client.ClientValidator;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MleKpiRequestClientValidator implements ClientValidator {
    private static final Logger LOGGER = LoggerFactory.getLogger(MleKpiRequestClientValidator.class);

    @Override
    public UtilityBean validate(RequestObject request) throws ClientException {
        LOGGER.trace("{}: validate(), param - {}", Constants.INVOKED_METHOD, request);
        UtilityBean requestData = extract(request);
        if( requestData == null ) {
            throw new ClientException("Empty request received.");
        }

        if( requestData.getAccountIdString() == null ) {
            throw new ClientException(Constants.CLIENT_VALIDATION_MESSAGE_ACCOUNT_INVALID);
        }

        if( requestData.getComponentInstanceIdString() == null ) {
            throw new ClientException(Constants.CLIENT_VALIDATION_MESSAGE_COMP_INSTANCE_INVALID);
        }

        if( requestData.getFromTimeString() == null ) {
            throw new ClientException(Constants.CLIENT_VALIDATION_MESSAGE_FROM_TIME_INBALID);
        }

        if( requestData.getToTimeString() == null ) {
            throw new ClientException(Constants.CLIENT_VALIDATION_MESSAGE_TO_TIME_INVALID);
        }

        if( requestData.getKpiFilterListString() == null ) {
            LOGGER.warn("There is no kpi ids sent, hence data will be fetched for all kpi's mapped to the instance.");
        }

        LOGGER.debug("Mle kpidata request client validation was successful for input: {}.", requestData);
        return requestData;
    }

    @Override
    public UtilityBean extract(RequestObject request) throws ClientException {
        if( request == null ) {
            LOGGER.error("MLE KPIDATA request object is received as null");
            throw new ClientException("MLE KPIDATA request object is received as null");
        }
        String[] tempArray = {null};

        return UtilityBean.builder()
                .accountIdString(request.getParams().get(Constants.REQUEST_PARAM_IDENTIFIER))
                .componentInstanceIdString(request.getParams().get(Constants.REQUEST_PARAM_COM_INSTANCE_ID.toLowerCase()))
                .kpiFilterListString(request.getQueryParams().getOrDefault(Constants.REQUEST_PARAM_KPI_LIST, tempArray)[0])
                .fromTimeString(request.getQueryParams().get(Constants.REQUEST_PARAM_FROM_TIME)[0])
                .toTimeString(request.getQueryParams().get(Constants.REQUEST_PARAM_TO_TIME)[0])
                .doAnalyticsFlagString(request.getQueryParams().get("doAnalytics")[0])
                .build();
    }
}

