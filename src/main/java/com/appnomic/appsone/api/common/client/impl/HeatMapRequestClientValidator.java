package com.appnomic.appsone.api.common.client.impl;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.client.ClientValidator;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.ValidationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class HeatMapRequestClientValidator implements ClientValidator<Object> {
    private static final Logger LOGGER = LoggerFactory.getLogger(HeatMapRequestClientValidator.class);

    @Override
    public UtilityBean<Object> validate(RequestObject requestObject) throws ClientException {
        UtilityBean<Object> utilityBean = extract(requestObject);

        if (utilityBean.getAccountIdString() == null) throw new ClientException(Constants.MESSAGE_INVALID_ACCOUNT);

        if (utilityBean.getServiceIdString() == null) throw new ClientException(Constants.MESSAGE_INVALID_SERVICE);

        if (utilityBean.getFromTimeString() == null) throw new ClientException(Constants.MESSAGE_INVALID_TIME_RANGE);

        if (utilityBean.getToTimeString() == null) throw new ClientException(Constants.MESSAGE_INVALID_TIME_RANGE);

        validateInputFormat(utilityBean);

        return utilityBean;
    }

    @Override
    public UtilityBean<Object> extract(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            LOGGER.error("Heat Map request object is received as null");
            throw new ClientException("Heat Map request object is received as null");
        }

        return UtilityBean.builder()
                .accountIdString(requestObject.getParams().get(Constants.REQUEST_PARAM_IDENTIFIER))
                .serviceIdString(requestObject.getParams().get(Constants.REQUEST_PARAM_SERVICE_ID))
                .fromTimeString(requestObject.getQueryParams().get(Constants.REQUEST_PARAM_FROM_TIME)[0])
                .toTimeString(requestObject.getQueryParams().get(Constants.REQUEST_PARAM_TO_TIME)[0])
                .build();
    }

    private void validateInputFormat(UtilityBean utilityBean) throws ClientException {
        LOGGER.trace("Validating format of input data.");
        try {

            utilityBean.setServiceId(Integer.parseInt(utilityBean.getServiceIdString()));
            utilityBean.setFromTime(Long.parseLong(utilityBean.getFromTimeString()));
            utilityBean.setToTime(Long.parseLong(utilityBean.getToTimeString()));
            ValidationUtils.isValidTimeRange(utilityBean.getFromTime(), utilityBean.getToTime());

        } catch (NumberFormatException ne) {

            LOGGER.error("Error occurred while validating numerical input from request.", ne);
            throw new ClientException(Constants.MESSAGE_INVALID_NUMBER_FORMAT);

        } catch (Exception e) {

            LOGGER.error("Error occurred while validating input from request.", e);
            throw new ClientException(Constants.MESSAGE_INTERNAL_ERROR);

        }
    }

}
