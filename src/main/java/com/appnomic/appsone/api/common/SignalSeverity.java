package com.appnomic.appsone.api.common;

public enum SignalSeverity {
    CRITICAL("CRITICAL","Critical"),
    NON_CRITICAL("NON_CRITICAL","Non Critical");

    private String type;
    private String returnType;

    SignalSeverity(String type, String returnType) {
        this.type = type;
        this.returnType = returnType;
    }

    public String getType() {
        return type;
    }

    public String getReturnType(){
        return returnType;
    }

    @Override
    public String toString() {
        return type;
    }
}
