package com.appnomic.appsone.api.common.server.impl;

import com.appnomic.appsone.api.beans.MleKpiDataRequestData;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.cache.MasterCache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.common.server.ServerValidator;
import com.appnomic.appsone.api.custom.exceptions.RequestException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.InstanceRepo;
import com.appnomic.appsone.api.pojo.AllAccountDetails;
import com.appnomic.appsone.api.pojo.CompInstClusterDetails;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.heal.configuration.pojos.Account;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

public class MleKpiRequestServerValidator implements ServerValidator<MleKpiDataRequestData> {
    private static final Logger LOGGER = LoggerFactory.getLogger(MleKpiRequestServerValidator.class);
    private MleKpiDataRequestData requestDataObject = null;

    @Override
    public MleKpiDataRequestData validate(UtilityBean requestData) throws ServerException {
        requestDataObject = new MleKpiDataRequestData();
        validateNumericInput(requestData);
        validateAccount(requestData.getAccountIdString());
        validateInstance();
        requestDataObject.setKpiListString(requestData.getKpiFilterListString());
        return requestDataObject;
    }

    private void validateNumericInput(UtilityBean requestData) throws ServerException {
        try {
            requestDataObject.setFromTime(Long.parseLong(requestData.getFromTimeString()));
            requestDataObject.setToTime(Long.parseLong(requestData.getToTimeString()));
            requestDataObject.setComponentInstanceId(Integer.parseInt(requestData.getComponentInstanceIdString()));
            if( requestDataObject.getFromTime() > requestDataObject.getToTime()) {
                LOGGER.error("from time is greater than to time");
                throw new NumberFormatException("Time range is invalid.");
            }
        } catch (NumberFormatException ne) {
            LOGGER.error("Invalid numeric input.",ne);
            throw new ServerException("Invalid numeric input.");
        }
    }

    private void validateAccount(String accountIdentifier) throws ServerException {

        AccountRepo accountRepo = new AccountRepo();
        Account account = accountRepo.getAccount(accountIdentifier);
        if( account == null ) {
            LOGGER.error("Invalid account identifier: {}.", accountIdentifier);
            throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
        }

        requestDataObject.setAccount(account);
    }

    private void validateInstance() throws ServerException {
        com.heal.configuration.pojos.CompInstClusterDetails instanceDetails = new InstanceRepo().getInstanceDetailsWithId(requestDataObject.getAccount().getIdentifier(), requestDataObject.getComponentInstanceId());
        if( instanceDetails == null ) {
            LOGGER.error(Constants.MESSAGE_INVALID_COMP_INSTANCE);
            throw new ServerException(Constants.MESSAGE_INVALID_COMP_INSTANCE);
        } else {
            requestDataObject.setInstanceDetails(instanceDetails);
        }
    }
}

