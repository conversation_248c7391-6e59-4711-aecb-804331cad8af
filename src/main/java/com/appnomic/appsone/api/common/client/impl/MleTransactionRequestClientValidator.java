package com.appnomic.appsone.api.common.client.impl;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.client.ClientValidator;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MleTransactionRequestClientValidator implements ClientValidator<Object> {
    private static final Logger LOGGER = LoggerFactory.getLogger(MleTransactionRequestClientValidator.class);

    @Override
    public UtilityBean<Object> validate(RequestObject requestObject) throws ClientException {
        UtilityBean<Object> utilityBean = extract(requestObject);

        if( utilityBean.getAccountIdString() == null ) throw new ClientException("Invalid account received.");

        if( utilityBean.getApplicationIdString() == null ) throw new ClientException("Invalid app id received");

        if( utilityBean.getResponseType() == null ) throw new ClientException("Invalid response type received");

        if( utilityBean.getFromTimeString() == null ) throw new ClientException("Invalid from time received");

        if( utilityBean.getToTimeString() == null ) throw new ClientException("Invalid to time received");

        validateInputFormat(utilityBean);

        LOGGER.debug("Client validation successful for input:{}.", utilityBean);
        return utilityBean;
    }

    @Override
    public UtilityBean<Object> extract(RequestObject requestObject) throws ClientException {
        if( requestObject == null ) {
            LOGGER.error("MLE TxnDATA request object is received as null");
            throw new ClientException("MLE TxnDATA request object is received as null");
        }
        String[] tempArray = {null};

        return UtilityBean.builder()
                .accountIdString(requestObject.getParams().get(Constants.REQUEST_PARAM_IDENTIFIER.toLowerCase()))
                .applicationIdString(requestObject.getParams().get(":applicationId".toLowerCase()))
                .responseType("DC")
                .kpiFilterListString(requestObject.getQueryParams().getOrDefault(Constants.REQUEST_PARAM_TXN_KPI_LIST, tempArray)[0])
                .fromTimeString(requestObject.getQueryParams().get(Constants.REQUEST_PARAM_FROM_TIME)[0])
                .toTimeString(requestObject.getQueryParams().get(Constants.REQUEST_PARAM_TO_TIME)[0])
                .build();
    }

    private void validateInputFormat(UtilityBean utilityBean) throws ClientException {
        LOGGER.trace("Validating number format input for validity in MLE txn data API.");
        try {
            utilityBean.setApplicationId(Integer.parseInt(utilityBean.getApplicationIdString()));
            long from = Long.parseLong(utilityBean.getFromTimeString());
            long to = Long.parseLong(utilityBean.getToTimeString());
            if( from > to ) throw new ClientException("Invalid time range received");
            utilityBean.setFromTime(from);
            utilityBean.setToTime(to);
        } catch (NumberFormatException ne) {
            LOGGER.error("input validation failed with reason: ",ne);
            throw new ClientException("Invalid input received instead of number.");
        }
    }
}
