package com.appnomic.appsone.api.common;

import java.util.HashMap;
import java.util.Map;

public enum TxnKpiName {

    RESPONSE_TIME("avg_response"),
    TOTAL_VOLUME("total_txn_count"),
    SLOW_PERCENTAGE("slow_perc"),
    FAIL_PERCENTAGE("fail_perc"),
    TIMEOUT_PERCENTAGE("timeout_perc"),
    SLOW_VOLUME("slow"),
    FAIL_VOLUME("fail"),
    UNKNOWN_VOLUME("unknown"),
    TIMEOUT_VOLUME("timeout"),
    SUCCESS_VOLUME("success");

    private String name;
    private static Map<String, TxnKpiName> map = new HashMap<>();
    static{
        for(TxnKpiName txnKpiName : TxnKpiName.values()){
            map.put(txnKpiName.getName(), txnKpiName);
        }
    }

    TxnKpiName(String name){
        this.name = name;
    }

    public String getName(){
        return this.name;
    }

    public static TxnKpiName getValue(String name){
        return map.getOrDefault(name, null);
    }

}
