package com.appnomic.appsone.api.common;

public enum ComponentSelection {
    COMPONENT("Component"),
    HOST("Host"),
    BOTH("Both");

    private String selection;

    ComponentSelection(String selection) {
        this.selection = selection;
    }

    public String getSelection() {
        return selection;
    }

    @Override
    public String toString() {
        return selection;
    }

    public static boolean contains(String str) {
        for (ComponentSelection v : values()) {
            if (v.getSelection().equals(str)) {
                return true;
            }
        }
        return false;
    }
}
