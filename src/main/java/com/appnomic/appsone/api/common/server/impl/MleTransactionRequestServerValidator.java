package com.appnomic.appsone.api.common.server.impl;

import com.appnomic.appsone.api.beans.MleTransactionDataRequestData;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.server.ServerValidator;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.ApplicationRepo;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.ConfProperties;
import com.heal.configuration.pojos.Application;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class MleTransactionRequestServerValidator implements ServerValidator<MleTransactionDataRequestData> {
    private static final Logger LOGGER = LoggerFactory.getLogger(MleTransactionRequestServerValidator.class);

    @Override
    public MleTransactionDataRequestData validate(UtilityBean utilityBean) throws ServerException {
        MleTransactionDataRequestData requestData = new MleTransactionDataRequestData();
        AccountRepo accountRepo = new AccountRepo();
        ApplicationRepo applicationRepo = new ApplicationRepo();
        com.heal.configuration.pojos.Account account =accountRepo.getAccount(utilityBean.getAccountIdString());
        if( account == null ) {
            LOGGER.error("Invalid account identifier: {}.", utilityBean.getAccountIdString());
            throw new ServerException("Invalid account id.");
        }

        Application application = applicationRepo.getApplicationDetailsWithAppId(utilityBean.getAccountIdString(), utilityBean.getApplicationId());

        requestData.setFromTime(utilityBean.getFromTime());
        requestData.setToTime(utilityBean.getToTime());
        requestData.setApplicationId(utilityBean.getApplicationId());
        requestData.setAccount(account);
        requestData.setTransactionKpi(validateTransactionKpi(utilityBean.getKpiFilterListString()));
        requestData.setApplication(application);

        if(utilityBean.getResponseType() == null)    {
            utilityBean.setResponseType(ConfProperties.getString(Constants.TRANSACTION_TYPE,
                    Constants.TRANSACTION_TYPE_DEFAULT));
        }
        requestData.setResponseType(utilityBean.getResponseType());
        return requestData;
    }

    private List<Integer> validateTransactionKpi(String kpiList) throws ServerException {
        List<Integer> kpiIds = null;
        try {
            if (kpiList != null) {
                kpiIds = Arrays.stream(kpiList.split(",")).map(Integer::parseInt)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            LOGGER.error("Error occurred while processing filtering KPI id for mle txn data.",e);
            throw new ServerException("Error occurred while processing filtering KPI id for mle txn data.");
        }
        return kpiIds;
    }
}
