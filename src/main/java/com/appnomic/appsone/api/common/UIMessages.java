package com.appnomic.appsone.api.common;

public class UIMessages {


    private UIMessages(){}

    public static final String ERROR_INVALID_COMP_INSTANCE_ID = "Invalid component instance id is" +
                                                                " found";
    public static final String ERROR_INVALID_TYPE = "type is invalid.";


    public static final String ERROR_INVALID_ACCOUNT_ID = "Invalid account id provided";
    public static final String ERROR_INVALID_SERVICE_ID = "Invalid service id provided";
    public static final String ERROR_INVALID_APPLICATION_ID = "Invalid application id provided";
    public static final String ERROR_NO_TXN_AVAILABLE = "No transaction available";
    public static final String ERROR_PARAMETER_VALIDATION_FAILED = "Parameter validation failed";
    public static final String ERROR_INVALID_INPUT = "Invalid input parameters provided.";
    public static final String ERROR_INVALID_INPUT_PARAM = "Invalid input parameters provided. Param name:{0}, value:{1}";
    public static final String ERROR_INTERNAL = "Internal error.";
    public static final String ERROR_INVALID_REQUEST = "Invalid Request.";
    public static final String SUCCESS = "SUCCESS";

    public static final String ACCOUNT_EMPTY = "Account Identifier should not be empty.";
    public static final String REQUEST_NULL = "Request object is null or empty.";
    public static final String SERVICE_EMPTY_ERROR="Service Id should not be empty or null.";
    public static final String SERVICE_EMPTY_ERROR_MESSAGE="Service Id should not be empty or null, serviceId: {0}";
    public static final String INVALID_SERVICE="Invalid service id provided.";

    public static final String INVALID_TIME_INTERVAL = "Start time {0} cannot be greater than end time {0}";
    public static final String USER_NOT_EXISTS = "User doesn't exists in HEAL. Identifier: {0}";


    public static final String MESSAGE_INVALID_PREFERENCE = "Invalid preference for the user.";
    public static final String MESSAGE_PREFERENCE_EXISTS = "User preference already exists. Name: {0}";

    public static final String NOTIFICATION_TYPE_ERROR="Error occurred while getting the notification type details.";
    public static final String NOTIFICATION_DATA_ERROR="Requested notification data is not available for selected account.";

    public static final String INVALID_ACCOUNT_MESSAGE = "Invalid account id provided.";
    public static final String INVALID_INSTANCE_MESSAGE = "Invalid instance id provided.";
    public static final String USER_ERROR = "User identifier should not be empty or NULL";
    public static final String NOTIFICATION_ACCOUNT_ERROR = "User does not have access to given account. Please contact admin.";
    public static final String INVALID_REQUEST_BODY = "Invalid request body. Reason: Request body is either NULL or empty.";
    public static final String LIST_NOTIFICATION_ERROR = "notification list have some problem please see the log";
    public static final String NOTIFICATION_DETAILS_ERROR = "error in adding notification details";
    public static final String ADD_NOTIFICATION_ERROR = "error in adding user notification details";
    public static final String LOG_NOTIFICATION_ERROR = "user notification list have some problem please check the log";

    public static final String TAG_DOES_NOT_EXIST = "Tag does not exist in database: ";
    public static final String TAG_DOES_NOT_EXIST_MESSAGE = "Tag does not exist in database. Name: {0} ";

    public static final String USER_INACTIVE = "User account is in-active for My Profile changes.";
    public static final String USER_ERROR_INACTIVE = "User account is in-active for My Profile changes. Param name:{0}, value:{1}";

    public static final String INVALID_TIMEZONE_ID = "Timezone id does not exist in database. id: {0}";
    public static final String INVALID_CLUSTER_ID = "Invalid cluster id provided.";

    public static final String INVALID_TXN_ID = "Invalid transaction Id provided.";
    public static final String AUTH_KEY_INVALID = "Invalid Authorization Token.";

    public static final String CLIENT_VALIDATION_FAILED = "Client validation Failed.";
    public static final String ACCOUNT_IDENTIFIER_NULL_EMPTY = "Account identifier is null or empty.";
    public static final String INTERNAL_SERVER_ERROR = "Internal server error, Kindly contact the Administrator.";
    public static final String SERVICE_INSTANCE_HEALTH_GET_SUCCESS = "Service instance health details fetched successfully.";
    public static final String ERROR_INVALID_PARENT_APPLICATION_IDENTIFIER = "Invalid Parent Application Identifier";
    public static final String ERROR_GETTING_APPLICATION = "Error getting Applications for Parent Application";
}
