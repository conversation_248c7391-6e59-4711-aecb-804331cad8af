package com.appnomic.appsone.api.util;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.pojo.NewTimeRangeDefinition;
import com.heal.configuration.pojos.Account;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class NewTimeIntervalGenerationUtility {

    private final NewTimeRangeDefinition newTimeRangeDefinition;
    public volatile int timezoneOffset = 0;

    private String timezoneId;

    private final List<Long> osTimes = new ArrayList<>();

    private final List<Long> displayTimes = new ArrayList<>();

    public NewTimeIntervalGenerationUtility() {
        newTimeRangeDefinition = new NewTimeRangeDefinition();
    }

    public void setTimezoneOffset(String accountIdentifier) {
        try {
            Account account = new AccountRepo().getAccount(accountIdentifier);
            if (account == null) {
                log.error("Account information was not found hence setting timezone to GMT");
            } else {
                account.getTags().stream()
                        .filter(t -> t.getType().equalsIgnoreCase("Timezone"))
                        .findAny().ifPresent(timezoneTag -> this.timezoneOffset = Integer.parseInt(timezoneTag.getValue()));
            }
        } catch (Exception e) {
            log.error("Error occurred while setting time for account with identifier: {}, default GMT timezone will be used", accountIdentifier, e);
        }
    }

    public void processTimeRange(Long fromTime, Long toTime) {
        this.getNewTimeRangeDefinition().setFromTime(fromTime);
        this.getNewTimeRangeDefinition().setToTime(toTime);
        setTimeZone();
        setAggregationLevel(fromTime, toTime);
        setDisplayGeneratedTimeList(fromTime, toTime);
        setOSGeneratedTimeList(fromTime, toTime);
        setTimeListInLong();
    }

    private void setTimeListInLong() {

        this.getNewTimeRangeDefinition().getGeneratedCompleteTimeSeriesOpenSearchTimes().forEach(it -> {
            long epochTime = it.atZone(ZoneId.of(this.timezoneId)).toEpochSecond() * 1000;
            this.osTimes.add(epochTime);
        });

        this.getNewTimeRangeDefinition().getGeneratedTimeSeriesDisplayTimes().forEach(it -> {
            long epochTime = it.atZone(ZoneId.of(this.timezoneId)).toEpochSecond() * 1000;
            this.displayTimes.add(epochTime);
        });
    }

    private void setTimeZone() {
        this.timezoneId = ZoneId.ofOffset("UTC", ZoneOffset.ofTotalSeconds(this.timezoneOffset / 1000)).getId();
    }

    private void setAggregationLevel(Long fromTime, Long toTime) {
        this.getNewTimeRangeDefinition().setAggregationLevel(DateTimeUtil.getAggregationLevel(fromTime, toTime));
    }

    private void setDisplayGeneratedTimeList(Long fromTime, Long toTime) {

        log.trace(Constants.INVOKED_METHOD + "setGeneratedTimeList with params: {} , {} ", fromTime, toTime);

        List<LocalDateTime> timeGenerationStartTime = DateTimeUtil.getStartTimeStampDisplayTimes(fromTime, this.getNewTimeRangeDefinition().getAggregationLevel(), this.timezoneId);
        List<LocalDateTime> generatedTimeSeries = DateTimeUtil.getGeneratedTimeSeriesDisplayTimes(timeGenerationStartTime, toTime, this.getNewTimeRangeDefinition().getAggregationLevel());

        this.getNewTimeRangeDefinition().getGeneratedTimeSeriesDisplayTimes().addAll(generatedTimeSeries);
    }

    private void setOSGeneratedTimeList(Long fromTime, Long toTime) {

        log.trace(Constants.INVOKED_METHOD + "setOSGeneratedTimeList with params: {} , {} ", fromTime, toTime);

        List<LocalDateTime> timeGenerationStartTime = DateTimeUtil.getStartTimeStampOSTimes(fromTime, this.getNewTimeRangeDefinition().getAggregationLevel(), this.timezoneId);
        List<LocalDateTime> generatedTimeSeries = DateTimeUtil.getGeneratedTimeSeriesOSTimes(timeGenerationStartTime, toTime, this.getNewTimeRangeDefinition().getAggregationLevel());

        this.getNewTimeRangeDefinition().getGeneratedCompleteTimeSeriesOpenSearchTimes().addAll(generatedTimeSeries);

    }

    public NewTimeRangeDefinition getNewTimeRangeDefinition() {
        return this.newTimeRangeDefinition;
    }

    public String getTimeZoneId() {
        return this.timezoneId;
    }

    public List<Long> getOSTimes() {
        return this.osTimes;
    }

    public List<Long> getDisplayTimes() {
        return this.displayTimes;
    }

    public void addEndTimeInOSTime() {
        if (!this.osTimes.contains(this.getNewTimeRangeDefinition().getToTime())) {
            this.osTimes.add(this.getNewTimeRangeDefinition().getToTime());
        }
    }
}
