package com.appnomic.appsone.api.util;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.AppsoneException;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.dao.redis.MasterRepo;
import com.appnomic.appsone.api.service.KeyCloakAuthService;
import com.appnomic.appsone.model.JWTData;
import com.heal.configuration.pojos.TimeRangeDetails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> : 28/01/2019
 */
public class ValidationUtils {

    private static final Logger logger = LoggerFactory.getLogger(ValidationUtils.class);

    public static com.heal.configuration.pojos.ViewTypes validateTxnResponseType(int accountId, String txnResponseType) {

        com.heal.configuration.pojos.ViewTypes viewType = new MasterRepo().getTypes().stream()
                .filter(v -> v.getTypeName().equalsIgnoreCase(Constants.TRANSACTION_RESPONSE_TYPE))
                .filter(v -> v.getSubTypeName().equalsIgnoreCase(txnResponseType))
                .findAny().orElse(null);

        if (viewType == null) {
            logger.warn("No any data is found for -" + txnResponseType + " in DB.");
            return null;
        }

        return viewType;
    }

    public static boolean isValidTxnResponseType(int accountId, String txnResponseType) {
        return validateTxnResponseType(accountId, txnResponseType) != null;
    }

    public static String getUserId(String authToken) {
        if (authToken == null)
            return null;

        try {
            JWTData jwtData = KeyCloakAuthService.extractUserDetails(authToken);
            return jwtData.getSub();
        } catch (Exception e) {
            logger.error("Exception while fetching user details from JWT. Details: ", e);
            return null;
        }
    }

    public static void commonClientValidations(Request request) throws AppsoneException {
        if (StringUtils.isEmpty(request.headers(Constants.AUTHORIZATION_HEADER))) {
            logger.error(Constants.MESSAGE_EMPTY_AUTH_TOKEN);
            throw new AppsoneException(Constants.MESSAGE_EMPTY_AUTH_TOKEN);
        }

        if (StringUtils.isEmpty(request.params(Constants.REQUEST_PARAM_IDENTIFIER))) {
            logger.error(Constants.MESSAGE_EMPTY_ACCOUNT);
            throw new AppsoneException(Constants.MESSAGE_EMPTY_ACCOUNT);
        }
    }

    public static void isValidTimeRange(long from, long to) throws ClientException {
        logger.trace("Validating time range");
        if (from >= to) {
            logger.error("Invalid time range received.");
            throw new ClientException("Invalid time range received.");
        } else {
            logger.info("The time range received in request is valid.");
        }
    }

    public static TimeRangeDetails getRollupPojo(long fromTime, long toTime, List<TimeRangeDetails> timeRangeDetailsList) {
        logger.trace("Getting time range details from percona.");
        long currentTime = System.currentTimeMillis();

        int d1 = (int) ((currentTime - fromTime) / (1000 * 60 * 60 * 24));
//        int d2 = (int) ((currentTime - toTime) / (1000 * 60 * 60 * 24));

        String timeRangeName = String.valueOf((toTime - fromTime) / (1000 * 60));

        Map<String, TimeRangeDetails> timeRangeMap = timeRangeDetailsList.parallelStream()
                .collect(Collectors.toMap(TimeRangeDetails::getName, c -> c));

        int d3 = 0;
        if (timeRangeMap.containsKey(timeRangeName)) {
            d3 = timeRangeMap.get(timeRangeName).getLookBackTime();
        }

        if (d3 == 0) {
            logger.error("No record found in db for the requested operation.");
            return null;
        }
        if (d1 >= d3) {
            logger.error("Invalid time range operation requested");
            return null;
        }

        int d4 = 14;
        if (timeRangeMap.containsKey("60")) {
            d4 = timeRangeMap.get("60").getLookBackTime();
        }

        if (d1 > d4) {
            timeRangeMap.get(timeRangeName).setNeedRolledUpIndexes(1);
        } else timeRangeMap.get(timeRangeName).setNeedRolledUpIndexes(0);

        return timeRangeMap.get(timeRangeName);
    }


}
