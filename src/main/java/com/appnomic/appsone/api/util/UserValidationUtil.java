package com.appnomic.appsone.api.util;

import com.appnomic.appsone.api.beans.*;
import com.appnomic.appsone.api.cache.UserDetailsCache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.AppsoneException;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.ApplicationRepo;
import com.appnomic.appsone.api.pojo.TagMappingDetails;
import com.appnomic.appsone.api.pojo.UserAccessDetails;
import com.appnomic.appsone.api.service.mysql.*;
import com.google.gson.reflect.TypeToken;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.Application;
import lombok.extern.slf4j.Slf4j;
import spark.Request;

import java.lang.reflect.Type;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
public class UserValidationUtil {

    public static UserAccessDetails extractUserAccessDetails(String accessDetails, UserAccountIdentifiersBean userAccountInfo) {
        UserAccessDetails userAccessDetails = null;

        Type userBeanType = new TypeToken<AccessDetailsBean>() {
        }.getType();

        AccessDetailsBean bean = CommonUtils.jsonToObject(accessDetails, userBeanType);

        if (bean != null && bean.getAccounts() != null) {
            if (bean.getAccounts().contains(userAccountInfo.getAccountIdentifier())) {
                int accessibleAccountId = getAccessibleAccountId(userAccountInfo);

                Map<String, AccessDetailsBean.Application> accessibleApplications = bean.getAccountMapping();

                if (accessibleApplications == null || accessibleApplications.isEmpty()) {
                    log.error("Applications unavailable for account [{}] and user [{}]",
                            userAccountInfo.getAccountIdentifier(), userAccountInfo.getUserIdentifier());
                    return null;
                }

                AccessDetailsBean.Application applicationIdentifiers = accessibleApplications.get(userAccountInfo.getAccountIdentifier());

                List<Application> applicationControllerList = new ApplicationRepo().getAllApplicationDetails(userAccountInfo.getAccountIdentifier());

                if (applicationIdentifiers.getApplications().contains("*")) {
                    userAccessDetails = buildUserAccessDetails(accessibleAccountId, applicationControllerList);
                } else {
                    applicationControllerList = applicationControllerList.parallelStream()
                            .filter(app -> (applicationIdentifiers.getApplications().contains(app.getIdentifier())))
                            .collect(Collectors.toList());

                    userAccessDetails = buildUserAccessDetails(accessibleAccountId, applicationControllerList);
                }
            } else if (bean.getAccounts().contains("*")) {
                int accessibleAccountId = getAccessibleAccountId(userAccountInfo);

                List<Application> applicationControllerList = new ApplicationRepo().getAllApplicationDetails(userAccountInfo.getAccountIdentifier());

                userAccessDetails = buildUserAccessDetails(accessibleAccountId, applicationControllerList);
            }
        }

        return userAccessDetails;
    }

    private static int getAccessibleAccountId(UserAccountIdentifiersBean userAccountInfo) {

        Optional<Integer> accessibleAccount = new AccountRepo().getAccounts().parallelStream()
                .filter(acc -> acc.getIdentifier().equalsIgnoreCase(userAccountInfo.getAccountIdentifier()))
                .map(Account::getId).findAny();

        if (!accessibleAccount.isPresent()) {
            log.error("Account information unavailable for account identifier [{}]", userAccountInfo.getAccountIdentifier());
            return 0;
        }

        return accessibleAccount.get();
    }

    private static UserAccessDetails buildUserAccessDetails(int accountId, List<Application> applicationControllerList) {
        UserAccessDetails userAccessDetails = new UserAccessDetails();
        userAccessDetails.setApplicationIds(new ArrayList<>());
        userAccessDetails.setServiceIds(new ArrayList<>());
        userAccessDetails.setServiceIdentifiers(new ArrayList<>());
        userAccessDetails.setTransactionIds(new ArrayList<>());
        userAccessDetails.setAgents(new ArrayList<>());
        userAccessDetails.setApplicationIdentifiers(new ArrayList<>());

        if (applicationControllerList.isEmpty()) {
            return userAccessDetails;
        }

        List<ViewApplicationServiceMappingBean> beans = getAppServiceMappingDetails(accountId, applicationControllerList);

        userAccessDetails.setApplicationIds(beans.parallelStream().map(ViewApplicationServiceMappingBean::getApplicationId).distinct().collect(Collectors.toList()));
        userAccessDetails.setApplicationIdentifiers(beans.parallelStream().map(ViewApplicationServiceMappingBean::getApplicationIdentifier).distinct().collect(Collectors.toList()));
        userAccessDetails.setServiceIds(beans.parallelStream().map(ViewApplicationServiceMappingBean::getServiceId).filter(serviceId -> serviceId > 0).distinct().collect(Collectors.toList()));
        userAccessDetails.setServiceIdentifiers(beans.parallelStream().map(ViewApplicationServiceMappingBean::getServiceIdentifier).filter(s -> !StringUtils.isEmpty(s)).distinct().collect(Collectors.toList()));
        userAccessDetails.setTransactionIds(MasterDataService.getTransactionsIdsForAccount(accountId));

        List<AgentBean> agentBeanList = AgentDataService.getAgentsByAccountId(accountId);

        List<TagMappingDetails> agentTags = TagsDataService.getAgentTags(1, "agent", accountId);
        if (agentTags != null && !agentTags.isEmpty()) {
            Set<Integer> agentIds = agentTags.parallelStream()
                    .filter(a -> userAccessDetails.getServiceIds().contains(Integer.parseInt(a.getTagKey())))
                    .map(TagMappingDetails::getObjectId).collect(Collectors.toSet());

            Map<Integer, Set<Integer>> agentToServices = agentTags.parallelStream()
                    .collect(Collectors.groupingBy(TagMappingDetails::getObjectId, Collectors.mapping(t -> Integer.parseInt(t.getTagKey()), Collectors.toSet())));

            List<Integer> mappedAgentIds = agentTags.parallelStream().map(TagMappingDetails::getObjectId).collect(Collectors.toList());

            List<AgentBean> agentBeans = agentBeanList.parallelStream().filter(a -> !mappedAgentIds.contains(a.getId())).collect(Collectors.toList());
            agentBeans.addAll(agentBeanList.parallelStream().filter(a -> agentIds.contains(a.getId())).collect(Collectors.toList()));
            agentBeans.forEach(a -> a.setServiceIds(new ArrayList<>(agentToServices.getOrDefault(a.getId(), new HashSet<>()))));

            userAccessDetails.setAgents(agentBeans);
        } else {
            userAccessDetails.setAgents(agentBeanList);
        }

        return userAccessDetails;
    }

    private static List<ViewApplicationServiceMappingBean> getAppServiceMappingDetails(int accountId, List<Application> applicationControllerList) {
        Map<String, List<ViewApplicationServiceMappingBean>> servicesMap =
                new ControllerDataService().getServicesForApplicationWithAccID(accountId).parallelStream()
                        .collect(Collectors.groupingBy(ViewApplicationServiceMappingBean::getApplicationIdentifier));

        return applicationControllerList.parallelStream()
                .map(controller -> {
                    List<ViewApplicationServiceMappingBean> services =
                            servicesMap.getOrDefault(controller.getIdentifier(), new ArrayList<>());
                    if (!services.isEmpty()) {
                        return services;
                    }
                    return Collections.singleton(ViewApplicationServiceMappingBean.builder()
                            .applicationId(controller.getId())
                            .applicationIdentifier(controller.getIdentifier())
                            .applicationName(controller.getName())
                            .build());
                })
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    public static boolean verifyUserAccessibility(Request request) {
        if (null == request) {
            log.error("Request is invalid");
            return false;
        }

        String userId;
        try {
            userId = CommonUtils.getUserId(request);
        } catch (Exception e) {
            log.error("Exception encountered while fetching userId from request header. Reason: {}", e.getMessage(), e);
            return false;
        }

        try {
            boolean matchedRoute = getRouteInformation(userId, request.uri(), request.requestMethod());

            if (matchedRoute) {
                return validateUser(request.uri(), userId);
            } else {
                log.error("There are no routes matching the incoming request [{}] and the profile of the user", request.uri());
            }
        } catch (AppsoneException e) {
            log.error("Exception encountered while retrieving user details. Reason: {}", e.getMessage(), e);
            return false;
        }

        return false;
    }

    private static boolean getRouteInformation(String userId, String uri, String requestMethod) throws AppsoneException {
        String[] str = uri.split(Constants.UI_BASE_URL);

        UserAccessDataService dataService = new UserAccessDataService();

        UserAttributesBean userAttributesBean = dataService.getUserAttributeDetails(userId);

        if (null == userAttributesBean) {
            log.error("User details unavailable for userId [{}]", userId);
            throw new AppsoneException("User details unavailable");
        }

        List<RoutesInformation> accessibleRoutes = dataService.getAccessibleRoutesForUser(userAttributesBean.getAccessProfileId());

        return accessibleRoutes.parallelStream()
                .filter(route -> route.getRequestMethod().equalsIgnoreCase(requestMethod))
                .filter(route -> uri.replace(Constants.REPLACE_UI_BASE_URL, "").startsWith(route.getBaseUrl()))
                .anyMatch(route -> Pattern.compile(route.getPathInfoInRegex()).matcher(str[1]).matches());
    }

    private static boolean validateUser(String uri, String userId) throws AppsoneException {
        String accountIdentifier;
        String applicationIdentifier;
        String serviceId;
        String transactionIdentifier;

        if (uri.contains("/accounts/")) {
            String[] splitStr = uri.split("/accounts/");
            accountIdentifier = splitStr[1].contains("/") ? splitStr[1].substring(0, splitStr[1].indexOf("/")) : splitStr[1];

            Account account = new AccountRepo().getAccount(accountIdentifier);

            if (null != account) {
                UserAccessDetails accessDetails;
                try {
                    accessDetails = UserDetailsCache.getInstance().userApplications.get(new UserAccountIdentifiersBean(userId, account.getIdentifier()));
                } catch (ExecutionException e) {
                    log.error("Exception encountered while fetching user access details from cache. Reason: {}", e.getMessage(), e);
                    throw new AppsoneException("User access details unavailable for account");
                }

                if (null == accessDetails) {
                    log.error("Access details unavailable for user [{}]", userId);
                    throw new AppsoneException("Access details unavailable for user");
                }

                if (uri.contains("/applications/") && !uri.endsWith("/applications/")) {
                    splitStr = uri.split("/applications/");
                    applicationIdentifier = splitStr[1].contains("/") ? splitStr[1].substring(0, splitStr[1].indexOf("/")) : splitStr[1];

                    if (Pattern.compile("[a-zA-Z]").matcher(applicationIdentifier).find()) {
                        if (applicationIdentifier.equalsIgnoreCase("all")) {
                            if (accessDetails.getApplicationIdentifiers().isEmpty()) {
                                log.error("User does not have access to application [{}]", applicationIdentifier);
                                return false;
                            }
                        } else {
                            if (!(accessDetails.getApplicationIdentifiers().contains(applicationIdentifier))) {
                                log.error("User does not have access to application [{}]", applicationIdentifier);
                                return false;
                            }
                        }
                    } else {
                        if (!applicationIdentifier.equalsIgnoreCase("0") && !(accessDetails.getApplicationIds().contains(Integer.parseInt(applicationIdentifier)))) {
                            log.error("User does not have access to application [{}]", applicationIdentifier);
                            return false;
                        }
                    }
                }

                if (uri.contains("/services/") && !uri.endsWith("/services/")) {
                    splitStr = uri.split("/services/");
                    serviceId = splitStr[1].contains("/") ? splitStr[1].substring(0, splitStr[1].indexOf("/")) : splitStr[1];

                    if (Pattern.compile("[a-zA-Z]").matcher(serviceId).find()) {
                        if (!accessDetails.getServiceIdentifiers().contains(serviceId)) {
                            log.error("User does not have access to service [{}]", serviceId);
                            return false;
                        }
                    } else {
                        if (!serviceId.equalsIgnoreCase("0") && !(accessDetails.getServiceIds().contains(Integer.parseInt(serviceId)))) {
                            log.error("User does not have access to service [{}]", serviceId);
                            return false;
                        }
                    }
                }

                if (uri.contains("/transactions/") && !uri.endsWith("/transactions/")
                        && !uri.contains("/services/") && !uri.contains("/applications/")) {
                    splitStr = uri.split("/transactions/");
                    transactionIdentifier = splitStr[1].contains("/") ? splitStr[1].substring(0, splitStr[1].indexOf("/")) : splitStr[1];

                    if (!Pattern.compile("[a-zA-Z]").matcher(transactionIdentifier).find()) {
                        if (!transactionIdentifier.equalsIgnoreCase("0") && !(accessDetails.getTransactionIds().contains(Integer.parseInt(transactionIdentifier)))) {
                            log.error("User does not have access to transaction [{}]", transactionIdentifier);
                            return false;
                        }
                    }
                }
            }
        } else {
            log.info("No validation needed for incoming request");
        }
        return true;
    }

}
