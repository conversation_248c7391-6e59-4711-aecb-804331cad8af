package com.appnomic.appsone.api.util;

import com.appnomic.appsone.api.pojo.tfp.TFPAnomalyTransactionDetails;

import java.util.Comparator;

/**
 *
 * Sorting order of Transactions
 *
 * 1. Anomalies transactions first. If 2 Txns have anomalies, sort in descending order by
 *    - Failed
 *    - If failed is equal, By Slow
 *    - If slow is equal, By Volume
 *    - If Volume is also equal, Default - by Txn Name
 * 2. Remaining non-anomalous transactions By Failures
 *    - If volume is equal, By Slow
 *    - if failed is equal, By Volume
 *    - Default - by Txn Name
 *
 */

public class TFPTransactionAnomalyComparator implements Comparator<TFPAnomalyTransactionDetails> {
    @Override
    public int compare(TFPAnomalyTransactionDetails o1, TFPAnomalyTransactionDetails o2) {
        if (o1.getIsAnomaly() > 0 && o2.getIsAnomaly() <= 0)    {
            return -1;
        }
        else if (o1.getIsAnomaly() <= 0 && o2.getIsAnomaly() > 0)   {
            return 1;
        }
        else if (o1.getIsAnomaly() == o2.getIsAnomaly() && o1.getIsAnomaly() > 0)    {
            // Either both have anomalies or both don't have anomalies
            if (o1.getFailVolume() > o2.getFailVolume())    {
                return -1;
            }
            else if (o1.getFailVolume() < o2.getFailVolume())   {
                return 1;
            }
            else if (o1.getFailVolume() == o2.getFailVolume())  {
                // Both have same fail counts and possibly both have same anomalies
                if (o1.getSlowVolume() > o2.getSlowVolume())    {
                    return -1;
                }
                else if (o1.getSlowVolume() < o2.getSlowVolume())   {
                    return 1;
                }
                else if (o1.getSlowVolume() == o2.getSlowVolume())  {
                    // Both have same fail, same slow and same anomalous counts
                    if (o1.getVolume() > o2.getVolume())    {
                        return -1;
                    }
                    else if (o1.getVolume() < o2.getVolume())   {
                        return 1;
                    }
                    else if (o1.getVolume() == o2.getVolume())  {
                        // Both have same fail, same slow, same anomalous counts and overall volume
                        return  o2.getTransactionName().compareTo(o1.getTransactionName());
                    }
                }
            }
        }
        else {
            // No Anomalies
            if (o1.getFailVolume() > o2.getFailVolume())    {
                return -1;
            }
            else if (o1.getFailVolume() < o2.getFailVolume())   {
                return 1;
            }
            else if (o1.getFailVolume() == o2.getVolume())  {
                // Both have same fail, same slow and same anomalous counts
                if (o1.getSlowVolume() > o2.getSlowVolume())    {
                    return -1;
                }
                else if (o1.getSlowVolume() < o2.getSlowVolume())   {
                    return 1;
                }
                else if (o1.getSlowVolume() == o2.getSlowVolume())  {
                    // Both have same fail, same slow, same anomalous counts and overall volume
                    if (o1.getVolume() > o2.getVolume())    {
                        return -1;
                    }
                    else if (o1.getVolume() < o2.getVolume())   {
                        return 1;
                    }
                    else {
                        return o2.getTransactionName().compareTo(o1.getTransactionName());
                    }
                }
            }
        }
        return 0;
    }
}
