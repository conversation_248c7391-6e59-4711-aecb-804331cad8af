package com.appnomic.appsone.api.util;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.pojo.ServiceDependencyGraph;
import com.appnomic.appsone.api.pojo.TopologyDetailsResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> : 19/04/2019
 */
public class RCAPathGenerator {
    private static final Logger logger = LoggerFactory.getLogger(RCAPathGenerator.class);
    private static final String RCA_PATH_SEQUENCE_EDGE_DISPLAY_KEY = ConfProperties.getString(Constants.RCA_PATH_SEQUENCE_POSTION_KEY,
            Constants.RCA_PATH_SEQUENCE_POSTION_KEY_DEFAULT);
    private static final String RCA_PATH_SEQUENCE_EDGE_DISPLAY_VALUE = ConfProperties.getString(Constants.RCA_PATH_SEQUENCE_POSTION_VALUE,
            Constants.RCA_PATH_SEQUENCE_POSTION_VALUE_DEFAULT);
    private static final int COMMONALITY_FACTOR = Integer.parseInt(ConfProperties.getString(Constants.RCA_PATH_COMMONALITY_CONSTANT,
            Constants.RCA_PATH_COMMONALITY_CONSTANT_DEFAULT));
    private static final int COMMONALITY_LOGIC = Integer.parseInt(ConfProperties.getString(Constants.RCA_PATH_COMMONALITY_LOGIC,
            Constants.RCA_PATH_COMMONALITY_LOGIC_DEFAULT));
    public static final String EDGES = "EDGES";
    public static final String NODES = "NODES";
    private final Set<String> visited = new HashSet<>();
    private final List<TopologyDetailsResponse.Nodes> generatedPath = new LinkedList<>();
    private final List<List<TopologyDetailsResponse.Nodes>> generatedPathList = new LinkedList<>();
    private ServiceDependencyGraph graph;

    public RCAPathGenerator() {
    }

    public RCAPathGenerator(ServiceDependencyGraph graph) {
        this.graph = graph;
    }

    private List<List<TopologyDetailsResponse.Nodes>> sortRCAPath(List<TopologyDetailsResponse.Nodes> affectedNodes) {
        Map<Integer, List<List<TopologyDetailsResponse.Nodes>>> sortedRCAPaths = new HashMap<>();
        for (List<TopologyDetailsResponse.Nodes> rcaPathList : this.generatedPathList) {
            int score = 0;
            for (TopologyDetailsResponse.Nodes node : rcaPathList) {
                for (TopologyDetailsResponse.Nodes affectedNode : affectedNodes) {
                    if (affectedNode.getId().equals(node.getId())) {
                        score += 1;
                        break;
                    }
                }
            }
            sortedRCAPaths.computeIfAbsent(score, k -> new ArrayList<>());
            sortedRCAPaths.get(score).add(rcaPathList);
        }
        List<Integer> sortedList = sortedRCAPaths.keySet().stream().sorted(Integer::compareTo).collect(Collectors.toList());
        List<List<TopologyDetailsResponse.Nodes>> sortedRCAPathsResponse = new ArrayList<>();
        sortedList.forEach(it -> {
            sortedRCAPathsResponse.addAll(sortedRCAPaths.get(it));
        });
        return sortedRCAPathsResponse;
    }

    public List<List<TopologyDetailsResponse.Nodes>> getAllRCAPath(TopologyDetailsResponse.Nodes source, TopologyDetailsResponse.Nodes destination,
                                                                   List<TopologyDetailsResponse.Nodes> affectedNodes) {
        this.generatedPath.add(source);
        getRCAPathUtil(source, destination);
        //return generatedPathList;
        return sortRCAPath(affectedNodes);
    }

    private List<TopologyDetailsResponse.Nodes> copyOf(List<TopologyDetailsResponse.Nodes> original) {
        List<TopologyDetailsResponse.Nodes> copy = new ArrayList<>();

        if (original == null || original.isEmpty()) {
            return copy;
        }

        copy.addAll(original);
        return copy;
    }

    private void getRCAPathUtil(TopologyDetailsResponse.Nodes source, TopologyDetailsResponse.Nodes destination) {
        this.visited.add(source.getId());

        if (source.getId().equals(destination.getId())) {
            this.generatedPathList.add(copyOf(this.generatedPath));
            this.visited.remove(source.getId());
            return;
        }

        if (this.graph.getOutgoingNeighbors(source.getId(), true) == null)
            return;

        for (TopologyDetailsResponse.Nodes s : this.graph.getOutgoingNeighbors(source.getId(), true)) {
            if (!this.visited.contains(s.getId())) {
                this.generatedPath.add(s);
                getRCAPathUtil(s, destination);
                this.generatedPath.remove(s);
            }
            this.visited.remove(s.getId());
        }
    }

    /**
     * This method will take a new RCA path and add it to existing list of RCA paths if it is not already present as
     * part of any RCA path.
     */
    public List<List<TopologyDetailsResponse.Nodes>> addRCAPath(List<TopologyDetailsResponse.Nodes> currentPath,
                                                                List<List<TopologyDetailsResponse.Nodes>> RCAPathData) {
        List<List<TopologyDetailsResponse.Nodes>> result = new ArrayList<>();//RCAPathData.subList(0,RCAPathData.size());
        RCAPathDuplicateSwitch flag = new RCAPathDuplicateSwitch();
        try {
            RCAPathData.forEach(rcaPath -> {
                //Need to check input and validation list sizes and check for subset and superset
                if (rcaPath.size() < currentPath.size() && isSubPath(rcaPath, currentPath)) {
                    //if the current lists size is greater than the one we are validating then we will need replace the
                    //existing list we are validating against because it would be a super set of the list
                    flag.setState(true);
                } else if (currentPath.size() <= rcaPath.size() && !isSubPath(currentPath, rcaPath)) {
                    result.add(rcaPath);
                    flag.setState(true);
                } else if (currentPath.size() <= rcaPath.size() && isSubPath(currentPath, rcaPath)) {
                    result.add(rcaPath);
                    flag.setState(false);
                } else {
                    result.add(rcaPath);
                }
            });

            if (flag.getState() || RCAPathData.isEmpty()) {
                result.add(currentPath);
            }

            return result;
        } catch (Exception e) {
            logger.error("Error occurred while checking if a generated path is already part of RCA path.", e);
            return result;
        }
    }

    /**
     * This method check if a given RCA path is already part of an existing list of RCA paths
     */
    public boolean isSubPath(List<TopologyDetailsResponse.Nodes> inputList, List<TopologyDetailsResponse.Nodes> validatorList) {
        try {
            if (inputList.size() == 1 || validatorList.size() == 1) {
                //base case to terminate recursion when one of the list is exhausted
                return inputList.get(0).getId().equals(validatorList.get(0).getId());
            } else if (inputList.get(0).getId().equals(validatorList.get(0).getId())) {
                //if the first element is same then we eliminate the elements and check for if the remaining are same
                return isSubPath(inputList.subList(1, inputList.size()), validatorList.subList(1, validatorList.size()));
            } else if (!inputList.get(0).getId().equals(validatorList.get(0).getId()) && (validatorList.size() > inputList.size())) {
                //If the first elements of both are unequal then we will remove 1 element from validator list and then repeat the validation process
                return isSubPath(inputList, validatorList.subList(1, validatorList.size()));
            } else {
                //If the length of validator list is lesser than the input list we terminate with false since it cannot contain
                // a complete sub path
                return false;
            }
        } catch (Exception e) {
            logger.error("Error occurred while comparing new RCA path for duplicates", e);
            return false;
        }
    }

    public Map<String, Object> getDetailedSubGraph(ServiceDependencyGraph serviceDependencyGraph, List<TopologyDetailsResponse.Nodes> rcaPath,
                                                   List<TopologyDetailsResponse.Nodes> affectedNodes,
                                                   List<TopologyDetailsResponse.Nodes> entrypointServiceList) {
        Map<String, Object> graphDetails = null;
        try {
            graphDetails = new HashMap<>();

            //Add all edges and Nodes in the RCA path
            List<TopologyDetailsResponse.Edges> edges = new ArrayList<>(getRCAPathEdges(serviceDependencyGraph, rcaPath));
            List<TopologyDetailsResponse.Nodes> nodes = new ArrayList<>(rcaPath);

            //Add all edges and Nodes that are within 1 degree vicinity of the RCA path
            Map<String, Object> neighborInfo = get1DegreeNodesAndEdges(serviceDependencyGraph, rcaPath);
            edges.addAll((List<TopologyDetailsResponse.Edges>) neighborInfo.get(EDGES));
            nodes.addAll((List<TopologyDetailsResponse.Nodes>) neighborInfo.get(NODES));

            List<TopologyDetailsResponse.Nodes> enrichedNodes = enrichNodeInformation(nodes, rcaPath, affectedNodes, entrypointServiceList);

            graphDetails.put(EDGES, edges);
            graphDetails.put(NODES, enrichedNodes);

        } catch (Exception e) {
            logger.error("Error occurred while getting sub graph for RCA path", e);
            return graphDetails;
        }
        return graphDetails;
    }

    public Map<String, Object> get1DegreeNodesAndEdges(ServiceDependencyGraph serviceDependencyGraph,
                                                       List<TopologyDetailsResponse.Nodes> rcaPath) {
        Map<String, Object> graphDetails = null;
        try {
            graphDetails = new HashMap<>();
            List<TopologyDetailsResponse.Edges> edges = new ArrayList<>();
            List<TopologyDetailsResponse.Nodes> nodes = new ArrayList<>();
            rcaPath.forEach(rcaPathNode -> {
                serviceDependencyGraph.getOutgoingNeighbors(rcaPathNode.getId(), false)
                        .iterator()
                        .forEachRemaining(adjecentNodes -> {
                            TopologyDetailsResponse.Edges currentEdge = new TopologyDetailsResponse.Edges();
                            currentEdge.setSource(rcaPathNode.getId());
                            currentEdge.setTarget(adjecentNodes.getId());

                            //Edges have to be added if the node to which it lead is not in RCA path, since edge can be
                            // from different sources
                            if (rcaPath.stream().noneMatch(it -> (it.getId().equals(adjecentNodes.getId())))) {
                                edges.add(currentEdge);
                                //Additional check has to be performed to see if the node already exists before adding it in node list
                                if (nodes.stream().noneMatch(it -> (it.getId().equals(adjecentNodes.getId())))) {
                                    nodes.add(adjecentNodes);
                                }
                            }
                        });

                serviceDependencyGraph.getIncomingNeighbors(rcaPathNode.getId(), false)
                        .iterator()
                        .forEachRemaining(adjecentNodes -> {
                            TopologyDetailsResponse.Edges currentEdge = new TopologyDetailsResponse.Edges();
                            currentEdge.setSource(adjecentNodes.getId());
                            currentEdge.setTarget(rcaPathNode.getId());

                            //Edges have to be added if the node to which it lead is not in RCA path, since edge can be
                            // from different sources
                            if (rcaPath.stream().noneMatch(it -> (it.getId().equals(adjecentNodes.getId())))) {
                                edges.add(currentEdge);
                                //Additional check has to be performed to see if the node already exists before adding it in node list
                                if (nodes.stream().noneMatch(it -> (it.getId().equals(adjecentNodes.getId())))) {
                                    nodes.add(adjecentNodes);
                                }
                            }
                        });
            });
            graphDetails.put(EDGES, edges);
            graphDetails.put(NODES, nodes);
        } catch (Exception e) {
            logger.error("Error occurred while fetching 1 degree nodes and edges", e);
            return graphDetails;
        }
        return graphDetails;
    }

    public List<TopologyDetailsResponse.Edges> getRCAPathEdges(ServiceDependencyGraph serviceDependencyGraph, List<TopologyDetailsResponse.Nodes> rcaPath) {
        List<TopologyDetailsResponse.Edges> edges = null;
        try {
            edges = new ArrayList<>();

            //Merged paths edges need to be processed and validated, the list is reversed so path is from RCA node to terminal
            // node
            Collections.reverse(rcaPath);
            for (TopologyDetailsResponse.Nodes node : rcaPath) {
                for (TopologyDetailsResponse.Nodes neighborNodes : serviceDependencyGraph.getIncomingNeighbors(node.getId(), true)) {
                    if (rcaPath.stream().anyMatch(it -> it.getId().equals(neighborNodes.getId()))) {
                        TopologyDetailsResponse.Edges currentEdge = new TopologyDetailsResponse.Edges();
                        currentEdge.setSource(neighborNodes.getId());
                        currentEdge.setTarget(node.getId());
                        currentEdge.getData().put(RCA_PATH_SEQUENCE_EDGE_DISPLAY_KEY, RCA_PATH_SEQUENCE_EDGE_DISPLAY_VALUE);
                        edges.add(currentEdge);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Error occurred while fetching 1 degree nodes and edges", e);
            return edges;
        }
        return edges;
    }

    private List<TopologyDetailsResponse.Nodes> enrichNodeInformation(List<TopologyDetailsResponse.Nodes> nodesList,
                                                                      List<TopologyDetailsResponse.Nodes> rcaPath,
                                                                      List<TopologyDetailsResponse.Nodes> affectedNodes,
                                                                      List<TopologyDetailsResponse.Nodes> entrypointServiceList) {

        List<TopologyDetailsResponse.Nodes> enrichedList = new ArrayList<>();
        try {
            //Timestamp date =  new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());
            //List<TopologyDetailsResponse.Nodes> completeNodeList = TopologyDetailsService.getNodeList(account, userId, 0,date.getTime());

            Map<String, TopologyDetailsResponse.Nodes> rcaPathMap = rcaPath
                    .stream()
                    .collect(Collectors.toMap(TopologyDetailsResponse.Nodes::getId, Function.identity()));

            nodesList.forEach(node -> {
                /*Optional<TopologyDetailsResponse.Nodes> nodeWithInformation = completeNodeList.stream()
                        .filter(it -> (it.getId().equals(node.getId())))
                        .findAny();*/
                Optional<TopologyDetailsResponse.Nodes> affectedNodeOptional = affectedNodes.stream()
                        .filter(it -> it.getId().equals(node.getId()))
                        .findAny();
                Optional<TopologyDetailsResponse.Nodes> entryPointService = entrypointServiceList.stream()
                        .filter(it -> it.getId().equals(node.getId()))
                        .findAny();

                //  if (nodeWithInformation.isPresent()) {
                //If the node is part of RCA path then set the appropriate flag
                if (rcaPathMap.get(node.getId()) != null) {
                    node.setRCAPathNode(true);
                }

                if (affectedNodeOptional.isPresent()) {
                    node.setServiceAffected(true);
                }

                if (entryPointService.isPresent()) {
                    node.setEntryPointNode(true);
                }

                node.setStartNode(node.isStartNode());
                enrichedList.add(node);
                // }
            });
        } catch (Exception e) {
            logger.error("Error occurred while enriching Node information", e);
            return new ArrayList<>();
        }
        return enrichedList;
    }

    public List<List<TopologyDetailsResponse.Nodes>> mergeRCAPath(List<List<TopologyDetailsResponse.Nodes>> rcaPaths) {

        if (COMMONALITY_LOGIC == 0) {
            logger.info("No collation logic has been applied to merge rca paths");
            return rcaPaths;
        }

        logger.trace(Constants.INVOKED_METHOD + "mergeRCAPath");
        List<List<TopologyDetailsResponse.Nodes>> result = new ArrayList<>();
        List<List<TopologyDetailsResponse.Nodes>> processedPaths = new ArrayList<>();
        for (List<TopologyDetailsResponse.Nodes> rcaPath : rcaPaths) {
            if (processedPaths.contains(rcaPath))
                continue;
            processedPaths.add(rcaPath);
            List<List<TopologyDetailsResponse.Nodes>> pathsToBeMerged = new ArrayList<>();
            pathsToBeMerged.add(rcaPath);

            logger.debug("Merging paths: ");
            for (List<TopologyDetailsResponse.Nodes> remainingRCAPath : rcaPaths) {
                if (processedPaths.contains(remainingRCAPath))
                    continue;
                if (checkMergeCriteria(rcaPath, remainingRCAPath)) {

                    remainingRCAPath.forEach(it -> logger.debug(it.toString()));

                    pathsToBeMerged.add(remainingRCAPath);
                    processedPaths.add(remainingRCAPath);
                }
            }
            result.add(mergeGeneratedRCAPathsWithCommonality(pathsToBeMerged));
        }

        return result;
    }


    private boolean checkMergeCriteria(List<TopologyDetailsResponse.Nodes> p1, List<TopologyDetailsResponse.Nodes> p2) {
        //Check if the RCA nodes i.e the KPI affected has to be common for 2 RCA paths to be merged
        if (!p1.get(p1.size() - 1).getId().equals(p2.get(p2.size() - 1).getId())) {
            return false;
        }
        if (COMMONALITY_LOGIC == 1) {
            return checkCommonalityFactor(p1, p2);
        } else if (COMMONALITY_LOGIC == 2) {
            return checkCommonTerminalNodes(p1, p2);
        } else {
            logger.error("Unknown logic used for merging RCA paths: {}", COMMONALITY_LOGIC);
            return false;
        }
    }

    private boolean checkCommonalityFactor(List<TopologyDetailsResponse.Nodes> p1, List<TopologyDetailsResponse.Nodes> p2) {
        if (p1.size() > 2 && p2.size() > 2) {
            logger.debug("Checking if the paths have enough commonality to be merged");
            logger.debug("Path1: {}", p1);
            logger.debug("Path2: {}", p2);
            int maxCounter = Math.min(p1.size() - 2, p2.size() - 2);
            int matched = 0;

            //loop is initialized from 1 since we have to ignore first and last node as they are source and destination
            //the RCA path and should not be checked for commonality.
            for (int i = 1; i < maxCounter; i++) {
                for (int j = 1; j < maxCounter; j++) {
                    if (p1.get(i).equals(p2.get(j))) {
                        matched++;
                    }
                    if (matched >= COMMONALITY_FACTOR) {
                        logger.debug("These path's satisfy the commonality factor , hence can be merged...");
                        return true;
                    }
                }
            }

            return false;
        }
        return false;
    }

    private boolean checkCommonTerminalNodes(List<TopologyDetailsResponse.Nodes> p1, List<TopologyDetailsResponse.Nodes> p2) {
        if (p1 != null && p2 != null && !p1.isEmpty() && !p2.isEmpty()) {
            // If the source and destination nodes are same then we merge the paths
            return p1.get(0).getId().equals(p2.get(0).getId()) && p1.get(p1.size() - 1).getId().equals(p2.get(p2.size() - 1).getId());
        } else {
            return false;
        }
    }

    private List<TopologyDetailsResponse.Nodes> mergeGeneratedRCAPathsWithCommonality(List<List<TopologyDetailsResponse.Nodes>> pathList) {
        List<TopologyDetailsResponse.Nodes> result = new ArrayList<>();
        Map<String, String> tracker = new HashMap<>();
        TopologyDetailsResponse.Nodes rcaNode = pathList.get(0).get(pathList.get(0).size() - 1);

        pathList.forEach(rcaPath -> rcaPath.forEach(node -> {
            if (tracker.get(node.getId()) == null) {
                result.add(node);
                tracker.put(node.getId(), node.getId());
            }
        }));

        //RCA nodes has to be always at the end, hence remove it from the merged path and put it at the end, this will
        // help us to add sequence number to the edges in the RCA path that is merged.
        result.remove(rcaNode);
        rcaNode.setStartNode(true);
        result.add(rcaNode);
        return result;
    }
}
