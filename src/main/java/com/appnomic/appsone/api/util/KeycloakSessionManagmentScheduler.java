package com.appnomic.appsone.api.util;


import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.service.KeyCloakAuthService;
import com.google.common.util.concurrent.AbstractScheduledService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> : 07/05/2019
 */
public class KeycloakSessionManagmentScheduler extends AbstractScheduledService {
    private static final Logger logger = LoggerFactory.getLogger(KeycloakSessionManagmentScheduler.class);
    private static final String refreshInterval = ConfProperties.getString(Constants.KEYCLOAK_CONNECTION_REFRESH,
            Constants.KEYCLOAK_CONNECTION_REFRESH_DEFAULT);
    @Override
    protected void runOneIteration() throws Exception {
        logger.debug("Refreshing keycloak connection");
        try {
            KeyCloakAuthService.init();
        }   catch (Exception e) {
            logger.error("Error occurred while refreshing keycloak connection",e);
        }
    }

    @Override
    protected Scheduler scheduler() {
        try {
            long schedulerPeriodInMinutes = Integer.valueOf(refreshInterval);
            logger.info("Connection refresh interval for keycloak is : "+schedulerPeriodInMinutes+" minutes");
            return Scheduler.newFixedRateSchedule(0L, schedulerPeriodInMinutes, TimeUnit.MINUTES);
        }   catch (Exception e) {
            logger.error("Error while creating scheduler for keycloak connection refresh",e);
        }
        return null;
    }
}
