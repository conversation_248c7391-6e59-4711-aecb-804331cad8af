package com.appnomic.appsone.api.util;

import org.bouncycastle.jce.ECNamedCurveTable;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.jce.spec.ECParameterSpec;
import org.bouncycastle.util.encoders.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.security.*;

/**
 * <AUTHOR> : 7/2/19
 */
public class KeyGenerator {
    private static final Logger logger = LoggerFactory.getLogger(KeyGenerator.class);

    private static final String publicKeyFileExtension=".public";
    private static final String privateKeyFileExtension=".private";

    private static KeyPair generateKeys() throws NoSuchAlgorithmException, NoSuchProviderException, InvalidAlgorithmParameterException {
        Security.addProvider(new BouncyCastleProvider());
        ECParameterSpec ecSpec = ECNamedCurveTable.getParameterSpec("B-571");
        KeyPairGenerator g = KeyPairGenerator.getInstance("ECDSA", "BC");
        g.initialize(ecSpec, new SecureRandom());
        return g.generateKeyPair();
    }

    public static String getPublicKey(String accountName) {
        try {
            KeyPair pair = generateKeys();
            byte[] bytes = Base64.encode(pair.getPublic().getEncoded());
            File publicFile = new File("./keys/"+ accountName + publicKeyFileExtension);
            boolean isFileAvailable = true;
            if (!publicFile.exists()) {
                if (!publicFile.isDirectory()) isFileAvailable = publicFile.getParentFile().mkdir();
                if (isFileAvailable) isFileAvailable = publicFile.createNewFile();
            }
            if (isFileAvailable) Files.write(publicFile.toPath(), bytes);
            return new String(bytes);
        } catch (NoSuchAlgorithmException e) {
            logger.error("Error while generating public key" + e.getMessage(),e);
        } catch (NoSuchProviderException e) {
            logger.error("Error while generating public key" + e.getMessage(),e);
        } catch (InvalidAlgorithmParameterException e) {
            logger.error("Error while generating public key" + e.getMessage(),e);
        } catch (IOException e) {
            logger.error("Error while saving public key" + e.getMessage(),e);
        }
        return null;
    }

    public static String getPrivateKey(String accountName) {
        try {
            KeyPair pair = generateKeys();
            byte[] bytes = Base64.encode(pair.getPrivate().getEncoded());
            File privateFile = new File("./keys/"+ accountName + privateKeyFileExtension);
            boolean isFileAvailable = true;
            if (!privateFile.exists()) {
                if (!privateFile.isDirectory()) isFileAvailable = privateFile.getParentFile().mkdirs();
                if (isFileAvailable) isFileAvailable = privateFile.createNewFile();
            }
            if (isFileAvailable) Files.write(privateFile.toPath(), bytes);
            return new String(bytes);
        } catch (NoSuchAlgorithmException e) {
            logger.error("Error while generating private key" + e.getMessage(),e);
        } catch (NoSuchProviderException e) {
            logger.error("Error while generating private key" + e.getMessage(),e);
        } catch (InvalidAlgorithmParameterException e) {
            logger.error("Error while generating private key" + e.getMessage(),e);
        } catch (IOException e) {
            logger.error("Error while saving private key" + e.getMessage(),e);
        }
        return null;
    }
}
