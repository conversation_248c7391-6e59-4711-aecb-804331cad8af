package com.appnomic.appsone.api.util;

import com.appnomic.appsone.api.common.Constants;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.Map;
import java.util.Properties;

public class ConfProperties {

    private static final Logger log = LoggerFactory.getLogger(ConfProperties.class);


    final static Properties properties = new Properties();
    static Map<String, Object> keycloakSSOConfigurations = null;
    private static volatile Map<String, String> headerConfigurations = null;

    static {
        load();
        loadKeycloakSSOConfig();
        loadHeaderConfiguration();
    }

    static void load() {
        try {
            properties.load(ConfProperties.class.getClassLoader().getResourceAsStream(Constants.CONF_PROPERTIES_FILE_NAME));

            if (properties.isEmpty()) {
                log.info("{} file doesn't exist in class path", Constants.CONF_PROPERTIES_FILE_NAME);
            } else {
                log.info("{} file loaded successfully", Constants.CONF_PROPERTIES_FILE_NAME);
            }
        } catch (IOException e) {
            log.info("Reading/Loading {} failed {}", Constants.CONF_PROPERTIES_FILE_NAME, e);
        }
    }

    static void loadKeycloakSSOConfig() {
        try {
            InputStream keycloakConfStream = ConfProperties.class.getClassLoader().getResourceAsStream(Constants.KEYCLOAK_SSO_CONF_FILE_NAME);
            if (keycloakSSOConfigurations == null)
                keycloakSSOConfigurations = new ObjectMapper().readValue(keycloakConfStream, new TypeReference<Map<String, Object>>() {
                });
        } catch (Exception e) {
            log.info("Reading/Loading {} failed {}", Constants.KEYCLOAK_SSO_CONF_FILE_NAME, e);
        }
    }

    private static void loadHeaderConfiguration() {
        try {
            InputStream keycloakConfStream = ConfProperties.class.getClassLoader().getResourceAsStream(Constants.HEADER_PROPERTIES_FILE_NAME);
            if (headerConfigurations == null)
                headerConfigurations = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(keycloakConfStream, new TypeReference<Map<String, String>>() {
                });

            log.info("headerConfigurations: {}", headerConfigurations);
        } catch (Exception e) {
            log.info("Reading/Loading {} failed {}", Constants.HEADER_PROPERTIES_FILE_NAME, e);
        }
    }

    public static Map<String, Object> getKeycloakSSOConfigurations() {
        return keycloakSSOConfigurations;
    }

    public static Map<String, String> getHeaderConfigurations() {
        return headerConfigurations;
    }

    public static String getString(String key) {
        return properties.getProperty(key);
    }

    public static String getString(String key, String defaultValue) {
        String value = getString(key);
        if ((value == null) || (value.trim().isEmpty())) {
            value = defaultValue;
        }
        return value;

    }

    public static Integer getInt(String key) {
        String value = properties.getProperty(key);
        if (value == null || value.trim().isEmpty())
            return null;
        return Integer.parseInt(properties.getProperty(key));
    }

    public static Integer getInt(String key, String defaultValue) {
        Integer value = getInt(key);
        if (value == null) {
            value = Integer.parseInt(defaultValue);
        }
        return value;
    }

    public static boolean getBoolean(String key, boolean defaultVal) {
        String value = getString(key);
        if (value == null || value.isEmpty()) {
            return defaultVal;
        }
        if (value.equalsIgnoreCase("true")) {
            return true;
        } else if (value.equalsIgnoreCase("false")) {
            return false;
        } else return defaultVal;
    }

}
