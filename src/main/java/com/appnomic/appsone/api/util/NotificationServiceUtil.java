package com.appnomic.appsone.api.util;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.pojo.notification.EmailDetails;
import com.appnomic.appsone.api.pojo.notification.Notification;
import com.appnomic.appsone.api.pojo.notification.NotificationServiceDetails;
import com.appnomic.appsone.api.pojo.notification.SMSDetails;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import groovy.json.JsonOutput;
import groovy.lang.Writable;
import groovy.text.SimpleTemplateEngine;
import groovy.text.Template;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> : 24/5/19
 */
public class NotificationServiceUtil {
    private static final Logger logger = LoggerFactory.getLogger(NotificationServiceUtil.class);
    private static ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();
    private static Map<String, NotificationServiceDetails> notificationServiceDetailsMap;
    private static Map<String, Double> thresholdMap;
    private static Map<String, Template> smsTemplateMap;
    private static Map<String, Template> emailTemplateMap;
    private static SimpleTemplateEngine engine;
    private static String nsUrl;
    public static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");

    static {
        notificationServiceDetailsMap = new HashMap<>();
        engine = new SimpleTemplateEngine();
        smsTemplateMap = new HashMap<>();
        emailTemplateMap = new HashMap<>();
        thresholdMap = new HashMap<>();
        nsUrl = ConfProperties.getString(Constants.NOTIFICATION_SERVICE_URL, Constants.NOTIFICATION_SERVICE_URL_DEF);
        loadNotificationConfig();
    }

    private static void loadNotificationConfig() {
        try {
            InputStream notificationConfStream = ConfProperties.class.getClassLoader().getResourceAsStream("notification_config.json");
            List<Notification> notificationConfigList = objectMapper.readValue(notificationConfStream, new TypeReference<List<Notification>>() {
            });
            for (Notification notification : notificationConfigList) {
                NotificationServiceDetails notificationServiceDetails = new NotificationServiceDetails();

                String type = notification.getType();
                notificationServiceDetails.setName(type);
                notificationServiceDetails.setEmailEnabled(String.valueOf(notification.getEmailEnabled()));
                notificationServiceDetails.setSmsEnabled(String.valueOf(notification.getSmsEnabled()));

                EmailDetails emailDetails = new EmailDetails();
                emailDetails.setToAddress(notification.getEmailToAddress());
                emailDetails.setCcAddress(notification.getEmailCcAddress());
                emailDetails.setBccAddress(notification.getEmailBccAddress());
                emailDetails.setSubject(notification.getSubject());
                emailDetails.setBody(notification.getBody());

                SMSDetails smsDetails = new SMSDetails();
                smsDetails.setMobileNumbers(String.valueOf(notification.getMobileNumbers()));
                smsDetails.setMessage(notification.getMessage());

                notificationServiceDetails.setEmail(emailDetails);
                notificationServiceDetails.setSms(smsDetails);
                notificationServiceDetails.setAlertProfileId(String.valueOf(notification.getAlertProfileId()));
                notificationServiceDetailsMap.put(type, notificationServiceDetails);
                double threshold = notification.getThreshold();
                if (threshold == 0) threshold = 50;
                thresholdMap.put(type, threshold);
            }
        } catch (IOException e) {
            logger.error("Error while loading notifiaction service config-"+e.getMessage(), e);
        }
    }

    public static String notificationMessageBuilder(int accountId, String type, Map<String, String> placeHolders) throws IOException, ClassNotFoundException {
        if (notificationServiceDetailsMap == null || notificationServiceDetailsMap.isEmpty()) return null;
        NotificationServiceDetails notificationServiceDetails = notificationServiceDetailsMap.get(type);
        if (notificationServiceDetails == null) {
            logger.error("Notifiaction configuration is not found for given type-" + type);
            return null;
        }
        EmailDetails email = notificationServiceDetails.getEmail();
        SMSDetails sms = notificationServiceDetails.getSms();
        Template emailTemplate = emailTemplateMap.get(type);
        if (emailTemplate == null) {
            String emailM = email.getBody();
            emailTemplate = engine.createTemplate(emailM);
            emailTemplateMap.put(type, emailTemplate);
        }
        Writable emailT = emailTemplate.make(placeHolders);
        email.setBody(emailT.toString());
        Template smsTemplate = smsTemplateMap.get(type);
        if (smsTemplate == null) {
            String smsM = sms.getMessage();
            smsTemplate = engine.createTemplate(smsM);
            smsTemplateMap.put(type, smsTemplate);
        }
        Writable smsT = smsTemplate.make(placeHolders);
        sms.setMessage(smsT.toString());
        notificationServiceDetails.setAccountId(String.valueOf(accountId));
        return outputJsonSting(notificationServiceDetails);
    }

    public static double getThreshold(String type) {
        if (!thresholdMap.containsKey(type)) {
            logger.warn("Threshold is not configured for given type-" + type + " that is why we are taking default threshold value as - 50");
            return 50;
        }
        return thresholdMap.get(type);
    }

    private static String outputJsonSting(NotificationServiceDetails notificationServiceDetails) {
        StringBuilder output = new StringBuilder();
        output.append("[");
        output.append(JsonOutput.toJson(notificationServiceDetails));
        output.append("]");
        return output.toString();
    }

    public static int sendNotification(String message, OkHttpClient client) throws IOException {
        RequestBody body = RequestBody.create(JSON, message);
        Request request = new Request.Builder().url(nsUrl).post(body).build();
        Response response = client.newCall(request).execute();
        logger.debug("Response code:{}, body:{}", response.code(), (response.body() != null) ? response.body().toString() : "Empty response body.");
        return response.code();
    }
}
