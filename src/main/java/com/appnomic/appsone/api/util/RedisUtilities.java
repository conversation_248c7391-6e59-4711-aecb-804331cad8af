package com.appnomic.appsone.api.util;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.manager.RedisConnectionManager;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.lettuce.core.api.sync.RedisHashCommands;
import lombok.extern.slf4j.Slf4j;

import java.util.stream.Stream;

@Slf4j
public class RedisUtilities {

    private static final ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();

    public static void updateKey(String key, String hashKey, Object data) {
        try {
            if (data == null || objectMapper.writeValueAsString(data).trim().equalsIgnoreCase("")) {
                log.error("Value received to update Redis Key:{}, Hash key:{} is null/empty.", key, hashKey);
                return;
            }

            log.trace("Redis UPDATE Query details:- Key:{}, Hash key:{}, Value: {}", key, hashKey, data);

            String cacheData = getKey(key, hashKey);
            if (cacheData != null && cacheData.hashCode() == objectMapper.writeValueAsString(data).hashCode()) {
                log.debug("No change in value for the Redis Key:{}, Hash key:{}, existing value:{}, new value:{}",
                        key, hashKey, cacheData, data);
                return;
            }

            RedisHashCommands<String, String> redisClient = RedisConnectionManager.INSTANCE.getRedisClient();
            if (redisClient == null) {
                log.debug("Could not connect to Redis cluster.");
                return;
            }

            Boolean status = redisClient.hset(key, hashKey, objectMapper.writeValueAsString(data));
            if (status == null || status.equals(Boolean.FALSE)) {
                log.error("Failed to update in Redis:- Key:{}, Hash key:{} with Value:{}", key, hashKey, data);
            }
        } catch (Exception ex) {
            log.error("Exception encountered while updating Redis Key:{}, Hash key:{} with Value:{}. ", key, hashKey, data, ex);
            HealUICache.INSTANCE.updateHealUIErrors(1);
        }
    }

    public static String getKey(String key, String hashKey) {
        try {
            log.trace("Redis GET Query details:- Key:{}, Hash key:{}", key, hashKey);

            RedisHashCommands<String, String> redisClient = RedisConnectionManager.INSTANCE.getRedisClient();
            if (redisClient == null) {
                log.debug("Could not connect to Redis cluster.");
                return null;
            }

            String data = redisClient.hget(key, hashKey);
            if (data == null || data.isEmpty()) {
                log.debug("Failed to get details for Redis Key:{}, Hash key:{}", key, hashKey);
                return null;
            } else log.trace("Redis GET Query successful. Key:{}, Hash key:{}, Value: {}", key, hashKey, data);
            return data;
        } catch (Exception ex) {
            log.error("Exception encountered while getting value of Redis Key:{}, Hash key:{}. ", key, hashKey, ex);
            HealUICache.INSTANCE.updateHealUIErrors(1);
            return null;
        } finally {
            Stream.of(Thread.currentThread().getStackTrace())
                    .filter(c -> c.getClassName().contains("com.appnomic.appsone.api.businesslogic.") ||
                            c.getClassName().contains("com.appnomic.appsone.api.service."))
                    .findFirst()
                    .ifPresent(stackTraceElement -> HealUICache.INSTANCE.updateClassRedisKeyCountMap(stackTraceElement.getClassName()));
        }
    }
}
