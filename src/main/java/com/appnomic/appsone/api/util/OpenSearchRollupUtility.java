package com.appnomic.appsone.api.util;


import com.appnomic.appsone.opeasearchquery.enumerations.Aggregations;
/**
 * <AUTHOR> : 22/09/2022
 */
public class OpenSearchRollupUtility {

    public static String getValueFieldName(String operation) {
        String fieldName = "value";
        if (operation.equalsIgnoreCase(Aggregations.SUM.name())) {
            fieldName = "sumValue";
        } else if (operation.equalsIgnoreCase(Aggregations.AVERAGE.name())) {
            fieldName = "averageValue";
        } else if (operation.equalsIgnoreCase(Aggregations.MIN.name())) {
            fieldName = "minValue";
        } else if (operation.equalsIgnoreCase(Aggregations.MAX.name())) {
            fieldName = "maxValue";
        } else if (operation.equalsIgnoreCase(Aggregations.LAST.name())) {
            fieldName = "lastValue";
        }
        return fieldName;
    }
}
