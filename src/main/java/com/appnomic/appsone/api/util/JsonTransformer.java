package com.appnomic.appsone.api.util;

import spark.ResponseTransformer;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * Implementation of Response Transformer
 *
 * <AUTHOR> on 23-Feb-16.
 */
public class JsonTransformer implements ResponseTransformer {

    // Create new object mapper
    private ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Override render method with Jackson value as string
     *
     * @param model Object passed
     * @return Generated JSON String
     * @throws Exception Exception occurred
     */
    @Override
    public String render(Object model) throws Exception {

        // Write value as string for an object
        String response = objectMapper.writeValueAsString(model);
//        System.out.println(response);
        return response;

    }
}
