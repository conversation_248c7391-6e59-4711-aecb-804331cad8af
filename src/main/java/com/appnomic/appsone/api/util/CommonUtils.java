package com.appnomic.appsone.api.util;

import com.appnomic.appsone.api.beans.OperationTypeEnum;
import com.appnomic.appsone.api.beans.UserAttributeBean;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.mysql.entity.UserDetailsBean;
import com.appnomic.appsone.api.dao.redis.ForensicRepo;
import com.appnomic.appsone.api.dao.redis.MasterRepo;
import com.appnomic.appsone.api.health.ApplicationInfo;
import com.appnomic.appsone.api.pojo.*;
import com.appnomic.appsone.api.service.KeyCloakAuthService;
import com.appnomic.appsone.api.service.mysql.TimezoneDataService;
import com.appnomic.appsone.api.service.mysql.UserAccessDataService;
import com.appnomic.appsone.model.JWTData;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.Tags;
import com.heal.configuration.pojos.Action;
import com.heal.configuration.pojos.Service;
import org.apache.commons.lang3.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import javax.management.MBeanServer;
import javax.management.ObjectName;
import java.io.IOException;
import java.lang.management.ManagementFactory;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

public class CommonUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(CommonUtils.class);
    private static final String PRECISION_VALUE = ConfProperties.getString(Constants.KPI_VALUE_PRECISION,
            Constants.KPI_VALUE_PRECISION_DEFAULT);
    private static final String ROLLUP_LEVELS = ConfProperties.getString(Constants.SUPPORTED_ROLLUP_LEVELS,
            Constants.SUPPORTED_ROLLUP_LEVELS_DEFAULT);

    private static final Integer enableEncoder = ConfProperties.getInt(Constants.ENCODE_ENABLED, String.valueOf(Constants.ENCODE_ENABLED_DEFAULT));
    private static final Gson GSON = new GsonBuilder().create();
    private static boolean isUnitTest = false;

    private CommonUtils() {
    }

    private static class SingletonHelper {
        private static final CommonUtils INSTANCE = new CommonUtils();
    }

    public static CommonUtils getInstance() {
        return SingletonHelper.INSTANCE;
    }

    public static <T> T jsonToObject(String jsonStr, Type clazz) {
        return GSON.fromJson(jsonStr, clazz);
    }

    public static String getUserId(Request request) throws Exception {
        String authKey = request.headers("Authorization");
        JWTData jwtData = KeyCloakAuthService.extractUserDetails(authKey);
        return jwtData.getSub();
    }

    public static <T> GenericResponse<T> getGenericResponse(Response response, String status, int statusCode, String message, Throwable throwable, boolean isError) {
        GenericResponse<T> responseObject = new GenericResponse<>();
        responseObject.setResponseStatus(status);
        responseObject.setMessage(message);
        response.status(statusCode);
        if (!isError) {
            LOGGER.info(message);
            return responseObject;
        }
        if (throwable == null) LOGGER.error(message);
        else LOGGER.error(message, throwable);
        return responseObject;
    }

    public static String getDecryptedData(String encryptedData) {
        //TODO: BouncyCastle encryption algorithm has to be used in further releases.
        return new String(Base64.getDecoder().decode(encryptedData));
    }

    public static String changePrecision(String value, int precision) {
        String convertedValue;
        try {
            if (value == null || value.trim().isEmpty() || !isNumeric(value)) {
                return value;
            }
            //If we want to override the values on per call basis from conf file
            String precisionFormat = null;
            if (PRECISION_VALUE.equalsIgnoreCase("0"))
                precisionFormat = "%." + precision + "f";
            else
                precisionFormat = "%." + PRECISION_VALUE + "f";
            convertedValue = String.format(precisionFormat, Float.parseFloat(value));
            return convertedValue;
        } catch (Exception e) {
            LOGGER.warn("changePrecision() - value: {}, precision: {}", value, precision);
            return value;
        }
    }

    public static boolean isNumeric(String str) {
        if (str.matches("((-|\\+)?[0-9]+(\\.[0-9]+)?)+")) {
            return true;
        } else {
            return false;
        }
    }

    public static double round(double number, int decimalPlace) {
        if (Double.isInfinite(number) || Double.isNaN(number)) return 0;
        BigDecimal bd = BigDecimal.valueOf(number);
        bd = bd.setScale(decimalPlace, BigDecimal.ROUND_HALF_UP);
        return bd.doubleValue();
    }

    /**
     * This method will accept a list of numeric values converted to String and an aggregation operation it will perform
     * required action and return the result as a String
     */
    public static String aggregator(List<String> values, AggregationOpertion aggregationOperation) {
        String result = "";
        if (values != null && !values.isEmpty()) {
            int inputSize = values.size();
            switch (aggregationOperation.getOperationId()) {
                case 1:
                    result = Double.toString(values.stream().map(Double::valueOf).mapToDouble(it -> it).sum());
                    break;
                case 2:
                    result = Double.toString(values.stream().map(Double::valueOf).mapToDouble(it -> it).sum() / inputSize);
                    break;
                case 3:
                    OptionalDouble maxValue = values.stream().map(Double::valueOf).mapToDouble(it -> it).max();
                    result = Double.toString(maxValue.getAsDouble());
                    break;
                case 4:
                    OptionalDouble minValue = values.stream().map(Double::valueOf).mapToDouble(it -> it).min();
                    result = Double.toString(minValue.getAsDouble());
                    break;

                default:
                    LOGGER.error("Unknown aggregation action provided: " + aggregationOperation.getOperationId());
            }
        }
        return result;
    }

    /**
     * This methods takes a list of violated timestamp sorted in ascending order and will match violation to right
     * bucket , to find out if any violation occurred for the timestamp.
     */
    public static List<Integer> findViolations(List<KpiAnomalyData> violations, List<Long> times, long toTime) {
        LOGGER.trace(Constants.INVOKED_METHOD + "findViolations");
        List<Integer> result = new ArrayList<>();
        boolean addedUpperBoundDataPoint = false;
        try {
            if (violations != null && !violations.isEmpty() && times != null && !times.isEmpty()) {

                //adding toTime to the list so that the last time interval can be bounded
                if (!times.get(times.size() - 1).equals(toTime)) {
                    times.add(toTime);
                    addedUpperBoundDataPoint = true;
                }

                for (int i = 0; i < times.size() - 1; i++) {
                    long from = times.get(i);
                    long to = times.get(i + 1);

                    if (violations.stream().anyMatch(it -> (it.getTime() >= from && it.getTime() < to))) {
                        result.add(1);
                    } else {
                        result.add(0);
                    }
                }
            }
            // Removing the to time which was added for providing upper bound
            if (addedUpperBoundDataPoint) {
                times.remove(toTime);
            }

        } catch (Exception e) {
            LOGGER.error("Error occurred while processing violations. ", e);
        }
        return result;
    }

    public static boolean forensicExistsForCategory(String accountIdentifier, String categoryIdentifier) {
        LOGGER.trace(Constants.INVOKED_METHOD + "forensicExistsForCategory, accountIdentifier: {}, categoryId: {}",
                accountIdentifier, categoryIdentifier);
        List<Action> forensics = new ForensicRepo().getAllForensicActions(accountIdentifier, categoryIdentifier);
        return (null != forensics && !forensics.isEmpty());
    }

    public static <T> void populateErrorResponse(GenericResponse<T> responseObject, Response response,
                                                 final String errorMessage, int statusCode) {
        responseObject.setResponseStatus(StatusResponse.FAILURE.name());
        responseObject.setMessage(errorMessage);
        response.status(statusCode);
        LOGGER.error(errorMessage);
    }

    public static Map<String, Double> getThresholdsForOperationLevel(String operationType, Map<String, Double> thresholds, String upperKey, String lowerKey) {
        if (Objects.isNull(operationType)) {
            LOGGER.error("Invalid operation type: {}, operation can not be null.", operationType);
            return null;
        }
        try {
            Double upper = thresholds.get(upperKey);
            Double lower = thresholds.get(lowerKey);
            if (upper != 0.0 && lower != 0.0) {
                thresholds.put(lowerKey, Math.min(upper, lower));
                thresholds.put(upperKey, Math.max(upper, lower));
            }
            OperationTypeEnum opEnum = OperationTypeEnum.findByType(operationType);
            if (opEnum == null) return null;
            switch (opEnum) {
                case GREATER:
                    Double min = thresholds.get(lowerKey);
                    thresholds.put(lowerKey, 0.0);
                    thresholds.put(upperKey, min);
                    return thresholds;
                case LESS_THAN:
                    thresholds.put(upperKey, -1.0);
                    return thresholds;
                case IN_BETWEEN:
                case NOT_BETWEEN:
                case NOT_EQUALS:
                    return thresholds;
                default:
                    return null;
            }
        } catch (IllegalArgumentException e) {
            LOGGER.error("Invalid operation type: {}", operationType);
        }
        return null;
    }

    public static int[] getAggregationLevels() {
        return Arrays.stream(ROLLUP_LEVELS.split(",")).mapToInt(it -> Integer.parseInt(it.trim())).toArray();
    }

    //return all the instances including host
    public static List<CompInstClusterDetails> getInstancesOfService(AllAccountDetails allAccountDetails, int serviceId, boolean includeHost, boolean includeCluster) {

        //get all tags for instances
        int controllerTagId = new MasterRepo().getTagDetails().stream()
                .filter(t -> t.getName().equalsIgnoreCase(Constants.CONTROLLER_TAG))
                .findAny()
                .orElse(null).getId();
        Map<Integer, TagMappingDetails> serviceInstTags = allAccountDetails
                .getTagMappingDetailsList()
                .stream()
                .filter(it -> it.getTagId() == controllerTagId)
                .filter(it -> (it.getObjectRefTable().equals(Constants.COMP_INSTANCE_TABLE)))
                .filter(it -> (it.getTagKey()).equals(Integer.toString(serviceId)))
                .collect(Collectors.toMap(TagMappingDetails::getObjectId, it -> it));

        //get instance details for all the tags collected
        List<CompInstClusterDetails> serviceClusterInsts = allAccountDetails
                .getCompInstanceDetailsList()
                .stream()
                .filter(it -> it.getIsCluster() == 1)
                .filter(it -> it.getStatus() == 1)
                .filter(it -> (serviceInstTags.containsKey(it.getInstanceId())))
                .collect(Collectors.toList());

        if (!includeHost) {
            Predicate<CompInstClusterDetails> hostInstances = inst -> inst.getComponentTypeName().equalsIgnoreCase(Constants.HOST);
            serviceClusterInsts = serviceClusterInsts.stream().filter(hostInstances.negate()).collect(Collectors.toList());
        }

        return getInstancesByCluster(allAccountDetails, serviceClusterInsts, includeCluster);

    }

    public static List<CompInstClusterDetails> getInstancesByCluster(AllAccountDetails allAccountDetails,
                                                                     List<CompInstClusterDetails> clusterDetails, boolean includeCluster) {
        List<CompInstClusterDetails> instances = new ArrayList<>();
        Map<Integer, Set<Integer>> clusterInstsMapping = allAccountDetails.getClusterInstanceMappingList()
                .stream()
                .collect(Collectors.groupingBy(ClusterInstanceMapping::getClusterId,
                        Collectors.mapping(ClusterInstanceMapping::getCompInstanceId, Collectors.toSet())));
        Set<Integer> instIds = new HashSet<>();
        clusterDetails.forEach(cluster -> {
            instIds.addAll(clusterInstsMapping.get(cluster.getInstanceId()));
            if (includeCluster)
                instIds.add(cluster.getInstanceId());
        });

        instances = allAccountDetails.getCompInstanceDetailsList()
                .stream()
                .filter(it -> it.getStatus() == 1)
                .filter(it -> instIds.contains(it.getInstanceId()))
                .collect(Collectors.toList());
        return instances;
    }

    public static boolean isKubernetesService(Service service) {
        if (service == null || service.getTags() == null || service.getTags().isEmpty()) {
            return false;
        }
        Tags kubernetesTag = service.getTags()
                .stream()
                .filter(t -> t.getType().equalsIgnoreCase(Constants.SERVICE_TYPE_TAG) &&
                        t.getKey().equalsIgnoreCase(Constants.SERVICE_TYPE_DEFAULT) &&
                        t.getValue().equalsIgnoreCase(Constants.KUBERNETES)).
                findAny()
                .orElse(null);
        return kubernetesTag != null;
    }

    public static ObjectMapper getObjectMapperWithHtmlEncoder() {
        ObjectMapper objectMapper = new ObjectMapper();

        if (enableEncoder == 1) {
            SimpleModule simpleModule = new SimpleModule("HTML-Encoder", objectMapper.version()).addDeserializer(String.class, new EscapeHTML());
            objectMapper.registerModule(simpleModule);
        }

        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return objectMapper;
    }

    public static ObjectMapper getObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();

        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        return objectMapper;
    }

    public static long getTimezoneMilli(Account account) {
        long timezone = 0;
        List<Tags> tags = account.getTags();
        if (tags != null) {
            Tags timezoneTag = tags.parallelStream()
                    .filter(t -> t.getType().equalsIgnoreCase("Timezone"))
                    .findAny().orElse(null);
            if (timezoneTag != null) {
                timezone = Long.parseLong(timezoneTag.getValue());
            }
        }
        return timezone;
    }

    public static UserDetailsBean getUserDetails(String authToken) throws ServerException {
        String userId;
        if (authToken == null)
            throw new ServerException("Auth token can't be null.");

        try {
            JWTData jwtData = KeyCloakAuthService.extractUserDetails(authToken);
            userId = jwtData.getSub();
        } catch (Exception e) {
            throw new ServerException("Error occurred while extracting user details.");
        }

        Optional<UserDetailsBean> userDetail = UserAccessDataService.getUsers().parallelStream()
                .filter(u -> u.getUserIdentifier().equals(userId))
                .findAny();

        if (!userDetail.isPresent()) {
            LOGGER.error("User details is unavailable for user id [{}]", userId);
            throw new ServerException("User is not available.");
        }
        return userDetail.get();
    }

    public static boolean verifyUserStatus(Request request) {
        try {
            TimezoneDataService timezoneDataService = new TimezoneDataService();
            String userId = CommonUtils.getUserId(request);
            UserAttributeBean userAttributesBean = timezoneDataService.getUserAttributesForUserIdentifier(userId);
            if (userAttributesBean == null || userAttributesBean.getStatus() == 0) {
                LOGGER.debug("User is inactive or not present");
                return false;
            } else {
                timezoneDataService.updateUserLastLoginTime(userId, DateTimeUtil.getTimeInGMT(System.currentTimeMillis()), null);
            }
        } catch (Exception e) {
            LOGGER.error("Exception encountered while fetching userId from request header. Reason: {}", e.getMessage(), e);
            return false;
        }
        return true;
    }

    public static void registerMBean(String beanName, String objName, ApplicationInfo applicationInfo) {
        try {
            ObjectName objectName = new ObjectName(objName + ":name=" + beanName);
            MBeanServer server = ManagementFactory.getPlatformMBeanServer();
            server.registerMBean(applicationInfo, objectName);
        } catch (Exception e) {
            LOGGER.error("Error occurred while registering the bean, Bean:{}, Object:{}", beanName, objName);
        }
    }

}

class EscapeHTML extends JsonDeserializer<String> {

    @Override
    public String deserialize(JsonParser jp, DeserializationContext ctxt)
            throws IOException {
        String s = jp.getValueAsString();
        return StringEscapeUtils.escapeHtml4(s);
    }
}