package com.appnomic.appsone.api.util;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.dao.redis.MasterRepo;
import com.appnomic.appsone.api.pojo.AggregationLevel;
import com.heal.configuration.pojos.InstallationAttributes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> : 17/1/19
 */
public class DateTimeUtil {
    private static final Logger log = LoggerFactory.getLogger(DateTimeUtil.class);
    private static final int cassandraDataOffset = Integer.parseInt(ConfProperties.getString(Constants.CASSANDRA_AGGREGATION_OFFSET,
            Constants.CASSANDRA_AGGREGATION_OFFSET_DEFAULT));
    private static String timezoneOffSet = ConfProperties.getString(Constants.TIMEZONE_OFFSET, Constants.TIMEZONE_OFFSET_DEFAULT);
    private static DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private static final int checkRawTxn = ConfProperties.getInt(Constants.CHECK_RAW_TRANSACTION, Constants.CHECK_RAW_TRANSACTION_DEFAULT);

    /**
     * Private constructor to prevent Instance creation
     */
    private DateTimeUtil() {
    }

    public static long getEpochTime(String timeString) throws ParseException {

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-d HH:mm:ss");
        format.setTimeZone(TimeZone.getTimeZone("GMT"));
        return format.parse(timeString).getTime();

    }

    public static long getTimeInMilis(int timeInMinutes) {
        return TimeUnit.MINUTES.toMillis(timeInMinutes);
    }

    public static String getTime(String timeInMilis) {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return df.format(StringUtils.getLong(timeInMilis));
    }

    public static String getTimeInGMT(long time) {

        DateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        return simpleDateFormat.format(time);
    }

    public static Date getDateInGMT(long time) throws ParseException {
        DateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        String dateTime = simpleDateFormat.format(time);
        return simpleDateFormat.parse(dateTime);
    }

    public static String getTimeInRequiredTimezone(long time, String timeZone) {

        DateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone(timeZone));
        return simpleDateFormat.format(time);
    }

    public static Timestamp getTimestampInGMT(String time) throws ParseException {

        DateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        Date date = simpleDateFormat.parse(time.trim());
        return new Timestamp(date.getTime());
    }

    public static boolean inRange(long startTime) {

        List<InstallationAttributes> installationAttributesList = new MasterRepo().getInstallationAttributes();
        Integer redisNumberOfHours = installationAttributesList.parallelStream()
                .filter(c -> c.getName().equalsIgnoreCase(Constants.CHECK_RAW_TRANSACTION))
                .map(c -> Integer.parseInt(c.getValue())).findFirst().orElse(null);
        log.trace("redisNumberOfHours:{}", redisNumberOfHours);
        if ((redisNumberOfHours != null && redisNumberOfHours <= 0) || checkRawTxn <= 0) {
            return false;
        }

        int noOfHours = redisNumberOfHours == null ? checkRawTxn : redisNumberOfHours;
        log.trace("checkRawTxn:{}", checkRawTxn);
        log.trace("noOfHours:{}", noOfHours);

        Calendar now = Calendar.getInstance();
        //get current TimeZone using getTimeZone method of Calendar class
        TimeZone timeZone = now.getTimeZone();
        log.trace("Server TimeZone details: {}", timeZone);
        Timestamp fromTimeStamp = new Timestamp(startTime);

        long currentTime = System.currentTimeMillis();
        long rangeStartTime = currentTime - (noOfHours * Constants.HOUR);

        Timestamp rangeStartTimeStamp = new Timestamp(rangeStartTime);
        Timestamp rangeEndTimeStamp = new Timestamp(currentTime);

        TimeZone.setDefault(TimeZone.getTimeZone(timeZone.getID()));

        log.trace("fromTimeStamp:{}", fromTimeStamp);
        log.trace("rangeStartTimeStamp:{}", rangeStartTimeStamp);
        log.trace("rangeEndTimeStamp:{}", rangeEndTimeStamp);

        boolean inRange = (fromTimeStamp.after(rangeStartTimeStamp) || fromTimeStamp.equals(rangeStartTimeStamp)) && fromTimeStamp.before(rangeEndTimeStamp);
        log.trace("inRange:{}", inRange);
        return inRange;
    }

    public static Timestamp getTimeStampWithOffset(String time, Long offset) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:00+0000");
        LocalDateTime localDateTime = LocalDateTime.parse(time, dateTimeFormatter);
        localDateTime = localDateTime.minus(offset, ChronoUnit.MILLIS);
        return Timestamp.valueOf(localDateTime);
    }

    public static Timestamp getTimeStampWithoutOffset(Long time) {
        return new Timestamp(time);
    }

    public static Long getGMTToEpochTime(String time) {
        DateFormat simpleDateFormat;
        try {
            simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
            Date date = simpleDateFormat.parse(time.trim());
            return date.getTime();
        } catch (Exception e) {
            return 0L;
        }
    }

    public static Long getGMTToLongTimeInRequiredTimeZone(String time, String accountTimeZone) {

        if (StringUtils.isEmpty(time)) {
            return 0L;
        }
        DateFormat simpleDateFormat;
        try {
            simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
            Date date = simpleDateFormat.parse(time.trim());
            TimeZone.setDefault(TimeZone.getTimeZone(accountTimeZone));
            return date.getTime();
        } catch (Exception e) {
            return 0L;
        }

    }

    public static String getEpochToGMTTime(Long epochTimestamp) {
        Date date = new Date(epochTimestamp);
        DateFormat format = new SimpleDateFormat("dd-MM-yyyy HH:mm:00");
        format.setTimeZone(TimeZone.getTimeZone("Etc/UTC"));
        return format.format(date);
    }

    public static LocalDateTime dateStrToLocalDateTime(String dateStr) {
        try {
            return LocalDateTime.parse(dateStr, formatter);
        } catch (DateTimeParseException e) {
            log.error("Exception While Parsing date", e);
        }
        return null;
    }

    public static String convertEpochToFormattedTime(long epochMillis) {
        Date date = new Date(epochMillis);
        SimpleDateFormat formatter = new SimpleDateFormat("MM-dd-yyyy, HH:mm:ss");
        formatter.setTimeZone(TimeZone.getTimeZone("GMT"));
        return formatter.format(date);
    }

    /**
     * @param startTime
     * @param endTime
     * @param aggregationLevel
     * @return
     */
    public static List<Long> getCustomGeneratedTimeSeries(long startTime, long endTime, AggregationLevel aggregationLevel) {

        List<Long> result = new ArrayList<>();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(startTime), ZoneId.of("UTC"));
        LocalDateTime localDateTimeEndTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(endTime), ZoneId.of("UTC"));
        result.add(localDateTime.plusMinutes(cassandraDataOffset).atZone(ZoneId.of("UTC")).toEpochSecond() * 1000);

        for (int i = 1; i <= aggregationLevel.getNoOfPoints(); i++) {

            if (aggregationLevel.getIncrementScale().equals("m")) {
                result.add(localDateTime.plusMinutes((long) i * aggregationLevel.getIncrementValue()).atZone(ZoneId.of("UTC")).toEpochSecond() * 1000);
            } else if (aggregationLevel.getIncrementScale().equals("h")) {
                result.add(localDateTime.plusHours((long) i * aggregationLevel.getIncrementValue()).atZone(ZoneId.of("UTC")).toEpochSecond() * 1000);
            } else if (aggregationLevel.getIncrementScale().equals("d")) {
                result.add(localDateTime.plusDays((long) i * aggregationLevel.getIncrementValue()).atZone(ZoneId.of("UTC")).toEpochSecond() * 1000);
            } else if (aggregationLevel.getIncrementScale().equals("mo")) {
                result.add(localDateTime.plusMonths((long) i * aggregationLevel.getIncrementValue()).atZone(ZoneId.of("UTC")).toEpochSecond() * 1000);
            } else if (aggregationLevel.getIncrementScale().equals("y")) {
                result.add(localDateTime.plusYears((long) i * aggregationLevel.getIncrementValue()).atZone(ZoneId.of("UTC")).toEpochSecond() * 1000);
            }

            if (result.get(result.size() - 1) > (localDateTimeEndTime.atZone(ZoneId.of("UTC")).toEpochSecond() * 1000)) {
                return result;
            }
        }
        return result;
    }

    public static LocalDateTime addOffset(long startTimeEpoch, LocalDateTime startTime, long offset,
                                          AggregationLevel aggregationLevel) {
        LocalDateTime baseStartTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(startTimeEpoch), ZoneId.of("UTC"));
        //apply offset based on aggregation level
        if (aggregationLevel.getAggregrationValue() > 60) {
            startTime = startTime.minusSeconds((offset / 1000));
        } else if (aggregationLevel.getAggregrationValue() == 60) {
            //extract only minutes from offset value, because only minutes is offset when agg level is hourly
            long minutes = ((offset % (60 * 60 * 1000)) / (60 * 1000));
            startTime = startTime.minusMinutes(minutes);
        }

        //check that adding offset to has not shifted the point far into the past than required, we will remove all
        // generated that are before the from time provided by the user, ideally we need just 1 aggregated point before
        // the user provided from time.
        while (startTime.isBefore(baseStartTime)) {
            startTime = startTime.plusMinutes(aggregationLevel.getAggregrationValue());
        }

        //subtracting 1 minute as the time series generating function will start generation from input parameter(exclusive)
        return startTime.minusMinutes(aggregationLevel.getAggregrationValue());
    }

    public static AggregationLevel getAggregationLevelWIP(long fromTime, long toTime) {
        long differenceInMinutes = (toTime - fromTime) / (1000 * 60);

        if (differenceInMinutes >= AggregationLevel.YEARLY.getAggregrationValue()) {

            return AggregationLevel.YEARLY;

        } else if ((differenceInMinutes > AggregationLevel.MONTHLY.getAggregrationValue())) {

            return AggregationLevel.MONTHLY;

        } else if (differenceInMinutes > (AggregationLevel.DAILY_WEEK.getAggregrationValue() * AggregationLevel.DAILY_WEEK.getNoOfPoints())) {

            return AggregationLevel.DAILY_MONTH;

        } else if (differenceInMinutes > AggregationLevel.DAILY_WEEK.getAggregrationValue()) {

            return AggregationLevel.DAILY_WEEK;

        } else if (differenceInMinutes > (AggregationLevel.THIRTYMINUTELY_TWELVEHOUR.getAggregrationValue() *
                AggregationLevel.THIRTYMINUTELY_TWELVEHOUR.getNoOfPoints())) {

            return AggregationLevel.HOURLY_TWENTYHOUR;

        } else if (differenceInMinutes > (AggregationLevel.FIFTEENMINUTELY_FOURHOUR.getAggregrationValue() *
                AggregationLevel.FIFTEENMINUTELY_FOURHOUR.getNoOfPoints())) {

            return AggregationLevel.THIRTYMINUTELY_TWELVEHOUR;

        } else if (differenceInMinutes > (AggregationLevel.MINUTELY_FULLHOUR.getAggregrationValue() *
                AggregationLevel.MINUTELY_FULLHOUR.getNoOfPoints())) {

            return AggregationLevel.FIFTEENMINUTELY_FOURHOUR;

        } else if (differenceInMinutes > (AggregationLevel.MINUTELY_HALFHOUR.getAggregrationValue() *
                AggregationLevel.MINUTELY_HALFHOUR.getNoOfPoints())) {

            return AggregationLevel.MINUTELY_FULLHOUR;

        } else if (differenceInMinutes > AggregationLevel.MINUTELY_FULLHOUR.getAggregrationValue()) {

            return AggregationLevel.MINUTELY_HALFHOUR;

        } else {

            return AggregationLevel.INVALID_RANGE;
        }

    }

    public static AggregationLevel getAggregationLevelForTopN(long fromTime, long toTime) {
        long differenceInMinutes = (toTime - fromTime) / (1000 * 60);

        if (differenceInMinutes >= AggregationLevel.YEARLY.getAggregrationValue()) {
            return AggregationLevel.YEARLY;
        } else if (differenceInMinutes >= AggregationLevel.MONTHLY.getAggregrationValue()) {
            return AggregationLevel.YEARLY;
        } else if (differenceInMinutes > AggregationLevel.DAILY.getAggregrationValue()) {
            return AggregationLevel.MONTHLY;
        } else if (differenceInMinutes > AggregationLevel.HOURLY.getAggregrationValue()) {
            return AggregationLevel.DAILY;
        } else {
            return AggregationLevel.HOURLY;
        }

    }

    public static String getGMTTimeFromCassandraforProblemDetail(String timeFromCassandra, long timeOffSet) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");
        DateTimeFormatter dateTimeFormatterSend = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:00");
        LocalDateTime localDateTime = LocalDateTime.parse(timeFromCassandra, dateTimeFormatter);
        localDateTime = localDateTime.minus(timeOffSet, ChronoUnit.MILLIS);
        return localDateTime.format(dateTimeFormatterSend);
    }

    public static String getGMTTimeFromCassandra(String timeFromCassandra, long timeOffSet) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("E MMM dd HH:mm:ss z uuuu");
        DateTimeFormatter dateTimeFormatterSend = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:00");
        LocalDateTime localDateTime = LocalDateTime.parse(timeFromCassandra, dateTimeFormatter);
        localDateTime = localDateTime.minus(timeOffSet, ChronoUnit.MILLIS);
        return localDateTime.format(dateTimeFormatterSend);
    }

    public static long getEPOCHGMTTimeFromCassandra(String timeFromCassandra, long timeOffSet) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("E MMM dd HH:mm:ss z uuuu");
        DateTimeFormatter dateTimeFormatterSend = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:00");
        LocalDateTime localDateTime = LocalDateTime.parse(timeFromCassandra, dateTimeFormatter);
        localDateTime = localDateTime.minus(timeOffSet, ChronoUnit.MILLIS);
        return getGMTToEpochTime(localDateTime.format(dateTimeFormatterSend));
    }

    public static String getGMTTimeFromCassandra(LocalDateTime time, long timeOffset) {
        DateTimeFormatter dateTimeFormatterSend = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:00");
        time = time.minus(timeOffset, ChronoUnit.MILLIS);
        return time.format(dateTimeFormatterSend);
    }

    public static long getCassandraTimeGMT(long epochTime) {
        return (epochTime + Long.valueOf(timezoneOffSet));
    }

    public static String getGreaterValue(String startTime, String endTime) {

        Timestamp st = null, et = null;
        try {
            st = getTimestampInGMT(startTime);
            et = getTimestampInGMT(endTime);
            return st.after(et) ? startTime : endTime;
        } catch (Exception e) {
            log.error("Error occurred while comparing timestamps.", e);
            return null;
        }
    }

    public static String getLesserValue(String startTime, String endTime) {

        Timestamp st = null, et = null;
        try {
            st = getTimestampInGMT(startTime);
            et = getTimestampInGMT(endTime);
            return st.before(et) ? startTime : endTime;
        } catch (Exception e) {
            log.error("Error occurred while comparing timestamps.", e);
            return null;
        }
    }

    public static LocalDateTime epochSecondsToLocalDateTime(long epochSeconds) {
        return LocalDateTime.ofInstant(Instant.ofEpochSecond(epochSeconds), ZoneOffset.UTC);
    }

    public static int getTotalDaysInYear(int year) {
        if ((year % 400 == 0) || ((year % 4 == 0) && (year % 100 != 0)))
            return 366;
        else
            return 365;

    }

    public static int getDaysInMonth(int month, int year) {

        int numberOfDaysInMonth = 0;
        switch (month) {
            case 1:

                numberOfDaysInMonth = 31;
                break;
            case 2:

                if ((year % 400 == 0) || ((year % 4 == 0) && (year % 100 != 0))) {
                    numberOfDaysInMonth = 29;
                } else {
                    numberOfDaysInMonth = 28;
                }
                break;
            case 3:

                numberOfDaysInMonth = 31;
                break;
            case 4:
                numberOfDaysInMonth = 30;
                break;
            case 5:
                numberOfDaysInMonth = 31;
                break;
            case 6:
                numberOfDaysInMonth = 30;
                break;
            case 7:
                numberOfDaysInMonth = 31;
                break;
            case 8:
                numberOfDaysInMonth = 31;
                break;
            case 9:
                numberOfDaysInMonth = 30;
                break;
            case 10:
                numberOfDaysInMonth = 31;
                break;
            case 11:
                numberOfDaysInMonth = 30;
                break;
            case 12:
                numberOfDaysInMonth = 31;
                break;
            default:
                numberOfDaysInMonth = 30;
        }
        return numberOfDaysInMonth;
    }

    public static Timestamp getCurrentTimestampInGMT() {
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(Constants.DATE_TIME);
            simpleDateFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
            SimpleDateFormat localDateFormat = new SimpleDateFormat(Constants.DATE_TIME);
            return new Timestamp(localDateFormat.parse(simpleDateFormat.format(new Date())).getTime());
        } catch (ParseException e) {
            log.error("Error in getting current time stamp in GMT", e);
        }
        return null;
    }

    public static AggregationLevel getAggregationLevel(long fromTime, long toTime) {
        long differenceInMinutes = (toTime - fromTime) / (1000 * 60);

        if (differenceInMinutes >= AggregationLevel.YEARLY.getAggregrationValue()) {

            return AggregationLevel.YEARLY;

        } else if ((differenceInMinutes > AggregationLevel.MONTHLY.getAggregrationValue())) {

            return AggregationLevel.MONTHLY;

        } else if (differenceInMinutes > ((long) AggregationLevel.DAILY_WEEK.getAggregrationValue() * AggregationLevel.DAILY_WEEK.getNoOfPoints())) {

            return AggregationLevel.DAILY_MONTH;

        } else if (differenceInMinutes > AggregationLevel.DAILY_WEEK.getAggregrationValue()) {

            return AggregationLevel.DAILY_WEEK;

        } else if (differenceInMinutes > ((long) AggregationLevel.THIRTYMINUTELY_TWELVEHOUR.getAggregrationValue() *
                AggregationLevel.THIRTYMINUTELY_TWELVEHOUR.getNoOfPoints())) {

            return AggregationLevel.HOURLY_TWENTYHOUR;

        } else if (differenceInMinutes > ((long) AggregationLevel.FIFTEENMINUTELY_FOURHOUR.getAggregrationValue() *
                AggregationLevel.FIFTEENMINUTELY_FOURHOUR.getNoOfPoints())) {

            return AggregationLevel.THIRTYMINUTELY_TWELVEHOUR;

        } else if (differenceInMinutes > ((long) AggregationLevel.MINUTELY_FULLHOUR.getAggregrationValue() *
                AggregationLevel.MINUTELY_FULLHOUR.getNoOfPoints())) {

            return AggregationLevel.FIFTEENMINUTELY_FOURHOUR;

        } else if (differenceInMinutes > ((long) AggregationLevel.MINUTELY_HALFHOUR.getAggregrationValue() *
                AggregationLevel.MINUTELY_HALFHOUR.getNoOfPoints())) {

            return AggregationLevel.MINUTELY_FULLHOUR;

        } else if (differenceInMinutes > AggregationLevel.MINUTELY_FULLHOUR.getAggregrationValue()) {

            return AggregationLevel.MINUTELY_HALFHOUR;

        } else {

            return AggregationLevel.INVALID_RANGE;

        }

    }


    public static List<LocalDateTime> getStartTimeStampDisplayTimes(long startTimeEpoch, AggregationLevel aggregationLevel, String timezoneId) {

        List<LocalDateTime> result = new ArrayList<>();
        LocalDateTime startTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(startTimeEpoch), ZoneId.of(timezoneId));
        startTime = startTime.withSecond(0);

        LocalDateTime initialStartTime = startTime;

        if (aggregationLevel.getAggregrationValue() == 525600) {
            startTime = startTime.withMonth(0);
        }

        if (aggregationLevel.getAggregrationValue() >= 44640) {
            startTime = startTime.withDayOfMonth(1);
        }

        if (aggregationLevel.getAggregrationValue() >= 1) {
            if (aggregationLevel.getAggregrationValue() == 1440) {

                if (startTime.getHour() != 0 || startTime.getMinute() != 0) {
                    startTime = startTime.withHour(0).withMinute(0).plusMinutes(1440);
                    result.add(initialStartTime);
                }
            } else if (aggregationLevel.getAggregrationValue() == 60) {

                if (startTime.getMinute() != 0) {
                    startTime = startTime.withMinute(0).plusMinutes(60);
                    result.add(initialStartTime);
                }

            } else if (aggregationLevel.getAggregrationValue() == 30) {

                if (startTime.getMinute() == 15 || startTime.getMinute() == 45) {
                    for (int i = 0; i < 30; i++) {
                        long minute = startTime.getMinute();
                        if (minute == 0 || minute == 30) {
                            result.add(initialStartTime);
                            break;
                        }
                        //We subtract delta until it matches a whole value of 30
                        startTime = startTime.plusMinutes(1);
                    }
                }

            } else if (aggregationLevel.getAggregrationValue() == 15) {

                for (int i = 0; i < 15; i++) {
                    long minute = startTime.getMinute();
                    if (minute == 0 || minute == 15 || minute == 30 || minute == 45)
                        break;
                    //We subtract delta until it matches a whole value of 15
                    startTime = startTime.minusMinutes(1);
                }

            }
        }

        startTime = startTime.withNano(0);
        result.add(startTime);
        return result;
    }

    public static List<LocalDateTime> getGeneratedTimeSeriesDisplayTimes(List<LocalDateTime> startTimeList, long endTime,
                                                                         AggregationLevel aggregationLevel) {
        List<LocalDateTime> result = new ArrayList<>(startTimeList);
        try {
            int limit;
            if (startTimeList.size() != 1) {
                limit = aggregationLevel.getNoOfPoints();
            } else {
                limit = aggregationLevel.getNoOfPoints() - 1;
            }

            for (int i = result.size(); i <= limit; i++) {
                LocalDateTime startTime = result.get(result.size() - 1);
                switch (aggregationLevel.getIncrementScale()) {
                    case "y":
                        result.add(startTime.plusYears(aggregationLevel.getIncrementValue()));
                        break;
                    case "mo":
                        result.add(startTime.plusMonths(aggregationLevel.getIncrementValue()));
                        break;
                    case "d":
                        result.add(startTime.plusDays(aggregationLevel.getIncrementValue()));
                        break;
                    case "h":
                        result.add(startTime.plusHours(aggregationLevel.getIncrementValue()));
                        break;
                    default:
                        result.add(startTime.plusMinutes(aggregationLevel.getIncrementValue()));
                        break;
                }
            }
        } catch (Exception e) {
            log.error("Error occurred while generating time series for time range from: {}, to: {}", startTimeList.get(0), endTime, e);
        }
        return result;
    }

    public static List<LocalDateTime> getStartTimeStampOSTimes(long startTimeEpoch, AggregationLevel aggregationLevel, String timezoneId) {

        List<LocalDateTime> result = new ArrayList<>();
        LocalDateTime startTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(startTimeEpoch), ZoneId.of(timezoneId));
        startTime = startTime.withSecond(0);

        if (aggregationLevel.getAggregrationValue() == 525600) {
            startTime = startTime.withMonth(0);
        }

        if (aggregationLevel.getAggregrationValue() >= 44640) {
            startTime = startTime.withDayOfMonth(1);
        }

        if (aggregationLevel.getAggregrationValue() >= 1) {
            if (aggregationLevel.getAggregrationValue() == 1440) {

                startTime = startTime.withHour(0).withMinute(0);

            } else if (aggregationLevel.getAggregrationValue() == 60) {

                startTime = startTime.withMinute(0);

            } else if (aggregationLevel.getAggregrationValue() == 30) {

                if (startTime.getMinute() == 15 || startTime.getMinute() == 45) {
                    for (int i = 0; i < 30; i++) {
                        long minute = startTime.getMinute();
                        if (minute == 0 || minute == 30) {
                            break;
                        }
                        //We subtract delta until it matches a whole value of 30
                        startTime = startTime.minusMinutes(1);
                    }
                }

            } else if (aggregationLevel.getAggregrationValue() == 15) {

                for (int i = 0; i < 15; i++) {
                    long minute = startTime.getMinute();
                    if (minute == 0 || minute == 15 || minute == 30 || minute == 45)
                        break;
                    //We subtract delta until it matches a whole value of 15
                    startTime = startTime.minusMinutes(1);
                }

            }
        }

        startTime = startTime.withNano(0);
        result.add(startTime);
        return result;
    }

    public static List<LocalDateTime> getGeneratedTimeSeriesOSTimes(List<LocalDateTime> startTimeList, long endTime,
                                                                    AggregationLevel aggregationLevel) {
        List<LocalDateTime> result = new ArrayList<>(startTimeList);
        try {
            int limit = aggregationLevel.getNoOfPoints();
            for (int i = result.size(); i <= limit; i++) {
                LocalDateTime startTime = result.get(result.size() - 1);
                switch (aggregationLevel.getIncrementScale()) {
                    case "y":
                        result.add(startTime.plusYears(aggregationLevel.getIncrementValue()));
                        break;
                    case "mo":
                        result.add(startTime.plusMonths(aggregationLevel.getIncrementValue()));
                        break;
                    case "d":
                        result.add(startTime.plusDays(aggregationLevel.getIncrementValue()));
                        break;
                    case "h":
                        result.add(startTime.plusHours(aggregationLevel.getIncrementValue()));
                        break;
                    default:
                        result.add(startTime.plusMinutes(aggregationLevel.getIncrementValue()));
                        break;
                }
            }
        } catch (Exception e) {
            log.error("Error occurred while generating time series for time range from: {}, to: {}", startTimeList.get(0), endTime, e);
        }
        return result;
    }
}
