package com.appnomic.appsone.api.util;

import com.appnomic.appsone.api.common.Constants;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.*;

public class ExecutorPool {
    private static final Logger logger = LoggerFactory.getLogger(ExecutorPool.class);
    // Java Executor Service for for IO bound works/tasks
    static int minSize = Integer.parseInt(ConfProperties.getString(Constants.THREAD_POOL_MIN_SIZE_NAME, Constants.THREAD_POOL_MIN_SIZE_DEFAULT_VALUE));
    static int maxSize = Integer.parseInt(ConfProperties.getString(Constants.THREAD_POOL_MAX_SIZE_NAME, Constants.THREAD_POOL_MAX_SIZE_DEFAULT_VALUE));
    private static final ExecutorService ioBoundExecutor = new ThreadPoolExecutor(minSize,maxSize,
            0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>(),
            new ThreadFactoryBuilder().setNameFormat("UI-SERVICE-IO_BOUND-Thread %d").build(), new RejectionHandler());

    public static void executeIOBoundTask(Runnable r)
    {
        ioBoundExecutor.execute(r);
    }

    static
    {
        Runtime.getRuntime().addShutdownHook(new Thread()
        {
            @Override
            public void run()
            {
                ioBoundExecutor.shutdown();
                try
                {
                    if (!ioBoundExecutor.awaitTermination(10, TimeUnit.SECONDS))
                    {
                        logger.warn("io-bound Executor did not terminate in the specified time.");
                        List<Runnable> droppedTasks = ioBoundExecutor.shutdownNow();
                        logger.warn("io-bound Executor was abruptly shut down. " + droppedTasks.size() + " tasks will not be executed.");
                    }
                }
                catch (InterruptedException e)
                {
                    logger.error("Error while stopping Executor Services", e);
                    Thread.currentThread().interrupt();
                }
            }
        });
    }

    public static String getStatistics()
    {
        ThreadPoolExecutor threadPoolExecutor = (ThreadPoolExecutor) ioBoundExecutor;
        String stats = String.format("IO thread pool size : %d, Currently Executing Task Count: %d, Waiting Tasks in Queue: %d ,Total Completed Task Count: %d, Total Submitted Task Count: %d",
                threadPoolExecutor.getPoolSize(),
                threadPoolExecutor.getActiveCount(),
                threadPoolExecutor.getQueue().size(),
                threadPoolExecutor.getCompletedTaskCount(),
                threadPoolExecutor.getTaskCount());
        return stats;
    }


    public static class RejectionHandler implements RejectedExecutionHandler
    {
        @Override
        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor)
        {
            logger.error("Could not submit runnable " + r.getClass().getName() + " as the executor pool can not take any more tasks at this moment, The present data is lost");
        }
    }
}
