package com.appnomic.appsone.api.util;

/**
 * <AUTHOR> : 20/05/2019
 */
public class RCAPathDuplicateSwitch {
    private boolean active = true;
    private boolean state = false;

    public void setState(boolean state) {
        if(!active)  {
            return;
        }
        if(!state) {
            this.state = false;
            this.active = false;
        }   else {
            this.state = true;
        }
    }

    public boolean getState()   {
        return this.state;
    }

    public boolean isActive() {
        return this.active;
    }
}
