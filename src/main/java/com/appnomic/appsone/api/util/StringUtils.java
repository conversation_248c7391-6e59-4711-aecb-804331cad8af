package com.appnomic.appsone.api.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;

/**
 * <AUTHOR> : 17/1/19
 */
public class StringUtils {
    private static final Logger logger = LoggerFactory.getLogger(StringUtils.class);

    public static boolean isEmpty(String s) {
        return (s == null || s.trim().isEmpty());
    }

    public static long getLong(String number) {
        try {
            return Long.parseLong(number);
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    public static boolean isNumber(String value) {
        try {
            Integer.parseInt(value);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    public static Double getDouble(String number) {
        try {
            return Double.parseDouble(number);
        } catch (NumberFormatException e) {
            logger.error("Error while parsing number : {}\n", number, e);
            return null;
        }
    }

    public static String readLine(BufferedReader br) {
        try {
            int intC;
            StringBuilder line = new StringBuilder();
            boolean isNewLine = false;

            while ((intC = br.read()) != -1) {
                char c = (char) intC;

                if (c == '\r') {
                    continue;
                }

                if (c == '\n') {
                    isNewLine = true;
                    break;
                }

                line.append(c);
            }

            if (line.length() > 0 || isNewLine) {
                return line.toString();
            }
        } catch (IOException e) {
            logger.error("Error occurred while reading line", e);
        }

        return null;
    }
}
