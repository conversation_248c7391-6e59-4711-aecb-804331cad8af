package com.appnomic.appsone.api.custom.exceptions;

public class DataProcessingException extends Exception {

    private final String errorMessage;

    public DataProcessingException(String errorMessage)
    {
        super(errorMessage);
        this.errorMessage  = errorMessage;
    }

    public DataProcessingException(Throwable cause, String errorMessage) {
        super(cause);
        this.errorMessage = errorMessage;
    }


    public String getSimpleMessage()    {
        return "DataProcessingException :: "+this.errorMessage;
    }
}
