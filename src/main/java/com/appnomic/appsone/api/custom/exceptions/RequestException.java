package com.appnomic.appsone.api.custom.exceptions;

public class RequestException extends  Exception {
    private final String errorMessage;

    public RequestException(String errorMessage)
    {
        super(errorMessage);
        this.errorMessage  = errorMessage;
    }

    public RequestException(Throwable cause, String errorMessage) {
        super(cause);
        this.errorMessage = errorMessage;
    }


    public String getSimpleMessage()    {
        return "RequestException :: "+this.errorMessage;
    }

}
