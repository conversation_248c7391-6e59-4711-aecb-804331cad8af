package com.appnomic.appsone.api.custom.exceptions;

public class UserException extends Exception {

    private static final long serialVersionUID = 8689986943184519138L;
    private String errorCode;
    private String errorMessage;

    public UserException(String message, Throwable cause, String errorMessage) {
        super(message, cause);
        this.errorMessage = errorMessage;
    }

    public UserException(Throwable cause, String errorMessage) {
        super(cause);
        this.errorMessage = errorMessage;
    }

    public UserException(String errorCode, String errorMessage) {
        super(errorCode + "::" + errorMessage);
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    public UserException(String message) {
        super(message);
    }

    public String getSimpleMessage() {
        return this.errorCode + " :: " + this.errorMessage;
    }
}
