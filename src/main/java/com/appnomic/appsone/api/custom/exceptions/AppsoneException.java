package com.appnomic.appsone.api.custom.exceptions;

import lombok.Data;

@Data
public class AppsoneException extends RuntimeException {
    private String errorCode;
    private String errorMessage;

    public AppsoneException(String message, Throwable cause, String errorMessage) {
        super(message, cause);
        this.errorMessage = errorMessage;
    }

    public AppsoneException(Throwable cause, String errorMessage) {
        super(cause);
        this.errorMessage = errorMessage;
    }

    public AppsoneException(String errorCode, String errorMessage)
    {
        super(errorCode + "::"+ errorMessage);
        this.errorCode = errorCode;
        this.errorMessage  = errorMessage;
    }

    public AppsoneException(String message) {
        super(message);
        this.errorMessage=message;
    }

    public String getSimpleMessage()    {
        return this.errorCode+" :: "+this.errorMessage;
    }
}
