package com.appnomic.appsone.api.mobile.util;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.pojo.RequestObject;

public class BigQueryUtil {
	public static String getInParameter(String input) {
	    List<String> inputList = Arrays.asList(input.split(","));
	    String [] temp = new String [inputList.size()];
	    int tempCounter=0;
	    for(String s : inputList) {
	        temp[tempCounter] = "'" + s + "'";
	        tempCounter++;
	    }
		return "(" + String.join(",", temp) + ")";
	}
	
	public static String getIntervalBucket(String fromTime, String toTime) {
		Long diffMillis = Long.parseLong(toTime) - Long.parseLong(fromTime);
		return getBucketMap().get(diffMillis);
	}
	
	public static Map<String, Long> createTimeBucket(Long fromTime, Long toTime, String intervalBucket) {
		Map<String, Long> timeBucket = new HashMap<>();
		long intervalValue = Long.parseLong(intervalBucket.split(" ")[0]) * getMillis(intervalBucket.split(" ")[1]);
		Long time = fromTime;
		while(time <= toTime) {
			timeBucket.put(String.valueOf(time), 0L);
			time += intervalValue;
		}
		return timeBucket;
	}
	
	public static Map<String, BigDecimal> createTimeBucketBigDecimal(Long fromTime, Long toTime, String intervalBucket) {
		Map<String, BigDecimal> timeBucket = new HashMap<>();
		long intervalValue = Long.parseLong(intervalBucket.split(" ")[0]) * getMillis(intervalBucket.split(" ")[1]);
		Long time = fromTime;
		while(time <= toTime) {
			timeBucket.put(String.valueOf(time), BigDecimal.ZERO);
			time += intervalValue;
		}
		return timeBucket;
	}
	
	public static Boolean appNameValidation(RequestObject requestObject, List<String> errorMessages) {
		if(null == requestObject.getQueryParams().get("app-name") || requestObject.getQueryParams().get("app-name").length == 0) {
			errorMessages.add("App name not present in params");
			return false;
		} else {
			return true;
		}
	}
	
	public static Boolean appDisplayVersionValidation(RequestObject requestObject, List<String> errorMessages) {
		if(null == requestObject.getQueryParams().get("app-build-version") || requestObject.getQueryParams().get("app-build-version").length == 0) {
			errorMessages.add("App build version not present in params");
			return false;
		} else if (requestObject.getQueryParams().get("app-build-version").length == 1 && requestObject.getQueryParams().get("app-build-version")[0].equalsIgnoreCase("")) {
			errorMessages.add("App build version empty in params");
			return false;
		} else {
			return true;
		}
	}
	
	public static Boolean appOSStringValidation(RequestObject requestObject, List<String> errorMessages) {
		if(null == requestObject.getQueryParams().get("app-os") || requestObject.getQueryParams().get("app-os").length == 0) {
			errorMessages.add("App os string not present in params");
			return false;
		} else if(requestObject.getQueryParams().get("app-os")[0].indexOf(".") == -1) {
			errorMessages.add("App os string malformed. No '.' present between app name and os type.");
			return false;
		} else {
			return true;
		}
	}
	
	public static Boolean fromTimeToTimeValidation(RequestObject requestObject, List<String> errorMessages) {
		if (null == requestObject.getQueryParams().get("from-time") || requestObject.getQueryParams().get("from-time").length == 0 ||
				   null == requestObject.getQueryParams().get("to-time") || requestObject.getQueryParams().get("to-time").length == 0){
			errorMessages.add("From time or To time not present in request");
			return false;
		} else if (Long.parseLong(requestObject.getQueryParams().get("from-time")[0]) >= Long.parseLong(requestObject.getQueryParams().get("to-time")[0])) {
			errorMessages.add("Param from-time greater than or equal to to-time");
			return false;
		} else {
			return true;
		}
	}
	
	public static Boolean issueIdValidation(RequestObject requestObject, List<String> errorMessages) {
		if(null == requestObject.getParams().get(":id") || requestObject.getParams().get(":id") == "") {
			errorMessages.add("Issue id null or not present in request");
			return false;
		} else {
			return true;
		}
	}
	
	public static Boolean requestNameValidation(RequestObject requestObject, List<String> errorMessages) {
		if(null == requestObject.getParams().get(":request-name") || requestObject.getParams().get(":request-name") == "") {
			errorMessages.add("Request name null or not present in request");
			return false;
		} else {
			return true;
		}
	}
	
	public static String getTruncatedEventTimestamp(String intervalBucket, String intervalBucketUnit, String alias) {
		if(!alias.equalsIgnoreCase("analytics")) {
			if(intervalBucket.equalsIgnoreCase(Constants.BUCKET_FOUR_HOURS)) {
				return "TIMESTAMP_SECONDS(900 * DIV(UNIX_SECONDS("+alias+".event_timestamp) + 0, 900)) ";
			} else if(intervalBucket.equalsIgnoreCase(Constants.BUCKET_TWELVE_HOURS)) {
				return "TIMESTAMP_SECONDS(1800 * DIV(UNIX_SECONDS("+alias+".event_timestamp) + 0, 1800)) ";
			} else {
				return "TIMESTAMP_TRUNC("+alias+".event_timestamp, "+intervalBucketUnit+") ";
			}
		} else {
			if(intervalBucket.equalsIgnoreCase(Constants.BUCKET_FOUR_HOURS)) {
				return "TIMESTAMP_SECONDS(900 * DIV(UNIX_SECONDS(TIMESTAMP_MICROS(T.event_timestamp)) + 0, 900)) ";
			} else if(intervalBucket.equalsIgnoreCase(Constants.BUCKET_TWELVE_HOURS)) {
				return "TIMESTAMP_SECONDS(1800 * DIV(UNIX_SECONDS(TIMESTAMP_MICROS(T.event_timestamp)) + 0, 1800)) ";
			} else {
				return "TIMESTAMP_TRUNC(TIMESTAMP_MICROS(T.event_timestamp), "+intervalBucketUnit+") ";
			}
		}
	}

	private static long getMillis(String value) {
		if(value.equalsIgnoreCase("MINUTE")) {
			return Constants.MINUTE;
		} else if(value.equalsIgnoreCase("HOUR")) {
			return Constants.HOUR;
		} else if(value.equalsIgnoreCase("DAY")) {
			return Constants.DAY;
		} else {
			return 0L;
		}
	}

	private static Map<Long, String> getBucketMap() {
		Map<Long, String> bucketValue = new HashMap<>();
		bucketValue.put(Constants.MILLIS_THIRTY_MINUTES, Constants.BUCKET_THIRTY_MINUTE);
		bucketValue.put(Constants.MILLIS_ONE_HOUR, Constants.BUCKET_ONE_HOUR);
		bucketValue.put(Constants.MILLIS_FOUR_HOURS, Constants.BUCKET_FOUR_HOURS);
		bucketValue.put(Constants.MILLIS_TWELVE_HOURS, Constants.BUCKET_TWELVE_HOURS);
		bucketValue.put(Constants.MILLIS_TWENTY_FOUR_HOURS, Constants.BUCKET_TWENTY_FOUR_HOURS);
		bucketValue.put(Constants.MILLIS_ONE_WEEK, Constants.BUCKET_ONE_WEEK);
		bucketValue.put(Constants.MILLIS_ONE_MONTH_28, Constants.BUCKET_ONE_MONTH);
		bucketValue.put(Constants.MILLIS_ONE_MONTH_29, Constants.BUCKET_ONE_MONTH);
		bucketValue.put(Constants.MILLIS_ONE_MONTH_30, Constants.BUCKET_ONE_MONTH);
		bucketValue.put(Constants.MILLIS_ONE_MONTH_31, Constants.BUCKET_ONE_MONTH);
		return bucketValue;
	}
}
