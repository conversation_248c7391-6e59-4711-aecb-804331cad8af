package com.appnomic.appsone.api.mobile.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.appnomic.appsone.api.businesslogic.GetNetworkRequestFiltersBL;
import com.appnomic.appsone.api.businesslogic.GetNetworkRequestsBL;
import com.appnomic.appsone.api.businesslogic.GetRequestPayloadSizeTrendBL;
import com.appnomic.appsone.api.businesslogic.GetResponsePayloadSizeTrendBL;
import com.appnomic.appsone.api.businesslogic.GetResponseTimeTrendBL;
import com.appnomic.appsone.api.businesslogic.GetSuccessRateTrendBL;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.mobile.model.NetworkRequest;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import com.google.cloud.bigquery.JobException;

import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

@Slf4j
public class NetworkMonitoringService {
	
	public GenericResponse<List<NetworkRequest>> getNetworkRequests(Request request, Response response) {
		try {
			RequestObject requestObject = new RequestObject(request);
			GetNetworkRequestsBL getNetworkRequestsBL = new GetNetworkRequestsBL();
			UtilityBean<Object> clientValidation = getNetworkRequestsBL.clientValidation(requestObject);
			UtilityBean<Object> serverValidation = getNetworkRequestsBL.serverValidation(clientValidation);
			List<NetworkRequest> networkRequests = getNetworkRequestsBL.processData(serverValidation);

			GenericResponse<List<NetworkRequest>> genericResponse = CommonUtils.getGenericResponse(response,
					StatusResponse.SUCCESS.name(),
					Constants.SUCCESS_STATUS_CODE, "Network Requests successfully fetched", null, false);
			genericResponse.setData(networkRequests);

			return genericResponse;
		} catch (JobException | DataProcessingException exc) {
			log.error("Error in NetworkMonitoringService.getNetworkRequests. Details: ", exc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					exc.getMessage(), null, true);
		} catch (ClientException clExc) {
			log.error(clExc.getMessage());
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					clExc.getMessage(), null, true);
		} catch (Exception e) {
			log.error("Error in NetworkMonitoringService.getNetworkRequests. Details: ", e);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					e.getMessage(), null, true);
		}
	}
	
	public GenericResponse<Map<String, Set<String>>> getNetworkRequestFilters(Request request, Response response) {
		try {
			RequestObject requestObject = new RequestObject(request);
			GetNetworkRequestFiltersBL getNetworkRequestFiltersBL = new GetNetworkRequestFiltersBL();
			UtilityBean<Object> clientValidation = getNetworkRequestFiltersBL.clientValidation(requestObject);
			UtilityBean<Object> serverValidation = getNetworkRequestFiltersBL.serverValidation(clientValidation);
			Map<String, Set<String>> networkRequestFilters = getNetworkRequestFiltersBL.processData(serverValidation);
			
			GenericResponse<Map<String, Set<String>>> genericResponse = CommonUtils.getGenericResponse(response, 
					StatusResponse.SUCCESS.name(), 
					Constants.SUCCESS_STATUS_CODE, "Network Request Filters successfully fetched", null, false);
			genericResponse.setData(networkRequestFilters);

			return genericResponse;
		} catch (JobException | DataProcessingException exc) {
			log.error("Error in NetworkMonitoringService.getNetworkRequestFilters. Details: ", exc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					exc.getMessage(), null, true);
		} catch (ClientException clExc) {
			log.error(clExc.getMessage());
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					clExc.getMessage(), null, true);
		} catch (Exception e) {
			log.error("Error in NetworkMonitoringService.getNetworkRequestFilters. Details: ", e);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					e.getMessage(), null, true);
		}
	}
	
	public GenericResponse<Map<String, BigDecimal>> getResponseTimeTrend(Request request, Response response) {
		try {
			RequestObject requestObject = new RequestObject(request);
			GetResponseTimeTrendBL getResponseTimeTrendBL = new GetResponseTimeTrendBL();
			UtilityBean<Object> clientValidation = getResponseTimeTrendBL.clientValidation(requestObject);
			UtilityBean<Object> serverValidation = getResponseTimeTrendBL.serverValidation(clientValidation);
			Map<String, BigDecimal> responseTimeTrend = getResponseTimeTrendBL.processData(serverValidation);
			
			GenericResponse<Map<String, BigDecimal>> genericResponse = CommonUtils.getGenericResponse(response, 
					StatusResponse.SUCCESS.name(), 
					Constants.SUCCESS_STATUS_CODE, "Response Time trend details successfully fetched", null, false);
			genericResponse.setData(responseTimeTrend);

			return genericResponse;
		} catch (JobException | DataProcessingException exc) {
			log.error("Error in NetworkMonitoringService.getResponseTimeTrend. Details: ", exc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					exc.getMessage(), null, true);
		} catch (ClientException clExc) {
			log.error(clExc.getMessage());
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					clExc.getMessage(), null, true);
		} catch (Exception e) {
			log.error("Error in NetworkMonitoringService.getResponseTimeTrend. Details: ", e);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					e.getMessage(), null, true);
		}
	}
	
	public GenericResponse<Map<String, BigDecimal>> getResponsePayloadSizeTrend(Request request, Response response) {
		try {
			RequestObject requestObject = new RequestObject(request);
			GetResponsePayloadSizeTrendBL getResponsePayloadSizeTrendBL = new GetResponsePayloadSizeTrendBL();
			UtilityBean<Object> clientValidation = getResponsePayloadSizeTrendBL.clientValidation(requestObject);
			UtilityBean<Object> serverValidation = getResponsePayloadSizeTrendBL.serverValidation(clientValidation);
			Map<String, BigDecimal> responseTimeTrend = getResponsePayloadSizeTrendBL.processData(serverValidation);
			
			GenericResponse<Map<String, BigDecimal>> genericResponse = CommonUtils.getGenericResponse(response, 
					StatusResponse.SUCCESS.name(), 
					Constants.SUCCESS_STATUS_CODE, "Response Payload size trend successfully fetched", null, false);
			genericResponse.setData(responseTimeTrend);

			return genericResponse;
		} catch (JobException | DataProcessingException exc) {
			log.error("Error in NetworkMonitoringService.getResponsePayloadSize. Details: ", exc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					exc.getMessage(), null, true);
		} catch (ClientException clExc) {
			log.error(clExc.getMessage());
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					clExc.getMessage(), null, true);
		} catch (Exception e) {
			log.error("Error in NetworkMonitoringService.getResponsePayloadSize. Details: ", e);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					e.getMessage(), null, true);
		}
	}
	
	public GenericResponse<Map<String, BigDecimal>> getRequestPayloadSizeTrend(Request request, Response response) {
		try {
			RequestObject requestObject = new RequestObject(request);
			GetRequestPayloadSizeTrendBL getRequestPayloadSizeTrendBL = new GetRequestPayloadSizeTrendBL();
			UtilityBean<Object> clientValidation = getRequestPayloadSizeTrendBL.clientValidation(requestObject);
			UtilityBean<Object> serverValidation = getRequestPayloadSizeTrendBL.serverValidation(clientValidation);
			Map<String, BigDecimal> responseTimeTrend = getRequestPayloadSizeTrendBL.processData(serverValidation);
			
			GenericResponse<Map<String, BigDecimal>> genericResponse = CommonUtils.getGenericResponse(response, 
					StatusResponse.SUCCESS.name(), 
					Constants.SUCCESS_STATUS_CODE, "Request Payload size trend successfully fetched", null, false);
			genericResponse.setData(responseTimeTrend);

			return genericResponse;
		} catch (JobException | DataProcessingException exc) {
			log.error("Error in NetworkMonitoringService.getRequestPayloadSizeTrend. Details: ", exc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					exc.getMessage(), null, true);
		} catch (ClientException clExc) {
			log.error(clExc.getMessage());
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					clExc.getMessage(), null, true);
		} catch (Exception e) {
			log.error("Error in NetworkMonitoringService.getRequestPayloadSizeTrend. Details: ", e);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					e.getMessage(), null, true);
		}
	}
	
	public GenericResponse<Map<String, BigDecimal>> getSuccessRateTrend(Request request, Response response) {
		try {
			RequestObject requestObject = new RequestObject(request);
			GetSuccessRateTrendBL getSuccessRateTrendBL = new GetSuccessRateTrendBL();
			UtilityBean<Object> clientValidation = getSuccessRateTrendBL.clientValidation(requestObject);
			UtilityBean<Object> serverValidation = getSuccessRateTrendBL.serverValidation(clientValidation);
			Map<String, BigDecimal> responseTimeTrend = getSuccessRateTrendBL.processData(serverValidation);
			
			GenericResponse<Map<String, BigDecimal>> genericResponse = CommonUtils.getGenericResponse(response, 
					StatusResponse.SUCCESS.name(), 
					Constants.SUCCESS_STATUS_CODE, "Success Rate trend successfully fetched", null, false);
			genericResponse.setData(responseTimeTrend);

			return genericResponse;
		} catch (JobException | DataProcessingException exc) {
			log.error("Error in NetworkMonitoringService.getSuccessRateTrend. Details: ", exc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					exc.getMessage(), null, true);
		} catch (ClientException clExc) {
			log.error(clExc.getMessage());
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					clExc.getMessage(), null, true);
		} catch (Exception e) {
			log.error("Error in NetworkMonitoringService.getSuccessRateTrend. Details: ", e);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					e.getMessage(), null, true);
		}
	}
	
}
