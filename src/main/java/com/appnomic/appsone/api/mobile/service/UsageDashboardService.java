package com.appnomic.appsone.api.mobile.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.appnomic.appsone.api.businesslogic.CarrierOverviewBL;
import com.appnomic.appsone.api.businesslogic.NewUsersActiveUsersTrendBL;
import com.appnomic.appsone.api.businesslogic.UserActionsBL;
import com.appnomic.appsone.api.businesslogic.UserEngagementTrendBL;
import com.appnomic.appsone.api.businesslogic.UserRetentionTrendBL;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.mobile.model.CarrierOverview;
import com.appnomic.appsone.api.mobile.model.NewUsersActiveUsersTrend;
import com.appnomic.appsone.api.mobile.model.UserAction;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import com.google.cloud.bigquery.JobException;

import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

@Slf4j
public class UsageDashboardService {
	
	public GenericResponse<NewUsersActiveUsersTrend> getNewUsersActiveUsersTrend(Request request, Response response) {
		try {
			RequestObject requestObject = new RequestObject(request);
			NewUsersActiveUsersTrendBL newUsersActiveUsersTrendBL = new NewUsersActiveUsersTrendBL();
			UtilityBean<Object> clientValidation = newUsersActiveUsersTrendBL.clientValidation(requestObject);
			UtilityBean<Object> serverValidation = newUsersActiveUsersTrendBL.serverValidation(clientValidation);
			NewUsersActiveUsersTrend newUsersActiveUsersTrend = newUsersActiveUsersTrendBL.processData(serverValidation);

			GenericResponse<NewUsersActiveUsersTrend> genericResponse = CommonUtils.getGenericResponse(response,
					StatusResponse.SUCCESS.name(),
					Constants.SUCCESS_STATUS_CODE, "New and Active Users Trend successfully fetched", null, false);
			genericResponse.setData(newUsersActiveUsersTrend);

			return genericResponse;
		} catch (JobException | DataProcessingException exc) {
			log.error("Error in UsageDashboardService.getNewUsersActiveUsersTrend. Details: ", exc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					exc.getMessage(), null, true);
		} catch (ClientException clExc) {
			log.error(clExc.getMessage());
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					clExc.getMessage(), null, true);
		} catch (Exception e) {
			log.error("Error in UsageDashboardService.getNewUsersActiveUsersTrend. Details: ", e);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					e.getMessage(), null, true);
		}
	}
	
	public GenericResponse<Map<String, BigDecimal>> getUserEngagementTrend(Request request, Response response) {
		try {
			RequestObject requestObject = new RequestObject(request);
			UserEngagementTrendBL newUsersActiveUsersTrendBL = new UserEngagementTrendBL();
			UtilityBean<Object> clientValidation = newUsersActiveUsersTrendBL.clientValidation(requestObject);
			UtilityBean<Object> serverValidation = newUsersActiveUsersTrendBL.serverValidation(clientValidation);
			Map<String, BigDecimal> userEngagementTrend = newUsersActiveUsersTrendBL.processData(serverValidation);

			GenericResponse<Map<String, BigDecimal>> genericResponse = CommonUtils.getGenericResponse(response,
					StatusResponse.SUCCESS.name(),
					Constants.SUCCESS_STATUS_CODE, "User Engagement Trend successfully fetched", null, false);
			genericResponse.setData(userEngagementTrend);

			return genericResponse;
		} catch (JobException | DataProcessingException exc) {
			log.error("Error in UsageDashboardService.getUserEngagementTrend. Details: ", exc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					exc.getMessage(), null, true);
		} catch (ClientException clExc) {
			log.error(clExc.getMessage());
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					clExc.getMessage(), null, true);
		} catch (Exception e) {
			log.error("Error in UsageDashboardService.getUserEngagementTrend. Details: ", e);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					e.getMessage(), null, true);
		}
	}
	
	public GenericResponse<List<UserAction>> getUserActions(Request request, Response response) {
		try {
			RequestObject requestObject = new RequestObject(request);
			UserActionsBL userActionsBL = new UserActionsBL();
			UtilityBean<Object> clientValidation = userActionsBL.clientValidation(requestObject);
			UtilityBean<Object> serverValidation = userActionsBL.serverValidation(clientValidation);
			List<UserAction> userActions = userActionsBL.processData(serverValidation);

			GenericResponse<List<UserAction>> genericResponse = CommonUtils.getGenericResponse(response,
					StatusResponse.SUCCESS.name(),
					Constants.SUCCESS_STATUS_CODE, "User Actions successfully fetched", null, false);
			genericResponse.setData(userActions);

			return genericResponse;
		} catch (JobException | DataProcessingException exc) {
			log.error("Error in UsageDashboardService.getUserActions. Details: ", exc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					exc.getMessage(), null, true);
		} catch (ClientException clExc) {
			log.error(clExc.getMessage());
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					clExc.getMessage(), null, true);
		} catch (Exception e) {
			log.error("Error in UsageDashboardService.getUserActions. Details: ", e);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					e.getMessage(), null, true);
		}
	}
	
	public GenericResponse<List<CarrierOverview>> getCarrierOverview(Request request, Response response) {
		try {
			RequestObject requestObject = new RequestObject(request);
			CarrierOverviewBL carrierOverviewBL = new CarrierOverviewBL();
			UtilityBean<Object> clientValidation = carrierOverviewBL.clientValidation(requestObject);
			UtilityBean<Object> serverValidation = carrierOverviewBL.serverValidation(clientValidation);
			List<CarrierOverview> carrierOVerview = carrierOverviewBL.processData(serverValidation);

			GenericResponse<List<CarrierOverview>> genericResponse = CommonUtils.getGenericResponse(response,
					StatusResponse.SUCCESS.name(),
					Constants.SUCCESS_STATUS_CODE, "Carrier Overview successfully fetched", null, false);
			genericResponse.setData(carrierOVerview);

			return genericResponse;
		} catch (JobException | DataProcessingException exc) {
			log.error("Error in UsageDashboardService.getCarrierOverview. Details: ", exc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					exc.getMessage(), null, true);
		} catch (ClientException clExc) {
			log.error(clExc.getMessage());
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					clExc.getMessage(), null, true);
		} catch (Exception e) {
			log.error("Error in UsageDashboardService.getCarrierOverview. Details: ", e);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					e.getMessage(), null, true);
		}
	}
	
	public GenericResponse<Map<String, BigDecimal>> getUserRetentionTrend(Request request, Response response) {
		try {
			RequestObject requestObject = new RequestObject(request);
			UserRetentionTrendBL userRetentionTrendBL = new UserRetentionTrendBL();
			UtilityBean<Object> clientValidation = userRetentionTrendBL.clientValidation(requestObject);
			UtilityBean<Object> serverValidation = userRetentionTrendBL.serverValidation(clientValidation);
			Map<String, BigDecimal> userRetentionTrend = userRetentionTrendBL.processData(serverValidation);

			GenericResponse<Map<String, BigDecimal>> genericResponse = CommonUtils.getGenericResponse(response,
					StatusResponse.SUCCESS.name(),
					Constants.SUCCESS_STATUS_CODE, "User Retention Trend successfully fetched", null, false);
			genericResponse.setData(userRetentionTrend);

			return genericResponse;
		} catch (JobException | DataProcessingException exc) {
			log.error("Error in UsageDashboardService.getUserRetentionTrend. Details: ", exc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					exc.getMessage(), null, true);
		} catch (ClientException clExc) {
			log.error(clExc.getMessage());
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					clExc.getMessage(), null, true);
		} catch (Exception e) {
			log.error("Error in UsageDashboardService.getUserRetentionTrend. Details: ", e);
			if(e.getMessage().contains("division by zero")) {
				return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
						Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
						"No new users signed up on app in given date", null, true);
			} else {
				return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
						Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
						e.getMessage(), null, true);
			}
		}
	}
	
}
