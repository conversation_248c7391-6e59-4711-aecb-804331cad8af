package com.appnomic.appsone.api.mobile.service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.appnomic.appsone.api.businesslogic.GetErrorFilesTrendBL;
import com.appnomic.appsone.api.businesslogic.GetLatestCrashedAppVersionsBL;
import com.appnomic.appsone.api.businesslogic.GetMobileApplicationVersionsBL;
import com.appnomic.appsone.api.businesslogic.GetSlowPagesTrendBL;
import com.appnomic.appsone.api.businesslogic.GetTopErrorFilesBL;
import com.appnomic.appsone.api.businesslogic.GetTopSlowPagesBL;
import com.appnomic.appsone.api.businesslogic.GetUsageSummaryBL;
import com.appnomic.appsone.api.businesslogic.GetVersionCrashTrendBL;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.mobile.model.TopErrorFile;
import com.appnomic.appsone.api.mobile.model.TopSlowPage;
import com.appnomic.appsone.api.mobile.model.UsageSummary;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.ConfProperties;
import com.google.cloud.bigquery.JobException;

import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

@Slf4j
public class MonitoringDashboardService {
	public GenericResponse<Map<String, String>> getMobileApps() {
		GenericResponse<Map<String, String>> response = new GenericResponse<>();
		List<String> apps = Arrays.asList(ConfProperties.getString(Constants.ALL_APPLICATIONS).split(","));
		if(apps.size() > 0) {
			Map<String, String> appMap = new HashMap<>();
			for(String app : apps) {
				appMap.put(app, ConfProperties.getString(app+Constants.APP_NAME_SUFFIX));
			}
			response.setData(appMap);
			response.setMessage("SUCCESS");
			response.setResponseStatus("SUCCESS");
		} else {
			response.setMessage("No Application configured");
			response.setResponseStatus("FAILURE");
		}
		return response;
	}
	
	public GenericResponse<List<String>> getAppOS(Request request) {
		String appName = request.queryParams("app-name");
		GenericResponse<List<String>> response = new GenericResponse<>();
		if(null != appName && !appName.isEmpty()) {
			List<String> apps = Arrays.asList(ConfProperties.getString(Constants.ALL_APPLICATIONS).split(","));
			if(apps.contains(appName)) {
				List<String> appOSList = Arrays.asList(ConfProperties.getString(appName.concat(Constants.OS_LIST_SUFFIX)).split(","));
				response.setData(appOSList);
				response.setMessage("SUCCESS");
				response.setResponseStatus("SUCCESS");
			} else {
				response.setMessage("App Not Registered");
				response.setResponseStatus("FAILURE");
			}
			return response;
		} else {
			response.setMessage("app-name not found in request");
			response.setResponseStatus("FAILURE");
			return response;
		}
	}
	
	public GenericResponse<List<String>> getAppVersions(Request request, Response response) {
		try {
			RequestObject requestObject = new RequestObject(request);
			GetMobileApplicationVersionsBL getMobileApplicationVersionsBL = new GetMobileApplicationVersionsBL();
			UtilityBean<Object> clientValidation = getMobileApplicationVersionsBL.clientValidation(requestObject);
			UtilityBean<Object> serverValidation = getMobileApplicationVersionsBL.serverValidation(clientValidation);
			List<String> appVersions = getMobileApplicationVersionsBL.processData(serverValidation);

			GenericResponse<List<String>> genericResponse = CommonUtils.getGenericResponse(response,
					StatusResponse.SUCCESS.name(),
					Constants.SUCCESS_STATUS_CODE, "Application versions successfully fetched", null, false);
			genericResponse.setData(appVersions);

			return genericResponse;
		} catch (JobException | DataProcessingException exc) {
			log.error("Error in MonitoringService.getAppVersion. Details: ", exc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					exc.getMessage(), null, true);
		} catch (ClientException clExc) {
			log.error(clExc.getMessage());
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					clExc.getMessage(), null, true);
		} catch (Exception e) {
			log.error("Error in MonitoringService.getAppVersion. Details: ", e);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					e.getMessage(), null, true);
		}
	}

	public GenericResponse<List<TopErrorFile>> getTopErrorFiles(Request request, Response response) {
		try {
			RequestObject requestObject = new RequestObject(request);
			GetTopErrorFilesBL getTopErrorFilesBL = new GetTopErrorFilesBL();
			UtilityBean<Object> clientValidation = getTopErrorFilesBL.clientValidation(requestObject);
			UtilityBean<Object> serverValidation = getTopErrorFilesBL.serverValidation(clientValidation);
			List<TopErrorFile> topErrorFilesResponse = getTopErrorFilesBL.processData(serverValidation);

			GenericResponse<List<TopErrorFile>> genericResponse = CommonUtils.getGenericResponse(response,
					StatusResponse.SUCCESS.name(),
					Constants.SUCCESS_STATUS_CODE, "Top error files successfully fetched", null, false);
			genericResponse.setData(topErrorFilesResponse);

			return genericResponse;
		} catch (JobException | DataProcessingException exc) {
			log.error("Error in MonitoringService.getTopErrorFiles. Details: ", exc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					exc.getMessage(), null, true);
		} catch (ClientException clExc) {
			log.error(clExc.getMessage());
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					clExc.getMessage(), null, true);
		} catch (Exception e) {
			log.error("Error in MonitoringService.getTopErrorFiles. Details: ", e);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					e.getMessage(), null, true);
		}
	}

	public GenericResponse<List<TopSlowPage>> getTopSlowPages(Request request, Response response) {
		try {
			RequestObject requestObject = new RequestObject(request);
			GetTopSlowPagesBL getTopSlowPagesBL = new GetTopSlowPagesBL();
			UtilityBean<Object> clientValidation = getTopSlowPagesBL.clientValidation(requestObject);
			UtilityBean<Object> serverValidation = getTopSlowPagesBL.serverValidation(clientValidation);
			List<TopSlowPage> topSlowPageResponse = getTopSlowPagesBL.processData(serverValidation);

			GenericResponse<List<TopSlowPage>> genericResponse = CommonUtils.getGenericResponse(response,
					StatusResponse.SUCCESS.name(),
					Constants.SUCCESS_STATUS_CODE, "Top slow pages details successfully fetched", null, false);
			genericResponse.setData(topSlowPageResponse);

			return genericResponse;
		} catch (JobException | DataProcessingException exc) {
			log.error("Error in MonitoringService.getTopSlowPages. Details: ", exc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					exc.getMessage(), null, true);
		} catch (ClientException clExc) {
			log.error(clExc.getMessage());
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					clExc.getMessage(), null, true);
		} catch (Exception e) {
			log.error("Error in MonitoringService.getTopSlowPages. Details: ", e);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					e.getMessage(), null, true);
		}
	}

	public GenericResponse<Map<String, Long>> getSlowPagesTrend(Request request, Response response) {
		try {
			RequestObject requestObject = new RequestObject(request);
			GetSlowPagesTrendBL getSlowPagesTrendBL = new GetSlowPagesTrendBL();
			UtilityBean<Object> clientValidation = getSlowPagesTrendBL.clientValidation(requestObject);
			UtilityBean<Object> serverValidation = getSlowPagesTrendBL.serverValidation(clientValidation);
			Map<String, Long> slowPagesTrendMap = getSlowPagesTrendBL.processData(serverValidation);

			GenericResponse<Map<String, Long>> genericResponse = CommonUtils.getGenericResponse(response,
					StatusResponse.SUCCESS.name(),
					Constants.SUCCESS_STATUS_CODE, "Slow pages trend successfully fetched", null, false);
			genericResponse.setData(slowPagesTrendMap);

			return genericResponse;
		} catch (JobException | DataProcessingException exc) {
			log.error("Error in MonitoringService.getSlowPagesTrend. Details: ", exc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					exc.getMessage(), null, true);
		} catch (ClientException clExc) {
			log.error(clExc.getMessage());
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					clExc.getMessage(), null, true);
		} catch (Exception e) {
			log.error("Error in MonitoringService.getSlowPagesTrend. Details: ", e);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					e.getMessage(), null, true);
		}
	}

	public GenericResponse<Map<String, Long>> getErrorFilesTrend(Request request, Response response) {
		try {
			RequestObject requestObject = new RequestObject(request);
			GetErrorFilesTrendBL getErrorFilesTrendBL = new GetErrorFilesTrendBL();
			UtilityBean<Object> clientValidation = getErrorFilesTrendBL.clientValidation(requestObject);
			UtilityBean<Object> serverValidation = getErrorFilesTrendBL.serverValidation(clientValidation);
			Map<String, Long> errorFilesTrendMap = getErrorFilesTrendBL.processData(serverValidation);

			GenericResponse<Map<String, Long>> genericResponse = CommonUtils.getGenericResponse(response,
					StatusResponse.SUCCESS.name(),
					Constants.SUCCESS_STATUS_CODE, "Error files trend successfully fetched", null, false);
			genericResponse.setData(errorFilesTrendMap);

			return genericResponse;
		} catch (JobException | DataProcessingException exc) {
			log.error("Error in MonitoringService.getErrorFilesTrend. Details: ", exc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					exc.getMessage(), null, true);
		} catch (ClientException clExc) {
			log.error(clExc.getMessage());
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					clExc.getMessage(), null, true);
		} catch (Exception e) {
			log.error("Error in MonitoringService.getErrorFilesTrend. Details: ", e);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					e.getMessage(), null, true);
		}
	}

	public GenericResponse<List<String>> getLatestCrashedAppVersions(Request request, Response response) {
		try {
			RequestObject requestObject = new RequestObject(request);
			GetLatestCrashedAppVersionsBL getLatestCrashedAppVersionsBL = new GetLatestCrashedAppVersionsBL();
			UtilityBean<Object> clientValidation = getLatestCrashedAppVersionsBL.clientValidation(requestObject);
			UtilityBean<Object> serverValidation = getLatestCrashedAppVersionsBL.serverValidation(clientValidation);
			List<String> lastCrashedAppVersions = getLatestCrashedAppVersionsBL.processData(serverValidation);
			GenericResponse<List<String>> genericResponse = CommonUtils.getGenericResponse(response,
					StatusResponse.SUCCESS.name(),
					Constants.SUCCESS_STATUS_CODE, "Latest crashed application version details successfully fetched", null, false);
			genericResponse.setData(lastCrashedAppVersions);

			return genericResponse;
		} catch (JobException | DataProcessingException exc) {
			log.error("Error in MonitoringService.getLatestCrashedAppVersions. Details: ", exc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					exc.getMessage(), null, true);
		} catch (ClientException clExc) {
			log.error(clExc.getMessage());
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					clExc.getMessage(), null, true);
		} catch (Exception e) {
			log.error("Error in MonitoringService.getLatestCrashedAppVersions. Details: ", e);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					e.getMessage(), null, true);
		}
	}

	public GenericResponse<Map<String, Map<String, Long>>> getVersionCrashTrend(Request request, Response response) {
		try {
			RequestObject requestObject = new RequestObject(request);
			GetVersionCrashTrendBL getVersionCrashTrendBL = new GetVersionCrashTrendBL();
			UtilityBean<Object> clientValidation = getVersionCrashTrendBL.clientValidation(requestObject);
			UtilityBean<Object> serverValidation = getVersionCrashTrendBL.serverValidation(clientValidation);
			Map<String, Map<String, Long>> versionWiseCrashMap = getVersionCrashTrendBL.processData(serverValidation);

			GenericResponse<Map<String, Map<String, Long>>> genericResponse = CommonUtils.getGenericResponse(response,
					StatusResponse.SUCCESS.name(),
					Constants.SUCCESS_STATUS_CODE, "Application version-wise crash details successfully fetched", null, false);
			genericResponse.setData(versionWiseCrashMap);

			return genericResponse;
		} catch (JobException | DataProcessingException exc) {
			log.error("Error in MonitoringService.getVersionCrashTrend. Details: ", exc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					exc.getMessage(), null, true);
		} catch (ClientException clExc) {
			log.error(clExc.getMessage());
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					clExc.getMessage(), null, true);
		} catch (Exception e) {
			log.error("Error in MonitoringService.getVersionCrashTrend. Details: ", e);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					e.getMessage(), null, true);
		}
	}

	public GenericResponse<UsageSummary> getUsageSummary(Request request, Response response) {
		try {
			RequestObject requestObject = new RequestObject(request);
			GetUsageSummaryBL getUsageSummaryBL = new GetUsageSummaryBL();
			UtilityBean<Object> clientValidation = getUsageSummaryBL.clientValidation(requestObject);
			UtilityBean<Object> serverValidation = getUsageSummaryBL.serverValidation(clientValidation);
			UsageSummary usageSummary = getUsageSummaryBL.processData(serverValidation);

			GenericResponse<UsageSummary> genericResponse = CommonUtils.getGenericResponse(response,
					StatusResponse.SUCCESS.name(),
					Constants.SUCCESS_STATUS_CODE, "Application usage summary successfully fetched", null, false);
			genericResponse.setData(usageSummary);

			return genericResponse;
		} catch (JobException | DataProcessingException exc) {
			log.error("Error in MonitoringService.getUsageSummary. Details: ", exc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					exc.getMessage(), null, true);
		} catch (ClientException clExc) {
			log.error(clExc.getMessage());
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					clExc.getMessage(), null, true);
		} catch (Exception e) {
			log.error("Error in MonitoringService.getUsageSummary. Details: ", e);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					e.getMessage(), null, true);
		}
	}
	
}
