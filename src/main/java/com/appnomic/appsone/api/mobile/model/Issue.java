package com.appnomic.appsone.api.mobile.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class Issue {
	@JsonProperty("id")
	private String id;
	
	@JsonProperty("issueTitle")
	private String title;
	
	@JsonProperty("issueSubTitle")
	private String subTitle;
	
	private Boolean crashIsFatal;

	@JsonProperty("appVersions")
	private String appDisplayVersions;
	
	private Long crashCount;
	private Long userCount;
}
