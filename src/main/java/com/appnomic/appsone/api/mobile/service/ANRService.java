package com.appnomic.appsone.api.mobile.service;

import java.util.List;

import com.appnomic.appsone.api.businesslogic.GetANRDetailsBL;
import com.appnomic.appsone.api.businesslogic.GetANROverviewBL;
import com.appnomic.appsone.api.businesslogic.GetFrozenSlowFramesTrendBL;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.mobile.model.ANRDetail;
import com.appnomic.appsone.api.mobile.model.ANROverview;
import com.appnomic.appsone.api.mobile.model.FrozenSlowFramesTrend;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import com.google.cloud.bigquery.JobException;

import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

@Slf4j
public class ANRService {
	
	public GenericResponse<ANROverview> getANROverview(Request request, Response response) {
		try {
			RequestObject requestObject = new RequestObject(request);
			GetANROverviewBL getANROverviewBL = new GetANROverviewBL();
			UtilityBean<Object> clientValidation = getANROverviewBL.clientValidation(requestObject);
			UtilityBean<Object> serverValidation = getANROverviewBL.serverValidation(clientValidation);
			ANROverview anrOverview = getANROverviewBL.processData(serverValidation);

			GenericResponse<ANROverview> genericResponse = CommonUtils.getGenericResponse(response,
					StatusResponse.SUCCESS.name(),
					Constants.SUCCESS_STATUS_CODE, "ANR Overview successfully fetched", null, false);
			genericResponse.setData(anrOverview);

			return genericResponse;
		} catch (JobException | DataProcessingException exc) {
			log.error("Error in ANRService.getANROverview. Details: ", exc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					exc.getMessage(), null, true);
		} catch (ClientException clExc) {
			log.error(clExc.getMessage());
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					clExc.getMessage(), null, true);
		} catch (Exception e) {
			log.error("Error in ANRService.getANROverview. Details: ", e);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					e.getMessage(), null, true);
		}
	}
	
	public GenericResponse<List<ANRDetail>> getANRDetails(Request request, Response response) {
		try {
			RequestObject requestObject = new RequestObject(request);
			GetANRDetailsBL getANRDetailsBL = new GetANRDetailsBL();
			UtilityBean<Object> clientValidation = getANRDetailsBL.clientValidation(requestObject);
			UtilityBean<Object> serverValidation = getANRDetailsBL.serverValidation(clientValidation);
			List<ANRDetail> anrDetails = getANRDetailsBL.processData(serverValidation);
			
			GenericResponse<List<ANRDetail>> genericResponse = CommonUtils.getGenericResponse(response, 
					StatusResponse.SUCCESS.name(), 
					Constants.SUCCESS_STATUS_CODE, "ANR Details successfully fetched", null, false);
			genericResponse.setData(anrDetails);

			return genericResponse;
		} catch (JobException | DataProcessingException exc) {
			log.error("Error in ANRService.getANRDetails. Details: ", exc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					exc.getMessage(), null, true);
		} catch (ClientException clExc) {
			log.error(clExc.getMessage());
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					clExc.getMessage(), null, true);
		} catch (Exception e) {
			log.error("Error in ANRService.getANRDetails. Details: ", e);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					e.getMessage(), null, true);
		}
	}
	
	public GenericResponse<FrozenSlowFramesTrend> getFrozenSlowFramesTrend(Request request, Response response) {
		try {
			RequestObject requestObject = new RequestObject(request);
			GetFrozenSlowFramesTrendBL getFrozenSlowFramesTrendBL = new GetFrozenSlowFramesTrendBL();
			UtilityBean<Object> clientValidation = getFrozenSlowFramesTrendBL.clientValidation(requestObject);
			UtilityBean<Object> serverValidation = getFrozenSlowFramesTrendBL.serverValidation(clientValidation);
			FrozenSlowFramesTrend anrDetails = getFrozenSlowFramesTrendBL.processData(serverValidation);
			
			GenericResponse<FrozenSlowFramesTrend> genericResponse = CommonUtils.getGenericResponse(response, 
					StatusResponse.SUCCESS.name(), 
					Constants.SUCCESS_STATUS_CODE, "Frozen and Slow Frames Trend successfully fetched", null, false);
			genericResponse.setData(anrDetails);

			return genericResponse;
		} catch (JobException | DataProcessingException exc) {
			log.error("Error in ANRService.getFrozenSlowFramesTrend. Details: ", exc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					exc.getMessage(), null, true);
		} catch (ClientException clExc) {
			log.error(clExc.getMessage());
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					clExc.getMessage(), null, true);
		} catch (Exception e) {
			log.error("Error in ANRService.getFrozenSlowFramesTrend. Details: ", e);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					e.getMessage(), null, true);
		}
	}
	
}
