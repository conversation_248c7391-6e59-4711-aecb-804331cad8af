package com.appnomic.appsone.api.mobile.util;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.appnomic.appsone.api.common.Constants;
import com.google.auth.oauth2.ServiceAccountCredentials;
import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.BigQueryOptions;

public class BigQueryService {
	
	private static final Logger LOGGER = LoggerFactory.getLogger(BigQueryService.class);
	
	private static BigQueryService bigQueryService = null;
	
	private static Map<String, BigQuery> bigQueryMap = new HashMap<>();
	
	public BigQuery bigQuery;
	
	private BigQueryService(String appName) {
		try {
			InputStream input = BigQueryService.class.getResourceAsStream(Constants.getKeyPath(appName));
			LOGGER.debug("----------------------------------key path : "+Constants.getKeyPath(appName));
			LOGGER.debug("----------------------------------inputStream Object : "+input);
			ServiceAccountCredentials credentials = ServiceAccountCredentials.fromStream(input);
			bigQuery = BigQueryOptions.newBuilder()
					.setCredentials(credentials)
					.setProjectId(credentials.getProjectId())
					.build()
					.getService();
		} catch(IOException exc) {
			LOGGER.error("Could not establish BigQuery Connection", exc);
		}
	}
	
	public static BigQuery getInstance(String appName) {
		if(bigQueryMap.isEmpty() || !bigQueryMap.containsKey(appName)) {
			bigQueryService = new BigQueryService(appName);
			bigQueryMap.put(appName, bigQueryService.bigQuery);
		}
		return bigQueryMap.get(appName);
	}
}
