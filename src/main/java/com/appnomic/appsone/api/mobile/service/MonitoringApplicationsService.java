package com.appnomic.appsone.api.mobile.service;

import com.appnomic.appsone.api.businesslogic.MonitoringApplicationsBL;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.mobile.model.MonitoringApplication;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import com.google.cloud.bigquery.JobException;

import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

@Slf4j
public class MonitoringApplicationsService {
	
	public GenericResponse<MonitoringApplication> getMonitoringApplications(Request request, Response response) {
		try {
			RequestObject requestObject = new RequestObject(request);
			MonitoringApplicationsBL monitoringApplicationsBL = new MonitoringApplicationsBL();
			UtilityBean<Object> clientValidation = monitoringApplicationsBL.clientValidation(requestObject);
			UtilityBean<Object> serverValidation = monitoringApplicationsBL.serverValidation(clientValidation);
			MonitoringApplication monitoringApplication = monitoringApplicationsBL.processData(serverValidation);

			GenericResponse<MonitoringApplication> genericResponse = CommonUtils.getGenericResponse(response,
					StatusResponse.SUCCESS.name(),
					Constants.SUCCESS_STATUS_CODE, "Monitoring Applications summary successfully fetched", null, false);
			genericResponse.setData(monitoringApplication);

			return genericResponse;
		} catch (JobException | DataProcessingException exc) {
			log.error("Error in MonitoringApplicationsService.getMonitoringApplications. Details: ", exc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					exc.getMessage(), null, true);
		} catch (ClientException clExc) {
			log.error("Error in MonitoringApplicationsService.getMonitoringApplications. Details: ", clExc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					clExc.getMessage(), null, true);
		} catch (Exception e) {
			log.error("Error in MonitoringApplicationsService.getMonitoringApplications. Details: ", e);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					e.getMessage(), null, true);
		}
	}
}
