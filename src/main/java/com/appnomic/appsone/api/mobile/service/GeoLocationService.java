package com.appnomic.appsone.api.mobile.service;

import java.util.List;

import com.appnomic.appsone.api.businesslogic.GeoLocationBL;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.mobile.model.GeoLocation;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import com.google.cloud.bigquery.JobException;

import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

@Slf4j
public class GeoLocationService {
	public GenericResponse<List<GeoLocation>> getGeoLocationsOverview(Request request, Response response) {
		try {
			RequestObject requestObject = new RequestObject(request);
			GeoLocationBL geoLocationBL = new GeoLocationBL();
			UtilityBean<Object> clientValidation = geoLocationBL.clientValidation(requestObject);
			UtilityBean<Object> serverValidation = geoLocationBL.serverValidation(clientValidation);
			List<GeoLocation> geoLocations = geoLocationBL.processData(serverValidation);

			GenericResponse<List<GeoLocation>> genericResponse = CommonUtils.getGenericResponse(response,
					StatusResponse.SUCCESS.name(),
					Constants.SUCCESS_STATUS_CODE, "Geo Locations Overview successfully fetched", null, false);
			genericResponse.setData(geoLocations);

			return genericResponse;
		} catch (JobException | DataProcessingException exc) {
			log.error("Error in GeoLocationService.getGeoLocationsOverview. Details: ", exc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					exc.getMessage(), null, true);
		} catch (ClientException clExc) {
			log.error(clExc.getMessage());
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					clExc.getMessage(), null, true);
		} catch (Exception e) {
			log.error("Error in GeoLocationService.getGeoLocationsOverview. Details: ", e);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					e.getMessage(), null, true);
		}
	}
}
