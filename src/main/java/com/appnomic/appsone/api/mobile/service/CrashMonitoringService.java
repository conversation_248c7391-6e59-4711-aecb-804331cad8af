package com.appnomic.appsone.api.mobile.service;

import java.util.List;
import java.util.Map;

import com.appnomic.appsone.api.businesslogic.CrashFreeUsersTrendBL;
import com.appnomic.appsone.api.businesslogic.CrashMonitoringOverviewBL;
import com.appnomic.appsone.api.businesslogic.CrashTrendBL;
import com.appnomic.appsone.api.businesslogic.GetCrashStackTraceBL;
import com.appnomic.appsone.api.businesslogic.GetDeviceStatusBL;
import com.appnomic.appsone.api.businesslogic.GetDevicesBL;
import com.appnomic.appsone.api.businesslogic.GetEventVersionTrendBL;
import com.appnomic.appsone.api.businesslogic.GetIssuesListBL;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.mobile.model.CrashFreeUsersTrend;
import com.appnomic.appsone.api.mobile.model.CrashOverview;
import com.appnomic.appsone.api.mobile.model.CrashTrend;
import com.appnomic.appsone.api.mobile.model.DeviceStatus;
import com.appnomic.appsone.api.mobile.model.Devices;
import com.appnomic.appsone.api.mobile.model.EventVersionTrend;
import com.appnomic.appsone.api.mobile.model.Issue;
import com.appnomic.appsone.api.mobile.model.StackTrace;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import com.google.cloud.bigquery.JobException;

import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

@Slf4j
public class CrashMonitoringService {
	public GenericResponse<CrashOverview> getCrashOverview(Request request, Response response) {
		try {
			RequestObject requestObject = new RequestObject(request);
			CrashMonitoringOverviewBL crashMonitoringOverviewBL = new CrashMonitoringOverviewBL();
			UtilityBean<Object> clientValidation = crashMonitoringOverviewBL.clientValidation(requestObject);
			UtilityBean<Object> serverValidation = crashMonitoringOverviewBL.serverValidation(clientValidation);
			CrashOverview crashOverview = crashMonitoringOverviewBL.processData(serverValidation);
			
			GenericResponse<CrashOverview> genericResponse = CommonUtils.getGenericResponse(response, 
					StatusResponse.SUCCESS.name(), 
					Constants.SUCCESS_STATUS_CODE, "Crash overview details successfully fetched", null, false);
			genericResponse.setData(crashOverview);

			return genericResponse;
		} catch (JobException | DataProcessingException exc) {
			log.error("Error in CrashMonitoringService.getCrashOverview. Details: ", exc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					exc.getMessage(), null, true);
		} catch (ClientException clExc) {
			log.error(clExc.getMessage());
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					clExc.getMessage(), null, true);
		} catch (Exception e) {
			log.error("Error in CrashMonitoringService.getCrashOverview. Details: ", e);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					e.getMessage(), null, true);
		}
	}
	
	public GenericResponse<CrashTrend> getCrashTrend(Request request, Response response) {
		try {
			RequestObject requestObject = new RequestObject(request);
			CrashTrendBL crashTrendBL = new CrashTrendBL();
			UtilityBean<Object> clientValidation = crashTrendBL.clientValidation(requestObject);
			UtilityBean<Object> serverValidation = crashTrendBL.serverValidation(clientValidation);
			CrashTrend crashTrend = crashTrendBL.processData(serverValidation);

			GenericResponse<CrashTrend> genericResponse = CommonUtils.getGenericResponse(response,
					StatusResponse.SUCCESS.name(),
					Constants.SUCCESS_STATUS_CODE, "Crash Trend details successfully fetched", null, false);
			genericResponse.setData(crashTrend);

			return genericResponse;
		} catch (JobException | DataProcessingException exc) {
			log.error("Error in CrashMonitoringService.getCrashTrend. Details: ", exc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					exc.getMessage(), null, true);
		} catch (ClientException clExc) {
			log.error(clExc.getMessage());
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					clExc.getMessage(), null, true);
		} catch (Exception e) {
			log.error("Error in CrashMonitoringService.getCrashTrend. Details: ", e);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					e.getMessage(), null, true);
		}
	}
	
	public GenericResponse<CrashFreeUsersTrend> getCrashFreeUsersTrend(Request request, Response response) {
		try {
			RequestObject requestObject = new RequestObject(request);
			CrashFreeUsersTrendBL crashFreeUsersTrendBL = new CrashFreeUsersTrendBL();
			UtilityBean<Object> clientValidation = crashFreeUsersTrendBL.clientValidation(requestObject);
			UtilityBean<Object> serverValidation = crashFreeUsersTrendBL.serverValidation(clientValidation);
			CrashFreeUsersTrend crashFreeUsersTrend = crashFreeUsersTrendBL.processData(serverValidation);
			
			GenericResponse<CrashFreeUsersTrend> genericResponse = CommonUtils.getGenericResponse(response,
					StatusResponse.SUCCESS.name(),
					Constants.SUCCESS_STATUS_CODE, "Crash Free Users Trend details successfully fetched", null, false);
			genericResponse.setData(crashFreeUsersTrend);
			return genericResponse;
		} catch (JobException | DataProcessingException exc) {
			log.error("Error in CrashMonitoringService.getCrashFreeUserTrend. Details: ", exc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					exc.getMessage(), null, true);
		} catch (ClientException clExc) {
			log.error(clExc.getMessage());
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					clExc.getMessage(), null, true);
		} catch (Exception e) {
			log.error("Error in CrashMonitoringService.getCrashFreeUserTrend. Details: ", e);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					e.getMessage(), null, true);
		}
	}
	
	public GenericResponse<List<Issue>> getIssuesList(Request request, Response response) {
		try {
			RequestObject requestObject = new RequestObject(request);
			GetIssuesListBL getIssuesListBL = new GetIssuesListBL();
			UtilityBean<Object> clientValidation = getIssuesListBL.clientValidation(requestObject);
			UtilityBean<Object> serverValidation = getIssuesListBL.serverValidation(clientValidation);
			List<Issue> issues = getIssuesListBL.processData(serverValidation);
			
			GenericResponse<List<Issue>> genericResponse = CommonUtils.getGenericResponse(response,
					StatusResponse.SUCCESS.name(),
					Constants.SUCCESS_STATUS_CODE, "Issue List successfully fetched", null, false);
			genericResponse.setData(issues);
			return genericResponse;
		} catch (JobException | DataProcessingException exc) {
			log.error("Error in CrashMonitoringService.getIssuesList. Details: ", exc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					exc.getMessage(), null, true);
		} catch (ClientException clExc) {
			log.error(clExc.getMessage());
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					clExc.getMessage(), null, true);
		} catch (Exception e) {
			log.error("Error in CrashMonitoringService.getIssuesList. Details: ", e);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					e.getMessage(), null, true);
		}
	}
	
	public GenericResponse<Map<String, EventVersionTrend>> getEventVersionTrend(Request request, Response response) {
		try {
			RequestObject requestObject = new RequestObject(request);
			GetEventVersionTrendBL getEventVersionTrendBL = new GetEventVersionTrendBL();
			UtilityBean<Object> clientValidation = getEventVersionTrendBL.clientValidation(requestObject);
			UtilityBean<Object> serverValidation = getEventVersionTrendBL.serverValidation(clientValidation);
			Map<String, EventVersionTrend> eventVersionTrend = getEventVersionTrendBL.processData(serverValidation);
			
			GenericResponse<Map<String, EventVersionTrend>> genericResponse = CommonUtils.getGenericResponse(response,
					StatusResponse.SUCCESS.name(),
					Constants.SUCCESS_STATUS_CODE, "Event Version Trend details successfully fetched", null, false);
			genericResponse.setData(eventVersionTrend);
			return genericResponse;
		} catch (JobException | DataProcessingException exc) {
			log.error("Error in CrashMonitoringService.getCrashFreeUserTrend. Details: ", exc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					exc.getMessage(), null, true);
		} catch (ClientException clExc) {
			log.error(clExc.getMessage());
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					clExc.getMessage(), null, true);
		} catch (Exception e) {
			log.error("Error in CrashMonitoringService.getCrashFreeUserTrend. Details: ", e);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					e.getMessage(), null, true);
		}
	}

	public GenericResponse<StackTrace> getCrashStackTrace(Request request, Response response) {
		try {
			RequestObject requestObject = new RequestObject(request);
			GetCrashStackTraceBL getCrashStackTraceBL = new GetCrashStackTraceBL();
			UtilityBean<Object> clientValidation = getCrashStackTraceBL.clientValidation(requestObject);
			UtilityBean<Object> serverValidation = getCrashStackTraceBL.serverValidation(clientValidation);
			StackTrace stackTrace = getCrashStackTraceBL.processData(serverValidation);
			
			GenericResponse<StackTrace> genericResponse = CommonUtils.getGenericResponse(response,
					StatusResponse.SUCCESS.name(),
					Constants.SUCCESS_STATUS_CODE, "Crash stack trace successfully fetched", null, false);
			genericResponse.setData(stackTrace);

			return genericResponse;
		} catch (JobException | DataProcessingException exc) {
			log.error("Error in CrashMonitoringService.getCrashStackTrace. Details: ", exc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					exc.getMessage(), null, true);
		} catch (ClientException clExc) {
			log.error(clExc.getMessage());
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					clExc.getMessage(), null, true);
		} catch (Exception e) {
			log.error("Error in CrashMonitoringService.getCrashStackTrace. Details: ", e);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					e.getMessage(), null, true);
		}
	}

	public GenericResponse<List<DeviceStatus>> getDeviceStatus(Request request, Response response) {
		try {
			RequestObject requestObject = new RequestObject(request);
			GetDeviceStatusBL getDeviceStatusBL = new GetDeviceStatusBL();
			UtilityBean<Object> clientValidation = getDeviceStatusBL.clientValidation(requestObject);
			UtilityBean<Object> serverValidation = getDeviceStatusBL.serverValidation(clientValidation);
			List<DeviceStatus> deviceStatus = getDeviceStatusBL.processData(serverValidation);
			
			GenericResponse<List<DeviceStatus>> genericResponse = CommonUtils.getGenericResponse(response,
					StatusResponse.SUCCESS.name(),
					Constants.SUCCESS_STATUS_CODE, "Device Status successfully fetched", null, false);
			genericResponse.setData(deviceStatus);

			return genericResponse;
		} catch (JobException | DataProcessingException exc) {
			log.error("Error in CrashMonitoringService.getDeviceStatus. Details: ", exc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					exc.getMessage(), null, true);
		} catch (ClientException clExc) {
			log.error(clExc.getMessage());
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					clExc.getMessage(), null, true);
		} catch (Exception e) {
			log.error("Error in CrashMonitoringService.getDeviceStatus. Details: ", e);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					e.getMessage(), null, true);
		}
	}

	public GenericResponse<List<Devices>> getDevices(Request request, Response response) {
		try {
			RequestObject requestObject = new RequestObject(request);
			GetDevicesBL getDevicesBL = new GetDevicesBL();
			UtilityBean<Object> clientValidation = getDevicesBL.clientValidation(requestObject);
			UtilityBean<Object> serverValidation = getDevicesBL.serverValidation(clientValidation);
			List<Devices> devices = getDevicesBL.processData(serverValidation);
			
			GenericResponse<List<Devices>> genericResponse = CommonUtils.getGenericResponse(response,
					StatusResponse.SUCCESS.name(),
					Constants.SUCCESS_STATUS_CODE, "Devices successfully fetched", null, false);
			genericResponse.setData(devices);

			return genericResponse;
		} catch (JobException | DataProcessingException exc) {
			log.error("Error in CrashMonitoringService.getDevices. Details: ", exc);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					exc.getMessage(), null, true);
		} catch (ClientException clExc) {
			log.error(clExc.getMessage());
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
					clExc.getMessage(), null, true);
		} catch (Exception e) {
			log.error("Error in CrashMonitoringService.getDevices. Details: ", e);
			return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
					Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
					e.getMessage(), null, true);
		}
	}
}
