package com.appnomic.appsone.api.manager;

import com.appnomic.appsone.api.util.KeycloakSessionManagmentScheduler;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.common.util.concurrent.Service;
import com.google.common.util.concurrent.ServiceManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;

/**
 * <AUTHOR> : 07/05/2019
 */
public class KeycloakSessionManager {
    private static final Logger logger = LoggerFactory.getLogger(KeycloakSessionManager.class);
    public KeycloakSessionManager() {
        final ServiceManager serviceManager = new ServiceManager(Collections.singleton(
                new KeycloakSessionManagmentScheduler()));
        serviceManager.addListener(new ServiceManager.Listener() {
            @Override
            public void healthy() {
                logger.info("CacheManager Scheduler started with state :  HEALTHY");
            }

            @Override
            public void stopped() {
                super.stopped();
            }

            @Override
            public void failure(Service service) {
                super.failure(service);
            }
        }, MoreExecutors.directExecutor());
    }
}
