package com.appnomic.appsone.api.manager;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.util.ConfProperties;
import io.lettuce.core.ClientOptions;
import io.lettuce.core.RedisClient;
import io.lettuce.core.RedisURI;
import io.lettuce.core.api.sync.RedisHashCommands;
import io.lettuce.core.cluster.ClusterClientOptions;
import io.lettuce.core.cluster.ClusterTopologyRefreshOptions;
import io.lettuce.core.cluster.RedisClusterClient;
import io.lettuce.core.protocol.ProtocolVersion;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public enum RedisConnectionManager {
    INSTANCE;

    // Redis configuration details
    private final String redisUsername = ConfProperties.getString(Constants.REDIS_USERNAME);
    private final String redisEncryptedPwd = ConfProperties.getString(Constants.REDIS_PASSWORD);
    private final String redisHosts = ConfProperties.getString(Constants.REDIS_HOSTS);
    private final boolean redisClusterMode = ConfProperties.getBoolean(Constants.REDIS_CLUSTER_MODE, Constants.REDIS_CLUSTER_MODE_DEFAULT);
    private final boolean redisSSLEnabled = ConfProperties.getBoolean(Constants.REDIS_SSL_ENABLED, Constants.REDIS_SSL_ENABLED_DEFAULT);
    private RedisHashCommands<String, String> redisClient = null;

    public RedisHashCommands<String, String> getRedisClient() {
        if (redisClient != null) {
            return redisClient;
        }

        if (redisHosts == null || redisHosts.trim().isEmpty()) {
            log.error("{} property defined in conf.properties is empty/null. Could not create a connection to redis cluster.", redisHosts);
            HealUICache.INSTANCE.updateHealUIErrors(1);
            return null;
        }

        List<String> redisHostDetails = Arrays.asList(redisHosts.split(","));
        if (redisHostDetails.isEmpty()) {
            log.error("{} property defined in conf.properties is invalid. Could not create a connection to redis cluster.", redisHosts);
            HealUICache.INSTANCE.updateHealUIErrors(1);
            return null;
        }


        log.debug("Factory method called with cluster mode as {}", redisClusterMode);
        if (redisClusterMode) {
            redisClient = getClusterModeRedisClient(redisHostDetails);
        } else {
            redisClient = getStandAloneRedisClient(redisHostDetails);
        }

        log.debug("Established Connection with Redis");
        return redisClient;

    }

    private RedisHashCommands<String, String> getClusterModeRedisClient(List<String> redisHostDetails) {

        try {
            String plainPassword = null;
            if (redisEncryptedPwd != null && !redisEncryptedPwd.trim().isEmpty()) {
                plainPassword = new String(Base64.getDecoder().decode(redisEncryptedPwd), StandardCharsets.UTF_8);
            }

            String finalPlainPassword = plainPassword;
            List<RedisURI> redisNodes = redisHostDetails.stream()
                    .map(hostPort -> {
                        try {
                            String[] hostAndPort = hostPort.split(Constants.COLON);
                            RedisURI node = RedisURI.Builder
                                    .redis(hostAndPort[0])
                                    .withSsl(redisSSLEnabled)
                                    .withPort(Integer.parseInt(hostAndPort[1]))
                                    .build();
                            if (redisUsername != null && finalPlainPassword != null) {
                                node.setUsername(redisUsername);
                                node.setPassword(finalPlainPassword.toCharArray());
                            }
                            return node;
                        } catch (Exception e) {
                            log.error("Invalid redis host and port details, Host:{}", hostPort, e);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (redisNodes.isEmpty()) {
                log.error("{} property defined in conf.properties is invalid. Could not create a connection to redis cluster.", redisHosts);
                HealUICache.INSTANCE.updateHealUIErrors(1);
                return null;
            }

            log.debug("Attempting to establish connection with Redis Cluster - {}, SSL:{}, Username:{}", redisHosts, redisSSLEnabled, redisUsername);
            RedisClusterClient clusterClient = RedisClusterClient.create(redisNodes);
            clusterClient.setOptions(ClusterClientOptions.builder()
                    .autoReconnect(true)
                    .protocolVersion(ProtocolVersion.RESP3)
                    .validateClusterNodeMembership(true)
                    .maxRedirects(5)
                    .cancelCommandsOnReconnectFailure(true)
                    .pingBeforeActivateConnection(true)
                    .topologyRefreshOptions(ClusterTopologyRefreshOptions.builder()
                            .enablePeriodicRefresh()
                            .build())
                    .build());
            return clusterClient.connect().sync();
        } catch (Exception e) {
            log.error("Error occurred while establishing the connection with Redis Cluster - {}, SSL:{}, Username:{}", redisHosts, redisSSLEnabled, redisUsername, e);
            HealUICache.INSTANCE.updateHealUIErrors(1);
            return null;
        }
    }

    private RedisHashCommands<String, String> getStandAloneRedisClient(List<String> redisHostDetails) {

        try {
            String plainPassword = null;
            if (redisEncryptedPwd != null && !redisEncryptedPwd.trim().isEmpty()) {
                plainPassword = new String(Base64.getDecoder().decode(redisEncryptedPwd), StandardCharsets.UTF_8);
            }

            String[] hostAndPort = redisHostDetails.get(0).split(Constants.COLON);

            RedisURI node = RedisURI.Builder
                    .redis(hostAndPort[0])
                    .withSsl(redisSSLEnabled)
                    .withPort(Integer.parseInt(hostAndPort[1]))
                    .build();
            if (redisUsername != null && plainPassword != null) {
                node.setUsername(redisUsername);
                node.setPassword(plainPassword.toCharArray());
            }

            if (node == null) {
                log.error("{} property defined in conf.properties is invalid. Could not create a connection to redis in standalone mode.", redisHostDetails.get(0));
                HealUICache.INSTANCE.updateHealUIErrors(1);
                return null;
            }

            log.debug("Attempting to establish connection with Redis in standalone mode - {}, SSL:{}, Username:{}", redisHosts, redisSSLEnabled, redisUsername);
            RedisClient standAloneClient = RedisClient.create(node);
            standAloneClient.setOptions(ClientOptions.builder()
                    .autoReconnect(true)
                    .protocolVersion(ProtocolVersion.RESP2)
                    .cancelCommandsOnReconnectFailure(true)
                    .pingBeforeActivateConnection(true)
                    .build());
            return standAloneClient.connect().sync();
        } catch (Exception e) {
            log.error("Error occurred while establishing the connection with Redis in standalone mode - {}, SSL:{}, Username:{}", redisHosts, redisSSLEnabled, redisUsername, e);
            HealUICache.INSTANCE.updateHealUIErrors(1);
            return null;
        }

    }
}
