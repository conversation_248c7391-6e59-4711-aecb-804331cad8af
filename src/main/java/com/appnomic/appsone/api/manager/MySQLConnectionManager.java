package com.appnomic.appsone.api.manager;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.api.common.Constants;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.skife.jdbi.v2.DBI;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


import javax.sql.DataSource;

public enum MySQLConnectionManager {
    INSTANCE();
    private boolean haveToRunTestCases = false;
    private boolean isIntegrationTest = false;
    private static final Logger log = LoggerFactory.getLogger(MySQLConnectionManager.class);
    private static String h2url = "jdbc:h2:mem:appsone;DB_CLOSE_DELAY=-1;MODE=MySQL;INIT=RUNSCRIPT FROM './src/test/resources/create.sql'\\;RUNSCRIPT FROM './src/test/resources/populate.sql'";
    private Integer poolSize = ConfProperties.getInt(Constants.MYSQL_DB_POOL_SIZE_PROPERTY_NAME,
            Constants.MYSQL_DB_POOL_SIZE_DEFAULT);
    private Integer maxPoolSize = ConfProperties.getInt(Constants.MYSQL_DB_MAX_POOL_SIZE_PROPERTY_NAME,
            Constants.MYSQL_DB_MAX_POOL_SIZE_DEFAULT);
    private String driverName = ConfProperties.getString(Constants.MYSQL_DB_DRIVER_NAME_PROPERTY_NAME,
            Constants.MYSQL_DB_DRIVER_NAME_DEFAULT);
    private String h2DriverName = ConfProperties.getString(Constants.H2_DB_DRIVER_NAME_PROPERT,
            Constants.H2_DB_DRIVER_NAME_PROPERT_DEFAULT);
    public static MySQLConnectionManager getInstance() {
        return INSTANCE;
    }

    private DBI dbi = null;

    public DBI getDbi() {
        return dbi;
    }

    public void setDbi(DBI dbi) {
        this.dbi = dbi;
    }

    public String getH2url() {
        return h2url;
    }

    public void setH2URL(String H2URL) {
        MySQLConnectionManager.h2url = H2URL;
    }

    public DBI getHandle()  {
        log.debug("Getting the DB Handle");
        if (dbi == null) {
            DataSource dataSource = createDataSource();
            dbi = new DBI(dataSource);
        }
        log.debug("Got the DB Handle, hence returning");
        return  dbi;
    }


    public <JdbiDao> JdbiDao open(Class<JdbiDao> dao) {
        if (dbi == null) {
            DataSource dataSource = createDataSource();
            dbi = new DBI(dataSource);
        }
        return dbi.onDemand(dao);
    }

    public <JdbiDao> void close(JdbiDao dao) {
        if (dbi != null) dbi.close(dao);
    }

    private DataSource createDataSource() {
        //for unit test cases
        if (haveToRunTestCases) return createTestDatasource();
        String url = ConfProperties.getString(Constants.MYSQL_DB_CONNECTION_URL_PROPERTY_NAME,
                Constants.MYSQL_DB_CONNECTION_URL_DEFAULT);
        String username = ConfProperties.getString(Constants.MYSQL_DB_USERNAME_PROPERTY_NAME,
                Constants.MYSQL_DB_USERNAME_PROPERTY_DEFAULT);
        String encryptedPassword = ConfProperties.getString(Constants.MYSQL_DB_AUTH_PROPERTY_NAME,
                Constants.MYSQL_DB_AUTH_PROPERTY_DEFAULT);

        HikariConfig hikariConfig = new HikariConfig();
        hikariConfig.setDriverClassName(driverName);
        hikariConfig.setJdbcUrl(url);
        hikariConfig.setUsername(username);
        hikariConfig.setPassword(CommonUtils.getDecryptedData(encryptedPassword));
        hikariConfig.setAutoCommit(true);
        hikariConfig.setMinimumIdle(poolSize);
        hikariConfig.setMaximumPoolSize(maxPoolSize);
        return new HikariDataSource(hikariConfig);

    }

    private DataSource createTestDatasource()   {
        HikariConfig hikariConfig = new HikariConfig();
        hikariConfig.setDriverClassName(h2DriverName);
        hikariConfig.setJdbcUrl(h2url);
        hikariConfig.setUsername("sa");
        hikariConfig.setPassword("");
        hikariConfig.setAutoCommit(true);
        hikariConfig.setMinimumIdle(2);
        hikariConfig.setMaximumPoolSize(maxPoolSize);
        return new HikariDataSource(hikariConfig);
    }

    public boolean setHaveToRunTestCases(boolean haveToRunTestCases) {
        this.haveToRunTestCases = haveToRunTestCases;
        return haveToRunTestCases;
    }

    public void setIntegrationTestStatus(boolean isIntegrationTest){
        log.info("integration test:::::::::::::::::::::::::");
        this.isIntegrationTest = isIntegrationTest;
    }

    public boolean isIntegrationTest(){
        return isIntegrationTest;
    }
}
