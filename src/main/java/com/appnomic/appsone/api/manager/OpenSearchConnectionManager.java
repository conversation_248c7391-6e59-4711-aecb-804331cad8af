package com.appnomic.appsone.api.manager;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.MasterRepo;
import com.appnomic.appsone.api.dao.redis.TenantRepo;
import com.appnomic.appsone.api.util.StringUtils;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.OSIndexZoneDetails;
import com.heal.configuration.pojos.TenantDetails;
import com.heal.configuration.pojos.TenantOpenSearchDetails;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.opensearch.client.RequestOptions;
import org.opensearch.client.RestClient;
import org.opensearch.client.RestClientBuilder;
import org.opensearch.client.RestHighLevelClient;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

@Slf4j
public enum OpenSearchConnectionManager {
    INSTANCE;

    private final ConcurrentMap<String, RestHighLevelClient> accountZoneOpenSearchConfigMap = new ConcurrentHashMap<>();

    public RestHighLevelClient getElasticClient(String accountIdentifier, String indexName) {
        String zone;
        List<OSIndexZoneDetails> osIndexZoneDetails = new MasterRepo().getHealIndexZones();
        if (osIndexZoneDetails == null || osIndexZoneDetails.isEmpty()) {
            log.warn("No OS Index to zone mappings found in redis. Defaulting to 'MISC' zone. Account:{}, index:{}", accountIdentifier, indexName);
            zone = "MISC";
        } else {
            OSIndexZoneDetails zoneDetails = osIndexZoneDetails.stream().filter(c -> c.getIndexName().equals(indexName) || indexName.startsWith(c.getIndexName())).findAny().orElse(null);
            if (zoneDetails == null) {
                log.warn("No zone found for index:{}, account:{}. Defaulting to 'MISC' zone.", indexName, accountIdentifier);
                zone = "MISC";
            } else {
                zone = zoneDetails.getZoneName();
            }
        }

        String key = accountIdentifier + "_" + zone;

        return accountZoneOpenSearchConfigMap.compute(key, (k, existingClient) -> {
            if (existingClient == null) {
                log.warn("OpenSearch Client is null! Need to re-initialize, key:{}", key);
            } else {
                try {
                    if (!existingClient.ping(RequestOptions.DEFAULT)) {
                        log.warn("Ping failed! Need to re-initialize OpenSearch Client, key:{}", key);
                        existingClient.close();
                    } else {
                        log.debug("OpenSearch Client is initialized for account {} and zone {}", accountIdentifier, zone);
                        return existingClient;
                    }
                } catch (Exception eox) {
                    try {
                        existingClient.close();
                    } catch (Exception e) {
                        log.error("Exception when closing OpenSearch client, key:{}", key, e);
                    }
                }
            }

            Account account = new AccountRepo().getAccount(accountIdentifier);
            if (account == null) {
                log.error("Account details not found for accountIdentifier: {}, index:{}", accountIdentifier, indexName);
                return null;
            }

            TenantDetails tenantDetails = account.getTenantDetails();
            if (tenantDetails == null) {
                log.error("Tenant details not found for accountIdentifier: {}, index:{}", accountIdentifier, indexName);
                return null;
            }

            List<TenantOpenSearchDetails> tenantOpenSearchDetailsList = new TenantRepo().getTenantOpenSearchDetails(tenantDetails.getTenantIdentifier());
            if (tenantOpenSearchDetailsList.isEmpty()) {
                log.error("OpenSearch mapping not found for tenantId: {}, account:{}, index:{}", tenantDetails.getTenantId(), accountIdentifier, indexName);
                return null;
            }

            TenantOpenSearchDetails tenantOpenSearchDetails = tenantOpenSearchDetailsList.stream()
                    .filter(c -> c.getZone().equals(zone)).findAny().orElse(null);
            if (tenantOpenSearchDetails == null) {
                log.error("No OpenSearch config found for account:{}, zone: {} and tenantId: {}", accountIdentifier, zone, tenantDetails.getTenantId());
                return null;
            }

            return createOpenSearchClient(tenantOpenSearchDetails, accountIdentifier, indexName);
        });
    }

    private RestHighLevelClient createOpenSearchClient(TenantOpenSearchDetails openSearchDetails, String accountIdentifier, String indexName) {
        RestHighLevelClient client;
        try {
            List<HttpHost> httpHosts = getHttpHosts(openSearchDetails);
            if (httpHosts.isEmpty()) {
                log.error("OpenSearch node details are empty. nodes:{}", openSearchDetails.getNodeAddresses());
                return null;
            }

            RestClientBuilder restClientBuilder = RestClient.builder(httpHosts.toArray(new HttpHost[0]));

            if (!StringUtils.isEmpty(openSearchDetails.getUsername()) && !StringUtils.isEmpty(openSearchDetails.getEncryptedPassword())) {
                final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();

                String decryptedPwd = new String(Base64.getDecoder().decode(openSearchDetails.getEncryptedPassword()), StandardCharsets.UTF_8);
                credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(openSearchDetails.getUsername(), decryptedPwd));

                restClientBuilder.setHttpClientConfigCallback(httpAsyncClientBuilder ->
                                httpAsyncClientBuilder.setDefaultCredentialsProvider(credentialsProvider)
                                        .setMaxConnPerRoute(openSearchDetails.getPerRouteConnections())
                                        .setMaxConnTotal(openSearchDetails.getMaxConnections()))
                        .setRequestConfigCallback(requestConfigBuilder -> requestConfigBuilder
                                .setConnectTimeout(openSearchDetails.getConnectionTimeout()).setSocketTimeout(openSearchDetails.getSocketTimeout()));
            } else {
                restClientBuilder.setHttpClientConfigCallback(httpAsyncClientBuilder ->
                                httpAsyncClientBuilder.setMaxConnPerRoute(openSearchDetails.getPerRouteConnections())
                                        .setMaxConnTotal(openSearchDetails.getMaxConnections()))
                        .setRequestConfigCallback(requestConfigBuilder -> requestConfigBuilder
                                .setConnectTimeout(openSearchDetails.getConnectionTimeout()).setSocketTimeout(openSearchDetails.getSocketTimeout()));
            }

            client = new RestHighLevelClient(restClientBuilder);

            if (client.ping(RequestOptions.DEFAULT)) {
                log.info("Established connection with OpenSearch nodes [{}] with protocol:{}, username:{}, account:{}, index:{} ",
                        openSearchDetails.getNodeAddresses(), openSearchDetails.getProtocol(), openSearchDetails.getUsername(), accountIdentifier, indexName);
            } else {
                throw new Exception(String.format("Unable to establish connection to OpenSearch nodes [%s]", openSearchDetails.getNodeAddresses()));
            }
        } catch (Throwable e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            client = null;
            log.error("Error while establishing connection to OpenSearch nodes [{}], Protocol:{}, username:{}, account:{}, index:{}",
                    openSearchDetails.getNodeAddresses(), openSearchDetails.getProtocol(), openSearchDetails.getUsername(), accountIdentifier, indexName, e);
        }

        return client;
    }

    private List<HttpHost> getHttpHosts(TenantOpenSearchDetails openSearchDetails) {
        List<String> nodesList = Arrays.asList(openSearchDetails.getNodeAddresses().split(","));
        List<HttpHost> httpHosts = new ArrayList<>();
        nodesList.forEach(node -> {
            try {
                String[] hostAndPort = node.split(":");
                int port = Integer.parseInt(hostAndPort[1]);
                HttpHost httpHost = new HttpHost(hostAndPort[0], port, openSearchDetails.getProtocol());
                httpHosts.add(httpHost);
            } catch (Exception e) {
                log.error("Invalid OpenSearch node details, Host: {}", node, e);
            }
        });
        return httpHosts;
    }
}

