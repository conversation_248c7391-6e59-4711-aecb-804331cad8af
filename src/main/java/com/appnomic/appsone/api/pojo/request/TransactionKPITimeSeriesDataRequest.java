package com.appnomic.appsone.api.pojo.request;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.pojo.Account;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.heal.configuration.pojos.BasicTransactionEntity;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR> on 20/05/22
 */
@Data
@NoArgsConstructor
@Slf4j
public class TransactionKPITimeSeriesDataRequest {

    private String accountIdString;
    private String serviceIdString;
    private String instanceIdString;
    private String responseType;
    private String txnIdString;
    private String kpiName;
    private String fromTimeString;
    private String toTimeString;
    private String requestType;
    private Account account;

    private List<String> agentList;
    private BasicTransactionEntity taggedTransaction;

    public TransactionKPITimeSeriesDataRequest(RequestObject request) {

        accountIdString = request.getParams().get(":identifier");
        serviceIdString = request.getParams().get(":serviceId");
        instanceIdString = request.getParams().get(":instanceId");
        responseType = request.getParams().get(":response-type");
        txnIdString = request.getParams().get(":txnId");
        kpiName = request.getParams().get(":kpi-identifier");
        fromTimeString = request.getQueryParams().get("fromTime")[0];
        toTimeString = request.getQueryParams().get("toTime")[0];
        requestType = StringUtils.isEmpty(request.getQueryParams().get("type")[0]) ? Constants.APPLICATION_CONTROLLER_TYPE : request.getQueryParams().get("type")[0];

    }

    public boolean validateParameters(TransactionKPITimeSeriesDataRequest TransactionDataRequest) {

        if (StringUtils.isEmpty(TransactionDataRequest.getAccountIdString())) {
            log.error("Account Id is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(TransactionDataRequest.getServiceIdString())) {
            log.error("Service Id is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(TransactionDataRequest.getInstanceIdString())) {
            log.error("Instance Id is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(TransactionDataRequest.getResponseType())) {
            log.error("Response type is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(TransactionDataRequest.getTxnIdString())) {
            log.error("Transaction Id is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(TransactionDataRequest.getKpiName())) {
            log.error("Kpi identifier is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(TransactionDataRequest.getFromTimeString())) {
            log.error("From time is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(TransactionDataRequest.getToTimeString())) {
            log.error("To time is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(TransactionDataRequest.getRequestType())) {
            log.error("Request type is null/empty");
            return false;
        }

        long fromTime;
        long toTime;
        try {
            Integer.valueOf(serviceIdString);
            Integer.valueOf(instanceIdString);
            Integer.valueOf(txnIdString);
            fromTime = Long.parseLong(fromTimeString);
            toTime = Long.parseLong(toTimeString);
        } catch (NumberFormatException e) {
            log.error("Invalid value found where number is required. {}", e.getMessage());
            return false;
        }

        if (fromTime <= 0 || toTime <= 0 || fromTime >= toTime) {
            log.error("Invalid time range provided");
            return false;
        }

        return true;
    }

}
