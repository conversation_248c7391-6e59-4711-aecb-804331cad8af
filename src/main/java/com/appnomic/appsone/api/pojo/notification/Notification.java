package com.appnomic.appsone.api.pojo.notification;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> : 24/5/19
 */
@Data
public class Notification {
    private double threshold;
    private String type;
    private int alertProfileId;
    private boolean emailEnabled;
    private String emailToAddress;
    private String emailCcAddress;
    private String emailBccAddress;
    private String subject;
    private String body;
    private boolean smsEnabled;
    private String mobileNumbers;
    private String message;

    public boolean getEmailEnabled() {
        return emailEnabled;
    }

    public void setEmailEnabled(boolean emailEnabled) {
        this.emailEnabled = emailEnabled;
    }

    public boolean getSmsEnabled() {
        return smsEnabled;
    }

    public void setSmsEnabled(boolean smsEnabled) {
        this.smsEnabled = smsEnabled;
    }
}
