package com.appnomic.appsone.api.pojo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> : 15/2/19
 */
@Data
public class AgentCompInstMappingDetails {
    private String accountId;
    private List<String> compInstIds;
    private List<Tags> tags;

    public void validate() throws Exception {
        if (this.tags != null && !this.tags.isEmpty()) {
            for (Tags tag : tags) {
                tag.validate();
            }
        }
    }
}
