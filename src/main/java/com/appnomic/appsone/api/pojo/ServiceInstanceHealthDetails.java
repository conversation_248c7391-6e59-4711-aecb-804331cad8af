package com.appnomic.appsone.api.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ServiceInstanceHealthDetails {
    private int id;
    private String name;
    private String componentName;
    private String hostAddress;
    private int availabilityStatus;
    private Long availableLastTime;

    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceInstanceHealthDetails.class);

    /*public boolean validate() {
        return true;
    }*/
}