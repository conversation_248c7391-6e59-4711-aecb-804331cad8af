package com.appnomic.appsone.api.pojo.request;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.cache.MasterCache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.TransactionRepo;
import com.appnomic.appsone.api.pojo.Account;
import com.appnomic.appsone.api.pojo.AllAccountDetails;
import com.appnomic.appsone.api.pojo.TxnAndGroupBean;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.heal.configuration.pojos.BasicTransactionEntity;
import lombok.Data;
import lombok.NoArgsConstructor;
import spark.Request;
import spark.Response;

import java.util.List;

@Data
@NoArgsConstructor
public class TransactionConfigRequest {
    private Response response;
    private Request request;
    private String accountIdentifier;
    private String transactionId;
    private com.heal.configuration.pojos.Account account;
    private BasicTransactionEntity transaction;

    public TransactionConfigRequest(Request request, Response response) {
        this.setResponse(response);
        this.setAccountIdentifier(request.params(":identifier"));
        this.setTransactionId(request.params(":transaction_id"));
    }

    public GenericResponse validateAndPopulate() {
        this.setAccount(new AccountRepo().getAccount(this.accountIdentifier));
        if( this.getAccount() == null ) {
            return CommonUtils.getGenericResponse(this.getResponse(), StatusResponse.FAILURE.toString(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, Constants.MESSAGE_INVALID_ACCOUNT,
                    null, true);
        }

        this.setTransaction(new TransactionRepo().getTransactionDetailsById(this.getAccount().getIdentifier(), Integer.parseInt(this.getTransactionId())));
        if( this.getTransaction() == null ) {
            return CommonUtils.getGenericResponse(this.getResponse(), StatusResponse.FAILURE.toString(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, Constants.MESSAGE_INVALID_TRANSACTION,
                    null, true);
        }

        return null;
    }
}
