package com.appnomic.appsone.api.pojo;

import com.appnomic.appsone.api.common.UIMessages;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;

@Data
@ToString
@NoArgsConstructor
public class RequestObject {

    private String body;
    private Map<String, String> params = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
    private Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
    private Map<String,String[]> queryParams = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);

    private static final Logger LOGGER = LoggerFactory.getLogger(RequestObject.class);

    private Map<String,String> cookies = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);

    public RequestObject(Request request) {
        if (request == null) {
            LOGGER.error(UIMessages.REQUEST_NULL);
            return;
        }

        this.body = request.body();
        this.params.putAll(request.params());
        if(request.queryMap() != null && request.queryMap().toMap() != null) {
            this.queryParams = new HashMap<>(request.queryMap().toMap());
        }
        Objects.requireNonNull(request.headers()).forEach(header -> this.headers.put(header, request.headers(header)));
        this.cookies.putAll(Objects.requireNonNull(request.cookies()));
    }
}
