package com.appnomic.appsone.api.pojo.request;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.util.StringUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
public class AnomalyIncidentRequest {
    String accountIdentifier;
    String serviceIdString;
    String signalIdentifier;
    int serviceId;

    public AnomalyIncidentRequest(RequestObject request) {

        accountIdentifier = request.getParams().get(Constants.REQUEST_PARAM_IDENTIFIER);
        serviceIdString = request.getParams().get(Constants.REQUEST_PARAM_SERVICE_ID);
        signalIdentifier = request.getParams().get(Constants.REQUEST_PARAM_SIGNAL_ID);

    }

    public boolean validateParameters() {

        if (StringUtils.isEmpty(this.accountIdentifier)) {
            log.error(Constants.MESSAGE_EMPTY_ACCOUNT);
            return false;
        }

        if (StringUtils.isEmpty(this.serviceIdString)) {
            log.error(UIMessages.SERVICE_EMPTY_ERROR);
            return false;
        } else {
            try {
                this.serviceId = Integer.parseInt(this.serviceIdString);
            } catch (NumberFormatException e) {
                log.error("Error occurred while converting the service id {}. Reason: {}", this.serviceIdString, e.getMessage());
                return false;
            }
        }

        if (StringUtils.isEmpty(this.signalIdentifier)) {
            log.error("Signal identifier is null or empty");
            return false;
        }
        return true;
    }
}
