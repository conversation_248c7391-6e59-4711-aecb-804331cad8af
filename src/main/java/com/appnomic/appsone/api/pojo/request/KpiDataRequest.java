package com.appnomic.appsone.api.pojo.request;

import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.util.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Slf4j
public class KpiDataRequest {
    private String accountId;
    private String kpiId;
    private String compInstanceId;
    private String fromTime;
    private String toTime;
    private String type;
    private String groupId;
    private String serviceId;

    public KpiDataRequest(RequestObject request) {
        accountId = request.getParams().get(":identifier");
        serviceId = request.getParams().get(":serviceId");
        compInstanceId = request.getParams().get(":instanceId");
        kpiId = request.getParams().get(":kpi_id");
        groupId = request.getParams().get(":group_id");
        fromTime = request.getQueryParams().get("fromTime")[0];
        toTime = request.getQueryParams().get("toTime")[0];
        type = request.getQueryParams().get("type") != null ? request.getQueryParams().get("type")[0] : "";
    }

    public boolean validateParameters(KpiDataRequest kpiDataRequest) {

        if (StringUtils.isEmpty(kpiDataRequest.getAccountId())) {
            log.error("Account Id is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(kpiDataRequest.getServiceId())) {
            log.error("Service Id is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(kpiDataRequest.getCompInstanceId())) {
            log.error("Instance Id is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(kpiDataRequest.getKpiId())) {
            log.error("Kpi Id is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(kpiDataRequest.getGroupId())) {
            log.error("Group Id is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(kpiDataRequest.getFromTime())) {
            log.error("From time is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(kpiDataRequest.getToTime())) {
            log.error("To time is null/empty");
            return false;
        }

        long fromTime;
        long toTime;
        try {
            Integer.valueOf(serviceId);
            Integer.valueOf(compInstanceId);
            Integer.valueOf(kpiId);
            Integer.valueOf(groupId);
            fromTime = Long.parseLong(kpiDataRequest.getFromTime());
            toTime = Long.parseLong(kpiDataRequest.getToTime());
        } catch (NumberFormatException e) {
            log.error("Invalid value found where number is required. {}", e.getMessage());
            return false;
        }

        if (fromTime <= 0 || toTime <= 0 || fromTime >= toTime) {
            log.error("Invalid time range provided");
            return false;
        }

        return true;
    }
}
