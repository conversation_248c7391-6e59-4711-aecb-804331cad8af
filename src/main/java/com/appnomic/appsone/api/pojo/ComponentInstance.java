package com.appnomic.appsone.api.pojo;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.util.DateTimeUtil;
import com.appnomic.appsone.api.util.StringUtils;
import lombok.Data;

import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> <PERSON> : 17/1/19
 */

@Data
public class ComponentInstance {
    private int id;
    private String name;
    private String identifier;
    private int status;
    private int isCluster;
    private int isHost;
    private String clusterName;
    private String mstComponentVersion;
    private String agentIdentifier;
    private String mstComponentName;
    private String mstComponentType;
    private int discovery;
    private List<KPI> kpi;
    private List<GroupKPIs> groupKpi;
    private List<Attributes> attributes;
    private List<Tags> tags;
    private Map<String, String> errorMessage = new HashMap<>();

    public void validateMandatoryFields() {
        if (this.name == null && this.identifier == null) errorMessage.put("name_identifier", "Name or identifier or both should be present in component instance json data");
        if (this.name != null && StringUtils.isEmpty(this.name)) errorMessage.put("name", "Name can not be empty in component instance json data");
        if (this.identifier != null && StringUtils.isEmpty(this.identifier)) errorMessage.put("identifier", "Identifier can not be empty in component instance json data");
        if (this.clusterName != null && StringUtils.isEmpty(this.clusterName)) errorMessage.put("clusterName", "Cluster name can not be empty in component instance json data");
        if (StringUtils.isEmpty(this.mstComponentName)) errorMessage.put("mstComponentName", "Component name can not be null or empty in component instance json data");
        if (StringUtils.isEmpty(this.mstComponentType)) errorMessage.put("mstComponentType", "Component type can not be null or empty in component instance json data");
        if (StringUtils.isEmpty(this.mstComponentVersion)) errorMessage.put("mstComponentVersion", "Component version can not be null or empty in component instance json data");
        if (this.agentIdentifier != null && StringUtils.isEmpty(this.agentIdentifier)) errorMessage.put("agentIdentifier", "Agent identifier can not be empty in component instance json data");
        List<KPI> kpiList = this.kpi;
        if (kpiList !=  null && !kpiList.isEmpty()) {
            for (KPI kpi : kpiList) {
                if (StringUtils.isEmpty(kpi.getName())) errorMessage.put("kpiList", "KPI name can not be null or empty in component instance json data");
                List<ViolationConfig> violationConfigList = kpi.getViolationConfig();
                if (violationConfigList != null && !violationConfigList.isEmpty()) {
                    for (ViolationConfig violationConfig : violationConfigList) {
                        validateKpiViolation(violationConfig);
                    }
                }
            }
        }
        List<Tags> tagsList = this.tags;
        if (tagsList != null && !tagsList.isEmpty()) {
            for (Tags tag : tagsList) {
                if (StringUtils.isEmpty(tag.getName())) errorMessage.put("tagName", "Tag name can not be null or empty in component instance json data");
                if (StringUtils.isEmpty(tag.getIdentifier())) errorMessage.put("tagIdentifier", "Tag Identifier can not be null or empty in component instance json data");
            }
        }
        List<GroupKPIs> groupKPIsList = this.getGroupKpi();
        if (groupKPIsList != null && !groupKPIsList.isEmpty()) {
            for (GroupKPIs groupKPIs : groupKPIsList) {
                if (StringUtils.isEmpty(groupKPIs.getKpiName())) errorMessage.put("groupKpiName", "Kpi group name can not be null or empty in component instance json data");
                List<Attributes> attributesValues = groupKPIs.getAttributes();
                if (attributesValues != null && !attributesValues.isEmpty()) {
                    for (Attributes s : attributesValues) {
                        if (s == null) errorMessage.put("groupKpiValues", "Attributes values can not be null or empty for group kpi in component instance json data");
                        else {
                            List<ViolationConfig> violationConfigList = s.getViolationConfig();
                            if (violationConfigList != null && !violationConfigList.isEmpty()) {
                                for (ViolationConfig violationConfig : violationConfigList) {
                                    validateKpiViolation(violationConfig);
                                }
                            }
                        }

                    }
                }
            }
        }

        List<Attributes> attributesList = this.getAttributes();
        if (attributesList != null && !attributesList.isEmpty()) {
            for (Attributes attribute : attributesList) {
                if (attribute.getName() ==null) errorMessage.put("attributeName", "Attribute name can not be null in component instance json data");
                if (attribute.getValue() == null) errorMessage.put("attributeValue", "Attribute value can not be null in component instance json data");
            }
            Attributes hostAttribute = attributesList.stream().filter(attributes -> attributes.getName().equals(Constants.HOST_ADDRESS)).findAny().orElse(null);
            if (hostAttribute == null) errorMessage.put("hostName", "'HostAddress' attribute is missing in component instance json data");
        }
    }

    private void validateKpiViolation(ViolationConfig violationConfig) {
        if (StringUtils.isEmpty(violationConfig.getProfileName())) errorMessage.put("profileName", "Profile name can not be null");
        if (StringUtils.isEmpty(violationConfig.getOperation())) errorMessage.put("operation", "Operation name can not be null");
        try{
            Double.valueOf(violationConfig.getMinThreshold());
        } catch (Exception e) {
            errorMessage.put("minThreshold", "minThreshold should have float/Integer value, given value-" + violationConfig.getMinThreshold());
        }
        try{
            Double.valueOf(violationConfig.getMaxThreshold());
        } catch (Exception e) {
            errorMessage.put("maxThreshold", "maxThreshold should have float/Integer value, given value-"+ violationConfig.getMaxThreshold());
        }
        String startTime = violationConfig.getStartTime();
        java.sql.Timestamp startTimestamp = null;
        if (!StringUtils.isEmpty(startTime)) {
            try {
                startTimestamp = DateTimeUtil.getTimestampInGMT(startTime);
            } catch (ParseException e) {
                errorMessage.put("startTimestamp", "startTimestamp is invalid, given value-"+ violationConfig.getStartTime() + " is not this format-'yyyy-MM-dd HH:mm:ss'");
            }
        }
        String endTime = violationConfig.getEndTime();
        java.sql.Timestamp endTimestamp = null;
        if (!StringUtils.isEmpty(endTime)) {
            try {
                endTimestamp = DateTimeUtil.getTimestampInGMT(endTime);
            } catch (ParseException e) {
                errorMessage.put("endTimestamp", "endTimestamp is invalid, given value-"+ violationConfig.getEndTime() + " is not this format-'yyyy-MM-dd HH:mm:ss'");
            }
        }

        if (startTime == null && endTime != null) errorMessage.put("start time", "Start time-" + violationConfig.getStartTime() + " should not be null or empty if end time is given");

        if ((startTime != null && endTime != null) && (startTimestamp != null && endTimestamp != null) && endTimestamp.before(startTimestamp)) {
            errorMessage.put("start-end time", "Start time-" + violationConfig.getStartTime() + " should be less than end time-" + violationConfig.getEndTime());
        }
    }

    public void putErrorMessage(String key, String message) {
        errorMessage.put(key, message);
    }
}
