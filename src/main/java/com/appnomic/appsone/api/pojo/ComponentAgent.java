package com.appnomic.appsone.api.pojo;

import com.appnomic.appsone.api.util.StringUtils;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> : 7/2/19
 */
@Data
public class ComponentAgent {
    private String agentIdentifier;
    private int timeoutMultiplier;
    private String configOperationMode;
    private String dataOperationMode;
    private int status;
    private Network dataCommunication;
    private List<Tags> tags;

    public void validate() throws Exception {
        if (this.agentIdentifier != null && agentIdentifier.isEmpty())
            throw new Exception("Agent identifier can not be null or empty.");
        if (StringUtils.isEmpty(this.configOperationMode))
            throw new Exception("Config operation mode can not be null or empty.");
        if (StringUtils.isEmpty(this.dataOperationMode))
            throw new Exception("Data operation mode can not be null or empty.");
        this.dataCommunication.validate();
        if (this.tags != null && !this.tags.isEmpty()) {
            for (Tags tag : this.tags) {
                tag.validate();
            }
        }
    }
}
