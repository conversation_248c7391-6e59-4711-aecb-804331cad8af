package com.appnomic.appsone.api.pojo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> : 18/1/19
 */
@Data
public class GroupKPIs {
    private int kpiMappingId;
    private String kpiName;
    private int kpiId;
    private int groupKpiId;
    private int discovery;
    private int collectionInterval;
    private List<Attributes> attributes;

    public GroupKPIs(){}

    public GroupKPIs(GroupKPIs groupKPIs) {
        this.kpiId = groupKPIs.getKpiId();
        this.groupKpiId = groupKPIs.getGroupKpiId();
        this.attributes = groupKPIs.getAttributes();
    }
}
