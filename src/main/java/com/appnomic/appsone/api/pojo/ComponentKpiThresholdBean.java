package com.appnomic.appsone.api.pojo;

import com.appnomic.appsone.api.util.DateTimeUtil;
import lombok.Data;

/**
 * <AUTHOR> : 26/03/2019
 */
@Data
public class ComponentKpiThresholdBean {
    private int id;
    private int componentId;
    private int kpiId;
    private int operationId;
    private Double minThreshold;
    private Double maxThreshold;
    private String userDetails;
    private int kpiGroupId;
    private String kpiGroupValue;
    private int coverageWindowProfileId;
    private String startTime;
    private long startTimeEpoch;
    private String endTime;
    private int mstCommonVersionId;

    public long getStartTimeEpoch() {
        this.startTimeEpoch = DateTimeUtil.getGMTToEpochTime(this.getStartTime());
        return this.startTimeEpoch;
    }
}
