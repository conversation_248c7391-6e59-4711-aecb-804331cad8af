package com.appnomic.appsone.api.pojo;

import com.appnomic.appsone.api.beans.TransactionGroupDetailBean;
import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class TxnAndGroupBean {

    private int txnId;
    private String txnName;
    private int txnStatus;
    private int isAutoEnabled;
    private int isAutoConfigured;
    private String userDetailsId;
    String transactionTypeName;
    String patternHashCode;
    String description;
    String identifier;
    private int isBusinessTransaction;
    private String tagListString;

    /**
     * The tag id is fetched from DB in form of concatenated string separated by comma, the same needs to be split and
     * used, the below get method does it.
     * @return
     */
    public List<Integer> getTagList() {
        if( tagListString != null ) return Arrays.stream(this.getTagListString().split(","))
                .map(Integer::parseInt)
                .collect(Collectors.toList());
        return new ArrayList<>();
    }
}
