package com.appnomic.appsone.api.pojo;

import com.appnomic.appsone.api.util.StringUtils;
import lombok.Data;

/**
 * <AUTHOR> : 7/2/19
 */
@Data
public class Network {
    private int id;
    private String name;
    private String protocol;
    private String type;
    private String host;
    private int port;
    private int status;
    private String description;

    public void validate() throws Exception {
        if (this.id != 0) {
            if (StringUtils.isEmpty(this.name)) throw new Exception("Data communication name can not be null or empty.");
            if (StringUtils.isEmpty(this.protocol)) throw new Exception("Data communication protocol can not be null or empty.");
            if (StringUtils.isEmpty(this.host)) throw new Exception("Data communication host can not be null or empty.");
            if (StringUtils.isEmpty(this.type)) throw new Exception("Type can not be null or empty.");
            if (this.port == 0) throw new Exception("Data communication port can not be zero");
        }
    }
}
