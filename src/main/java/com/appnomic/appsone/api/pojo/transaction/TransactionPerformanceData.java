package com.appnomic.appsone.api.pojo.transaction;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.pojo.KpiData;
import com.appnomic.appsone.api.pojo.TransactionStats;
import com.appnomic.appsone.api.util.ConfProperties;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class TransactionPerformanceData {
    private static final String COUNT_IDENTIFIER = ConfProperties.getString(Constants.KPI_UNIT_COUNT,
            Constants.KPI_UNIT_COUNT_DEFAULT);

    KpiData failedData = new KpiData();
    KpiData slowData = new KpiData();
    KpiData successData = new KpiData();
    KpiData timeoutData = new KpiData();
    KpiData unknownData = new KpiData();

    public TransactionPerformanceData() {
        failedData.setKpi_name("Fail");
        failedData.setUnit(COUNT_IDENTIFIER);
        slowData.setKpi_name("Slow");
        slowData.setUnit(COUNT_IDENTIFIER);
        successData.setKpi_name("Success");
        successData.setUnit(COUNT_IDENTIFIER);
        timeoutData.setKpi_name("Timedout");
        timeoutData.setUnit(COUNT_IDENTIFIER);
        unknownData.setKpi_name("Unknown");
        unknownData.setUnit(COUNT_IDENTIFIER);
    }

    public void add(TransactionStats stats) {
        if( stats != null ) {
            failedData.getValue().add(stats.getFail());
            slowData.getValue().add(stats.getSlow());
            successData.getValue().add(stats.getSuccess());
            timeoutData.getValue().add(stats.getTimeout());
            unknownData.getValue().add(stats.getUnknown());
        } else {
            failedData.getValue().add(null);
            slowData.getValue().add(null);
            successData.getValue().add(null);
            timeoutData.getValue().add(null);
            unknownData.getValue().add(null);
        }
    }

    public List<KpiData> getKpiDataList() {
        List<KpiData> kpiDataList = new ArrayList<>();
        kpiDataList.add(this.getFailedData());
        kpiDataList.add(this.getSlowData());
        kpiDataList.add(this.getSuccessData());
        kpiDataList.add(this.getTimeoutData());
        kpiDataList.add(this.getUnknownData());
        return kpiDataList;
    }
}
