package com.appnomic.appsone.api.pojo.request;

import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.util.StringUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> on 09/05/22
 */
@Data
@Slf4j
public class AvailabilityKpiRequest {

    private String accountId;
    private String serviceId;
    private String compInstanceId;
    private String fromTime;
    private String toTime;

    public AvailabilityKpiRequest(RequestObject request) {

        accountId = request.getParams().get(":identifier");
        serviceId = request.getParams().get(":serviceId");
        compInstanceId = request.getParams().get(":instanceId");
        fromTime = request.getQueryParams().get("fromTime")[0];
        toTime = request.getQueryParams().get("toTime")[0];

    }

    public boolean isValidParameters(AvailabilityKpiRequest availabilityKpi) {
        if (StringUtils.isEmpty(availabilityKpi.getAccountId())) {
            log.error("Account Id is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(availabilityKpi.getServiceId())) {
            log.error("Service Id is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(availabilityKpi.getCompInstanceId())) {
            log.error("Instance Id is null/empty");
            return false;
        }

        long fromTime;
        long toTime;
        try {
            Integer.valueOf(serviceId);
            Integer.valueOf(compInstanceId);
            fromTime = Long.parseLong(availabilityKpi.getFromTime());
            toTime = Long.parseLong(availabilityKpi.getToTime());
        } catch (NumberFormatException e) {
            log.error("Invalid value found where number is required. {}", e.getMessage());
            return false;
        }

        if (fromTime <= 0 || toTime <= 0 || fromTime >= toTime) {
            log.error("Invalid time range provided");
            return false;
        }

        return true;
    }

}
