package com.appnomic.appsone.api.pojo.request;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.ServiceRepo;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.BasicEntity;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

@Data
public class TransactionTagDataRequest {
    private static final Logger LOGGER = LoggerFactory.getLogger(TransactionTagDataRequest.class);
    private Request request;
    private Response response;
    private String accountIdString;
    private String serviceIdString;
    private com.heal.configuration.pojos.Account account;
    private BasicEntity serviceDetails;
    private int serviceId;

    public TransactionTagDataRequest(Request request, Response response) {
        this.request = request;
        this.response = response;
        this.accountIdString = request.params(":identifier");
        this.serviceIdString = request.params(":serviceId");
    }

    public GenericResponse validateAndPopulate() {

        Account accountData = new AccountRepo().getAccount(this.getAccountIdString());
        if( accountData == null ) {
            GenericResponse temp = new GenericResponse();
            temp.setResponseStatus(StatusResponse.FAILURE.toString());
            temp.setMessage(Constants.MESSAGE_INVALID_ACCOUNT);
            this.getResponse().status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            LOGGER.error(Constants.MESSAGE_INVALID_ACCOUNT);
            return temp;
        }

        this.setAccount(accountData);
        try {
            this.serviceId = Integer.parseInt(this.getServiceIdString());
        } catch (NumberFormatException ne) {
            LOGGER.error("Invalid number received as service id: {}.", this.getServiceIdString(), ne);
            return null;
        }
        BasicEntity serviceDetail = new ServiceRepo().getBasicServiceDetailsWithServiceId(accountData.getIdentifier(), serviceId);
        if( serviceDetail == null ) {
            GenericResponse temp = new GenericResponse();
            temp.setResponseStatus(StatusResponse.FAILURE.toString());
            temp.setMessage(Constants.MESSAGE_INVALID_SERVICE);
            this.getResponse().status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            LOGGER.error(Constants.MESSAGE_INVALID_SERVICE);
            return temp;
        }
        this.setServiceDetails(serviceDetail);

        LOGGER.debug("All fields in the request are valid");
        return null;
    }

}
