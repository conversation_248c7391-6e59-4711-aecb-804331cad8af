package com.appnomic.appsone.api.pojo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> : 21/05/2019
 */
@Data
public class TimeRangeDefinition {
    private long fromTime;
    private long toTime;
    private AggregationLevel aggregationLevel;
    private long firstGeneratedTimestamp;
    private long lastGeneratedTimestamp;
    private List<Long> generatedCompleteTimeSeries = new ArrayList<>();
    private List<LocalDateTime> generatedTimeSeriesLocalDateTime = new ArrayList<>();
    private boolean isStaticTimeRange = false;
    private int generatedTimePoints;
    private String timeRangeType;
}
