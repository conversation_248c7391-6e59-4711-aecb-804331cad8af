package com.appnomic.appsone.api.pojo.request;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.InstanceRepo;
import com.appnomic.appsone.api.dao.redis.KpiRepo;
import com.appnomic.appsone.api.pojo.*;
import com.appnomic.appsone.api.util.CommonUtils;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.CompInstKpiEntity;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.List;
import java.util.Optional;

@Data
public class ForensicGridDataRequest {
    private static final Logger LOGGER = LoggerFactory.getLogger(ForensicGridDataRequest.class);
    private Response response;
    private String accountIdString;
    private String compInstanceIdString;
    private String kpiIdString;
    private String groupValue;
    private String fromTimeString;
    private String toTimeString;
    private Account account;
    private com.heal.configuration.pojos.CompInstClusterDetails compInstanceDetails;
    private CompInstKpiEntity kpiData;
    private long fromTime;
    private long toTime;
    private int instanceId;
    private int kpiId;

    public ForensicGridDataRequest(Request request, Response response) {
        this.response = response;
        this.accountIdString = request.params(":identifier");
        this.compInstanceIdString = request.params("instanceId");
        this.kpiIdString = request.params("kpiId");
        this.groupValue = request.params("group-value").trim();
        //The character replacement is done to ensure that there is no failure due to the presence of / in the group-value
        if(!groupValue.isEmpty()) {
            this.groupValue = groupValue.replace("-@-", "/");
        }

        this.fromTimeString = request.queryParams("fromTime");
        this.toTimeString = request.queryParams("toTime");
    }

    public GenericResponse<List<KpiAnomalyData>> validateAndPopulate() {
        boolean validateInputStatus = validateInputFormat();

        if( ! validateInputStatus ) {
            return CommonUtils.getGenericResponse(this.getResponse(), StatusResponse.FAILURE.toString(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, Constants.MESSAGE_INVALID_PARAMETERS,
                    null, true);
        }

        this.setAccount(new AccountRepo().getAccount(this.getAccountIdString()));
        if( this.getAccount() == null ) {
            return CommonUtils.getGenericResponse(this.getResponse(), StatusResponse.FAILURE.toString(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, Constants.MESSAGE_INVALID_ACCOUNT,
                    null, true);
        }

        this.setCompInstanceDetails(getComponentInstanceDetails());
        if( this.getCompInstanceDetails() == null ) {
            return CommonUtils.getGenericResponse(this.getResponse(), StatusResponse.FAILURE.toString(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, Constants.MESSAGE_INVALID_COMP_INSTANCE,
                    null, true);
        }

        this.setKpiData(new KpiRepo().getKpiDetailByAccInstKpiId(this.account.getIdentifier(),this.getCompInstanceDetails().getIdentifier(),kpiId));
        if( this.getKpiData() == null ) {
            return CommonUtils.getGenericResponse(this.getResponse(), StatusResponse.FAILURE.toString(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, Constants.MESSAGE_INVALID_KPI,
                    null, true);
        }

        return null;
    }

    private com.heal.configuration.pojos.CompInstClusterDetails getComponentInstanceDetails() {
        try {
            InstanceRepo instanceRepo = new InstanceRepo();
            Optional<com.heal.configuration.pojos.CompInstClusterDetails> instance = instanceRepo.getInstancesByAccount(this.getAccount().getIdentifier())
                    .stream()
                    .filter(it -> (it.getId() == Integer.parseInt(this.getCompInstanceIdString())))
                    .findAny();
            if(instance.isPresent()) {
                return instance.get();
            }
        }   catch (Exception e) {
            LOGGER.error("Error occurred while fetching instance details",e);
        }
        return null;
    }

    private boolean validateInputFormat()   {
        if(this.accountIdString == null || this.compInstanceIdString == null || this.kpiIdString == null ||
        this.groupValue == null || this.fromTimeString == null || this.toTimeString == null ) {
            LOGGER.error("Mandatory field/s are missing. accId: {}, compInst: {}, kpiId: {}, groupVal: {}, " +
                    "from: {}, to:{}", accountIdString, compInstanceIdString, kpiIdString,groupValue,fromTimeString,
                    toTimeString);
            return false;
        }
        try {
            this.instanceId = Integer.parseInt(this.compInstanceIdString);
            this.kpiId = Integer.parseInt(this.kpiIdString);
            this.fromTime = Long.parseLong(this.fromTimeString);
            this.toTime = Long.parseLong(this.toTimeString);
        }   catch (NumberFormatException ne)    {
            LOGGER.error("Error occurred while validating input parameters, received invalid number format.",ne);
            return false;
        }   catch (Exception e) {
            LOGGER.error("Error occurred while validating input parameters.",e);
            return false;
        }
        return true;
    }
}
