package com.appnomic.appsone.api.pojo.request;

import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.util.StringUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> on 09/05/22
 */
@Data
@Slf4j
public class AvailabilityKpiDataRequest {

    private String accountId;
    private String compInstanceId;
    private String kpiId;
    private String groupId;
    private String fromTime;
    private String attribute;
    private String aggLevel;

    public AvailabilityKpiDataRequest(RequestObject request) {

        accountId = request.getParams().get(":identifier");
        compInstanceId = request.getParams().get(":instanceId");
        kpiId = request.getParams().get(":kpi_id");
        groupId = request.getParams().get(":group_id");
        fromTime = request.getQueryParams().get("fromTime")[0];
        attribute = request.getQueryParams().get("kpiAttribute")[0];
        aggLevel = request.getQueryParams().get("aggregation-level")[0];

    }

    public boolean isValidParameters(AvailabilityKpiDataRequest availabilityKpi) {
        if (StringUtils.isEmpty(availabilityKpi.getAccountId())) {
            log.error("Account Id is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(availabilityKpi.getCompInstanceId())) {
            log.error("Instance Id is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(availabilityKpi.getKpiId())) {
            log.error("Kpi Id is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(availabilityKpi.getGroupId())) {
            log.error("Group Id is null/empty");
            return false;
        }

        if (availabilityKpi.getAttribute() != null && availabilityKpi.getAttribute().trim().isEmpty()) {
            log.error("Attribute value is empty");
            return false;
        }

        if (Integer.parseInt(availabilityKpi.getAggLevel()) <= 1) {
            log.error("Aggregation Level should be more than 1");
            return false;
        }

        int grpId;
        try {
            Integer.parseInt(compInstanceId);
            Integer.parseInt(kpiId);
            grpId = Integer.parseInt(groupId);
            Long.parseLong(availabilityKpi.getFromTime());
            Integer.parseInt(aggLevel);
        } catch (NumberFormatException e) {
            log.error("Invalid value found where number is required. {}", e.getMessage());
            return false;
        }

        if (grpId != 0 && StringUtils.isEmpty(availabilityKpi.getAttribute())) {
            log.error("Attribute value can not be empty/null for group KPI.");
            return false;
        }

        return true;
    }

}
