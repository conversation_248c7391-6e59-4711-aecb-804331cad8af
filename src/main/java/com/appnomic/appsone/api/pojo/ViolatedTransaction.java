package com.appnomic.appsone.api.pojo;

import lombok.Data;

/**
 * <AUTHOR> Parekaden 12/02/2019
 */
@Data
public class ViolatedTransaction implements Comparable<ViolatedTransaction> {
    String transactionId="0";
    Long failCount = 0l;
    Long slowCount = 0l;
    Double slowPercentage=0.0d;
    Double failPercentage=0.0d;
    Long totalCount = 0l;
    Double avgResponse = 0.0d;
    Double value = 0.0d;

    public ViolatedTransaction add(ViolatedTransaction violatedTransaction, String violationType) {
        ViolatedTransaction result = new ViolatedTransaction();
        long failCount = this.getFailCount()+ violatedTransaction.getFailCount();
        long slowCount = this.getSlowCount()+ violatedTransaction.getSlowCount();
        long totalCount = this.getTotalCount()+ violatedTransaction.getTotalCount();
        if(!this.getTransactionId().equals(violatedTransaction.getTransactionId()))  {
            result.setTransactionId("0");
        }   else {
            result.setTransactionId(this.getTransactionId());
        }
        result.setFailCount(failCount);
        result.setSlowCount(slowCount);
        result.setTotalCount(totalCount);
        Double temp;
        if(ViolationType.SLOW_PERCENTAGE.name().equalsIgnoreCase(violationType))   {
            temp = (result.getSlowCount().doubleValue()/result.getTotalCount().doubleValue())*100.00;
        }   else if(ViolationType.FAILURE_PERCENTAGE.name().equalsIgnoreCase(violationType))   {
            temp = (result.getFailCount().doubleValue()/result.getTotalCount().doubleValue())*100.00;
        }   else {
            temp = this.getValue() + violatedTransaction.getValue();
        }
        //:TODO Place holder , functionality will be added later when integrating with UI
        result.setFailPercentage(this.failPercentage);
        result.setSlowPercentage(this.slowPercentage);
        //ODOT

        //Double failurePercent = Double.parseDouble(String.format("%.2f", temp));
        //result.setValue(failurePercent);
        result.setValue(temp);
        return result;
    }

    @Override
    public int compareTo(ViolatedTransaction o) {
        return this.getValue().compareTo(o.getValue());
    }
}
