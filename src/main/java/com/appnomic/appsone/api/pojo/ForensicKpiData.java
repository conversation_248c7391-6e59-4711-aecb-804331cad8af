package com.appnomic.appsone.api.pojo;

import lombok.Data;

import java.util.Map;

@Data
public class ForensicKpiData {

    private String name;
    private int id;
    private String measureUnit;
    private Map<String, String> values;

    public ForensicKpiData(int id, String name, String measureUnit){
        this.id = id;
        this.name = name;
        this.measureUnit = measureUnit;
    }
}
