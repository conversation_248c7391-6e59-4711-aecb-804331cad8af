package com.appnomic.appsone.api.pojo.request;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.util.StringUtils;
import com.heal.configuration.enums.SignalType;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

import static com.appnomic.appsone.api.common.LoggerTags.TAG_INVALID_FROM_TO_TIME;

@Data
@NoArgsConstructor
@Slf4j
public class SignalDataRequest {

    String accountIdentifier;
    String serviceIdString;
    String signalId;
    String status;
    String fromTimeString;
    String toTimeString;
    int serviceId;
    String signalType;
    long fromTime;
    long toTime;
    long actualFromTime;
    Set<String> signalTypeSet = new HashSet<>();


    public SignalDataRequest(RequestObject request) {

        accountIdentifier = request.getParams().get(Constants.REQUEST_PARAM_IDENTIFIER);
        serviceIdString = request.getParams().get(Constants.REQUEST_PARAM_SERVICE_ID);
        signalId = request.getParams().get(Constants.REQUEST_PARAM_SIGNAL_ID);
        status = request.getQueryParams().containsKey(Constants.STATUS_TAG) ? request.getQueryParams().get(Constants.STATUS_TAG)[0] : null;
        signalType = request.getQueryParams().containsKey(Constants.REQUEST_SIGNAL_TYPE) ? request.getQueryParams().get(Constants.REQUEST_SIGNAL_TYPE)[0] : null;
        fromTimeString = request.getQueryParams().containsKey(Constants.REQUEST_PARAM_FROM_TIME) ? request.getQueryParams().get(Constants.REQUEST_PARAM_FROM_TIME)[0] : null;
        toTimeString = request.getQueryParams().containsKey(Constants.REQUEST_PARAM_TO_TIME) ? request.getQueryParams().get(Constants.REQUEST_PARAM_TO_TIME)[0] : null;

    }

    public boolean validateParameters() {

        if (StringUtils.isEmpty(this.accountIdentifier)) {
            log.error("Account Identifier is null/empty");
            return false;
        }

        if (!StringUtils.isEmpty(this.serviceIdString)) {
            try {
                this.serviceId = Integer.parseInt(this.serviceIdString);
            } catch (NumberFormatException e) {
                log.error("Invalid value found for service id where number is required. {}", e.getMessage());
                return false;
            }
        }

        if (!StringUtils.isEmpty(this.getSignalType())) {

            Arrays.stream(this.getSignalType().split(",")).forEach(c -> {
                if (c.trim().equalsIgnoreCase("LEAD")) {
                    signalTypeSet.add(SignalType.EARLY_WARNING.name());
                    signalTypeSet.add(SignalType.PROBLEM.name());
                } else if (c.trim().equalsIgnoreCase("INFO")) {
                    signalTypeSet.add(SignalType.INFO.name());
                } else if (c.trim().equalsIgnoreCase("BATCH_JOB")) {
                    signalTypeSet.add(SignalType.BATCH_JOB.name());
                } else if (c.trim().equalsIgnoreCase("EARLY_WARNING")) {
                    signalTypeSet.add(SignalType.EARLY_WARNING.name());
                } else if (c.trim().equalsIgnoreCase("PROBLEM")) {
                    signalTypeSet.add(SignalType.PROBLEM.name());
                }
            });

            if (signalTypeSet.isEmpty()) {
                log.error("Unsupported Signal Type Provided in request. {}", this.signalType);
                return false;
            }
        }

        if (!StringUtils.isEmpty(this.fromTimeString) && !StringUtils.isEmpty(this.toTimeString)) {
            try {
                this.fromTime = Long.parseLong(this.fromTimeString);
                this.toTime = Long.parseLong(this.toTimeString);
            } catch (NumberFormatException e) {
                log.error("Invalid value found for fromTime/endTime  where number is required. {}", e.getMessage());
                return false;
            }

            if (this.fromTime <= 0 || this.toTime <= 0 || this.fromTime > this.toTime) {
                log.error(TAG_INVALID_FROM_TO_TIME);
                return false;
            }
        }

        return true;
    }
}
