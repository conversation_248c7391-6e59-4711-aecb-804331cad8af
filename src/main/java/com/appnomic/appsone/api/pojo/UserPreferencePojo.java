package com.appnomic.appsone.api.pojo;

import com.appnomic.appsone.api.util.StringUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

@Data
public class UserPreferencePojo {
    int id;
    String tagKey;
    String tagValue;

    @JsonIgnore
    private Map<String, String> error = new HashMap<>();

    private static final Logger logger = LoggerFactory.getLogger(UserPreferencePojo.class);

    public void validate() {
        if (StringUtils.isEmpty(tagKey))
            error.put("tagKey", "tagKey is empty");
        if (StringUtils.isEmpty(tagValue))
            error.put("tagValue", "tagValue is empty");
    }

    @Override
    public int hashCode() {
        return 1;
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof UserPreferencePojo))
            return false;

        UserPreferencePojo userPreferencePojo = (UserPreferencePojo) obj;
        return ((userPreferencePojo.tagKey == tagKey) && (userPreferencePojo.tagValue == tagValue));
    }
}
