package com.appnomic.appsone.api.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ClusterDetails {

    Integer id;
    String name;
    String type;
    String compType;
    String compName;
    String compVersionName;
    String behaviourType;
    List<KpiNames> KPI;
    List<KpiCategory> categories = new ArrayList<>();

}
