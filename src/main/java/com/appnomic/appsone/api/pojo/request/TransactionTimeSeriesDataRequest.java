package com.appnomic.appsone.api.pojo.request;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.InstanceRepo;
import com.appnomic.appsone.api.dao.redis.ServiceRepo;
import com.appnomic.appsone.api.pojo.*;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.BasicTransactionEntity;
import com.heal.configuration.pojos.TimeRangeDetails;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
public class TransactionTimeSeriesDataRequest {
    private static final Logger LOGGER = LoggerFactory.getLogger(TransactionTimeSeriesDataRequest.class);
    private static final String RESPONSE_TYPE_DEFAULT = ConfProperties.getString(Constants.TRANSACTION_TYPE,
            Constants.TRANSACTION_TYPE_DEFAULT);

    private Request request;
    private Response response;
    private String accountIdString;
    private String serviceIdString;
    private String instanceIdString;
    private String responseTypeString;
    private String fromTimeString;
    private String toTimeString;
    private String kpiNameString;
    private String requestTypeString;
    private String tagIdString;

    private Account account;
    private BasicEntity serviceDetails;
    private com.heal.configuration.pojos.CompInstClusterDetails instanceDetails;
    private List<String> agentList;
    private KPINameDetails kpiNameDetails;
    private long fromTime;
    private long toTime;
    private int serviceId;
    private int tagId;
    private int instanceId;
    private List<BasicTransactionEntity> taggedTransaction;

    private TimeRangeDetails timeRangeDetails;
    private List<TimeRangeDetails> timeRangeDetailsList;

    public TransactionTimeSeriesDataRequest(Request request, Response response) {
        this.request = request;
        this.response = response;
        this.accountIdString = request.params(":identifier");
        this.instanceIdString = request.params(":instanceId");
        this.responseTypeString = request.params(":response-type");
        this.fromTimeString = request.queryParams("fromTime");
        this.toTimeString = request.queryParams("toTime");
        this.kpiNameString = request.params(":kpi-identifier");
        this.serviceIdString = request.params(":controllerId");
        this.requestTypeString = request.queryParams("type");
        this.tagIdString = request.queryParams("tagId");
    }

    public GenericResponse validateAndPopulate() {
        GenericResponse genericResponse = null;

        genericResponse = validateAccount();
        if( genericResponse != null ) return genericResponse;

        genericResponse = validateNumericInput();
        if( genericResponse != null ) return genericResponse;

        genericResponse = validateServiceId();
        if( genericResponse != null ) return genericResponse;

        genericResponse = validateInstance();
        if( genericResponse != null ) return genericResponse;

        genericResponse = validateGroupIdAndResponseType();
        if( genericResponse != null ) return genericResponse;

        genericResponse = populateAgentList();
        if( genericResponse != null ) return genericResponse;

        genericResponse = validateKpiType();
        if( genericResponse != null ) return genericResponse;

        populateTaggedTransaction();

        return null;
    }

    private GenericResponse validateKpiType() {
        try {
            kpiNameDetails = KPINameDetails.valueOf(this.getKpiNameString());
        } catch(IllegalArgumentException | NullPointerException e) {
            GenericResponse genericResponse = new GenericResponse();
            genericResponse.setMessage(Constants.MESSAGE_INVALID_PARAMETERS);
            genericResponse.setResponseStatus(StatusResponse.FAILURE.toString());
            this.getResponse().status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            LOGGER.error("Invalid kpi name provided: {}", this.getRequestTypeString());
            return genericResponse;
        }
        return null;
    }

    private void populateTaggedTransaction() {
        long start = System.currentTimeMillis();
        ServiceRepo serviceRepo = new ServiceRepo();
        BasicEntity service = serviceRepo.getBasicServiceDetailsWithServiceId(this.getAccount().getIdentifier(), this.getServiceId());
        if(service == null) {
            LOGGER.warn("Service id does not exist. AccountId:{}, ServiceId:{}", this.getAccount().getIdentifier(), this.getServiceId());
            return;
        }
        List<BasicTransactionEntity> transactions = new ServiceRepo().getTransactionsByServiceIdentifier(this.getAccount().getIdentifier(), service.getIdentifier())
                .parallelStream()
                .filter(t -> t.getStatus() == 1)
                .collect(Collectors.toList());
        LOGGER.debug("Found {} mapped transactions for the serviceId: {}.", transactions.size(),
                this.getServiceId());
        if( this.getTagId() == 0 ) {
            this.taggedTransaction = transactions;
        } else {
            this.taggedTransaction = transactions.stream()
                    .filter( txn -> (txn.getTransactionGroups().stream().anyMatch(it -> it.getTransactionGroupId() == tagId)))
                    .collect(Collectors.toList());
        }

        LOGGER.debug("Found {} transactions for the given tagId: {} in service: {}",
                this.getTaggedTransaction().size(), this.getTagId(), this.getServiceId());
        LOGGER.debug("Time taken to fetch transaction configuration is: {} ms.", (System.currentTimeMillis() - start));
    }

    private GenericResponse populateAgentList() {
        if(this.getRequestTypeString().trim().equalsIgnoreCase(Constants.SERVICES_CONTROLLER_TYPE)) {
            return null;

        } else if( this.getRequestTypeString().trim().equalsIgnoreCase(Constants.COM_INSTANCE_TYPE)) {

            agentList = instanceDetails.getAgentIds();

        } else {
            LOGGER.error("Invalid request type provided: {}", this.getRequestTypeString());
            return CommonUtils.getGenericResponse(this.getResponse(), StatusResponse.FAILURE.toString(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, Constants.MESSAGE_INVALID_PARAMETERS,
                    null, true);
        }
        if( agentList == null || agentList.isEmpty() ) {
            LOGGER.error("Unable to fetch agents for given service/comp instance: {}", this.getServiceId());
        }
        return null;
    }

    private GenericResponse validateGroupIdAndResponseType() {
        if( !ValidationUtils.isValidTxnResponseType(this.getAccount().getId(), this.getResponseTypeString())) {
            LOGGER.error("Invalid response type provided: {}", this.getResponseTypeString());
            return CommonUtils.getGenericResponse(this.getResponse(), StatusResponse.FAILURE.toString(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, Constants.MESSAGE_INVALID_PARAMETERS,
                    null, true);
        }
        return null;
    }

    private GenericResponse validateServiceId() {
        this.setServiceDetails(new ServiceRepo().getBasicServiceDetailsWithServiceId(this.getAccount().getIdentifier(), this.getServiceId()));
        if( this.getServiceDetails() == null ) {
            return CommonUtils.getGenericResponse(this.getResponse(), StatusResponse.FAILURE.toString(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, Constants.MESSAGE_INVALID_SERVICE,
                    null, true);
        }
        return null;
    }

    private GenericResponse validateNumericInput() {
        try {

            this.serviceId = Integer.parseInt(this.getServiceIdString());
            this.fromTime = Long.parseLong(this.getFromTimeString());
            this.toTime = Long.parseLong(this.getToTimeString());
            this.tagId = Integer.parseInt(this.getTagIdString());
            this.instanceId = Integer.parseInt(this.getInstanceIdString());

        } catch (NumberFormatException ne ) {
            LOGGER.error("Invalid numeric input received: ",ne);
            return CommonUtils.getGenericResponse(this.getResponse(), StatusResponse.FAILURE.toString(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, Constants.MESSAGE_INVALID_PARAMETERS,
                    null, true);
        }
        return null;
    }

    private GenericResponse validateInstance() {
        if( this.getInstanceId() == 0 && ! Constants.COM_INSTANCE_TYPE.equalsIgnoreCase(this.getRequestTypeString())) {
            return null;
        }
        com.heal.configuration.pojos.CompInstClusterDetails compInstance = HealUICache.INSTANCE.getServiceInstanceList(this.getAccount().getIdentifier(), this.getServiceDetails().getIdentifier(), true)
                .stream()
                .filter(i -> i.getId() == this.getInstanceId())
                .map(b -> new InstanceRepo().getInstanceDetailsWithInstIdentifier(this.getAccount().getIdentifier(), instanceDetails.getIdentifier()))
                .findAny()
                .orElse(null);

        if( compInstance == null ) {
            return CommonUtils.getGenericResponse(this.getResponse(), StatusResponse.FAILURE.toString(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, Constants.MESSAGE_INVALID_COMP_INSTANCE,
                    null, true);
        }
        this.setInstanceDetails(compInstance);
        return null;
    }

    private GenericResponse validateAccount() {
        this.setAccount(new AccountRepo().getAccount(this.getAccountIdString()));
        if( this.getAccount() == null ) {
            return CommonUtils.getGenericResponse(this.getResponse(), StatusResponse.FAILURE.toString(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, Constants.MESSAGE_INVALID_ACCOUNT,
                    null, true);
        }
        return null;
    }

    public List<TransactionQueryParams> getQueryParams(int aggLevel, long from, long to) {
        List<TransactionQueryParams> result = new ArrayList<>();
        if(this.getRequestTypeString().trim().equalsIgnoreCase(Constants.SERVICES_CONTROLLER_TYPE)) {
            TransactionQueryParams temp = TransactionQueryParams.builder()
                    .aggLevel(aggLevel)
                    .accountId(this.getAccount().getIdentifier())
                    .serviceId(this.getServiceDetails().getIdentifier())
                    .responseType(this.getResponseTypeString())
                    .fromTime(from)
                    .toTime(to)
                    .build();
            result.add(temp);

        } else {
            agentList.forEach( agent -> {
                TransactionQueryParams temp = TransactionQueryParams.builder()
                        .aggLevel(aggLevel)
                        .accountId(this.getAccount().getIdentifier())
                        .agentId(agent)
                        .responseType(this.getResponseTypeString())
                        .fromTime(from)
                        .toTime(to)
                        .build();
                result.add(temp);
            });
        }
        return result;
    }
}
