package com.appnomic.appsone.api.pojo;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class NotificationsPojo {
    private Map<String, List<NotificationTypePojo>> metaData;
    private List<UserPreferencesPojo> preferences;
    private boolean emailNotification;
    private boolean smsNotification;
    private NotificationChoice notificationChoice;
    private UserAttributesPojo userAttributes;
}
