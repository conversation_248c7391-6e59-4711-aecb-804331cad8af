package com.appnomic.appsone.api.pojo;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.util.StringUtils;
import lombok.Data;

/**
 * <AUTHOR> : 11/3/19
 */
@Data
public class TransactionResponseThresholdPojo {
    private String transactionKpiType;
    private String responseTimeType;
    private int slowThresholdValue = Constants.SLOW_THRESHOLD_VALUE;
    private String startTime;
    private String endTime;
    private String profileName;
    private int profileId;
    public void validate() throws Exception {
         if (StringUtils.isEmpty(this.transactionKpiType)) throw new Exception("transactionKpiType can not be null or empty");
        if (StringUtils.isEmpty(this.responseTimeType)) throw new Exception("responseTimeType can not be null or empty");
        if (StringUtils.isEmpty(this.profileName)) throw new Exception("Profile name can not be null or empty");

    }
}

