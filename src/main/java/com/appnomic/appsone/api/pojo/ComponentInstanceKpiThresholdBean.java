package com.appnomic.appsone.api.pojo;

import com.appnomic.appsone.api.util.DateTimeUtil;
import lombok.Data;

/**
 * <AUTHOR> : 26/03/2019
 */
@Data
public class ComponentInstanceKpiThresholdBean {
    private int id;
    private int componentInstanceId;
    private int kpiId;
    private int operationId = 193;
    private Double minThreshold;
    private Double maxThreshold;
    private String userDetailsId;
    private int kpiGroupId;
    private String kpiGroupValue;
    private int coverageWindowProfileId = 1;
    private String startTime;
    private long startTimeEpoch;
    private String endTime;
    private int accountId;

    public long getStartTimeEpoch() {
        this.startTimeEpoch = DateTimeUtil.getGMTToEpochTime(this.getStartTime());
        return startTimeEpoch;
    }
}
