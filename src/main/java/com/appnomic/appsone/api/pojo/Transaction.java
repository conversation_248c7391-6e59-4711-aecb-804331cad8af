package com.appnomic.appsone.api.pojo;


import com.appnomic.appsone.api.beans.SubTransactionBean;
import com.appnomic.appsone.api.beans.TransactionBean;
import com.appnomic.appsone.api.dao.TagsDao;
import com.appnomic.appsone.api.dao.redis.MasterRepo;
import com.appnomic.appsone.api.util.DateTimeUtil;
import com.appnomic.appsone.api.util.StringUtils;
import lombok.Data;

import java.text.ParseException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Parekaden : 23/1/19
 */
@Data
public class Transaction {
    private TransactionType txnType;
    private String txnName;
    private String txnIdentifier;
    private String description = "No description available";
    private Boolean isAuditEnabled = false;
    private Boolean isAutoConfigured = false;
    private Boolean isRawEnabled = false;
    private List<TransactionViolationConfig> txnThresholds;
    private List<TransactionResponseThresholdPojo> txnResponseThresholds;
    private List<SubTransaction> subTransactions;
    private List<BizValueExtractor> bizValueExtractorList;
    private List<TransactionAuditDetails> txnAuditDetails;
    private List<Tags> tags;
    private ResponseMessage responseMessage = new ResponseMessage();

    public boolean isValid(int accountId, String userId, TagsDao tagsDao) {
        try {
            /**
             * check if mandatory fields are provided
             */
            if (this.getTxnType() == null || this.getTxnType().name().trim().isEmpty()) {
                this.getResponseMessage().getMessages().put("txnType", "txnType(Mandatory): is null or empty");
            }
            /**
             * txn name or identifier atleast one should be present
             */
            if ((this.getTxnName() == null || this.getTxnName().trim().isEmpty()) &&
                    (this.getTxnIdentifier() == null || this.getTxnIdentifier().trim().isEmpty())) {
                this.getResponseMessage().getMessages().put("txnName|txnIdentifier", "txnName|txnIdentifier( atleast one is Mandatory): is null or empty");
            }
            //if(this.getTxnGrp() == null || this.getTxnGrp().trim().length() == 0) {this.getResponseMessage().getMessages().put("txnGrp","txnGrp(Mandatory): is null or empty");}
            if (this.getSubTransactions() == null || this.getSubTransactions().isEmpty()) {
                this.getResponseMessage().getMessages().put("subTransactions", "subTransactions(Mandatory): is null or empty");
            }

            /**
             * check in tags section if there is application tag(mandatory) and only 1 should be there
             */
            if (this.getTags() == null ||
                    this.getTags().isEmpty() ||
                    (this.getTags().stream()
                            .filter(tag -> (tag.getSubTypeName().trim()
                                    .equalsIgnoreCase("Application")))
                            .count() != 1)) {
                this.getResponseMessage().getMessages().put("Tags", "Application tag is mandatory and only 1 should be present.");
            }
            setNameAndIdentifier();
            validateTxnThresholdJson();
            validateTxnResponseJson();
        } catch (Exception e) {

        }
        return (this.getResponseMessage() == null || this.getResponseMessage().getMessages().isEmpty());
    }

    public String getHashCode() {
        setNameAndIdentifier();
        String hashCodegeneratingString = this.getTxnName().trim() + "," + this.getTxnIdentifier().trim();
        List<SubTransaction> subTransactionList = this.getSubTransactions();

        for (SubTransaction subTransaction : subTransactionList) {
            if (subTransaction.getTcpTxnConfig() != null) {

                if (subTransaction.getTcpTxnConfig().getTcpStartPattern() != null)
                    hashCodegeneratingString += "," + subTransaction.getTcpTxnConfig().getTcpStartPattern().trim();

                if (subTransaction.getTcpTxnConfig().getTcpEndPattern() != null)
                    hashCodegeneratingString += "," + subTransaction.getTcpTxnConfig().getTcpEndPattern().trim();

                if (subTransaction.getTcpTxnConfig().getLength() != null)
                    hashCodegeneratingString += "," + subTransaction.getTcpTxnConfig().getLength().toString();
            } else if (subTransaction.getHttpTxnConfig() != null) {

                if (subTransaction.getHttpTxnConfig().getType() != null)
                    hashCodegeneratingString += "," + subTransaction.getHttpTxnConfig().getType().trim();

                if (subTransaction.getHttpTxnConfig().getUrlPattern() != null)
                    hashCodegeneratingString += "," + subTransaction.getHttpTxnConfig().getUrlPattern().trim();

                if (subTransaction.getHttpTxnConfig().getHeaderPattern() != null)
                    hashCodegeneratingString += "," + subTransaction.getHttpTxnConfig().getHeaderPattern().trim();

                if (subTransaction.getHttpTxnConfig().getBodyPattern() != null)
                    hashCodegeneratingString += "," + subTransaction.getHttpTxnConfig().getBodyPattern().trim();

                if (subTransaction.getHttpTxnConfig().getQueryParam() != null && !subTransaction.getHttpTxnConfig().getQueryParam().isEmpty()) {

                    for (Map<String, String> queryParameters : subTransaction.getHttpTxnConfig().getQueryParam()) {
                        final String[] queryParamString = {""};
                        if (queryParameters != null) {
                            queryParameters.forEach((key, value) -> {
                                queryParamString[0] += "," + key;
                                queryParamString[0] += "," + value;
                            });
                        }
                        hashCodegeneratingString = queryParamString[0];
                    }
                }
            }
        }

        String[] hashingArray = hashCodegeneratingString.split(",");
        Arrays.sort(hashingArray);
        String sortedString = Arrays.toString(hashingArray);
        Integer hashCode = sortedString.hashCode();
        return hashCode.toString();
    }

    /**
     * This method will pares a valid JSON received from user and then produce the required bean
     *
     * @param accountId
     * @param userId
     * @return
     */
    public TransactionBean getTransactionBean(Integer accountId, String userId) {
        setNameAndIdentifier();
        TransactionBean transactionBean = new TransactionBean();
        transactionBean.setName(this.getTxnName().trim());
        transactionBean.setIdentifier(this.getTxnIdentifier().trim());
        transactionBean.setStatus(1);
        transactionBean.setAuditEnabled(this.getIsAuditEnabled() ? 1 : 0);
        transactionBean.setIsAutoConfigured(this.getIsAutoConfigured() ? 1 : 0);
        transactionBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        transactionBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        transactionBean.setUserDetailsId(userId.trim());
        transactionBean.setAccountId(accountId);
        com.heal.configuration.pojos.ViewTypes subType = new MasterRepo().getTypes().stream()
                .filter(v -> v.getTypeName().equalsIgnoreCase("Transaction"))
                .filter(v -> v.getSubTypeName().equalsIgnoreCase(this.getTxnType().name()))
                .findAny().orElse(null);
        if (subType == null)
            return null;
        transactionBean.setTransactionTypeId(subType.getSubTypeId());
        transactionBean.setPatternHashcode(this.getHashCode());
        if (this.getDescription() != null && !this.getDescription().trim().isEmpty())
            transactionBean.setDescription(this.getDescription().trim());

        List<SubTransaction> subTransactionList = this.getSubTransactions();
        for (SubTransaction subTransaction : subTransactionList) {
            SubTransactionBean subTransactionBean = subTransaction.getSubTransactionBean(userId, this.getTxnType().name().trim());
            transactionBean.getSubTransaction().add(subTransactionBean);
        }
        return transactionBean;
    }

    private void setNameAndIdentifier() {
        if ((this.getTxnName() == null || this.getTxnName().trim().isEmpty()) &&
                (this.getTxnIdentifier() != null && !this.getTxnIdentifier().trim().isEmpty())) {
            this.setTxnName(this.getTxnIdentifier().trim());
        } else if ((this.getTxnIdentifier() == null || this.getTxnIdentifier().trim().isEmpty()) &&
                (this.getTxnName() != null && !this.getTxnName().trim().isEmpty())) {
            this.setTxnIdentifier(this.getTxnName().trim());
        }
    }

    private void validateTxnThresholdJson() {
        if (this.txnThresholds == null || this.txnThresholds.isEmpty()) {
            return;
        }

        for (TransactionViolationConfig config : this.txnThresholds) {
            if (StringUtils.isEmpty(config.getProfileName()))
                this.getResponseMessage().getMessages().put("profile name", "Profile name should not be empty or null");
            if (StringUtils.isEmpty(config.getOperation()))
                this.getResponseMessage().getMessages().put("Operation", "Operation should not be empty or null");
            String minThreshold = config.getMinThreshold();
            try {
                Float.valueOf(minThreshold);
            } catch (Exception e) {
                this.getResponseMessage().getMessages().put("Min Threshold", "Invalid Min Threshold -" + minThreshold + " " + e.getMessage());
            }

            String maxThreshold = config.getMaxThreshold();
            try {
                Float.valueOf(maxThreshold);
            } catch (Exception e) {
                this.getResponseMessage().getMessages().put("Max Threshold", "Invalid Min Threshold -" + maxThreshold + " " + e.getMessage());
            }
            if (StringUtils.isEmpty(config.getTransactionKpiType()))
                this.getResponseMessage().getMessages().put("Transaction Kpi Type", "Transaction Kpi Type should not be empty or null");
            if (StringUtils.isEmpty(config.getResponseTimeType()))
                this.getResponseMessage().getMessages().put("ResponseTime Type", "ResponseTime Type should not be empty or null");

            String startTime = config.getStartTime();
            java.sql.Timestamp startTimestamp = null;
            if (!StringUtils.isEmpty(startTime)) {
                try {
                    startTimestamp = DateTimeUtil.getTimestampInGMT(startTime);
                } catch (ParseException e) {
                    this.getResponseMessage().getMessages().put("startTimestamp", "startTimestamp is invalid, given value-" + config.getStartTime() + " is not this format-'yyyy-MM-dd HH:mm:ss'");
                }
            }
            String endTime = config.getEndTime();
            java.sql.Timestamp endTimestamp = null;
            if (!StringUtils.isEmpty(endTime)) {
                try {
                    endTimestamp = DateTimeUtil.getTimestampInGMT(endTime);
                } catch (ParseException e) {
                    this.getResponseMessage().getMessages().put("endTimestamp", "endTimestamp is invalid, given value-" + config.getEndTime() + " is not this format-'yyyy-MM-dd HH:mm:ss'");
                }
            }
            if (startTime == null && endTime != null)
                this.getResponseMessage().getMessages().put("start time", "Start time-" + config.getStartTime() + " should not be null or empty if end time is given");
            if ((startTime != null && endTime != null) && (startTimestamp != null && endTimestamp != null) && endTimestamp.before(startTimestamp)) {
                this.getResponseMessage().getMessages().put("start-end time", "Start time-" + config.getStartTime() + " should be less than end time-" + config.getEndTime());
            }

        }
    }

    private void validateTxnResponseJson() {
        if (this.txnResponseThresholds == null || this.txnResponseThresholds.isEmpty()) {
            this.getResponseMessage().getMessages().put("txnResponseThresholds", "txnResponseThresholds can not be null or Empty");
        }
        for (TransactionResponseThresholdPojo txnResponse : this.txnResponseThresholds) {
            if (StringUtils.isEmpty(txnResponse.getResponseTimeType()))
                this.getResponseMessage().getMessages().put("Response Time Type", "response Time Type should not be empty or null");
            String startTime = txnResponse.getStartTime();
            java.sql.Timestamp startTimestamp = null;
            if (!StringUtils.isEmpty(startTime)) {
                try {
                    startTimestamp = DateTimeUtil.getTimestampInGMT(startTime);
                } catch (ParseException e) {
                    this.getResponseMessage().getMessages().put("startTimestamp", "startTimestamp is invalid, given value-" + txnResponse.getStartTime() + " is not this format-'yyyy-MM-dd HH:mm:ss'");
                }
            }
            String endTime = txnResponse.getEndTime();
            java.sql.Timestamp endTimestamp = null;
            if (!StringUtils.isEmpty(endTime)) {
                try {
                    endTimestamp = DateTimeUtil.getTimestampInGMT(endTime);
                } catch (ParseException e) {
                    this.getResponseMessage().getMessages().put("endTimestamp", "endTimestamp is invalid, given value-" + txnResponse.getEndTime() + " is not this format-'yyyy-MM-dd HH:mm:ss'");
                }
            }

            if (startTime == null && endTime != null)
                this.getResponseMessage().getMessages().put("start time", "Start time-" + txnResponse.getStartTime() + " should not be null or empty if end time is given");
            if ((startTime != null && endTime != null) && (startTimestamp != null && endTimestamp != null) && endTimestamp.before(startTimestamp)) {
                this.getResponseMessage().getMessages().put("start-end time", "Start time-" + txnResponse.getStartTime() + " should be less than end time-" + txnResponse.getEndTime());
            }

            if (StringUtils.isEmpty(txnResponse.getProfileName()))
                this.getResponseMessage().getMessages().put("profile name", "Profile name should not be empty or null");
        }

    }
}
