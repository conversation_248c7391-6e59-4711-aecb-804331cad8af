package com.appnomic.appsone.api.pojo;

import lombok.Data;

/**
 * <AUTHOR> : 21/02/2019
 */
@Data
public class TransactionStats {
    private String dataCollectionTime;
    private Double fail=0.0;
    private Double slow=0.0;
    private Double success=0.0;
    private Double timeout=0.0;
    private Double unknown=0.0;
    private Double avgResponseTime=0.0;

    public TransactionStats add(TransactionStats transactionStats)   {
        if(transactionStats != null)    {
            TransactionStats result = new TransactionStats();
            result.setFail(this.getFail() + transactionStats.getFail());
            result.setSlow(this.getSlow() + transactionStats.getSlow());
            result.setSuccess(this.getSuccess() + transactionStats.getSuccess());
            result.setTimeout(this.getTimeout() + transactionStats.getTimeout());
            result.setUnknown(this.getUnknown() + transactionStats.getUnknown());
            return result;
        }
        return this;
    }

    /**
     * This method adds the stats of the object passed to it to this object.
     * @param transactionStats
     */
    public void mergeStats(TransactionStats transactionStats) {
        if(transactionStats != null)    {
            this.setFail(this.getFail() + transactionStats.getFail());
            this.setSlow(this.getSlow() + transactionStats.getSlow());
            this.setSuccess(this.getSuccess() + transactionStats.getSuccess());
            this.setTimeout(this.getTimeout() + transactionStats.getTimeout());
            this.setUnknown(this.getUnknown() + transactionStats.getUnknown());
        }
    }
}
