package com.appnomic.appsone.api.pojo;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Slf4j
public class JimTraceRequest {
    private String accountIdentifier;
    private long start;
    private long end;
    private int limit;
    private String lookBack;
    private String maxDuration;
    private String minDuration;
    private String serviceIdentifier;
    private String transactionUrlPattern;

    public JimTraceRequest(RequestObject requestObject) throws ClientException {
        maxDuration = requestObject.getQueryParams().get("maxDuration") != null ? requestObject.getQueryParams().get("maxDuration")[0] : "";
        minDuration = requestObject.getQueryParams().get("minDuration") != null ? requestObject.getQueryParams().get("minDuration")[0] : "";
        try {
            limit = Integer.parseInt(requestObject.getQueryParams().get("limit") != null ? requestObject.getQueryParams().get("limit")[0] : Constants.JAEGER_TRACE_DEFAULT_RECORD_LIMIT);
        } catch (Exception e) {
            log.error("Exception while parsing limit.");
            throw new ClientException("Exception while parsing limit.");
        }
        lookBack = requestObject.getQueryParams().get("lookBack") != null ? requestObject.getQueryParams().get("lookBack")[0] : Constants.JAEGER_TRACE_DEFAULT_LOOKBACK_TIME;
    }
}
