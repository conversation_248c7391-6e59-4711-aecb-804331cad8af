package com.appnomic.appsone.api.pojo.applicationhealth;

import com.appnomic.appsone.api.common.Constants;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
public class ApplicationHealthDetail {
    private int id;
    private String identifier;
    private String name;
    private boolean maintenanceWindowStatus;
    private String dashboardUId;
    private int severeProblemCount;
    private int severeWarningCount;
    private int defaultProblemCount;
    private int defaultWarningCount;
    private int severeBatchCount;
    private int defaultBatchCount;
    private int severeProblemCountMaintenance;
    private int severeWarningCountMaintenance;
    private int defaultProblemCountMaintenance;
    private int defaultWarningCountMaintenance;
    private int severeBatchCountMaintenance;
    private int defaultBatchCountMaintenance;
    private int servicesMapped;

    private List<ApplicationHealthStatus> problem = new ArrayList<>();
    private List<ApplicationHealthStatus> warning = new ArrayList<>();
    private List<ApplicationHealthStatus> batch = new ArrayList<>();

    public ApplicationHealthDetail(Map<Integer, String> viewTypesMap) {
        String severeTypeStr = viewTypesMap.getOrDefault(Constants.SEVERITY_295, "Severe");
        String defaultTypeStr = viewTypesMap.getOrDefault(Constants.SEVERITY_296, "Default");

        ApplicationHealthStatus severeTypeProblem = new ApplicationHealthStatus();
        severeTypeProblem.setName(severeTypeStr);
        severeTypeProblem.setCount(0);
        severeTypeProblem.setPriority(1);

        ApplicationHealthStatus defaultTypeProblem = new ApplicationHealthStatus();
        defaultTypeProblem.setName(defaultTypeStr);
        defaultTypeProblem.setCount(0);
        defaultTypeProblem.setPriority(0);

        problem.add(severeTypeProblem);
        problem.add(defaultTypeProblem);

        ApplicationHealthStatus severeTypeWarning = new ApplicationHealthStatus();
        severeTypeWarning.setName(severeTypeStr);
        severeTypeWarning.setCount(0);
        severeTypeWarning.setPriority(1);

        ApplicationHealthStatus defaultTypeWarning = new ApplicationHealthStatus();
        defaultTypeWarning.setName(defaultTypeStr);
        defaultTypeWarning.setCount(0);
        defaultTypeWarning.setPriority(0);

        warning.add(severeTypeWarning);
        warning.add(defaultTypeWarning);

        batch.add(ApplicationHealthStatus.builder().name(severeTypeStr).priority(1).build());
        batch.add(ApplicationHealthStatus.builder().name(defaultTypeStr).build());
    }

    @Override
    public String toString() {
        return "ApplicationHealthDetail{" +
                "id=" + id +
                ", identifier='" + identifier + '\'' +
                ", name='" + name + '\'' +
                ", maintenanceWindowStatus=" + maintenanceWindowStatus +
                ", dashboardUId='" + dashboardUId + '\'' +
                ", severeProblemCount=" + severeProblemCount +
                ", severeWarningCount=" + severeWarningCount +
                ", defaultProblemCount=" + defaultProblemCount +
                ", defaultWarningCount=" + defaultWarningCount +
                ", severeBatchCount=" + severeBatchCount +
                ", defaultBatchCount=" + defaultBatchCount +
                ", severeProblemCountMaintenance=" + severeProblemCountMaintenance +
                ", severeWarningCountMaintenance=" + severeWarningCountMaintenance +
                ", defaultProblemCountMaintenance=" + defaultProblemCountMaintenance +
                ", defaultWarningCountMaintenance=" + defaultWarningCountMaintenance +
                ", severeBatchCountMaintenance=" + severeBatchCountMaintenance +
                ", defaultBatchCountMaintenance=" + defaultBatchCountMaintenance +
                ", servicesMapped=" + servicesMapped +
                ", problem=" + problem +
                ", warning=" + warning +
                ", batch=" + batch +
                '}';
    }

    public int getSevereProblemCount() {
        severeProblemCount = 0;

        if (!maintenanceWindowStatus && problem != null) {
            for (ApplicationHealthStatus sp : problem) {
                if (sp.getCount() > 0 && sp.getPriority() == 1) {
                    severeProblemCount += sp.getCount();
                }
            }
        }

        return severeProblemCount;
    }

    public int getSevereWarningCount() {
        severeWarningCount = 0;

        if (!maintenanceWindowStatus && warning != null) {
            for (ApplicationHealthStatus sw : warning) {
                if (sw.getCount() > 0 && sw.getPriority() == 1) {
                    severeWarningCount += sw.getCount();
                }
            }
        }

        return severeWarningCount;
    }

    public int getSevereBatchCount() {
        severeBatchCount = 0;

        if (!maintenanceWindowStatus && batch != null) {
            for (ApplicationHealthStatus sp : batch) {
                if (sp.getCount() > 0 && sp.getPriority() == 1) {
                    severeBatchCount += sp.getCount();
                }
            }
        }

        return severeBatchCount;
    }

    public int getDefaultProblemCount() {
        defaultProblemCount = 0;

        if (!maintenanceWindowStatus && problem != null) {
            for (ApplicationHealthStatus p : problem) {
                if (p.getCount() > 0 && p.getPriority() == 0) {
                    defaultProblemCount += p.getCount();
                }
            }
        }
        return defaultProblemCount;
    }

    public int getDefaultWarningCount() {
        defaultWarningCount = 0;

        if (!maintenanceWindowStatus && warning != null) {
            for (ApplicationHealthStatus w : warning) {
                if (w.getCount() > 0 && w.getPriority() == 0) {
                    defaultWarningCount += w.getCount();
                }
            }
        }
        return defaultWarningCount;
    }

    public int getDefaultBatchCount() {
        defaultBatchCount = 0;

        if (!maintenanceWindowStatus && batch != null) {
            for (ApplicationHealthStatus sp : batch) {
                if (sp.getCount() > 0 && sp.getPriority() == 0) {
                    defaultBatchCount += sp.getCount();
                }
            }
        }

        return defaultBatchCount;
    }

    public int getSevereProblemCountForMaintenanceWindow() {
        severeProblemCountMaintenance = 0;

        if (maintenanceWindowStatus && problem != null) {
            for (ApplicationHealthStatus sp : problem) {
                if (sp.getCount() > 0 && sp.getPriority() == 1) {
                    severeProblemCountMaintenance += sp.getCount();
                }
            }
        }
        return severeProblemCountMaintenance;
    }

    public int getSevereWarningCountForMaintenanceWindow() {
        severeWarningCountMaintenance = 0;

        if (maintenanceWindowStatus && warning != null) {
            for (ApplicationHealthStatus sw : warning) {
                if (sw.getCount() > 0 && sw.getPriority() == 1) {
                    severeWarningCountMaintenance += sw.getCount();
                }
            }
        }
        return severeWarningCountMaintenance;
    }

    public int getSevereBatchCountForMaintenanceWindow() {
        severeBatchCountMaintenance = 0;

        if (maintenanceWindowStatus && batch != null) {
            for (ApplicationHealthStatus sw : batch) {
                if (sw.getCount() > 0 && sw.getPriority() == 1) {
                    severeBatchCountMaintenance += sw.getCount();
                }
            }
        }
        return severeBatchCountMaintenance;
    }

    public int getProblemCountForMaintenance() {
        defaultProblemCountMaintenance = 0;

        if (maintenanceWindowStatus && problem != null) {
            for (ApplicationHealthStatus p : problem) {
                if (p.getCount() > 0 && p.getPriority() == 0) {
                    defaultProblemCountMaintenance += p.getCount();
                }
            }
        }
        return defaultProblemCountMaintenance;
    }

    public int getWarningCountForMaintenance() {
        defaultWarningCountMaintenance = 0;

        if (maintenanceWindowStatus && warning != null) {
            for (ApplicationHealthStatus w : warning) {
                if (w.getCount() > 0 && w.getPriority() == 0) {
                    defaultWarningCountMaintenance += w.getCount();
                }
            }
        }
        return defaultWarningCountMaintenance;
    }

    public int getBatchCountForMaintenance() {
        defaultBatchCountMaintenance = 0;

        if (maintenanceWindowStatus && batch != null) {
            for (ApplicationHealthStatus sw : batch) {
                if (sw.getCount() > 0 && sw.getPriority() == 0) {
                    defaultBatchCountMaintenance += sw.getCount();
                }
            }
        }
        return defaultBatchCountMaintenance;
    }


}
