package com.appnomic.appsone.api.pojo.request;

import com.appnomic.appsone.api.beans.ComponentInstanceBean;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.InstanceRepo;
import com.appnomic.appsone.api.dao.redis.ServiceRepo;
import com.appnomic.appsone.api.service.mysql.CompInstanceDataService;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.CompInstClusterDetails;
import com.heal.configuration.pojos.TimeRangeDetails;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.List;

@Data
@NoArgsConstructor
public class KPIAnomalyListDataRequest {
    private static final Logger LOGGER = LoggerFactory.getLogger(KPIAnomalyListDataRequest.class);
    private String accountIdString;
    private String serviceIdString;
    private String compInstanceIdString;
    private String categoryIdString;
    private String fromTimeString;
    private String toTimeString;
    private Request request;
    private Response response;
    private Account account;
    private BasicEntity serviceDetails;
    private CompInstClusterDetails instanceDetails;
    private int serviceId;
    private int instanceId;
    private int categoryId;
    private long fromTime;
    private long toTime;
    private TimeRangeDetails timeRangeDetails;
    private List<TimeRangeDetails> timeRangeDetailsList;

    public KPIAnomalyListDataRequest(Request request, Response response) {
        this.request = request;
        this.response = response;
        accountIdString = request.params(":identifier");
        serviceIdString = request.params(":serviceId");
        compInstanceIdString = request.params(":instanceId");
        categoryIdString = request.params(":categoryId");
        fromTimeString = request.queryParams("fromTime");
        toTimeString = request.queryParams("toTime");
    }

    public GenericResponse populateAndValidate() {
        checkNumberFormat();
        GenericResponse genericResponse = null;
        this.setAccount(new AccountRepo().getAccount(accountIdString));
        if( this.getAccount() == null ) {
            genericResponse = new GenericResponse();
            genericResponse.setMessage(Constants.MESSAGE_INVALID_ACCOUNT);
            genericResponse.setResponseStatus(StatusResponse.FAILURE.toString());
            this.getResponse().status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            LOGGER.error(Constants.MESSAGE_INVALID_ACCOUNT);
            return genericResponse;
        }

        this.setServiceDetails(new ServiceRepo().getBasicServiceDetailsWithServiceId(accountIdString, serviceId));
        if( this.getServiceDetails() == null ) {
            genericResponse = new GenericResponse();
            genericResponse.setMessage(Constants.MESSAGE_INVALID_SERVICE);
            genericResponse.setResponseStatus(StatusResponse.FAILURE.toString());
            this.getResponse().status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            LOGGER.error(Constants.MESSAGE_INVALID_SERVICE);
            return genericResponse;
        }

        this.setInstanceDetails(new InstanceRepo().getInstanceDetailsWithId(accountIdString, instanceId));
        if( this.getInstanceDetails() == null ) {
            genericResponse = new GenericResponse();
            genericResponse.setMessage(Constants.MESSAGE_INVALID_COMP_INSTANCE);
            genericResponse.setResponseStatus(StatusResponse.FAILURE.toString());
            this.getResponse().status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            LOGGER.error(Constants.MESSAGE_INVALID_COMP_INSTANCE);
            return genericResponse;
        }

        return genericResponse;
    }

    protected ComponentInstanceBean getInstanceDetailsData() {
        ComponentInstanceBean result = null;
        try {
            result = CompInstanceDataService.getComponentInstanceById(this.instanceId);
            if(result == null || result.getStatus() == 0) {
                return null;
            }
        } catch (Exception e) {
            LOGGER.error("Exception while getting component instance for id:{}.", this.instanceId, e);
        }
        return result;
    }

    protected void checkNumberFormat() {
        try {
            this.setServiceId(Integer.parseInt(this.getServiceIdString()));
            this.setInstanceId(Integer.parseInt(this.getCompInstanceIdString()));
            this.setCategoryId(Integer.parseInt(this.getCategoryIdString()));
            this.setFromTime(Long.parseLong(this.getFromTimeString()));
            this.setToTime(Long.parseLong(this.getToTimeString()));
        } catch (NumberFormatException ne) {
            LOGGER.error("Invalid input received for numerical value:", ne);
        }
    }
}
