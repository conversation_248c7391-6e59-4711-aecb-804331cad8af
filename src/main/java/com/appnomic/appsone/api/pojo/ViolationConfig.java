package com.appnomic.appsone.api.pojo;

import com.appnomic.appsone.api.util.StringUtils;
import lombok.Data;

/**
 * <AUTHOR> : 1/3/19
 */
@Data
public class ViolationConfig {
    private int id;
    private String profileName;
    private int profileId;
    private String operation;
    private String minThreshold;
    private String maxThreshold;
    private String startTime;
    private String endTime;

    public void validate() throws Exception {
        if (StringUtils.isEmpty(this.profileName)) throw new Exception("Profile name can not be null or empty");
        if (StringUtils.isEmpty(this.operation)) throw new Exception("operation can not be null or empty");
        try {
            Float.valueOf(this.minThreshold);
            Float.valueOf(this.maxThreshold);
        }catch (Exception e) {
            throw new Exception(e);
        }
    }
}
