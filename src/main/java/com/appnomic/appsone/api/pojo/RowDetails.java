package com.appnomic.appsone.api.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;


/*
 * <AUTHOR> : 04/03/2019
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RowDetails {
    private int transaction_id;
    private int service_id;
    private int application_id;
    private int agent_id;
    private String transaction_name;
    private String transactionIdentifier;
    private int failures;
    private int slow;
    private int volume;
    private int auditEnabled;
    private int success;
    private float failurePercentage;
    private float slowPercentage;
    private float average_response_time;
    private String average_response_time_unit;
    private long time;
    private String name;
    private String value;
    private String url;
    private String source;

    public RowDetails add(RowDetails input) {

        if(input == null)   {
            return this;
        }

        RowDetails result = new RowDetails();
        result.setTransaction_id(this.getTransaction_id());
        result.setService_id(this.getService_id());
        result.setApplication_id(this.getApplication_id());
        result.setAgent_id(this.getAgent_id());
        result.setTransaction_name(this.getTransaction_name());

        result.setFailures(this.getFailures()+input.getFailures());
        result.setSlow(this.getSlow()+input.getSlow());
        result.setVolume(this.getVolume()+input.getVolume());
        result.setSuccess(this.getSuccess()+input.getSuccess());
        result.setSlowPercentage( ((float)result.getSlow()/result.getVolume())*100);
        result.setFailurePercentage(((float) result.getFailures()/result.getVolume())*100);
        //Calculate avg response time manually
        Float temp = (((this.getVolume()*this.getAverage_response_time())+
                (input.getVolume()*input.getAverage_response_time()))/
                (this.getVolume()+input.getVolume()));
        Float avgResponseTime = Float.parseFloat(String.format("%.2f", temp));
        result.setAverage_response_time(avgResponseTime);

        result.setAverage_response_time_unit(this.getAverage_response_time_unit());
        result.setTime(this.getTime());
        return result;
    }

    public void mergeStats(RowDetails input) {
        if( input == null ) return;
        this.setFailures(this.getFailures() + input.getFailures());
        this.setSlow(this.getSlow() + input.getSlow());
        this.setVolume(this.getVolume() + input.getVolume());
        this.setSuccess(this.getSuccess() + input.getSuccess());
        Float temp = (((this.getVolume()*this.getAverage_response_time())+
                (input.getVolume()*input.getAverage_response_time()))/
                (this.getVolume()+input.getVolume()));
        this.setAverage_response_time(temp);
    }

    public void calculatePercentages() {
        if( this.getVolume() > 0 && this.getSlow() > 0 ) {
            this.setSlowPercentage( Float.parseFloat( String.format("%.2f",
                    (((float) this.getSlow()/this.getVolume())*100)) ) );
        } else {
            this.setSlowPercentage(0.0f);
        }

        if( this.getVolume() > 0 && this.getFailures() > 0 ) {
            this.setFailurePercentage(Float.parseFloat( String.format("%.2f",
                    (((float) this.getFailures()/this.getVolume())*100)) ) );
        } else {
            this.setFailurePercentage(0.0f);
        }

        float avgResponseTime = Float.parseFloat(String.format("%.2f", this.getAverage_response_time()));
        this.setAverage_response_time(avgResponseTime);
    }


}
