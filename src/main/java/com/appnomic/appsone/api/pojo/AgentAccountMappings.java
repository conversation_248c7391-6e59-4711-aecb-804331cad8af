package com.appnomic.appsone.api.pojo;

import com.appnomic.appsone.api.util.StringUtils;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> : 25/4/19
 */
@Data
public class AgentAccountMappings {
    private String accountIdentifier;
    private List<Tags> tags;

    public void validate() throws Exception {
        if (StringUtils.isEmpty(this.accountIdentifier)) throw new Exception("account Identifier can not be null or empty.");
        if (this.tags == null || this.tags.isEmpty()) throw new Exception("tags can not be null or empty.");
        for (Tags tag : this.tags) {
            tag.validate();
        }
    }
}
