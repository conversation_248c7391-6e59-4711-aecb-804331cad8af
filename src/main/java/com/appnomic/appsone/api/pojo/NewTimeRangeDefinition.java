package com.appnomic.appsone.api.pojo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class NewTimeRangeDefinition {

    private long fromTime;
    private long toTime;
    private AggregationLevel aggregationLevel;
    private List<LocalDateTime> generatedCompleteTimeSeriesOpenSearchTimes = new ArrayList<>();
    private List<LocalDateTime> generatedTimeSeriesDisplayTimes = new ArrayList<>();

}
