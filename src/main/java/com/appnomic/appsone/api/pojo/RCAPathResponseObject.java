package com.appnomic.appsone.api.pojo;

import lombok.Data;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> : 24/04/2019
 */
@Data
public class RCAPathResponseObject {
    private String responseStatus;
    private String responseMessage;
    private ResponseData data;

    @Data
    public static class ResponseData {
        private List<TopologyDetailsResponse.TopologyDetails> rcaPaths = new ArrayList<>();
        private Set<String> relatedSignals = new HashSet<>();
        private Set<RelatedSignals> coreInfoSignals = new HashSet<>();
        private Set<RelatedSignals> configWatchInfoSignals = new HashSet<>();
        private Set<String> batchSignals = new HashSet<>();

    }
}
