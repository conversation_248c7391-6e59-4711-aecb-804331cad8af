package com.appnomic.appsone.api.pojo.request;


import com.heal.configuration.entities.BasicInstanceBean;
import com.heal.configuration.pojos.*;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * This class is a common place holder class which can hold fields extracted from spark request class, this contains
 * an exhaustive list of parameters which can be extended based on new requirements.

 * IMPORTANT: Please never remove an existing field or rename any field for consistency purpose.
 */
@Data
@Builder
@ToString
public class UtilityBean<T> {
    private T requestPayloadObject;
    private String accountIdString;
    private String applicationIdString;
    private String parentApplicationIdString;
    private String serviceIdString;
    private String componentInstanceIdString;
    private String requestTypeString;
    private String doAnalyticsFlagString;
    private String kpiFilterListString;
    private String fromTimeString;
    private String toTimeString;
    private String responseType;
    private String kpiNameString;
    private String clusterIdString;

    private ClusterOperationEnum operation;

    private TimeRangeDetails timeRangeDetails;
    private List<TimeRangeDetails> timeRangeDetailsList;

    private int serviceId;
    private int applicationId;
    private int clusterId;
    private Long fromTime;
    private Long toTime;
    private Long time;

    private Account account;
    private long timeZoneMilli;
    private Service service;
    private BasicInstanceBean instance;
    private CompInstClusterDetails compInstance;
    private BasicInstanceBean clusterInstance;
    private Application application;
    private ParentApplication parentApplication;
    private BasicKpiEntity kpiDetails;
    private String authToken;
    private String userId;

    private String srcServiceIdString;
    private String dstServiceIdString;
    
    private String appDisplayVersions;
    private Integer rowLimit;
    private String appName;
    private String appOSString;
    private String issueId;
    private String requestName;
    
    private String country;
    private String deviceName;
    private String carrier;
    private String radioType;
    private String mimeType;

    private int transactionId;

    private List<Integer> appIds;

    private String categoryIdentifier;
}
