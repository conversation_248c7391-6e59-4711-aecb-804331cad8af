package com.appnomic.appsone.api.pojo;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.util.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> : 1/3/19
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Application {
    private int id;
    private String name;
    private String identifier;
    private String dashboardUId;
    private List<TransactionViolationConfig> txnViolationConfigs;
    private List<Tags> tags;
    private Map<String, String> errorMessage = new HashMap<>();

    public void validate() {
        if (StringUtils.isEmpty(this.identifier))
            errorMessage.put("Identifier", "Identifier can not be null or empty.");
        if (this.name != null && this.name.trim().length() < 1) errorMessage.put("Name", "Name can not be empty.");
        if (this.tags == null || tags.isEmpty())
            errorMessage.put("Tags", "Tags are mandatory, you can not put null or empty tag.");
        boolean isTimezoneIsAvailable = false;
        boolean isServicesAvailable = false;
        for (Tags tag : tags) {
            if (StringUtils.isEmpty(tag.getIdentifier()))
                errorMessage.put("Tag identifier", "Tag identifier can not be null or empty.");
            if (StringUtils.isEmpty(tag.getName())) errorMessage.put("Tag name", "Tag name can not be null or empty.");
            if (tag.getValue() != null && tag.getValue().length() < 1)
                errorMessage.put("Tag value", "Tag value can not be null or empty.");
            if (tag.getName().equals(Constants.TIME_ZONE_TAG)) isTimezoneIsAvailable = true;
            if (tag.getSubTypeName() != null && tag.getSubTypeName().equals(Constants.SERVICES_CONTROLLER_TYPE))
                isServicesAvailable = true;
        }
        if (!isServicesAvailable)
            errorMessage.put("Services", "Services are missing in tag list. aAleast one service is mandatory for to create application.");
        if (!isTimezoneIsAvailable)
            errorMessage.put("Timezone", "Timezone is missing in tag list. Timezone is mandatory for to create application.");
        if (txnViolationConfigs != null) {
            for (TransactionViolationConfig violationConfig : this.txnViolationConfigs) {
                if (StringUtils.isEmpty(violationConfig.getProfileName()))
                    errorMessage.put("Profile name", "Profile name can not be null or empty");
                if (StringUtils.isEmpty(violationConfig.getOperation()))
                    errorMessage.put("operation", "Operation can not be null or empty");
                if (StringUtils.isEmpty(violationConfig.getTransactionKpiType()))
                    errorMessage.put("transactionKpiType", "transactionKpiType can not be null or empty");
                if (StringUtils.isEmpty(violationConfig.getResponseTimeType()))
                    errorMessage.put("responseTimeType", "responseTimeType can not be null or empty");
                try {
                    Float.valueOf(violationConfig.getMinThreshold());
                    Float.valueOf(violationConfig.getMaxThreshold());
                } catch (Exception e) {
                    errorMessage.put("Threshold Value", "Please provide valid threshold value.");
                }
            }
        }
    }
}
