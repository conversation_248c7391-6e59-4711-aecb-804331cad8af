package com.appnomic.appsone.api.pojo;


public enum KpiRollUpOperations {

    AVERAGE("Average", true),
    LAST("Last", false),
    MAX("Max", true),
    SUM("Sum", true),
    NONE("None", false);

    private final String operationName;
    private final boolean openSearchSupported;

    KpiRollUpOperations(String columnName, boolean openSearchSupported) {
        this.operationName = columnName;
        this.openSearchSupported = openSearchSupported;
    }

    public String getOperationName() {
        return operationName;
    }

    public boolean isOpenSearchSupported() {
        return openSearchSupported;
    }

    public static boolean isOpenSearchSupported(String operationName) {
        for (KpiRollUpOperations v : values()) {
            if (v.getOperationName().equalsIgnoreCase(operationName)) {
                return v.isOpenSearchSupported();
            }
        }
        return false;
    }

}
