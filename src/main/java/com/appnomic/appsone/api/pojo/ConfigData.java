package com.appnomic.appsone.api.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ConfigData {
    String name;
    Long lastUpdatedTime;
    String kpiName;
    Set<String> keys;
    Map<Long, Map<String, String>> values;
}
