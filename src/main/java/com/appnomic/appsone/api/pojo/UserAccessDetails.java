package com.appnomic.appsone.api.pojo;

import com.appnomic.appsone.api.beans.AgentBean;
import lombok.Data;

import java.util.List;

@Data
public class UserAccessDetails {
    private List<String> applicationIdentifiers;
    private List<Integer> applicationIds;
    private List<Integer> serviceIds;
    private List<String> serviceIdentifiers;
    private List<Integer> transactionIds;
    private List<AgentBean> agents;
}
