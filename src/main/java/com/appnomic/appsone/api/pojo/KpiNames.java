package com.appnomic.appsone.api.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class KpiNames {

    int id;
    String name;
    String description;
    String unit;
    String clusterOperation;
    String type;
    int isGroupKpi;
    Boolean isDataAvailable;
    String groupName;
    int isDiscovery;
    int groupId;
    int categoryId;
    String categoryName;
    String categoryIdentifier;
    boolean isInfo;
    long anomalyCount;
    private ComputedDetails computedDetails;
    private List<KpiAttribute> attribute;
    private int status;
    private String source;

    @Data
    @Builder
    public static class ComputedDetails {
        private String formula;
        private List<KpiInfo> kpisUsed;
    }

    @Data
    @Builder
    public static class KpiInfo {
        private int id;
        private String name;
        private String category;
    }
}
