package com.appnomic.appsone.api.pojo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ApplicationDetail {
    private int id;
    private String name;
    private boolean hasTransactionConfigured = false;
    private String identifier;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Map<String, Long> reportTimeRanges;
}
