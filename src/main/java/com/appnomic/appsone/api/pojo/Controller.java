package com.appnomic.appsone.api.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Controller {

    private String appId;
    private String name;
    private int controllerTypeId;
    private long timeOffset;
    private List<ServiceConfig> serviceDetails = new ArrayList<>();
    private List<TxnKPIViolationConfig> txnViolationConfig = new ArrayList<>();
    private String identifier;
    private boolean monitoringEnabled;
    private int accountId;
    private int status;
}
