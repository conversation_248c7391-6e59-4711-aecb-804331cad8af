package com.appnomic.appsone.api.pojo;

import com.heal.configuration.pojos.ParentApplication;
import lombok.*;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
public class SignalData {
    private String id;
    private String type;
    private String currentStatus;
    private String severity;
    private String description = "under construction...";
    private Long startTimeMilli;
    private Long updatedTimeMilli;
    private Set<SignalData.Application> applications;
    private Set<String> affectedServices;
    private Set<String> entryServices;
    private Set<String> rootCauseServiceSet;
    private long eventCount;
    private Long lastEventTime;
    private String metricCategory;
    private Set<SignalData.Services> services;
    private Set<ParentApplication> parentApplications;

    public void addApplications(List<Controller> apps) {
        if (apps != null) {
            if (this.applications == null) {
                this.applications = new HashSet<>();
            }
            apps.forEach(app -> this.applications.add(new SignalData.Application(Integer.parseInt(app.getAppId()),
                    app.getName(), app.getIdentifier())));
        }
    }

    public void addServices(Controller app){
        if(app != null){
            if(this.services == null){
                this.services = new HashSet<>();
            }
            this.services.add(new SignalData.Services(Integer.parseInt(app.getAppId()), app.getName()));
        }
    }


    @AllArgsConstructor
    @EqualsAndHashCode
    @Data
    public static class Application{
        private int id;
        private String name;
        private String identifier;
    }

    @AllArgsConstructor
    @EqualsAndHashCode
    @Data
    static class Services{
        private int id;
        private String name;
    }
}
