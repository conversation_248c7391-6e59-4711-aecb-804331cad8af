package com.appnomic.appsone.api.pojo;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> : 07/03/2019
 */
@Data
public class AnomalyEventDetail {
    private String name;
    private Long anomalyTime;
    private String instanceName;
    private int instanceId;
    private String instanceIdentifier;
    private int clusterId;
    private String type;
    private String kpiName;
    private String kpiUnit;
    private String kpiType;
    private Boolean impactKPI;
    private Map<String,Object> operatingRange;
    private String operationsType;
    private String observedValue;
    private String iconType;
    private Boolean entryServiceNode;
    private int kpiCategoryId;
    private String kpiCategoryName;
    private int serviceId;
    private String serviceIdentifier;
    private int isJIMEnabled;
    private boolean userAccessible;
    private String fileName;
    private String groupName;
    private String attribute;
    private String oldValue;
    private String operation;
    private Map<String, String> metaData = new HashMap<>();
    private String source;
}
