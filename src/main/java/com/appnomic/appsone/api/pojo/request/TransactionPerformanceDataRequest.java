package com.appnomic.appsone.api.pojo.request;

import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.util.StringUtils;
import com.heal.configuration.enums.TransactionResponseTypes;
import com.heal.configuration.pojos.BasicTransactionEntity;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Data
@NoArgsConstructor
@Slf4j
public class TransactionPerformanceDataRequest {
    private String accountIdentifierString;
    private String serviceIdString;
    private String instanceIdString;
    private String transactionIdString;
    private String transactionResponseTypeString;
    private String fromTimeString;
    private String toTimeString;
    private String requestTypeString;
    private String tagIdString;

    List<String> agentList;
    List<BasicTransactionEntity> taggedTransaction;
    String groupName;

    public TransactionPerformanceDataRequest(RequestObject request) {

        accountIdentifierString = request.getParams().get(":identifier");
        serviceIdString = request.getParams().get(":controllerId");
        instanceIdString = request.getParams().get(":instanceId");
        transactionIdString = request.getParams().get(":txnId");
        transactionResponseTypeString = request.getParams().get(":response-type");
        fromTimeString = request.getQueryParams().get("fromTime")[0];
        toTimeString = request.getQueryParams().get("toTime")[0];
        requestTypeString = request.getQueryParams().get("type")[0];
        tagIdString = request.getQueryParams().get("tagId") != null ? request.getQueryParams().get("tagId")[0] : "";

    }

    public boolean validateParameters(TransactionPerformanceDataRequest transactionPerformanceDataRequest) {

        if (StringUtils.isEmpty(transactionPerformanceDataRequest.getAccountIdentifierString())) {
            log.error("Account Id is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(transactionPerformanceDataRequest.getServiceIdString())) {
            log.error("Service Id is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(transactionPerformanceDataRequest.getInstanceIdString())) {
            log.error("Instance Id is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(transactionPerformanceDataRequest.getTransactionIdString())) {
            log.error("Transaction Id is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(transactionPerformanceDataRequest.getTransactionResponseTypeString())) {
            log.error("Response type is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(transactionPerformanceDataRequest.getFromTimeString())) {
            log.error("From time is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(transactionPerformanceDataRequest.getToTimeString())) {
            log.error("To time is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(transactionPerformanceDataRequest.getRequestTypeString())) {
            log.error("Request type is null/empty");
            return false;
        }

        try {
            this.transactionResponseTypeString = TransactionResponseTypes.valueOf(this.getTransactionResponseTypeString()).name();
        } catch (IllegalArgumentException | NullPointerException e) {
            log.error("Invalid transaction response type provided: {}", this.transactionResponseTypeString);
            return false;
        }

        long fromTime;
        long toTime;
        try {
            Integer.valueOf(serviceIdString);
            Integer.valueOf(instanceIdString);
            Integer.valueOf(transactionIdString);
            fromTime = Long.parseLong(fromTimeString);
            toTime = Long.parseLong(toTimeString);
        } catch (NumberFormatException e) {
            log.error("Invalid value found where number is required. {}", e.getMessage());
            return false;
        }

        if (fromTime <= 0 || toTime <= 0 || fromTime >= toTime) {
            log.error("Invalid time range provided");
            return false;
        }

        return true;
    }

}
