package com.appnomic.appsone.api.pojo;

import com.heal.configuration.pojos.Application;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.*;

@Data
public class TopologyDetailsResponse {


    String responseStatus;
    String responseMessage;
    List<TopologyDetails> data;

    @Data
    public static class TopologyDetails {
        List<String> impactedServiceName = new ArrayList<>();
        List<Nodes> nodes;
        List<Edges> edges;
    }


    @Data
    @Builder
    @AllArgsConstructor
    public static class Nodes {
        String id;
        String name;
        String identifier;
        String type;
        String title;
        boolean isMaintenance;
        List<Application> applications;
        private boolean userAccessible;
        private int workloadEventCount = -1;
        private int behaviorEventCount = -1;
        private boolean isRCAPathNode = false;
        private boolean isStartNode = false;
        private boolean entryPointNode = false;
        private boolean isServiceAffected = false;
        private Map<String, Object> metadata = new HashMap<>();
        private List<TimezonePojo> timezoneData = new ArrayList<>();

        public Nodes() {
        }

        public Nodes(String id, String name, String type) {
            this.id = id;
            this.name = name;
            this.type = type;
        }

        public Nodes(String serviceId) {
            this.id = serviceId;
        }

        public void addToMetaData(String key, Object value) {
            this.metadata.put(key, value);
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (!(o instanceof Nodes)) return false;
            Nodes nodes = (Nodes) o;
            return Objects.equals(this.id, nodes.id) &&
                    Objects.equals(this.name, nodes.name) &&
                    Objects.equals(this.type, nodes.type);
        }

        @Override
        public int hashCode() {

            return Objects.hash(this.id, this.name, this.type);
        }
    }

    @Data
    public static class Edges {
        String source;
        String target;
        Map<String, String> data = new HashMap<>();

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (!(o instanceof Edges)) return false;
            Edges edges = (Edges) o;
            return Objects.equals(this.source, edges.source) &&
                    Objects.equals(this.target, edges.target);
        }

        @Override
        public int hashCode() {
            return Objects.hash(this.source, this.target);
        }

        public static Edges clone(Edges e) {
            Edges edges = new Edges();
            edges.setSource(e.getSource());
            edges.setTarget(e.getTarget());
            edges.setData(e.getData());

            return edges;
        }
    }
}
