package com.appnomic.appsone.api.pojo.request;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.api.util.StringUtils;
import com.heal.configuration.enums.KPIAttributes;
import com.heal.configuration.enums.TransactionResponseTypes;
import com.heal.configuration.pojos.BasicTransactionEntity;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Data
@Slf4j
public class TransactionNonTimeSeriesDataRequest {

    private static final String RESPONSE_TYPE_DEFAULT = ConfProperties.getString(Constants.TRANSACTION_TYPE,
            Constants.TRANSACTION_TYPE_DEFAULT);

    private String accountIdString;
    private String serviceIdString;
    private String instanceIdString;
    private String transactionResponseTypeString;
    private String requestTypeString;
    private String fromTimeString;
    private String toTimeString;
    private String kpiNameString;
    private String tagIdString;
    private KPIAttributes kpiNameDetails;
    private List<BasicTransactionEntity> taggedTransaction;
    private List<String> agentList;
    private String groupName;

    public TransactionNonTimeSeriesDataRequest(RequestObject requestObject) {
        this.accountIdString = requestObject.getParams().get(":identifier");
        this.serviceIdString = requestObject.getParams().get(":controllerId");
        this.instanceIdString = requestObject.getParams().get(":instanceId");
        this.transactionResponseTypeString = requestObject.getParams().getOrDefault(":response-type", RESPONSE_TYPE_DEFAULT);
        this.fromTimeString = requestObject.getQueryParams().get("fromTime")[0];
        this.toTimeString = requestObject.getQueryParams().get("toTime")[0];
        this.kpiNameString = requestObject.getParams().get(":kpi-identifier");
        this.requestTypeString = requestObject.getQueryParams().get("type")[0];
        this.tagIdString = requestObject.getQueryParams().get("tagId")[0];
    }

    public boolean validateParameters(TransactionNonTimeSeriesDataRequest nts) {

        if (StringUtils.isEmpty(nts.getAccountIdString())) {
            log.error("Account id is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(nts.getServiceIdString())) {
            log.error("Service id is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(nts.getInstanceIdString())) {
            log.error("Instance id is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(nts.getKpiNameString())) {
            log.error("Kpi name is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(nts.getTransactionResponseTypeString())) {
            log.error("Transaction response type is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(nts.getRequestTypeString())) {
            log.error("Request type is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(nts.getTagIdString())) {
            log.error("Tag id is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(nts.getFromTimeString())) {
            log.error("From time is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(nts.getToTimeString())) {
            log.error("To time is null/empty");
            return false;
        }

        long fromTime;
        long toTime;
        try {
            Integer.valueOf(serviceIdString);
            Integer.valueOf(instanceIdString);
            Integer.valueOf(tagIdString);
            fromTime = Long.parseLong(nts.getFromTimeString());
            toTime = Long.parseLong(nts.getToTimeString());
        } catch (NumberFormatException e) {
            log.error("Invalid value found where number is required. {}", e.getMessage());
            return false;
        }

        if (fromTime <= 0 || toTime <= 0 || fromTime >= toTime) {
            log.error("Invalid time range provided");
            return false;
        }

        try {
            this.kpiNameDetails = KPIAttributes.valueOfColumnName(this.getKpiNameString());
        } catch (IllegalArgumentException | NullPointerException e) {
            log.error("Invalid kpi name provided: {}", this.getKpiNameString());
            return false;
        }

        try {
            this.transactionResponseTypeString = TransactionResponseTypes.valueOf(this.getTransactionResponseTypeString()).name();
        } catch (IllegalArgumentException | NullPointerException e) {
            log.error("Invalid transaction response type provided: {}", this.transactionResponseTypeString);
            return false;
        }

        return true;
    }
}
