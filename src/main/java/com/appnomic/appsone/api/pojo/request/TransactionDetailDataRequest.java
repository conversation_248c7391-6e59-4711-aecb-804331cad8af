package com.appnomic.appsone.api.pojo.request;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.dao.redis.*;
import com.appnomic.appsone.api.pojo.*;
import com.appnomic.appsone.api.pojo.util.RollupTimeMetaData;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.ConfProperties;
import com.heal.configuration.pojos.*;
import com.heal.configuration.pojos.Account;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Response;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Request object that will parse, validate and populate all the required details after receiving the request details
 * from the consumer.
 */
@Data
@NoArgsConstructor
public class TransactionDetailDataRequest {
    private static final Logger LOGGER = LoggerFactory.getLogger(TransactionDetailDataRequest.class);
    private String accountIdString;
    private String serviceIdString;
    private String instanceIdString;
    private String responseType;
    private String topNCountString;
    private String fromTimeString;
    private String toTimeString;
    private String aggregationType;
    private String mode;
    private String tagIdString;

    private Response response;
    private Account account;
    private int serviceId;
    private int instanceId;
    private int topNCount;
    private long fromTime;
    private long toTime;
    private int tagId;
    private BasicEntity serviceDetails;
    private com.heal.configuration.pojos.CompInstClusterDetails instanceDetails;
    private List<String> agents = null;
    private List<BasicTransactionEntity> taggedTransactionList;
    private List<BasicTransactionEntity> serviceTransactions;
    private Service service;
    List<String> agentList;

    public GenericResponse validateAndPopulate() {
        LOGGER.trace("{} validateAndPopulate().", Constants.INVOKED_METHOD);

        if (!validateAccount()) {

            return CommonUtils.getGenericResponse(this.getResponse(), StatusResponse.FAILURE.toString(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, Constants.MESSAGE_INVALID_ACCOUNT,
                    null, true);
        }

        if (!validateParamType() || !validateAggregationType()) {
            return CommonUtils.getGenericResponse(this.getResponse(), StatusResponse.FAILURE.toString(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, Constants.MESSAGE_INVALID_PARAMETERS,
                    null, true);
        }

        // Assigning default type as application if nothing is received.
        this.setAggregationType((this.getAggregationType() != null) ? this.getAggregationType() :
                Constants.APPLICATION_CONTROLLER_TYPE);

        if (this.getResponseType() == null) {
            this.setResponseType(ConfProperties.getString(Constants.TRANSACTION_TYPE,
                    Constants.TRANSACTION_TYPE_DEFAULT));
        }
        populateTaggedTransactionList();

        return null;
    }

    public boolean validateAggregationType() {

        if (Constants.APPLICATION_CONTROLLER_TYPE.equalsIgnoreCase(this.getAggregationType())) {

            if (!validateServiceId(false)) {
                return false;
            }

        } else if (Constants.SERVICES_CONTROLLER_TYPE.equalsIgnoreCase(this.getAggregationType())) {

            return validateServiceId(true);

        } else if (Constants.COM_INSTANCE_TYPE.equalsIgnoreCase(this.getAggregationType())) {

            if (!validateInstance()) {
                return false;
            }
            agents = instanceDetails.getAgentIds();

        } else {

            LOGGER.error("Invalid type received: {}.", this.getAggregationType());
            return false;

        }

        LOGGER.debug("Valid type: {}, received.", this.getAggregationType());
        return true;
    }

    private boolean validateInstance() {
        com.heal.configuration.pojos.CompInstClusterDetails compInstance = HealUICache.INSTANCE.getServiceInstanceList(this.getAccount().getIdentifier(), this.getServiceDetails().getIdentifier(), true)
                .stream()
                .filter(i -> i.getId() == this.getInstanceId())
                .map(b -> new InstanceRepo().getInstanceDetailsWithInstIdentifier(this.getAccount().getIdentifier(), b.getIdentifier()))
                .findAny()
                .orElse(null);
        if (compInstance == null) {
            LOGGER.error("Invalid instance id: {}, provided.", this.getInstanceId());
            return false;
        } else {
            LOGGER.debug("Instance id is valid: {}.", this.getInstanceId());
            this.setInstanceDetails(compInstance);
            return true;
        }
    }

    public boolean validateServiceId(boolean isService) {

        BasicEntity basicEntity;
        if (isService) {
            basicEntity = new ServiceRepo().getBasicServiceDetailsWithServiceId(this.getAccount().getIdentifier(), this.getServiceId());
        } else {
            basicEntity = new ApplicationRepo().getApplicationDetailsWithAppId(this.getAccount().getIdentifier(), this.getServiceId());
        }
        if (basicEntity == null) {
            LOGGER.error("Invalid service id: {}.", this.getServiceId());
            return false;
        } else {
            LOGGER.debug("Service id: {},  is valid", this.getServiceId());
            this.setServiceDetails(basicEntity);
            return true;
        }
    }

    public boolean validateParamType() {
        try {
            this.setServiceId(Integer.parseInt(this.getServiceIdString()));
            this.setInstanceId(Integer.parseInt(this.getInstanceIdString()));
            this.setTopNCount(Integer.parseInt(this.getTopNCountString()));
            this.setFromTime(Long.parseLong(this.getFromTimeString()));
            this.setToTime(Long.parseLong(this.getToTimeString()));
            this.setTagId(Integer.parseInt(this.getTagIdString()));

        } catch (NumberFormatException ne) {
            LOGGER.error("Non numerical input found.", ne);
            return false;
        } catch (Exception e) {
            LOGGER.error("Error occurred while validating format of numerical input.", e);
            return false;
        }
        return true;
    }

    public boolean validateAccount() {
        com.heal.configuration.pojos.Account accountTemp = new AccountRepo().getAccount(this.getAccountIdString());
        if (accountTemp == null) {
            LOGGER.error("Invalid account id: {}, received.", this.getAccountIdString());
            return false;
        } else {
            this.setAccount(accountTemp);
            LOGGER.debug("Account validation successful for accId: {}.", this.getAccountIdString());
            return true;
        }
    }

    public List<TransactionQueryParams> getQueryParams(List<RollupTimeMetaData> rollupTimeMetaDataList) {
        List<TransactionQueryParams> result = new ArrayList<>();

        if (this.getAggregationType().equalsIgnoreCase(Constants.SERVICES_CONTROLLER_TYPE)) {
            LOGGER.debug("Service level transaction aggregation requested.");

            rollupTimeMetaDataList.forEach(aggLevel -> {
                TransactionQueryParams temp = TransactionQueryParams.builder()
                        .accountId(this.getAccount().getIdentifier())
                        .serviceId(this.getServiceDetails().getIdentifier())
                        .agentId(Constants.CASSANDRA_ALL_IDENTIFIER)
                        .responseType(Constants.TRANSACTION_TYPE_DEFAULT)
                        .aggLevel(aggLevel.getAggLevel())
                        .fromTime(aggLevel.getFrom())
                        .toTime(aggLevel.getTo())
                        .build();
                result.add(temp);
            });

        } else {
            LOGGER.debug("Instance level transaction aggregation requested.");
            agents.forEach(agent ->

                    rollupTimeMetaDataList.forEach(aggLevel -> {

                        TransactionQueryParams temp = TransactionQueryParams.builder()
                                .accountId(this.getAccount().getIdentifier())
                                .serviceId(Constants.CASSANDRA_ALL_IDENTIFIER)
                                .agentId(agent)
                                .responseType(Constants.TRANSACTION_TYPE_DEFAULT)
                                .aggLevel(aggLevel.getAggLevel())
                                .fromTime(aggLevel.getFrom())
                                .toTime(aggLevel.getTo())
                                .build();
                        result.add(temp);
                    }));
        }

        return result;
    }

    public void populateTaggedTransactionList() {
        long start = System.currentTimeMillis();
        ServiceRepo serviceRepo = new ServiceRepo();
        BasicEntity service = serviceRepo.getBasicServiceDetailsWithServiceId(this.getAccount().getIdentifier(), this.getServiceId());
        if (service == null) {
            LOGGER.warn("Service id does not exist. AccountId:{}, ServiceId:{}", this.getAccount().getIdentifier(), this.getServiceId());
            return;
        }
        List<BasicTransactionEntity> transactions = serviceRepo.getTransactionsByServiceIdentifier(this.getAccount().getIdentifier(), service.getIdentifier())
                .stream()
                .filter(c -> c.getStatus() == 1)
                .filter(c -> c.getMonitorEnabled() == 1)
                .collect(Collectors.toList());
        LOGGER.debug("Found {} mapped transactions for the serviceId: {}.", transactions.size(),
                this.getServiceId());
        if (this.getTagId() == 0) {
            this.taggedTransactionList = transactions;
        } else {
            this.taggedTransactionList = transactions.stream()
                    .filter(txn -> (txn.getTransactionGroups().stream().anyMatch(it -> it.getTransactionGroupId() == tagId)))
                    .collect(Collectors.toList());
        }
        LOGGER.debug("Found {} transactions for the given tagId: {} in service: {}",
                this.getTaggedTransactionList().size(), this.getTagId(), this.getServiceId());
        LOGGER.debug("Time taken to fetch transaction configuration is: {} ms.", (System.currentTimeMillis() - start));
    }
}

