package com.appnomic.appsone.api.pojo;

import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR> : 07/01/20
 */
@Data
public class StaticThresholdData implements Comparable<StaticThresholdData>{
    private long thresholdUpdateTime;
    private long thresholdEndTime;
    private String  operationType;
    private Map<String, Double> thresholds;

    @Override
    public int compareTo(StaticThresholdData o) {
        if (this.thresholdUpdateTime < o.thresholdUpdateTime) return -1;
        if (this.thresholdUpdateTime > o.thresholdUpdateTime) return 1;
        return 0;
    }

    public Double getMinThreshold() {
        if (thresholds != null) return thresholds.get("MIN");
        return null;
    }

    public Double getMaxThreshold() {
        if (thresholds != null) return thresholds.get("MAX");
        return null;
    }
}
