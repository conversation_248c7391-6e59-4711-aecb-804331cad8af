package com.appnomic.appsone.api.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MaintenanceDetails {

    private int id;
    private String name;
    private int typeId;
    private String maintenanceType;
    private int accountId;
    private String accountIdentifier;
    private String serviceIdentifier;
    private boolean status = true;
    private Timestamp startTime;
    private Timestamp endTime;
    private Timestamp createdTime;
    private Timestamp updatedTime;
    private String userDetails;
    private int serviceId;
    private int isCustom;
    private RecurringBean recurring;
}
