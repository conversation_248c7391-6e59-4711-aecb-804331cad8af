package com.appnomic.appsone.api.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TransactionAuditData {

    private long startTime;
    private long endTime;
    private double responseTime;
    private String status;
    private String operationType;
    private String thresholdType;
    private Map<String, Double> thresholds;
    private int responseCode;
    private String clientIP;
    private String clientPort;
    private String serverIP;
    private String serverPort;
    private List<Details> additionalDetails;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Details {

        private String displayName;
        private String lookupName;
        private String value;
    }
}
