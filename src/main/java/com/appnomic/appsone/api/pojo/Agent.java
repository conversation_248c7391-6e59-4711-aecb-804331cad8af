package com.appnomic.appsone.api.pojo;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.util.StringUtils;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> : 6/2/19
 */
@Data
public class Agent {
    private String uniqueToken;
    private String name;
    private String subType;
    private int status;
    private String hostAddress;
    private String mode;
    private String description;
    private List<AgentAccountMappings> accountMappings;
    private List<AgentCompInstMappingDetails> compInstIdentifiers;
    private ComponentAgent agentMappingDetails;
    private int compInstanceId;

    public void validate() throws Exception {
        if (this.uniqueToken != null && uniqueToken.isEmpty()) throw new Exception("uniqueToken can not be empty.");
        if (StringUtils.isEmpty(this.name)) throw new Exception("name can not be null or empty.");
        if (StringUtils.isEmpty(this.subType)) throw new Exception("sub type can not be null or empty.");
        if (StringUtils.isEmpty(this.hostAddress)) throw new Exception("host address can not be null or empty.");
        if (StringUtils.isEmpty(this.mode)) throw new Exception("mode can not be null or empty.");

        if (this.agentMappingDetails != null) {
            this.agentMappingDetails.validate();
        }

        if (this.compInstIdentifiers != null && !this.compInstIdentifiers.isEmpty()) {
            for (AgentCompInstMappingDetails compInstId : this.compInstIdentifiers) compInstId.validate();
        }

        if (this.accountMappings != null && !this.accountMappings.isEmpty()) {
            for (AgentAccountMappings accountMappings : this.accountMappings) {
                List<Tags> tagList = accountMappings.getTags();
                if (tagList == null || tagList.isEmpty())
                    throw new Exception("Tag list can not be or empty. Only one service is allowed in the tag list.Application into the tag list is optional.");
                int serviceCount = 0;
                int applicationCount = 0;
                for (Tags tag : tagList) {
                    if (StringUtils.isEmpty(tag.getIdentifier()))
                        throw new Exception("Tag identifier can not be null or empty.");
                    if (StringUtils.isEmpty(tag.getName())) throw new Exception("Tag name can not be null or empty.");
                    if (tag.getValue() != null && tag.getValue().isEmpty())
                        throw new Exception("Tag value can not be or empty.");
                    if (StringUtils.isEmpty(tag.getSubTypeName()))
                        throw new Exception("Tag subTypeName can not be null or empty.");
                    if (tag.getSubTypeName().equals(Constants.SERVICES_CONTROLLER_TYPE)) serviceCount++;
                    else if (tag.getSubTypeName().equals(Constants.APPLICATION_CONTROLLER_TYPE)) applicationCount++;
                }
                if (serviceCount > 1) throw new Exception("Only one service is allowed in the tag list.");
                if (applicationCount > 1) throw new Exception("Only one application is allowed in the tag list.");
            }
        }
    }
}
