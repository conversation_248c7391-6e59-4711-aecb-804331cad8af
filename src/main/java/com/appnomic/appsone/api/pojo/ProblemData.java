package com.appnomic.appsone.api.pojo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> : 07/03/2019
 */
@Data
public class ProblemData {
    private String id;
    private String type;
    private int status = 0;
    private String description = "under construction...";
    private long startTimeMilli;
    private Set<Application> applications;
    private Long endTimeMilli = null;
    private int severity;

    public void addApplications(List<Controller> apps){
        if(apps != null){
            if(applications == null){
                applications = new HashSet<>();
            }
            apps.forEach(app -> applications.add(new Application(Integer.valueOf(app.getAppId()),
                    app.getName())));
        }
    }


    @AllArgsConstructor
    @EqualsAndHashCode
    @Data
    static class Application{
        private int id;
        private String name;
    }
}
