package com.appnomic.appsone.api.pojo;

import com.appnomic.appsone.api.util.StringUtils;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR> : 24/1/19
 */
@Data
@Builder
public class Tags {
    private int tagId;
    private String name;
    private String value;
    private String identifier;
    private String subTypeName;
    private String layer;

    public void validate() throws Exception {
        if (StringUtils.isEmpty(this.identifier)) throw new Exception("Tag identifier can not be null or empty.");
        if (StringUtils.isEmpty(this.name)) throw new Exception("Tag name can not be null or empty.");
        if (this.value != null && this.value.length() < 1) throw new Exception("Tag value can not be or empty.");
        if (StringUtils.isEmpty(this.subTypeName)) throw new Exception("Tag subTypeName can not be null or empty.");
    }
}
