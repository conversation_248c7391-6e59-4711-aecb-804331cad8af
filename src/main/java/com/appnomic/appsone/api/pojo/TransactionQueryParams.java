package com.appnomic.appsone.api.pojo;

import com.heal.configuration.pojos.Transaction;
import lombok.Builder;
import lombok.Data;

import java.util.Set;

@Data
@Builder
public class TransactionQueryParams {

    private String accountId;
    private String groupId;
    private String applicationId;
    private String serviceId;
    private String txnId;
    private int aggLevel;
    private String txnKpiType;
    private String responseType;
    private long fromTime;
    private long toTime;
    private String agentId;
    private int transactionId;
    private Transaction txn;
    private long timezoneOffsetFromGMT;
    private String requestType;
    private Set<String> agentIdList;

}
