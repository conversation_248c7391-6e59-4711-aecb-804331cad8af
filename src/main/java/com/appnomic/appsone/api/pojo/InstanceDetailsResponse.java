package com.appnomic.appsone.api.pojo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class InstanceDetailsResponse {

     String response_message;
     String response_status;
     List<InstanceDetails> data;

    @Data
    public static class InstanceDetails{
        private Integer id;
        private String name;
        private int hostId;
        private int agentCount;
        private String hostName;
        private String ip;
        private int port;
        List<KpiCategory> categories = new ArrayList<>();
    }
}
