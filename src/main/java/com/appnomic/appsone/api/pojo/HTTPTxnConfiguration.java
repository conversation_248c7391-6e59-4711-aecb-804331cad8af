package com.appnomic.appsone.api.pojo;

import com.appnomic.appsone.api.beans.MasterSubTypeBean;
import com.appnomic.appsone.api.beans.MasterTypeBean;
import com.appnomic.appsone.api.beans.TransactionMatcherDetailsBean;
import com.appnomic.appsone.api.beans.ViewTypes;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.cache.MasterCache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.dao.redis.MasterRepo;
import com.appnomic.appsone.api.service.mysql.TransactionDataService;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.api.util.DateTimeUtil;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR> : 23/1/19
 */
@Data
public class HTTPTxnConfiguration {
    private String type;
    private String urlPattern;
    private List<Map<String, String>> queryParam;
    private String headerPattern;
    private String bodyPattern;
    private static final String attributeTypeName = ConfProperties.getString(Constants.TRANSACTION_ATTRIBUTES_NAME,
            Constants.TRANSACTION_ATTRIBUTES_NAME_DEFAULT);
    private static final String queryParamType = ConfProperties.getString(Constants.TRANSACTION_ATTRIBUTE_QUERY_PARAMS_TYPE,
            Constants.TRANSACTION_ATTRIBUTE_QUERY_PARAMS_TYPE_DEFAULT);


    public TransactionMatcherDetailsBean getHTTPTransactionMatcherDetailsBean(String attributeName, String attributePattern, String userId) {

        TransactionMatcherDetailsBean transactionMatcherDetailsBean = null;

        com.heal.configuration.pojos.ViewTypes subType = new MasterRepo().getTypes().stream()
                .filter(v -> v.getTypeName().equalsIgnoreCase(attributeTypeName))
                .filter(v -> v.getSubTypeName().equalsIgnoreCase(attributeName))
                .findAny().orElse(null);
        if(subType == null) {
            return transactionMatcherDetailsBean;
        }

        transactionMatcherDetailsBean = new TransactionMatcherDetailsBean();
        transactionMatcherDetailsBean.setTransactionAttributeId(subType.getSubTypeId());
        transactionMatcherDetailsBean.setAttribute1(attributePattern);
        transactionMatcherDetailsBean.setUserDetailsId(userId);
        transactionMatcherDetailsBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        transactionMatcherDetailsBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));

        if (attributeName.equalsIgnoreCase(queryParamType)) { // Query params will have key value pairs.
            String[] paramsKeyValue = attributePattern.split(",");
            transactionMatcherDetailsBean.setAttribute1(paramsKeyValue[0].trim());
            transactionMatcherDetailsBean.setAttribute2(paramsKeyValue[1].trim());
        } else {
            transactionMatcherDetailsBean.setAttribute1(attributePattern);
        }
        return transactionMatcherDetailsBean;
    }
}
