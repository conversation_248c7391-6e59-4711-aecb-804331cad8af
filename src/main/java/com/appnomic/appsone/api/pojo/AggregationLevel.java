package com.appnomic.appsone.api.pojo;

public enum AggregationLevel {
    //Unused, just to avoid build errors
    MINUTELY (1, 60,  "HH:mm", "m", 1),
    QUARTERHOURLY(1, 4, "HH:mm", "m", 15),
    HOURLY (60, 24, "HH:mm", "h", 1),
    SIXHOURLY(60,4,"HH:mm", "h", 6),
    DAILY (1440, 31, "MM-DD", "d", 1),
    //End of unused values
    MINUTELY_HALFHOUR (1, 30, "HH:mm", "m", 1),
    MINUTELY_FULLHOUR (1, 60,  "HH:mm", "m", 1),
    FIFTEENMINUTELY_FOURHOUR (15,16, "HH:mm", "m", 15),
    THIRTYMINUTELY_TWELVEHOUR (30, 24, "HH:mm", "m", 30),
    HOURLY_TWENTYHOUR (60, 24, "HH:mm", "h", 1),
    DAILY_WEEK (1440, 7, "MM-DD", "d", 1),
    DAILY_MONTH (1440, 31, "MM-DD", "d", 1),
    MONTHLY (44640, 12, "YYYY-MM","mo",1),
    YEARLY (525600, 3, "YYYY", "y", 1),
    INVALID_RANGE(0,0,"HH:mm","m",0);


    private final Integer aggregationValue;
    private final Integer noOfPoints;
    private final String dataTimePattern;
    private final String incrementScale;
    private final Integer incrementValue;

    AggregationLevel(int aggregationValue, int noOfPoints, String dataTimePattern, String incrementScale, int incrementValue)  {
        this.aggregationValue = aggregationValue;
        this.noOfPoints = noOfPoints;
        this.dataTimePattern = dataTimePattern;
        this.incrementScale = incrementScale;
        this.incrementValue = incrementValue;
    }

    public int getAggregrationValue()   {
        return this.aggregationValue;
    }

    public int getNoOfPoints()  {
        return this.noOfPoints;
    }

    public String getDataTimePattern() { return  this.dataTimePattern; }

    public String getIncrementScale() {
        return this.incrementScale;
    }

    public Integer getIncrementValue() {
        return this.incrementValue;
    }
}
