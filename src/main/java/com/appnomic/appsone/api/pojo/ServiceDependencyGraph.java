package com.appnomic.appsone.api.pojo;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> : 19/04/2019
 */
public class ServiceDependencyGraph {
    private static final Logger logger = LoggerFactory.getLogger(ServiceDependencyGraph.class);
    private final Map<String, List<TopologyDetailsResponse.Nodes>> graph;
    private final Map<String, TopologyDetailsResponse.Nodes> affectedNodesMap;

    public ServiceDependencyGraph() {
        graph = new HashMap<>();
        affectedNodesMap = new HashMap<>();
    }

    public ServiceDependencyGraph(List<TopologyDetailsResponse.Nodes> nodeList, List<TopologyDetailsResponse.Edges> edgeList,
                                  List<TopologyDetailsResponse.Nodes> affectedNodes) {
        graph = new HashMap<>();
        if (affectedNodes == null) {
            affectedNodesMap = new HashMap<>();
        } else {
            this.affectedNodesMap = affectedNodes.stream()
                    .collect(Collectors.toMap(TopologyDetailsResponse.Nodes::getId, Function.identity()));
        }

        nodeList.forEach(node -> this.graph.put(node.getId(), new ArrayList<>()));
        edgeList.forEach(edge -> {
            if (this.graph.get(edge.getSource()) != null) {
                if (affectedNodesMap.get(edge.getTarget()) == null)
                    this.graph.get(edge.getSource()).add(new TopologyDetailsResponse.Nodes(edge.getTarget()));
                else
                    this.graph.get(edge.getSource()).add(affectedNodesMap.get(edge.getTarget()));
            }
        });
    }

    public void addEdge(String node1, String node2) {
        graph.computeIfAbsent(node1, k -> new ArrayList<>());
        graph.get(node1).add(new TopologyDetailsResponse.Nodes(node2));
    }

    public Iterable<TopologyDetailsResponse.Nodes> getOutgoingNeighbors(String sourceNode, boolean getVoilationDetail) {
        List<TopologyDetailsResponse.Nodes> neighbors = null;
        if (getVoilationDetail) {
            neighbors = graph.get(sourceNode);
        } else {
            neighbors = new ArrayList<>();
            if (graph.get(sourceNode) != null && !graph.get(sourceNode).isEmpty()) {
                neighbors.addAll(graph.get(sourceNode));
            }
        }
        if (neighbors == null) {
            return Collections.emptyList();
        } else {
            return Collections.unmodifiableList(neighbors);
        }
    }

    public Iterable<TopologyDetailsResponse.Nodes> getIncomingNeighbors(String sourceNode, boolean getVoilationDetail) {
        try {
            List<TopologyDetailsResponse.Nodes> neighbors = new ArrayList<>();
            this.graph.forEach((key, value) -> {
                if (value.stream().anyMatch(it -> (it.getId().equals(sourceNode)))) {
                    if (this.affectedNodesMap.get(key) == null || !getVoilationDetail)
                        neighbors.add(new TopologyDetailsResponse.Nodes(key));
                    else
                        neighbors.add(this.affectedNodesMap.get(key));
                }
            });

            if (neighbors.isEmpty()) {
                return Collections.emptyList();
            } else {
                return Collections.unmodifiableList(neighbors);
            }
        } catch (Exception e) {
            logger.warn("Error occurred while fetching incoming nodes.", e);
            return null;
        }
    }
}
