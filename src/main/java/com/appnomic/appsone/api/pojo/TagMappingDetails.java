package com.appnomic.appsone.api.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TagMappingDetails {
    int id;
    int tagId;
    int objectId;
    String objectRefTable;
    String tagKey;
    String tagValue;
    int accountId;
    String userDetailsId;
    String createdTime;
    String updatedTime;
}
