package com.appnomic.appsone.api.pojo;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class RecurringBean {

    private int id;
    private String recurringType;
    private int recurringTypeId;
    private int day;
    private int week;
    private int month;
    private String weeknames;
    private String startHour;
    private String endHour;
    private long duration;
    private String recurringData;
    private Timestamp createdTime;
    private Timestamp updatedTime;
    private String userDetails;
}
