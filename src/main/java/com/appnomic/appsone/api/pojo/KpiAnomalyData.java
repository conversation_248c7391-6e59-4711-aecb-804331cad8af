package com.appnomic.appsone.api.pojo;

import lombok.Data;

/**
 * <AUTHOR> : 4/7/2019
 */

@Data
public class KpiAnomalyData {
    private long time;
    private String value;
    private Double minThreshold;
    private Double maxThreshold;
    private boolean forensicAvailable = false;
    private Long forensicTime;
    private int instanceId;
    private String instanceName;
    private String operationType;

    public KpiAnomalyData setTime(long time)    {
        this.time = time;
        return this;
    }

    public KpiAnomalyData setMaxThreshold(Double maxThreshold) {
        this.maxThreshold = maxThreshold;
        return this;
    }

    public KpiAnomalyData setLowerThreshold(Double lowerThreshold) {
        this.minThreshold = lowerThreshold;
        return this;
    }

    public KpiAnomalyData setValue(String value) {
        this.value = value;
        return this;
    }

    public KpiAnomalyData setInstanceId(int instanceId) {
        this.instanceId = instanceId;
        return this;
    }

    public KpiAnomalyData setInstanceName(String instanceName) {
        this.instanceName = instanceName;
        return this;
    }

    public KpiAnomalyData setOperationType(String operationType) {
        this.operationType = operationType;
        return this;
    }

    @Override
    protected Object clone() throws CloneNotSupportedException {
        KpiAnomalyData clone = (KpiAnomalyData) super.clone();
        clone.setTime(this.getTime());
        clone.setValue(this.getValue());
        clone.setMinThreshold(this.getMinThreshold());
        clone.setMaxThreshold(this.getMaxThreshold());
        clone.setForensicAvailable(this.isForensicAvailable());
        clone.setForensicTime(this.getForensicTime());
        clone.setInstanceId(this.getInstanceId());
        clone.setInstanceName(this.getInstanceName());
        clone.setOperationType(this.getOperationType());
        return clone;
    }
}
