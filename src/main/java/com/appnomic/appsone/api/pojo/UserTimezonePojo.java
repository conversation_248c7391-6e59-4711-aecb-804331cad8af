package com.appnomic.appsone.api.pojo;

import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import lombok.Data;

import java.text.MessageFormat;

@Data
public class UserTimezonePojo {
    int isTimezoneMychoice;
    int isNotificationsTimezoneMychoice;
    int timezoneId;

    public void validate() throws ClientException {
        if (this.isTimezoneMychoice<0 || this.isTimezoneMychoice>1) {
            throw new ClientException(MessageFormat.format(UIMessages.ERROR_INVALID_INPUT_PARAM,
                    "isTimezoneMychoice", this.isTimezoneMychoice));
        }
        if (this.isNotificationsTimezoneMychoice<0 || this.isNotificationsTimezoneMychoice>1) {
            throw new ClientException(MessageFormat.format(UIMessages.ERROR_INVALID_INPUT_PARAM,
                    "isNotificationsTimezoneMychoice", this.isNotificationsTimezoneMychoice));
        }
        if (this.timezoneId<0) {
            throw new ClientException(MessageFormat.format(UIMessages.ERROR_INVALID_INPUT_PARAM,
                    "timezoneId", this.timezoneId));
        }
    }
}
