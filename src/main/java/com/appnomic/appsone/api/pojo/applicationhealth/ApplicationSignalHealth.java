package com.appnomic.appsone.api.pojo.applicationhealth;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.Set;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApplicationSignalHealth {
    private ApplicationHealthDetail applicationHealthDetail;
    private Set<String> severeProblemCountSignalIds = new HashSet<>();
    private Set<String> severeWarningCountSignalIds = new HashSet<>();
    private Set<String> severeBatchCountSignalIds = new HashSet<>();
    private Set<String> defaultProblemCountSignalIds = new HashSet<>();
    private Set<String> defaultWarningCountSignalIds = new HashSet<>();
    private Set<String> defaultBatchCountSignalIds = new HashSet<>();
}
