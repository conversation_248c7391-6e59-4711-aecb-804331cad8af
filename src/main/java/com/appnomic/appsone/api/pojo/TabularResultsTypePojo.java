package com.appnomic.appsone.api.pojo;

import com.appnomic.appsone.opeasearchquery.results.RawDocumentResults;
import com.appnomic.appsone.opeasearchquery.results.TabularResults;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> on 29/08/22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TabularResultsTypePojo {

    private TabularResults tabularResults;
    private Boolean isTransformed;
    private long count;
    private RawDocumentResults rawDocumentResults;

}
