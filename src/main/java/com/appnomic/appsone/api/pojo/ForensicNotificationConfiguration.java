package com.appnomic.appsone.api.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ForensicNotificationConfiguration {

    private String applicableUserId;
    private List<Integer> applicationIds;
    private int emailNotification;
    private int suppressionDuration;
    private List<IdAction> applications;
    private String userDetailsId;
    private int accountId;


    private static final Logger LOGGER = LoggerFactory.getLogger(ForensicNotificationConfiguration.class);

    public boolean validate() {
        boolean val = true;

        if (emailNotification != 0 && emailNotification != 1) {
            LOGGER.error("emailNotification should ve 0 or 1 only.");
            val = false;
        }

        if (suppressionDuration < 1) {
            LOGGER.error("suppressionDuration should not be less than 1.");
            val = false;
        }

        if (applications.parallelStream().anyMatch(app -> app.getId() < 1)) {
            LOGGER.error("Invalid application ID.");
            val = false;
        }

        return val;
    }
}
