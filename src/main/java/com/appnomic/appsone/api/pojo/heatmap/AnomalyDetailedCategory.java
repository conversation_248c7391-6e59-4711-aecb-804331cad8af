package com.appnomic.appsone.api.pojo.heatmap;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> : 11/07/2019
 */

@Data
@EqualsAndHashCode
public class AnomalyDetailedCategory {
    private int categoryId;
    private int instanceId;
    private String categoryIdentifier;
    private String behaviourType; //change this to ENUM
    @EqualsAndHashCode.Exclude
    private long anomalyCount;
    @EqualsAndHashCode.Exclude
    private int dataExists;
    @EqualsAndHashCode.Exclude
    private boolean configChanges;
}