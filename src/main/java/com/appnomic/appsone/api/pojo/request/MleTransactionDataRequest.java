package com.appnomic.appsone.api.pojo.request;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.pojo.AggregationLevel;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.ConfProperties;
import com.heal.configuration.pojos.Account;
import lombok.Data;
import spark.Request;
import spark.Response;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class MleTransactionDataRequest {
    private Response response;
    private String accountIdString;
    private String requestType;
    private String applicationIdString;
    private String responseType;
    private String fromTimeString;
    private String toTimeString;
    private String[] transactionKpiArray;

    private Account account;
    private AggregationLevel aggregationLevel = AggregationLevel.MINUTELY;
    private int applicationId;
    private long fromTime;
    private long toTime;
    private String applicationIdentifier;
    private List<Integer> transactionKpi;


    public MleTransactionDataRequest(Request request, Response response) {
        this.response = response;
        this.setAccountIdString(request.params(":identifier"));
        this.setRequestType(request.queryParams("type"));
        this.setApplicationIdString("0");
        this.setResponseType("DC");
        this.setFromTimeString(request.queryParams("fromTime"));
        this.setToTimeString(request.queryParams("toTime"));
        if(this.getRequestType() == null ||
                this.getRequestType().equalsIgnoreCase(Constants.APPLICATION_CONTROLLER_TYPE)) {
            this.setApplicationIdString(request.params(":id"));
        }
        this.setTransactionKpiArray(request.queryParamsValues("transactionKpi"));
    }

    public GenericResponse validate() {
        try {
            this.setApplicationId(Integer.parseInt(this.getApplicationIdString()));
            this.setFromTime(Long.parseLong(this.getFromTimeString()));
            this.setToTime(Long.parseLong(this.getToTimeString()));
            this.validateTransactionKpi();
        } catch ( NumberFormatException ne ) {
            return CommonUtils.getGenericResponse(this.getResponse(), StatusResponse.FAILURE.toString(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, Constants.MESSAGE_INVALID_NUMBER_FORMAT,
                    ne, true);
        }

        this.setAccount(new AccountRepo().getAccount(this.getAccountIdString()));
        if( this.getAccount() == null ) {
            return CommonUtils.getGenericResponse(this.getResponse(), StatusResponse.FAILURE.toString(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, Constants.MESSAGE_INVALID_ACCOUNT,
                    null, true);
        }

        if( this.getFromTime() > this.getToTime() ) {
            return CommonUtils.getGenericResponse(this.getResponse(), StatusResponse.FAILURE.toString(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, Constants.MESSAGE_INVALID_PARAMETERS,
                    null, true);
        }

        if(this.getResponseType() == null)    {
            this.setResponseType(ConfProperties.getString(Constants.TRANSACTION_TYPE,
                    Constants.TRANSACTION_TYPE_DEFAULT));
        }

        return null;
    }

    private void validateTransactionKpi() {
        if (this.getTransactionKpiArray() != null)
            this.setTransactionKpi(Arrays.stream(this.getTransactionKpiArray()).map(Integer::parseInt)
                    .collect(Collectors.toList()));
    }
}
