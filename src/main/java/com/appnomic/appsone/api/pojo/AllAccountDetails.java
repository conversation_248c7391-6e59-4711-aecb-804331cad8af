package com.appnomic.appsone.api.pojo;

import com.appnomic.appsone.api.beans.TagDetailsBean;
import lombok.Data;

import java.util.List;


@Data
public class AllAccountDetails {

    List<TagMappingDetails> tagMappingDetailsList;
    List<ConnectionDetails> connectionDetailsList;
    List<CompInstClusterDetails> compInstanceDetailsList;
    List<ComponentKpis> componentKpisList;
    List<TagDetailsBean>  tagDetailsBeanList;
    List<AllKpiList> allKpiLists;
    List<ClusterInstanceMapping> clusterInstanceMappingList;
    List<TxnAndGroupBean> txnAndGroupBeanList;
    List<WindowProfileBean> windowProfileBeanList;
}
