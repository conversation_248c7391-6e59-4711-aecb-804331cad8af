package com.appnomic.appsone.api.pojo;

import com.appnomic.appsone.api.common.ClusterOperation;
import lombok.Data;
//import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
//@JsonInclude(JsonInclude.Include.NON_NULL)
public class ComponentKpiConfig {

    private String name;
    private int id;
    private String kpiType;
    private Boolean isGroup;
    private String groupName;
    private int groupId;
    private ClusterOperation aggOperation;
    private ClusterOperation rollUpOperation;
    private String clusterAggType;
    private String instanceAggType;
    private Map<String, List<KpiViolationConfig>> kpiViolationConfigMap = new HashMap<>();
}
