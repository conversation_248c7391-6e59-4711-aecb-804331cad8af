package com.appnomic.appsone.api.pojo;

import com.appnomic.appsone.api.beans.SubTransactionBean;
import com.appnomic.appsone.api.beans.TransactionMatcherDetailsBean;
import com.appnomic.appsone.api.util.DateTimeUtil;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Parekaden : 23/1/19
 */
@Data
public class SubTransaction {
    HTTPTxnConfiguration httpTxnConfig;
    TCPTxnConfiguration tcpTxnConfig;

    public SubTransactionBean getSubTransactionBean(String userId, String txnType) {
        SubTransactionBean subTransactionBean = new SubTransactionBean();
        subTransactionBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        subTransactionBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        subTransactionBean.setUserDetailsId(userId);

        if(txnType.equalsIgnoreCase(TransactionType.HTTP.name()))   {
            HTTPTxnConfiguration httpTxnConfiguration = this.getHttpTxnConfig();
            subTransactionBean.setHttpMethod(httpTxnConfiguration.getType().trim());
            subTransactionBean.setHttpUrl(httpTxnConfiguration.getUrlPattern().trim());

            if(httpTxnConfiguration.getHeaderPattern() != null && httpTxnConfiguration.getHeaderPattern().trim().length() > 0)   {
                TransactionMatcherDetailsBean transactionMatcherDetailsBean = httpTxnConfiguration.getHTTPTransactionMatcherDetailsBean("Header",
                        httpTxnConfiguration.getHeaderPattern().trim(), userId);
                if(transactionMatcherDetailsBean != null)
                    subTransactionBean.getTransactionMatcherDetailsList().add(transactionMatcherDetailsBean);
            }

            if(httpTxnConfiguration.getBodyPattern() != null && httpTxnConfiguration.getBodyPattern().trim().length() > 0)  {
                TransactionMatcherDetailsBean transactionMatcherDetailsBean = httpTxnConfiguration.getHTTPTransactionMatcherDetailsBean("Body",
                        httpTxnConfiguration.getBodyPattern().trim(), userId);
                if(transactionMatcherDetailsBean != null)
                    subTransactionBean.getTransactionMatcherDetailsList().add(transactionMatcherDetailsBean);
            }

            if(httpTxnConfiguration.getQueryParam() != null && !httpTxnConfiguration.getQueryParam().isEmpty()) {
                List<Map<String,String>> queryParametersList = httpTxnConfiguration.getQueryParam();

                for (Map<String,String> queryParameters: queryParametersList)  {
                        queryParameters.forEach((key, value) -> {
                            TransactionMatcherDetailsBean transactionMatcherDetailsBean = httpTxnConfiguration.getHTTPTransactionMatcherDetailsBean("QueryParams",
                                    key+","+value, userId);
                            if(transactionMatcherDetailsBean != null)
                                subTransactionBean.getTransactionMatcherDetailsList().add(transactionMatcherDetailsBean);
                            return;
                        });

                }
            }

        }   else if(txnType.equalsIgnoreCase(TransactionType.TCP.name()))   {

            TCPTxnConfiguration tcpTxnConfiguration = this.getTcpTxnConfig();
            TransactionMatcherDetailsBean transactionMatcherDetailsBean = tcpTxnConfiguration.getTCPTransactionMatcherDetailsBean(
                    tcpTxnConfiguration.getTcpStartPattern(), tcpTxnConfiguration.getTcpEndPattern(),
                    tcpTxnConfiguration.getLength(), userId);
            subTransactionBean.getTransactionMatcherDetailsList().add(transactionMatcherDetailsBean);
        }
        return subTransactionBean;
    }
}
