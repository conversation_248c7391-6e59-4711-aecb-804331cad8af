package com.appnomic.appsone.api.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserAccessibleActions {

    private int roleId;
    private String role;
    private int profileId;
    private String profile;
    private int isActiveDirectory;
    private List<String> allowedActions = new ArrayList<>();
}
