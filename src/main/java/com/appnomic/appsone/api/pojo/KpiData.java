package com.appnomic.appsone.api.pojo;

import com.appnomic.appsone.api.beans.Thresholds;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class KpiData {

    private static Logger LOGGER = LoggerFactory.getLogger(KpiData.class);

    String kpi_name;
    int kpiId;
    int groupId;
    String groupDisplayValue;
    String unit;
    //String no_data_message;
    //List<String> time;
    boolean anomaly = false;
    List<Integer> alerts = new ArrayList<>();
    List<Double> value = new ArrayList<>();
    List<Double> maxResponse = new ArrayList<>();
    List<Double> minResponse = new ArrayList<>();
    List<Double> counts = new ArrayList<>();
    List<Integer> anomalyCounts;
    Thresholds normalRange = new Thresholds();
    Thresholds staticRange = new Thresholds();
    long transactionPerMinute;

    public void fixAlerts() {
        //This method was implemented to fix WAR-311, if there is no data available there should be no violation/alert
        //FYI: This issue rises due to batch processing of kpi data and real time processing of violation data.
        if (alerts.isEmpty()) {
            LOGGER.error("There are no alerts available hence skipping sanity check for violations");
            return;
        }
        // When the to time of the request aligns exactly with the time series then data sent as response cannot have
        // alert on the last data point since it is the upper bound of the time range exactly. Therefore there will
        // be instances when the alert list size will be greater than value list by 1 and only 1
        // Fix for IO-2173
        if ((alerts.size() < value.size() - 1) || (alerts.size() > value.size())) {
            LOGGER.error("There is a mis-match in number of alert points to the data points, alert points: {}, " +
                    "data points: {}", alerts.size(), value.size());
            return;
        }
        int n = Math.min(value.size(), alerts.size());
        for (int i = 0; i < n; i++) {
            if (value.get(i) == null) alerts.set(i, 0);
        }
        if (alerts.size() + 1 == value.size()) {
            alerts.add(0);
        }
    }
}
