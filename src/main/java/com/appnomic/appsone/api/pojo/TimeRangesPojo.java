package com.appnomic.appsone.api.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TimeRangesPojo {

    List<NameValuePair> ranges;
    Map<String, Map<String, Boolean>> rangeEnabled;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class NameValuePair {
        private String name;
        private String value;
    }
}
