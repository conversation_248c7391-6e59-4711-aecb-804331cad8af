package com.appnomic.appsone.api.pojo;
import com.appnomic.appsone.api.beans.MasterComponentBean;
import com.appnomic.appsone.api.beans.MasterComponentTypeBean;
import lombok.Builder;
import lombok.Data;
import java.util.List;
/**
 * <AUTHOR> <PERSON>man - 07-05-2024
 */
@Data
@Builder
public class NotificationPreferences {
    private List<GetCategoriesResponse> category;
    private List<MasterComponentBean> componentnames;
    private List<MasterComponentTypeBean> componenttypes;
}