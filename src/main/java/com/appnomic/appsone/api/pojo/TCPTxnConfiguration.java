package com.appnomic.appsone.api.pojo;

import com.appnomic.appsone.api.beans.TransactionMatcherDetailsBean;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.dao.redis.MasterRepo;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.api.util.DateTimeUtil;
import lombok.Data;


/**
 * <AUTHOR> Parekaden : 23/1/19
 */
@Data
public class TCPTxnConfiguration {
    private String tcpStartPattern;
    private Integer length;
    private String tcpEndPattern;
    private static final int globalAccountId = Integer.parseInt(ConfProperties.getString(Constants.GLOBAL_ACCOUNT_ID,
            Constants.GLOBAL_ACCOUNT_ID_DEFAULT));
    private static final String attributeTypeName = ConfProperties.getString(Constants.TRANSACTION_ATTRIBUTES_NAME,
            Constants.TRANSACTION_ATTRIBUTES_NAME_DEFAULT);
    private static final String tcpDataType = ConfProperties.getString(Constants.TRANSACTION_ATTRIBUTE_TCP_TYPE,
            Constants.TRANSACTION_ATTRIBUTE_TCP_TYPE_DEFAULT);

    public TransactionMatcherDetailsBean getTCPTransactionMatcherDetailsBean(String startPattern, String endPattern,
                                                                             Integer length, String userId)  {

        TransactionMatcherDetailsBean transactionMatcherDetailsBean = null;
        com.heal.configuration.pojos.ViewTypes subType = new MasterRepo().getTypes().stream()
                .filter(v -> v.getTypeName().equalsIgnoreCase(attributeTypeName))
                .filter(v -> v.getSubTypeName().equalsIgnoreCase(tcpDataType))
                .findAny().orElse(null);
        if(subType == null) {
            return transactionMatcherDetailsBean;
        }

        transactionMatcherDetailsBean = new TransactionMatcherDetailsBean();
        transactionMatcherDetailsBean.setTransactionAttributeId(subType.getSubTypeId());
        if(startPattern != null)
            transactionMatcherDetailsBean.setAttribute1(startPattern.trim());
        if(endPattern != null)
            transactionMatcherDetailsBean.setAttribute2(endPattern.trim());
        if(length != null)
            transactionMatcherDetailsBean.setAttribute3(length);
        transactionMatcherDetailsBean.setUserDetailsId(userId);
        transactionMatcherDetailsBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        transactionMatcherDetailsBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        return transactionMatcherDetailsBean;
    }
}
