package com.appnomic.appsone.api.dao;

import com.appnomic.appsone.api.beans.ControllerBean;
import com.appnomic.appsone.api.pojo.TagMappingDetails;
import com.appnomic.appsone.api.pojo.TagPreDefinedData;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.Define;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

/**
 * <AUTHOR> : 30/1/19
 */
public interface TagsDao {
    @SqlBatch("insert into tag_mapping (tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) values (:tagId,:objectId,:objectRefTable,:tagKey,:tagValue,:createdTime,:updatedTime,:accountId,:userDetailsId)")
    void addTagMappingDetails(@BindBean List<TagMappingDetails> tagMappingDetails);

    @SqlUpdate("INSERT INTO controller ( name, identifier, account_id, user_details_id, created_time, updated_time, controller_type_id) VALUES ( :name, :identifier, :accountId, :userDetailsId, :createdTime, :updatedTime, :controllerTypeId)")
    @GetGeneratedKeys
    int createTagEntry(@BindBean ControllerBean controllerBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select <selectSection> from <table> where <whereSection>")
    TagPreDefinedData getTagData(@Define("table") String table, @Bind("name") String tagValue, @Define("selectSection") String selectSection, @Define("whereSection") String whereSection);

    @SqlQuery("select id from tag_mapping where tag_id = :tagId and object_id = :objectId and object_ref_table = :objectRefTable and tag_key = :tagKey and tag_value = :tagValue and account_id = :accountId")
    int getTagMappingId(@Bind("tagId") int tagId, @Bind("objectId") int objectId, @Bind("objectRefTable") String objectRefTable, @Bind("tagKey") String tagKey, @Bind("tagValue") String tagValue, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT id, tag_id tagId, object_id objectId, tag_key tagKey, tag_value tagValue, account_id accountId FROM tag_mapping where tag_id = :tagId and object_ref_table = :refTable and account_id = :accountId")
    List<TagMappingDetails> getAgentTags(@Bind("tagId") int tagId, @Bind("refTable") String refTable, @Bind("accountId") int accountId);
}
