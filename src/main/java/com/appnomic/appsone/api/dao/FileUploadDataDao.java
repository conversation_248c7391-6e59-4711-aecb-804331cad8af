package com.appnomic.appsone.api.dao;

import com.appnomic.appsone.api.dao.mysql.entity.InstallationAttributeBean;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

/**
 * <AUTHOR> : 29/3/19
 */
public interface FileUploadDataDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select name, value from a1_installation_attributes")
    List<InstallationAttributeBean> getInstallationAttributes();
}
