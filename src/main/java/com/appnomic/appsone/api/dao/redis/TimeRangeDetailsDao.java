package com.appnomic.appsone.api.dao.redis;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.RedisUtilities;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.TimeRangeDetails;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class TimeRangeDetailsDao {

    public List<TimeRangeDetails> getTimeRangeDetails(String accountIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/timerangedetails";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_TIMERANGEDETAILS";

        try {
            String timeRangeDetails = RedisUtilities.getKey(key, hashKey);
            if (timeRangeDetails == null) {
                log.debug("No time range details found for account: [{}]", accountIdentifier);
                return Collections.emptyList();
            }
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(timeRangeDetails, new TypeReference<List<TimeRangeDetails>>() {
            });

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting time range details list for account [{}]. Details: ", accountIdentifier, e);
            return null;
        }
    }

}
