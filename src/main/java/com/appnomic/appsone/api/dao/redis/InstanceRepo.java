package com.appnomic.appsone.api.dao.redis;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.RedisUtilities;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.CompInstClusterDetails;
import com.heal.configuration.pojos.InstanceAttributes;
import com.heal.configuration.pojos.MaintenanceDetails;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 25-03-2022
 */
@Slf4j
public class InstanceRepo {

    public List<CompInstClusterDetails> getInstancesByAccount(String accIdentifiers) {
        String key = "/accounts/" + accIdentifiers + "/instances";
        String hashKey = "ACCOUNTS_" + accIdentifiers + "_INSTANCES";
        try {
            String appDetails = RedisUtilities.getKey(key, hashKey);
            if (appDetails == null)
                return Collections.emptyList();
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(appDetails, new TypeReference<List<CompInstClusterDetails>>() {
            });
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Exception encountered while getting key '{}': {}", key, hashKey, e);
            return Collections.emptyList();
        }
    }

    public List<CompInstClusterDetails> getClusterInstances(String accountIdentifier, String clusterIdentifier) {
        return getInstancesByAccount(accountIdentifier).stream()
                .filter(i -> !i.isCluster())
                .filter(i -> i.getClusterIdentifiers() != null && i.getClusterIdentifiers().contains(clusterIdentifier))
                .collect(Collectors.toList());
    }

    public CompInstClusterDetails getInstanceDetailsWithInstIdentifier(String accountIdentifier, String instIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/instances/" + instIdentifier;
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_INSTANCES_" + instIdentifier;

        try {
            String instDetails = RedisUtilities.getKey(key, hashKey);
            if (instDetails == null) {
                log.debug("Instance details not found for account: [{}], instance [{}]", accountIdentifier, instIdentifier);
                return null;
            }
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(instDetails, new TypeReference<CompInstClusterDetails>() {
            });

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting instance details for account [{}], instance [{}]. Details: ", accountIdentifier, instIdentifier, e);
            return null;
        }
    }

    public CompInstClusterDetails getInstanceDetailsWithId(String accountIdentifier, int instanceId) {

        try {
            return getInstancesByAccount(accountIdentifier).stream().filter(i -> i.getId() == instanceId).findAny().orElse(null);

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting instance details for account [{}], instanceId [{}]. Details: ", accountIdentifier, instanceId, e);
            return null;
        }
    }

    public List<InstanceAttributes> getInstanceAttributesWithInstanceIdentifier(String accountIdentifier, String instIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/instances/" + instIdentifier + "/attributes";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_INSTANCES_" + instIdentifier + "_ATTRIBUTES";

        try {
            String instDetails = RedisUtilities.getKey(key, hashKey);
            if (instDetails == null) {
                log.debug("Instance Attributes details not found for account: [{}], instance [{}]", accountIdentifier, instIdentifier);
                return Collections.emptyList();
            }
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(instDetails, new TypeReference<List<InstanceAttributes>>() {
            });

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting instance attribute details for account [{}], instance [{}]. Details: ", accountIdentifier, instIdentifier, e);
            return Collections.emptyList();
        }
    }

    public List<MaintenanceDetails> getMaintenanceDetails(String accountIdentifier, String instIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/instances/" + instIdentifier + "/maintenanceDetails";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_INSTANCES_" + instIdentifier + "_MAINTENANCE_DETAILS";

        try {
            String maintenanceDetailsJson = RedisUtilities.getKey(key, hashKey);
            if (maintenanceDetailsJson == null) {
                log.debug("Maintenance details not found for account: [{}], instance [{}]", accountIdentifier, instIdentifier);
                return Collections.emptyList();
            }
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(maintenanceDetailsJson, new TypeReference<List<MaintenanceDetails>>() {
            });

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting maintenance details for account [{}], instance [{}]. Details: ", accountIdentifier, instIdentifier, e);
            return Collections.emptyList();
        }
    }

    public List<BasicEntity> getServiceDetailsWithInstanceIdentifier(String accountIdentifier, String instIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/instances/" + instIdentifier + "/services";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_INSTANCES_" + instIdentifier + "_SERVICES";

        try {
            String serviceDetails = RedisUtilities.getKey(key, hashKey);
            if (serviceDetails == null) {
                log.debug("Service details not found for account: [{}], instance [{}]", accountIdentifier, instIdentifier);
                return Collections.emptyList();
            }
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(serviceDetails, new TypeReference<List<BasicEntity>>() {
            });

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting Service details for account [{}], instance [{}]. Details: ", accountIdentifier, instIdentifier, e);
            return Collections.emptyList();
        }
    }

    public List<BasicEntity> getApplications(String accountIdentifier, String instanceIdentifier) {
        return getServiceDetailsWithInstanceIdentifier(accountIdentifier, instanceIdentifier)
                .stream()
                .map(s -> HealUICache.INSTANCE.getServiceApplicationList(accountIdentifier, s.getIdentifier()))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }
}
