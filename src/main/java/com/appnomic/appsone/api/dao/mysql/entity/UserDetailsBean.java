package com.appnomic.appsone.api.dao.mysql.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDetailsBean {
    Integer id;
    String userIdentifier;
    String userName;
    String role;
    String userProfile;
    int status;
    String createdOn;
    String createdBy;
}

