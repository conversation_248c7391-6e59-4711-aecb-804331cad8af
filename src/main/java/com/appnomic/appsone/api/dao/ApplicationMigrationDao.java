package com.appnomic.appsone.api.dao;

import com.heal.configuration.entities.ApplicationMigrationDateRangesBean;
import org.skife.jdbi.v2.sqlobject.SqlQuery;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ApplicationMigrationDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, application_id applicationId, rolled_up_index_start_time rollupIndexStartTime," +
            " rolled_up_index_end_time rollupIndexEndTime, non_rolled_up_index_start_time nonRollupIndexStartTime," +
            " non_rolled_up_index_end_time nonRollupIndexEndTime, status, user_details_id userDetailsId," +
            " created_time createdTime, updated_time updatedTime from application_migration_date_ranges;")
    List<ApplicationMigrationDateRangesBean> getApplicationMigrationDateRanges();

}
