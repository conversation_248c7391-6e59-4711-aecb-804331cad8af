package com.appnomic.appsone.api.dao.redis;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.RedisUtilities;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.Category;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> on 29-03-2022
 */
@Slf4j
public class CategoryRepo {

    public Category getCategoryDetails(String accountIdentifier, String categoryIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/categories/" + categoryIdentifier;
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_CATEGORIES_" + categoryIdentifier;

        try {
            String categoryDetails = RedisUtilities.getKey(key, hashKey);
            if (categoryDetails == null) {
                log.debug("Category details not found for account: [{}], category [{}]", accountIdentifier, categoryIdentifier);
                return null;
            }
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(categoryDetails, new TypeReference<Category>() {
            });

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting category details for accountId:{}, categoryId:{}. ", accountIdentifier, categoryIdentifier, e);
            return null;
        }
    }

    public List<Category> getCategoryDetails(String accountIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/categories";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_CATEGORIES";

        try {
            String categoryDetails = RedisUtilities.getKey(key, hashKey);
            if (categoryDetails == null) {
                log.debug("Category details not found for account:{}.", accountIdentifier);
                return new ArrayList<>();
            }
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(categoryDetails, new TypeReference<List<Category>>() {
            });

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting category details for accountId:{}.", accountIdentifier, e);
            return new ArrayList<>();
        }
    }


}
