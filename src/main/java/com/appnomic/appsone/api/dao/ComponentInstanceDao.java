package com.appnomic.appsone.api.dao;

import com.appnomic.appsone.api.beans.*;
import com.appnomic.appsone.api.pojo.CompInstClusterDetails;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

/**
 * <AUTHOR> : 17/1/19
 */
public interface ComponentInstanceDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,status,host_id hostId,is_dr isDR,is_cluster isCluster,mst_component_version_id mstComponentVersionId,created_time createdTime,updated_time updatedTime,user_details_id userDetailsId,account_id accountId,mst_component_id mstComponentId,mst_component_type_id mstComponentTypeId,discovery,host_address hostAddress,identifier,mst_common_version_id mstCommonVersionId from comp_instance where id = :id")
    ComponentInstanceBean getComponentInstanceById(@Bind("id") Integer id);
}
