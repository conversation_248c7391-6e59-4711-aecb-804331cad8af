package com.appnomic.appsone.api.dao.redis;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.RedisUtilities;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.Agent;
import com.heal.configuration.pojos.BasicAgentEntity;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> on 21-03-2022
 */
@Slf4j
public class AgentRepo {

    public List<BasicAgentEntity> getAllAgents(String accountIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/agents";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_AGENTS";
        try {
            String appDetails = RedisUtilities.getKey(key, hashKey);
            if (appDetails == null) {
                log.debug("Agent details not found for account: [{}]", accountIdentifier);
                return Collections.emptyList();
            }
            return CommonUtils.getObjectMapper().readValue(appDetails, new TypeReference<List<BasicAgentEntity>>() {
            });

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting agent details for account [{}]. Details: ", accountIdentifier, e);
            return Collections.emptyList();
        }
    }

    public Agent getAgentDetails(String agentIdentifier) {
        String key = "/agents/" + agentIdentifier;
        String hashKey = "AGENTS_" + agentIdentifier;
        try {
            String agentDetails = RedisUtilities.getKey(key, hashKey);
            if (agentDetails == null) {
                log.debug("Agent details not found for agent id : [{}]", agentIdentifier);
                return null;
            }
            return CommonUtils.getObjectMapper().readValue(agentDetails, new TypeReference<Agent>() {
            });

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting agent details for agent identifier [{}]. Details: ", agentIdentifier, e);
            return null;
        }
    }
}
