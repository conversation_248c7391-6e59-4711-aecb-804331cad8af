package com.appnomic.appsone.api.dao;

import com.appnomic.appsone.api.beans.CategoryDetailBean;
import org.skife.jdbi.v2.sqlobject.Bind;
import org.skife.jdbi.v2.sqlobject.SqlQuery;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

/**
 * <AUTHOR> on 21/10/20
 */
public interface CompInstanceKpiDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id categoryId,name,identifier, is_informative isInformative,is_workload isWorkload from mst_category_details where account_id = 1 or account_id = :account_id")
    List<CategoryDetailBean> getCategoryDetails(@Bind("account_id") Integer accountId);

}