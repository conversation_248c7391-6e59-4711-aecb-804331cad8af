package com.appnomic.appsone.api.dao;

import com.appnomic.appsone.api.beans.ViewApplicationServiceMappingBean;
import com.appnomic.appsone.api.pojo.Controller;
import org.skife.jdbi.v2.sqlobject.Bind;
import org.skife.jdbi.v2.sqlobject.SqlQuery;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface ControllerDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select service_id serviceId, service_name serviceName, service_identifier serviceIdentifier, " +
            "application_id applicationId, application_name applicationName, application_identifier applicationIdentifier " +
            "from view_application_service_mapping " +
            "where account_id = :account_id")
    List<ViewApplicationServiceMappingBean> getServicesForApplicationWithAccID(@Bind("account_id") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id appId,name name,controller_type_id controllerTypeId,identifier identifier, monitor_enabled monitoringEnabled," +
            " account_id accountId, status from controller where account_id = :accountId")
    List<Controller> getControllerList(@Bind("accountId") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select service_id serviceId, service_identifier serviceIdentifier, application_id applicationId, application_identifier applicationIdentifier, " +
            "application_name applicationName from view_application_service_mapping " +
            "where account_id = :account_id")
    List<ViewApplicationServiceMappingBean> getApplicationServicesByAccount(@Bind("account_id") Integer accountId);

}
