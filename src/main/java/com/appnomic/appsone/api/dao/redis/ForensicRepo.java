package com.appnomic.appsone.api.dao.redis;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.RedisUtilities;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.Action;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
@Slf4j
public class ForensicRepo {
    public List<Action> getAllForensicActions(String accountIdentifier, String categoryIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/categories/" + categoryIdentifier + "/forensics";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_CATEGORIES_" + categoryIdentifier + "_FORENSICS";

        try {
            String allForensicActions = RedisUtilities.getKey(key, hashKey);
            if (allForensicActions == null) {
                log.debug("No forensic actions found for account [{}], category [{}]", accountIdentifier, categoryIdentifier);
                return null;
            }
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(allForensicActions, new TypeReference<List<Action>>() {
            });

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting all forensic actions for account [{}], category [{}]. Details: ", accountIdentifier, categoryIdentifier, e);
            return null;
        }

    }

}
