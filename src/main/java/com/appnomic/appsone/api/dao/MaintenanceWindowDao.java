package com.appnomic.appsone.api.dao;

import com.appnomic.appsone.api.beans.ServiceMaintenanceMapping;
import org.skife.jdbi.v2.sqlobject.Bind;
import org.skife.jdbi.v2.sqlobject.SqlQuery;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;


public interface MaintenanceWindowDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,service_id serviceId, maintenance_id maintenanceId, user_details_id userDetailsId, created_time createdTime,"+
            "updated_time updatedTime, account_id accountId from service_maintenance_mapping where service_id = :serviceId")
    List<ServiceMaintenanceMapping> getMaintenanceWindowsByServiceId(@Bind("serviceId") int serviceId);

}
