package com.appnomic.appsone.api.dao.opensearch;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.manager.OpenSearchConnectionManager;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.opeasearchquery.helper.OpenSearchQueryHelper;
import com.appnomic.appsone.opeasearchquery.queryopts.QueryOptions;
import com.appnomic.appsone.opeasearchquery.results.NameValuePair;
import com.appnomic.appsone.opeasearchquery.results.RawDocumentResults;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.enums.SignalType;
import com.heal.configuration.pojos.opensearch.SignalDetails;
import com.heal.configuration.util.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.client.RestHighLevelClient;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class SignalSearchRepo {
    private final ObjectMapper objectMapper = CommonUtils.getObjectMapper();
    private static String indexExtension = ConfProperties.getString(Constants.OPENSEARCH_INDEX_EXTENSION, Constants.OPENSEARCH_INDEX_EXTENSION_DEFAULT);

    public Set<SignalDetails> getSignalBySignalType(String accountIdentifier, Set<String> serviceSet, Long fromTime, Long toTime) {
        RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_SIGNALS);
        if (elasticClient == null) {
            return Collections.emptySet();
        }
        String indexPrefix = Constants.INDEX_PREFIX_SIGNALS + "_" + accountIdentifier.toLowerCase();
        try {
            List<String> indexNames = new ArrayList<>();
            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexPrefix + "_" + date));

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("signalType", SignalType.INFO.name()));

            List<NameValuePair> matchAnyFields = new ArrayList<>();
            serviceSet.forEach(c -> matchAnyFields.add(new NameValuePair("serviceIds", c)));

            String epochEndTimeFieldName = "updatedTime";
            String epochStartTimeFieldName = "startedTime";

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochStartTimeFieldName(epochStartTimeFieldName)
                    .epochFromDate(fromTime)
                    .epochEndTimeFieldName(epochEndTimeFieldName)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .matchAllFields(Optional.of(matchFields))
                    .matchAnyOfFields(Optional.of(new ArrayList<List<NameValuePair>>() {{
                        add(matchAnyFields);
                    }}))
                    .build();

            Set<SignalDetails> signalDetails = new HashSet<>();
            log.debug("OS query for fetching Signal Details: {}", queryOptions);
            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);

            if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty())
                return Collections.emptySet();

            for (String hit : rawDocuments.getDocuments())
                signalDetails.add(objectMapper.readValue(hit, SignalDetails.class));

            return signalDetails;
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
        }
        return Collections.emptySet();
    }

    public Set<SignalDetails> getOpenSignals(String accountIdentifier, Set<String> signalTypeSet) {
        String indexPrefix = Constants.INDEX_PREFIX_SIGNALS + "_" + accountIdentifier.toLowerCase() + "_*";
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_SIGNALS);
            if (elasticClient == null) {
                return new HashSet<>();
            }

            List<String> indexNames = new ArrayList<>();
            indexNames.add(indexPrefix);

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("currentStatus", "OPEN"));
            matchFields.add(new NameValuePair("metadata.account_id", accountIdentifier));

            List<NameValuePair> matchAnyFields = new ArrayList<>();
            signalTypeSet.forEach(s -> matchAnyFields.add(new NameValuePair("signalType", s)));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .isTimeSeriesData(false)
                    .matchAllFields(Optional.of(matchFields))
                    .matchAnyOfFields(Optional.of(new ArrayList<List<NameValuePair>>() {{
                        add(matchAnyFields);
                    }}))
                    .fetchAllRecords(true)
                    .allIndex(true)
                    .build();

            log.debug("OS query for fetching anomaly data: {}", queryOptions);

            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);
            Set<SignalDetails> signalDetails = new HashSet<>();

            if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty())
                return new HashSet<>();

            for (String hit : rawDocuments.getDocuments())
                signalDetails.add(objectMapper.readValue(hit, SignalDetails.class));

            return signalDetails;
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
        }
        return new HashSet<>();
    }

    public SignalDetails getSignalsBySignalId(String signalId, String accountIdentifier) {
        String indexName = Constants.INDEX_PREFIX_SIGNALS + "_" + accountIdentifier.toLowerCase() + "_*";
        SignalDetails signalDetails = null;
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_SIGNALS);
            if (elasticClient == null) {
                return null;
            }
            List<String> indexNames = new ArrayList<>();
            indexNames.add(indexName);

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("_id", signalId));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .isTimeSeriesData(false)
                    .matchAllFields(Optional.of(matchFields))
                    .allIndex(true)
                    .build();

            log.debug("Query for getting signal detail : {}", queryOptions);

            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);

            if (rawDocuments != null && rawDocuments.getDocuments() != null && !rawDocuments.getDocuments().isEmpty()) {
                String doc = rawDocuments.getDocuments().get(0);
                signalDetails = objectMapper.readValue(doc, SignalDetails.class);
            }
        } catch (Exception exception) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in getting doc from signalId:{}, index {} : ", signalId, indexName, exception);
        }
        return signalDetails;
    }

    public Set<SignalDetails> getAllSignals(String accountIdentifier, Set<String> signalTypeList, Set<String> signalStatusList,
                                            Long fromTime, Long toTime) {
        String indexName = Constants.INDEX_PREFIX_SIGNALS + "_" + accountIdentifier.toLowerCase() + "_*";
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_SIGNALS);
            if (elasticClient == null) {
                return new HashSet<>();
            }

            List<String> indexNames = new ArrayList<>();
            indexNames.add(indexName);

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("metadata.account_id", accountIdentifier));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("updatedTime")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .matchAllFields(Optional.of(matchFields))
                    .fetchAllRecords(true)
                    .allIndex(true)
                    .build();

            List<NameValuePair> matchAnyFieldsSignalType = new ArrayList<>();
            if (signalTypeList != null && !signalTypeList.isEmpty()) {
                signalTypeList.forEach(s -> matchAnyFieldsSignalType.add(new NameValuePair("signalType", s)));
            }

            List<NameValuePair> matchAnyFieldsSignalStatus = new ArrayList<>();
            if (signalStatusList != null && !signalStatusList.isEmpty()) {
                signalStatusList.forEach(s -> matchAnyFieldsSignalStatus.add(new NameValuePair("currentStatus", s)));
            }

            queryOptions.setMatchAnyOfFields(Optional.of(new ArrayList<List<NameValuePair>>() {{
                if (!matchAnyFieldsSignalType.isEmpty())
                    add(matchAnyFieldsSignalType);

                if (!matchAnyFieldsSignalStatus.isEmpty())
                    add(matchAnyFieldsSignalStatus);
            }}));

            log.debug("OS query for fetching signal data: {}", queryOptions);

            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);
            if (rawDocuments != null && rawDocuments.getDocuments() != null && !rawDocuments.getDocuments().isEmpty()) {
                return rawDocuments.getDocuments().parallelStream()
                        .map(doc -> {
                            try {
                                return objectMapper.readValue(doc, SignalDetails.class);
                            } catch (JsonProcessingException e) {
                                throw new RuntimeException(e);
                            }
                        })
                        .collect(Collectors.toSet());
            }
        } catch (Exception ex) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in getting doc from index:{}, signalTypeList:{}, fromTime:{}, toTime:{},", indexName, signalTypeList, fromTime, toTime, ex);
        }
        return new HashSet<>();
    }

    public Set<SignalDetails> getSignals(List<String> signalIds, String accountIdentifier) {
        String indexName = Constants.INDEX_PREFIX_SIGNALS + "_" + accountIdentifier.toLowerCase() + "*";
        Set<SignalDetails> signalPojoList = new HashSet<>();

        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_SIGNALS);
            if (elasticClient == null) {
                return null;
            }
            List<String> indexNames = new ArrayList<>();
            indexNames.add(indexName);

            List<NameValuePair> matchFields = new ArrayList<>();
            for (String signalId : signalIds) {
                matchFields.add(new NameValuePair("_id", signalId));
            }

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .isTimeSeriesData(false)
                    .matchAllFields(Optional.of(matchFields))
                    .allIndex(true)
                    .build();

            log.debug("Query for getting signal detail : {}", queryOptions);

            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);
            if (rawDocuments != null && rawDocuments.getDocuments() != null && !rawDocuments.getDocuments().isEmpty()) {
                for (String doc : rawDocuments.getDocuments()) {
                    SignalDetails signalPojo = objectMapper.readValue(doc, SignalDetails.class);
                    signalPojoList.add(signalPojo);
                }
            }
        } catch (Exception exception) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in getting doc from index {} : ", indexName, exception);
        }
        return signalPojoList;
    }

    public Set<SignalDetails> getBatchSignalBySignalType(String accountIdentifier, Set<String> appIdsSet, String epochTimeFieldName, Long fromTime, Long toTime) {
        RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_SIGNALS);
        if (elasticClient == null) {
            return Collections.emptySet();
        }
        String indexPrefix = Constants.INDEX_PREFIX_SIGNALS + "_" + accountIdentifier.toLowerCase();
        try {
            List<String> indexNames = new ArrayList<>();
            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexPrefix + "_" + date));

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("signalType", "BATCH_JOB"));

            List<NameValuePair> matchAnyFields = new ArrayList<>();
            appIdsSet.forEach(c -> matchAnyFields.add(new NameValuePair("serviceIds", c)));


            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName(epochTimeFieldName)
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .matchAllFields(Optional.of(matchFields))
                    .matchAnyOfFields(Optional.of(new ArrayList<List<NameValuePair>>() {{
                        add(matchAnyFields);
                    }}))
                    .build();

            Set<SignalDetails> signalDetails = new HashSet<>();
            log.debug("OS query for fetching Signal Details: {}", queryOptions);
            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);

            if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty())
                return Collections.emptySet();

            for (String hit : rawDocuments.getDocuments())
                signalDetails.add(objectMapper.readValue(hit, SignalDetails.class));

            return signalDetails;
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
        }
        return Collections.emptySet();
    }

}
