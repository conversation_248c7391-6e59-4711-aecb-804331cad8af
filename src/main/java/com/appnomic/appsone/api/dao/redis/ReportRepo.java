package com.appnomic.appsone.api.dao.redis;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.RedisUtilities;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.ReportDetails;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;

@Slf4j
public class ReportRepo {
    public List<ReportDetails> getReportDetails(String identifier) {
        String key = "/accounts/" + identifier + "/reports";
        String hashKey = "ACCOUNTS_" + identifier + "_REPORTS";
        try {
            String accDetails = RedisUtilities.getKey(key, hashKey);
            if (accDetails == null)
                return Collections.emptyList();
            return CommonUtils.getObjectMapper().readValue(accDetails, new TypeReference<List<ReportDetails>>() {
            });

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Exception encountered while getting key '{}': {}", key, hashKey, e);
            return Collections.emptyList();
        }
    }
}
