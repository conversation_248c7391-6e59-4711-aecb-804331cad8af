package com.appnomic.appsone.api.dao;

import com.appnomic.appsone.api.beans.*;
import com.appnomic.appsone.api.dao.mysql.entity.UserForensicNotificationMappingBean;
import com.appnomic.appsone.api.pojo.UserAttributesPojo;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.Date;
import java.util.List;

public interface SignalNotificationPreferenceDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select u.is_sms_enabled smsEnabled, u.is_email_enabled emailEnabled, u.is_forensic_enabled forensicEnabled, " +
            "u.notification_preference_id notificationPreferenceId, u.forensic_notification_suppression forensicNotificationSuppression, " +
            "u.applicable_user_id applicableUserId from user_notifications_details u where u.applicable_user_id = :userId")
    UserNotificationDetailsBean getUserNotificationDetails(@Bind("userId") String userId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select ns.notification_type_id typeId, ns.no_of_minutes durationInMin, mst.name typeName from " +
            "notification_settings ns join mst_sub_type mst on mst.id=ns.notification_type_id " +
            "where ns.account_id =:accountId")
    List<NotificationSettingsBean> getNotificationSettings(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select application_id applicationId,notification_type_id notificationTypeId,signal_type_id signalTypeId,signal_severity_id severityTypeId " +
            "from user_notification_mapping where applicable_user_id=:applicableUserId and account_id=:accountId ")
    List<NotificationBean> getUserNotificationDetails(@Bind("accountId") int accountId, @Bind("applicableUserId") String applicableUserId);

    @SqlBatch("INSERT INTO user_notification_mapping (applicable_user_id, application_id, notification_type_id, signal_type_id, signal_severity_id, " +
            "account_id,status,created_time, updated_time,user_details_id) VALUES " +
            "(:applicableUserId, :applicationId, :notificationTypeId, :signalTypeId, :severityTypeId, :accountId, :status, " +
            ":createdTime, :updatedTime, :userDetailsId)")
    @GetGeneratedKeys
    int[] addNotificationDetails(@BindBean List<NotificationBean> notificationBean);


    @SqlBatch("UPDATE user_notification_mapping SET notification_type_id=:notificationTypeId,updated_time=:updatedTime,user_details_id=:applicableUserId where signal_type_id=:signalTypeId \n" +
            "and signal_severity_id=:severityTypeId and application_id=:applicationId and applicable_user_id=:applicableUserId and account_id=:accountId")
    @GetGeneratedKeys
    void updateNotifications(@BindBean List<NotificationBean> notificationBeans);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(1) from user_notification_mapping where signal_type_id=:signalTypeId \n" +
            "and signal_severity_id=:severityTypeId and application_id=:applicationId and applicable_user_id=:applicableUserId " +
            "and account_id=:accountId")
    int getNotificationPreferencesForUser(@Bind("signalTypeId") int signalTypeId, @Bind("severityTypeId") int severityTypeId,
                                          @Bind("applicationId") int applicationId, @Bind("applicableUserId") String applicableUserId,
                                          @Bind("accountId") int accountId);

    @SqlUpdate("INSERT INTO user_notifications_details (applicable_user_id,is_sms_enabled,is_email_enabled,account_id,created_time," +
            "updated_time,user_details_id, notification_preference_id, is_forensic_enabled, forensic_notification_suppression) " +
            "VALUES ( :applicableUserId, :smsEnabled, :emailEnabled, :accountId,:createdTime, :updatedTime, :userDetailsId, " +
            ":notificationPreferenceId, :forensicEnabled, :forensicNotificationSuppression)")
    @GetGeneratedKeys
    int addNotificationUserDetails(@BindBean UserNotificationDetailsBean userNotificationDetailsBean);

    @SqlUpdate("UPDATE user_notifications_details SET is_sms_enabled=:smsEnabled,is_email_enabled=:emailEnabled,updated_time=:updatedTime,user_details_id=:user, notification_preference_id=:notificationPreferenceId where applicable_user_id=:user")
    void updateNotificationUserDetails(@Bind("smsEnabled") boolean smsEnabled, @Bind("emailEnabled") boolean emailEnabled, @Bind("user") String user, @Bind("updatedTime") Date updatedTime, @Bind("notificationPreferenceId") int notificationPreferenceId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select und.notification_preference_id notificationChoiceId, unc.applicable_user_id applicableUserId, unc.component_selection componentSelection, unc.category_ids categoryIds," +
            "unc.created_time createdTime, unc.component_type_ids componentTypeIds, unc.component_ids componentIds,unc.updated_time updatedTime, unc.user_details_id userDetailsId " +
            "from user_notifications_details und, user_notification_choice unc where unc.applicable_user_id = und.applicable_user_id and unc.applicable_user_id=:applicableUserId")
    UserNotificationChoiceBean getUserNotificationChoice(@Bind("applicableUserId") String applicableUserId);

    @SqlUpdate("INSERT INTO user_notification_choice (applicable_user_id,component_selection,category_ids," +
            "created_time,updated_time, user_details_id, component_type_ids, component_ids) VALUES " +
            "(:applicableUserId, :componentSelection, :categoryIds, :createdTime, :updatedTime, :userDetailsId, :componentTypeIds, :componentIds)")
    @GetGeneratedKeys
    int addNotificationUserChoice(@BindBean UserNotificationChoiceBean userNotificationChoiceBean);

    @SqlUpdate("UPDATE user_notification_choice SET component_selection=:componentSelection," +
            "category_ids=:categoryIds, updated_time=:updatedTime, user_details_id=:userDetailsId, " +
            "component_type_ids=:componentTypeIds, component_ids=:componentIds where applicable_user_id=:applicableUserId")
    void updateNotificationUserChoice(@Bind("componentTypeIds") String componentTypeIds,
                                      @Bind("componentIds") String componentIds, @Bind("applicableUserId") String applicableUserId, @Bind("componentSelection") String componentSelection, @Bind("categoryIds") String categoryIds, @Bind("updatedTime") Date updatedTime, @Bind("userDetailsId") String userDetailsId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select application_id applicationId,notification_type_id notificationTypeId,signal_type_id signalTypeId,signal_severity_id severityTypeId " +
            "from user_notification_mapping where " +
            "application_id = :applicationId and applicable_user_id=:applicableUserId and account_id=:accountId ")
    List<NotificationBean> getUserNotificationDetails(@Bind("applicationId") Integer applicationId, @Bind("applicableUserId") String applicableUserId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select application_id applicationId, notification_type_id notificationTypeId, signal_type_id signalTypeId, signal_severity_id severityTypeId" +
            " from application_notification_mapping where account_id = :accountId")
    List<NotificationBean> getApplicationNotificationPreferences(@Bind("accountId") int accountId);

    @SqlUpdate("UPDATE user_notifications_details SET is_forensic_enabled = :forensicEnabled, user_details_id = :user, " +
            "forensic_notification_suppression = :forensicNotificationSuppressionInterval, updated_time = :updatedTime " +
            "where applicable_user_id = :applicableUserId")
    void updateForensicNotificationUserDetails(@Bind("forensicNotificationSuppressionInterval") int forensicNotificationSuppressionInterval,
                                               @Bind("updatedTime") Date updatedTime, @Bind("applicableUserId") String applicableUserId,
                                               @Bind("forensicEnabled") int forensicEnabled, @Bind("user") String user);

    @SqlBatch("INSERT INTO user_forensic_notification_mapping (applicable_user_id, application_id, forensic_notification_suppression, " +
            "account_id, status, created_time, updated_time, user_details_id) VALUES (:applicableUserId, :applicationId, " +
            ":forensicNotificationSuppression, :accountId, :status, :createdTime, :updatedTime, :userDetailsId)")
    void addForensicNotificationConfigurations(@BindBean List<UserForensicNotificationMappingBean> beans);

    @SqlUpdate("DELETE FROM user_forensic_notification_mapping where applicable_user_id = :userId AND application_id = :appId ")
    void deleteForensicNotificationConfigurations(@Bind("appId") int appId, @Bind("userId") String userId);

    @SqlUpdate("UPDATE user_attributes SET recipients_enabled=:enableEmailRecipients, to_emails=:toEmails, cc_emails=:ccEmails where user_identifier=:userIdentifier")
    void updateEmailNotificationDetails(@BindBean UserAttributesPojo userAttributes);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select to_emails toEmails, cc_emails ccEmails, recipients_enabled enableEmailRecipients, user_identifier userIdentifier" +
            " from user_attributes where user_identifier = :userIdentifier")
    UserAttributesPojo getEmailNotificationDetails(@Bind("userIdentifier") String userIdentifier);
}