package com.appnomic.appsone.api.dao;

import com.appnomic.appsone.api.beans.*;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

/**
 * <AUTHOR> : 6/3/19
 */
public interface ThresholdDataDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select profile_id profileId,  profile_name profileName,day_option_id dayOptionId, day_option_name dayOptionName, status status, account_id accountId, day day, start_hour startHour, start_minute startMinute, end_hour endHour, end_minute endMinute from view_coverage_window_profile_details")
    List<ViewCoverageWinProfDetailsBean> getCoverageWindowsProfiles();

}
