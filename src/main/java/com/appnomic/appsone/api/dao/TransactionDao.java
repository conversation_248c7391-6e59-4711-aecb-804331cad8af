package com.appnomic.appsone.api.dao;

import com.appnomic.appsone.api.dao.mysql.entity.TransactionAuditConfigurationBean;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

/**
 * <AUTHOR> Parekaden : 24/1/19
 */
public interface TransactionDao {
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(*) from tag_mapping where tag_id = :tagId and object_ref_table = :objectRefTable  " +
            "and tag_key = :tagKey and account_id = :accountId")
    Integer getTxnCountPerService(@Bind("accountId") Integer accountId, @Bind("tagId") Integer tagId,
                                  @Bind("objectRefTable") String objectRefTable, @Bind("tagKey") String serviceId);

}