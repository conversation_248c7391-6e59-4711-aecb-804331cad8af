package com.appnomic.appsone.api.dao.opensearch;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.manager.OpenSearchConnectionManager;
import com.appnomic.appsone.api.pojo.TabularResultsTypePojo;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.api.util.OpenSearchRollupUtility;
import com.appnomic.appsone.opeasearchquery.enumerations.Aggregations;
import com.appnomic.appsone.opeasearchquery.helper.OpenSearchQueryHelper;
import com.appnomic.appsone.opeasearchquery.queryopts.QueryOptions;
import com.appnomic.appsone.opeasearchquery.results.AggregationQuery;
import com.appnomic.appsone.opeasearchquery.results.NameValuePair;
import com.appnomic.appsone.opeasearchquery.results.RawDocumentResults;
import com.appnomic.appsone.opeasearchquery.results.TabularResults;
import com.heal.configuration.pojos.TimeRangeDetails;
import com.heal.configuration.pojos.TransformedIndexDetails;
import com.heal.configuration.pojos.opensearch.CollatedKpi;
import com.heal.configuration.util.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.client.RestHighLevelClient;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class CollatedKpiRepo {

    private static String indexExtension = ConfProperties.getString(Constants.OPENSEARCH_INDEX_EXTENSION, Constants.OPENSEARCH_INDEX_EXTENSION_DEFAULT);

    public Map<String, Map<String, Long>> getDataCheckInstanceCategory(String accountIdentifier, String serviceIdentifier,
                                                                       long fromTime, long toTime,
                                                                       TimeRangeDetails timeRangeDetails,
                                                                       List<TimeRangeDetails> timeRangeDetailsList, List<Long> times) {
        long start = System.currentTimeMillis();

        List<TabularResultsTypePojo> tabularResultsBooleanList = new ArrayList<>();
        String indexPrefix = Constants.INDEX_PREFIX_COLLATED_KPI + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_COLLATED_KPI);
            if (elasticClient == null) {
                HealUICache.INSTANCE.updateHealUIErrors(1);
                return Collections.emptyMap();
            }

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("serviceIdentifiers", serviceIdentifier));

            List<String> columns = new ArrayList<>();
            columns.add("compInstanceIdentifier");
            columns.add("categoryIdentifier");

            QueryOptions queryOptions = QueryOptions.builder()
                    .epochFieldName("timeInGMT")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .matchAllFields(Optional.of(matchFields))
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .build();

            if (timeRangeDetails.getNeedRolledUpIndexes() == 1) {

                List<TransformedIndexDetails> transformedIndicesList = DateHelper.getTransformedIndexAsString(times, timeRangeDetailsList, timeRangeDetails, 0);

                TransformedIndexDetails nonTransformedIndices = transformedIndicesList.parallelStream()
                        .filter(c -> !c.isTransformed()).findAny().orElse(null);
                if (nonTransformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    nonTransformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(columns)));
                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setEpochFromDate(nonTransformedIndices.getFromTime());
                    queryOptions.setEpochToDate(nonTransformedIndices.getToTime());

                    log.debug("OS query for fetching collated kpi data:{} ", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(false)
                                .build());
                    }
                }

                TransformedIndexDetails transformedIndices = transformedIndicesList.parallelStream()
                        .filter(TransformedIndexDetails::isTransformed).findAny().orElse(null);
                if (transformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    transformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(columns)));
                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setEpochFromDate(transformedIndices.getFromTime());
                    queryOptions.setEpochToDate(transformedIndices.getToTime());

                    log.debug("OS query for fetching collated kpi data:{} ", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(true)
                                .build());
                    }
                }
            } else {
                List<String> indexNames = new ArrayList<>();
                DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                        indexNames.add(indexPrefix + "_" + date));

                queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(columns)));
                queryOptions.setIndexNames(indexNames);

                log.debug("OS query for fetching collated kpi data:{} ", queryOptions);
                TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                    tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                            .tabularResults(results)
                            .isTransformed(false)
                            .build());
                }
            }

            if (tabularResultsBooleanList.isEmpty()) {
                return Collections.emptyMap();
            }

            Map<String, Map<String, Long>> instCatDataCountMap = new HashMap<>();
            for (TabularResultsTypePojo tabularResultsTypePojo : tabularResultsBooleanList) {
                TabularResults tabularResults = tabularResultsTypePojo.getTabularResults();

                if (tabularResults != null && tabularResults.getRowResults() != null && !tabularResults.getRowResults().isEmpty()) {

                    for (TabularResults.ResultRow resultRow : tabularResults.getRowResults()) {

                        Map<String, Long> categoryDataCountMap = new HashMap<>();

                        String instanceIdentifier = "";
                        String categoryIdentifier = "";

                        for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {


                            if (resultRowColumn.getColumnName().equalsIgnoreCase("compInstanceIdentifier")) {

                                instanceIdentifier = resultRowColumn.getColumnValue();

                            } else if (resultRowColumn.getColumnName().equalsIgnoreCase("categoryIdentifier")) {

                                categoryIdentifier = resultRowColumn.getColumnValue();

                            }
                        }

                        categoryDataCountMap.put(categoryIdentifier, resultRow.getCountValue());

                        instCatDataCountMap.getOrDefault(instanceIdentifier, new HashMap<>()).forEach((key, value) -> categoryDataCountMap.merge(key, value, Long::sum));
                        instCatDataCountMap.put(instanceIdentifier, categoryDataCountMap);
                    }
                }
            }

            return instCatDataCountMap;

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while fetching instance wise category data exists for for accountId:{}, serviceId:{}, from:{}, to:{}, index:{}",
                    accountIdentifier, serviceIdentifier, fromTime, toTime, indexPrefix, e);
            return Collections.emptyMap();
        } finally {
            log.debug("Time taken to execute getDataCheckInstanceCategory() method is {} ms.", (System.currentTimeMillis() - start));
        }
    }

    public List<TabularResultsTypePojo> getIsDataAvailableInCategory(String accountIdentifier, String instanceId,
                                                                     long fromTime, long toTime, TimeRangeDetails timeRangeDetails,
                                                                     List<TimeRangeDetails> timeRangeDetailsList, List<Long> times) {

        List<TabularResultsTypePojo> tabularResultsBooleanList = new ArrayList<>();
        String indexPrefix = Constants.INDEX_PREFIX_COLLATED_KPI + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_COLLATED_KPI);
            if (elasticClient == null) {
                return Collections.emptyList();
            }

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("compInstanceIdentifier", instanceId));

            List<String> columns = new ArrayList<>();
            columns.add("kpiId");
            columns.add("groupAttribute");

            List<AggregationQuery> aggregationQueries = new ArrayList<>();
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.valueOfLabel("UNIQUE")).fieldName("agentIdentifier").percentileValue( null).build());

            QueryOptions queryOptions = QueryOptions.builder()
                    .epochFieldName("timeInGMT")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .matchAllFields(Optional.of(matchFields))
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .build();

            if (timeRangeDetails.getNeedRolledUpIndexes() == 1) {

                List<TransformedIndexDetails> transformedIndicesList = DateHelper.getTransformedIndexAsString(times, timeRangeDetailsList, timeRangeDetails, 0);

                TransformedIndexDetails nonTransformedIndices = transformedIndicesList.parallelStream()
                        .filter(c -> !c.isTransformed()).findAny().orElse(null);

                if (nonTransformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    nonTransformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(columns)));
                    queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setEpochFromDate(nonTransformedIndices.getFromTime());
                    queryOptions.setEpochToDate(nonTransformedIndices.getToTime());

                    log.debug("OS query for fetching anomaly data:{} ", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(false)
                                .build());
                    }
                }

                TransformedIndexDetails transformedIndices = transformedIndicesList.parallelStream()
                        .filter(TransformedIndexDetails::isTransformed).findAny().orElse(null);
                if (transformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    transformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(columns)));
                    queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setEpochFromDate(transformedIndices.getFromTime());
                    queryOptions.setEpochToDate(transformedIndices.getToTime());

                    log.debug("OS query for fetching anomaly data:{} ", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(true)
                                .build());
                    }
                }
            } else {
                List<String> indexNames = new ArrayList<>();
                DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                        indexNames.add(indexPrefix + "_" + date));

                queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(columns)));
                queryOptions.setIndexNames(indexNames);
                queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                log.debug("OS query for fetching anomaly data:{} ", queryOptions);

                TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                    tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                            .tabularResults(results)
                            .isTransformed(false)
                            .build());
                }
            }

            if (tabularResultsBooleanList.isEmpty()) {
                return Collections.emptyList();
            }

            return tabularResultsBooleanList;
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
            return Collections.emptyList();
        }
    }

    public List<TabularResultsTypePojo> getCollatedKpiDetailsForKPI(List<Long> times, String accountIdentifier, String instanceIdentifier,
                                                                    String kpiId, int timeSliceInMinutes, String operation, long fromTime, long toTime,
                                                                    TimeRangeDetails timeRangeDetails, List<TimeRangeDetails> timeRangeDetailsList, String timezoneId) {

        List<TabularResultsTypePojo> tabularResultsBooleanList = new ArrayList<>();
        String indexPrefix = Constants.INDEX_PREFIX_COLLATED_KPI + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_COLLATED_KPI);
            if (elasticClient == null) {
                return new ArrayList<>();
            }

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("kpiId", kpiId));
            matchFields.add(new NameValuePair("compInstanceIdentifier", instanceIdentifier));


            QueryOptions queryOptions = QueryOptions.builder()
                    .timeStampFieldName("timeInGMT")
                    .epochFieldName("timeInGMT")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .matchAllFields(Optional.of(matchFields))
                    .isTimeSeriesData(true)
                    .fetchAllRecords(true)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .timeSliceInMins(timeSliceInMinutes)
                    .timeZone(timezoneId)
                    .build();

            if (timeRangeDetails.getNeedRolledUpIndexes() == 1) {

                List<TransformedIndexDetails> transformedIndicesList = DateHelper.getTransformedIndexAsString(times, timeRangeDetailsList, timeRangeDetails, 0);

                TransformedIndexDetails nonTransformedIndices = transformedIndicesList.parallelStream()
                        .filter(c -> !c.isTransformed()).findAny().orElse(null);
                if (nonTransformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    nonTransformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    List<AggregationQuery> aggregationQueries = new ArrayList<>();
                    aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.valueOfLabel(operation)).fieldName("value").percentileValue( null).build());

                    //Don't remove this groupByColumn Set line
                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<String>() {{
                        add("groupAttribute");
                    }}));
                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                    queryOptions.setEpochFromDate(nonTransformedIndices.getFromTime());
                    queryOptions.setEpochToDate(nonTransformedIndices.getToTime());

                    log.debug("OS query for fetching collated kpi data: {}", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(false)
                                .build());
                    }
                }

                TransformedIndexDetails transformedIndices = transformedIndicesList.parallelStream()
                        .filter(TransformedIndexDetails::isTransformed).findAny().orElse(null);
                if (transformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    transformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    List<AggregationQuery> aggregationQueries = new ArrayList<>();
                    String fieldName = OpenSearchRollupUtility.getValueFieldName(operation);
                    aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.valueOfLabel(operation)).fieldName(fieldName).percentileValue(null).build());

                    //Don't remove this groupByColumn Set line
                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<String>() {{
                        add("groupAttribute");
                    }}));
                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                    queryOptions.setEpochFromDate(transformedIndices.getFromTime());
                    queryOptions.setEpochToDate(transformedIndices.getToTime());

                    log.debug("OS query for fetching collated kpi data: {}", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(true)
                                .build());
                    }
                }
            } else {
                List<String> indexNames = new ArrayList<>();
                DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                        indexNames.add(indexPrefix + "_" + date));

                List<AggregationQuery> aggregationQueries = new ArrayList<>();
                aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.valueOfLabel(operation)).fieldName("value").percentileValue(null).build());

                queryOptions.setGroupByColumns(Optional.of(new ArrayList<String>() {{
                    add("groupAttribute");
                }}));
                queryOptions.setIndexNames(indexNames);
                queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));

                log.debug("OS query for fetching collated kpi data: {}", queryOptions);
                TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                if (results != null && results.getRowResults() != null) {
                    tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                            .tabularResults(results)
                            .isTransformed(false)
                            .build());
                }
            }

            return tabularResultsBooleanList;
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
        }
        return new ArrayList<>();
    }

    public List<CollatedKpi> getCollatedKpisByType(String accountIdentifier, String compInstanceIdentifier, String kpiType,
                                                   long fromTime, long toTime, TimeRangeDetails timeRangeDetails,
                                                   List<TimeRangeDetails> timeRangeDetailsList, List<Long> times,String categoryIdentifier) {

        List<TabularResultsTypePojo> tabularResultsBooleanList = new ArrayList<>();
        String indexPrefix = Constants.INDEX_PREFIX_COLLATED_KPI + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_COLLATED_KPI);
            if (elasticClient == null) {
                return Collections.emptyList();
            }

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("kpiType", kpiType));
            matchFields.add(new NameValuePair("compInstanceIdentifier", compInstanceIdentifier));
            if (categoryIdentifier != null && !categoryIdentifier.isEmpty()) {
                matchFields.add(new NameValuePair("categoryIdentifier", categoryIdentifier));
            }

            QueryOptions queryOptions = QueryOptions.builder()
                    .epochFieldName("timeInGMT")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .fetchAllRecords(true)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .matchAllFields(Optional.of(matchFields))
                    .build();

            if (timeRangeDetails.getNeedRolledUpIndexes() == 1) {

                List<TransformedIndexDetails> transformedIndicesList = DateHelper.getTransformedIndexAsString(times, timeRangeDetailsList, timeRangeDetails, 0);

                TransformedIndexDetails nonTransformedIndices = transformedIndicesList.parallelStream()
                        .filter(c -> !c.isTransformed()).findAny().orElse(null);
                if (nonTransformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    nonTransformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setEpochFromDate(nonTransformedIndices.getFromTime());
                    queryOptions.setEpochToDate(nonTransformedIndices.getToTime());

                    log.debug("getCollatedKpisByType: OS query for fetching Collated KPI Details: {}", queryOptions);
                    RawDocumentResults results = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);
                    if (results != null && results.getDocuments() != null && !results.getDocuments().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .rawDocumentResults(results)
                                .isTransformed(false)
                                .build());
                    }
                }

                TransformedIndexDetails transformedIndices = transformedIndicesList.parallelStream()
                        .filter(TransformedIndexDetails::isTransformed).findAny().orElse(null);
                if (transformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    transformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setEpochFromDate(transformedIndices.getFromTime());
                    queryOptions.setEpochToDate(transformedIndices.getToTime());

                    log.debug("getCollatedKpisByType: OS query for fetching Collated KPI Details: {}", queryOptions);
                    RawDocumentResults results = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);
                    if (results != null && results.getDocuments() != null && !results.getDocuments().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .rawDocumentResults(results)
                                .isTransformed(true)
                                .build());
                    }
                }
            } else {
                List<String> indexNames = new ArrayList<>();
                DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                        indexNames.add(indexPrefix + "_" + date));

                queryOptions.setIndexNames(indexNames);

                log.debug("getCollatedKpisByType: OS query for fetching Collated KPI Details: {}", queryOptions);
                RawDocumentResults results = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);
                if (results != null && results.getDocuments() != null && !results.getDocuments().isEmpty()) {
                    tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                            .rawDocumentResults(results)
                            .isTransformed(false)
                            .build());
                }
            }

            if (tabularResultsBooleanList.isEmpty()) {
                return Collections.emptyList();
            }

            List<CollatedKpi> resultList = new ArrayList<>();
            for (TabularResultsTypePojo tabularResultsTypePojo : tabularResultsBooleanList) {

                RawDocumentResults rawDocuments = tabularResultsTypePojo.getRawDocumentResults();
                resultList.addAll(rawDocuments.getDocuments().parallelStream()
                        .map(doc -> {
                            try {
                                return CommonUtils.getObjectMapper().readValue(doc, CollatedKpi.class);
                            } catch (Exception e) {
                                log.error("getCollatedKpisByType: Error occurred while mapping OpenSearch collated kpi data to CollatedKpi pojo for index {}.", indexPrefix, e);
                                HealUICache.INSTANCE.updateHealUIErrors(1);
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()));
            }

            return resultList.parallelStream().distinct().collect(Collectors.toList());

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("getCollatedKpisByType: Error in fetching Collated Kpi data from index {}. Details: accountId [{}], kpiType [{}]",
                    indexPrefix, accountIdentifier, kpiType, e);
            return Collections.emptyList();
        }
    }

    public Map<String, Long> getCountOfUnavailableDataPoints(String accountIdentifier, String instanceIdentifier, String kpiId,
                                                             Long fromTime, Long toTime, TimeRangeDetails timeRangeDetails,
                                                             List<TimeRangeDetails> timeRangeDetailsList, List<Long> times) {

        List<TabularResultsTypePojo> tabularResultsBooleanList = new ArrayList<>();
        String indexPrefix = Constants.INDEX_PREFIX_COLLATED_KPI + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_COLLATED_KPI);
            if (elasticClient == null) {
                return new HashMap<>();
            }

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("compInstanceIdentifier", instanceIdentifier));
            matchFields.add(new NameValuePair("kpiId", kpiId));

            List<String> columns = new ArrayList<>();
            columns.add("groupAttribute");

            QueryOptions queryOptions = QueryOptions.builder()
                    .epochFieldName("timeInGMT")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .numberOfAggregateRecords(10000)
                    .build();

            if (timeRangeDetails.getNeedRolledUpIndexes() == 1) {

                List<TransformedIndexDetails> transformedIndicesList = DateHelper.getTransformedIndexAsString(times, timeRangeDetailsList, timeRangeDetails, 0);

                TransformedIndexDetails nonTransformedIndices = transformedIndicesList.parallelStream()
                        .filter(c -> !c.isTransformed()).findAny().orElse(null);
                if (nonTransformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    nonTransformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    List<NameValuePair> tempMatchFields = new ArrayList<>(matchFields);
                    tempMatchFields.add(new NameValuePair("value", String.valueOf(0.0)));

                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(columns)));
                    queryOptions.setMatchAllFields(Optional.of(tempMatchFields));
                    queryOptions.setEpochFromDate(nonTransformedIndices.getFromTime());
                    queryOptions.setEpochToDate(nonTransformedIndices.getToTime());

                    log.debug("getCountOfUnavailableDataPoints: OS query for fetching Collated KPI Details: {}", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(false)
                                .build());
                    }

                }

                TransformedIndexDetails transformedIndices = transformedIndicesList.parallelStream()
                        .filter(TransformedIndexDetails::isTransformed).findAny().orElse(null);
                if (transformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    transformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    List<NameValuePair> tempMatchFields = new ArrayList<>(matchFields);
                    tempMatchFields.add(new NameValuePair("sumValue", String.valueOf(0.0)));

                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(columns)));
                    queryOptions.setMatchAllFields(Optional.of(tempMatchFields));
                    queryOptions.setEpochFromDate(transformedIndices.getFromTime());
                    queryOptions.setEpochToDate(transformedIndices.getToTime());

                    log.debug("getCountOfUnavailableDataPoints: OS query for fetching Collated KPI Details: {}", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(true)
                                .build());
                    }
                }
            } else {
                List<String> indexNames = new ArrayList<>();
                DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                        indexNames.add(indexPrefix + "_" + date));

                List<NameValuePair> tempMatchFields = new ArrayList<>(matchFields);
                tempMatchFields.add(new NameValuePair("value", String.valueOf(0.0)));

                queryOptions.setIndexNames(indexNames);
                queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(columns)));
                queryOptions.setMatchAllFields(Optional.of(tempMatchFields));

                log.debug("getCountOfUnavailableDataPoints: OS query for fetching Collated KPI Details: {}", queryOptions);
                TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                if (results != null && results.getRowResults() != null) {
                    tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                            .tabularResults(results)
                            .isTransformed(false)
                            .build());
                }
            }

            if (tabularResultsBooleanList.isEmpty()) {
                return new HashMap<>();
            }

            Map<String, Long> attributeUnavailableCountMap = new HashMap<>();
            for (TabularResultsTypePojo tabularResultsTypePojo : tabularResultsBooleanList) {
                TabularResults tabularResults = tabularResultsTypePojo.getTabularResults();

                if (tabularResults != null && tabularResults.getRowResults() != null && !tabularResults.getRowResults().isEmpty()) {

                    for (TabularResults.ResultRow resultRow : tabularResults.getRowResults()) {

                        String attributeName = "";

                        for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {


                            if (resultRowColumn.getColumnName().equalsIgnoreCase("groupAttribute")) {

                                attributeName = resultRowColumn.getColumnValue();

                            }
                        }

                        attributeUnavailableCountMap.put(attributeName, attributeUnavailableCountMap.getOrDefault(attributeName, 0L) + resultRow.getCountValue());

                    }
                }
            }

            return attributeUnavailableCountMap;

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching Collated Kpi data from index {}. Details: accountId [{}], kpiId [{}]",
                    indexPrefix, accountIdentifier, kpiId, e);
            return new HashMap<>();
        }
    }

    public List<CollatedKpi> getUnavailableDataPoints(String accountIdentifier, String instanceIdentifier, String kpiId, String attribute,
                                                      Long fromTime, Long toTime, TimeRangeDetails timeRangeDetails,
                                                      List<TimeRangeDetails> timeRangeDetailsList, List<Long> times) {

        List<TabularResultsTypePojo> tabularResultsBooleanList = new ArrayList<>();
        String indexPrefix = Constants.INDEX_PREFIX_COLLATED_KPI + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_COLLATED_KPI);
            if (elasticClient == null) {
                return new ArrayList<>();
            }

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("compInstanceIdentifier", instanceIdentifier));
            matchFields.add(new NameValuePair("kpiId", kpiId));
            matchFields.add(new NameValuePair("groupAttribute", attribute));

            QueryOptions queryOptions = QueryOptions.builder()
                    .epochFieldName("timeInGMT")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .fetchAllRecords(true)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .build();

            if (timeRangeDetails.getNeedRolledUpIndexes() == 1) {

                List<TransformedIndexDetails> transformedIndicesList = DateHelper.getTransformedIndexAsString(times, timeRangeDetailsList, timeRangeDetails, 0);

                TransformedIndexDetails nonTransformedIndices = transformedIndicesList.parallelStream()
                        .filter(c -> !c.isTransformed()).findAny().orElse(null);
                if (nonTransformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    nonTransformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    List<NameValuePair> tempMatchFields = new ArrayList<>(matchFields);
                    tempMatchFields.add(new NameValuePair("value", String.valueOf(0.0)));

                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setMatchAllFields(Optional.of(tempMatchFields));
                    queryOptions.setEpochFromDate(nonTransformedIndices.getFromTime());
                    queryOptions.setEpochToDate(nonTransformedIndices.getToTime());

                    log.debug("getUnavailableDataPoints: OS query for fetching Collated KPI Details: {}", queryOptions);
                    RawDocumentResults results = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);
                    if (results != null && results.getDocuments() != null && !results.getDocuments().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .rawDocumentResults(results)
                                .isTransformed(false)
                                .build());
                    }
                }

                TransformedIndexDetails transformedIndices = transformedIndicesList.parallelStream()
                        .filter(TransformedIndexDetails::isTransformed).findAny().orElse(null);
                if (transformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    transformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    List<NameValuePair> tempMatchFields = new ArrayList<>(matchFields);
                    tempMatchFields.add(new NameValuePair("sumValue", String.valueOf(0.0)));

                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setMatchAllFields(Optional.of(tempMatchFields));
                    queryOptions.setEpochFromDate(transformedIndices.getFromTime());
                    queryOptions.setEpochToDate(transformedIndices.getToTime());

                    log.debug("getUnavailableDataPoints: OS query for fetching Collated KPI Details: {}", queryOptions);
                    RawDocumentResults results = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);
                    if (results != null && results.getDocuments() != null && !results.getDocuments().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .rawDocumentResults(results)
                                .isTransformed(true)
                                .build());
                    }
                }
            } else {
                List<String> indexNames = new ArrayList<>();
                DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                        indexNames.add(indexPrefix + "_" + date));

                List<NameValuePair> tempMatchFields = new ArrayList<>(matchFields);
                tempMatchFields.add(new NameValuePair("value", String.valueOf(0.0)));

                queryOptions.setIndexNames(indexNames);
                queryOptions.setMatchAllFields(Optional.of(tempMatchFields));

                log.debug("getUnavailableDataPoints: OS query for fetching Collated KPI Details: {}", queryOptions);
                RawDocumentResults results = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);
                if (results != null && results.getDocuments() != null && !results.getDocuments().isEmpty()) {
                    tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                            .rawDocumentResults(results)
                            .isTransformed(false)
                            .build());
                }
            }

            if (tabularResultsBooleanList.isEmpty()) {
                return new ArrayList<>();
            }

            List<CollatedKpi> resultList = new ArrayList<>();
            for (TabularResultsTypePojo tabularResultsTypePojo : tabularResultsBooleanList) {

                RawDocumentResults rawDocuments = tabularResultsTypePojo.getRawDocumentResults();
                resultList.addAll(rawDocuments.getDocuments().parallelStream()
                        .map(doc -> {
                            try {
                                return CommonUtils.getObjectMapper().readValue(doc, CollatedKpi.class);
                            } catch (Exception e) {
                                log.error("Error occurred while mapping OpenSearch collated kpi data to CollatedKpi pojo for index {}.", indexPrefix, e);
                                HealUICache.INSTANCE.updateHealUIErrors(1);
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()));
            }

            return resultList;

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching Collated Kpi data from index {}. Details: accountId [{}], kpiId [{}]",
                    indexPrefix, accountIdentifier, kpiId, e);
            return new ArrayList<>();
        }
    }
}