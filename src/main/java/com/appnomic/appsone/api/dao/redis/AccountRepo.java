package com.appnomic.appsone.api.dao.redis;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.RedisUtilities;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.Account;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> on 07-03-2022
 */

@Slf4j
public class AccountRepo {

    public List<Account> getAccounts() {
        String key = "/accounts";
        String hashKey = "ACCOUNT_DATA";
        try {
            String accDetails = RedisUtilities.getKey(key, hashKey);
            if (accDetails == null)
                return Collections.emptyList();
            return CommonUtils.getObjectMapper().readValue(accDetails, new TypeReference<List<Account>>() {
            });

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Exception encountered while getting key '{}': {}", key, hashKey, e);
            return Collections.emptyList();
        }
    }

    public Account getAccount(String accountIdentifier) {
        String key = "/accounts/" + accountIdentifier;
        String hashKey = "ACCOUNT_DATA_" + accountIdentifier;
        try {
            String accountDetail = RedisUtilities.getKey(key, hashKey);
            if (accountDetail == null) {
                log.error("Account details unavailable for identifier [{}]", accountIdentifier);
                return null;
            }
            return CommonUtils.getObjectMapper().readValue(accountDetail, new TypeReference<Account>() {
            });
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting account details for account [{}]. Details: ", accountIdentifier, e);
            return null;
        }
    }

    public Account getAccountById(int accountId) {
        return this.getAccounts().parallelStream().filter(a -> a.getId() == accountId).findAny().orElse(null);
    }
}
