package com.appnomic.appsone.api.dao;

import com.appnomic.appsone.api.pojo.TagMappingDetails;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface UserPreferenceDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id id, tag_key tagKey, tag_value tagValue from tag_mapping where tag_id = :tagId and object_id = :objectId and object_ref_table = :objectRefTable and account_id = :accountId")
    List<TagMappingDetails> getUserPreferences(@BindBean TagMappingDetails tagMappingDetails);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id id, tag_key tagKey, tag_value tagValue from tag_mapping where tag_id = :tagId and object_id = :objectId and object_ref_table = :objectRefTable and tag_key = :tagKey and account_id = :accountId")
    TagMappingDetails getUserPreference(@BindBean TagMappingDetails tagMappingDetails);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("INSERT INTO tag_mapping ( tag_id, object_id, object_ref_table, tag_key, tag_value, created_time, updated_time, account_id, user_details_id) VALUES ( :tagId, :objectId ,:objectRefTable, :tagKey, :tagValue, :createdTime, :updatedTime, :accountId, :userDetailsId)")
    @GetGeneratedKeys
    int addUserPreferences(@BindBean TagMappingDetails tagMappingDetails);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("UPDATE tag_mapping SET tag_key=:tagKey, tag_value=:tagValue, updated_time=:updatedTime, user_details_id=:userDetailsId  where id = :id")
    void updateUserPreferences(@BindBean TagMappingDetails tagMappingDetails);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from tag_mapping where id = :id")
    void deleteUserPreferences(@BindBean TagMappingDetails tagMappingDetails);

}