package com.appnomic.appsone.api.dao.opensearch;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.enumeration.ResponseStatusTag;
import com.appnomic.appsone.api.manager.OpenSearchConnectionManager;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.opeasearchquery.enumerations.Aggregations;
import com.appnomic.appsone.opeasearchquery.helper.OpenSearchQueryHelper;
import com.appnomic.appsone.opeasearchquery.queryopts.QueryOptions;
import com.appnomic.appsone.opeasearchquery.results.AggregationQuery;
import com.appnomic.appsone.opeasearchquery.results.NameValuePair;
import com.appnomic.appsone.opeasearchquery.results.RawDocumentResults;
import com.appnomic.appsone.opeasearchquery.results.TabularResults;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.opensearch.RawTransactionData;
import com.heal.configuration.util.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.client.RestHighLevelClient;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class RawTransactionSearchRepo {
    private static String indexExtension = ConfProperties.getString(Constants.OPENSEARCH_INDEX_EXTENSION, Constants.OPENSEARCH_INDEX_EXTENSION_DEFAULT);
    public List<RawTransactionData> getAuditTransactionAtClusterLevel(String accountIdentifier, String txnIdentifier,
                                                                      long fromTime, long toTime) {

        String indexPrefix = Constants.INDEX_PREFIX_RAW_TRANSACTIONS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_RAW_TRANSACTIONS);
            if (elasticClient == null) {
                return Collections.emptyList();
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getDailyDatesAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexPrefix + "_" + date));

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("txnIdentifier", txnIdentifier));
            matchFields.add(new NameValuePair("discoveryStatus", "1"));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("timeInGMT")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .matchAllFields(Optional.of(matchFields))
                    .fetchAllRecords(true)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .build();

            log.debug("OS query for fetching service transaction data: {}", queryOptions);

            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);

            if (rawDocuments != null && rawDocuments.getDocuments() != null && !rawDocuments.getDocuments().isEmpty()) {
                return rawDocuments.getDocuments().parallelStream()
                        .map(doc -> {
                            try {
                                return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(doc, new TypeReference<RawTransactionData>() {
                                });
                            } catch (Exception e) {
                                HealUICache.INSTANCE.updateHealUIErrors(1);
                                log.error("Error occurred while mapping OpenSearch signals data to Collated Transaction for index {}. Details: accountId [{}]", indexPrefix, accountIdentifier, e);
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
        }
        return Collections.emptyList();
    }

    public List<RawTransactionData> getAuditTransactionAtInstanceLevel(String accountIdentifier, String txnIdentifier, Set<String> agentIdentifiers,
                                                                       long fromTime, long toTime) {

        String indexPrefix = Constants.INDEX_PREFIX_RAW_TRANSACTIONS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_RAW_TRANSACTIONS);
            if (elasticClient == null) {
                return Collections.emptyList();
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getDailyDatesAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexPrefix + "_" + date));

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("txnIdentifier", txnIdentifier));
            matchFields.add(new NameValuePair("discoveryStatus", "1"));

            List<NameValuePair> matchAnyFields = new ArrayList<>();
            agentIdentifiers.forEach(c -> matchAnyFields.add(new NameValuePair("agentIdentifier", c)));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("timeInGMT")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .matchAllFields(Optional.of(matchFields))
                    .matchAnyOfFields(Optional.of(new ArrayList<List<NameValuePair>>() {{
                        add(matchAnyFields);
                    }}))
                    .fetchAllRecords(true)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .build();

            log.debug("OS query for fetching service transaction data: {}", queryOptions);

            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);

            if (rawDocuments != null && rawDocuments.getDocuments() != null && !rawDocuments.getDocuments().isEmpty()) {
                return rawDocuments.getDocuments().parallelStream()
                        .map(doc -> {
                            try {
                                return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(doc, new TypeReference<RawTransactionData>() {
                                });
                            } catch (Exception e) {
                                HealUICache.INSTANCE.updateHealUIErrors(1);
                                log.error("Error occurred while mapping OpenSearch signals data to Collated Transaction for index {}. Details: accountId [{}]", indexPrefix, accountIdentifier, e);
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
        }
        return Collections.emptyList();
    }

    public TabularResults getResponsePercentileDataClusterLevel(String accountIdentifier, String txnIdentifier,
                                                                Set<Double> appPercentilesList, String responseType, long fromTime, long toTime,
                                                                int timeSliceInMinutes, String timezoneId) {

        String indexPrefix = Constants.INDEX_PREFIX_RAW_TRANSACTIONS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_RAW_TRANSACTIONS);
            if (elasticClient == null) {
                return null;
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getDailyDatesAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexPrefix + "_" + date));

            List<String> groupByColumns = new ArrayList<>();
            groupByColumns.add("txnIdentifier");

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("txnIdentifier", txnIdentifier));
            matchFields.add(new NameValuePair("responseTimes.responseTimeType", responseType));
            matchFields.add(new NameValuePair("discoveryStatus", "1"));

            List<NameValuePair> matchAnyFields = new ArrayList<>();
            matchAnyFields.add(new NameValuePair("responseTimes.responseStatusTag", ResponseStatusTag.GOOD.name()));
            matchAnyFields.add(new NameValuePair("responseTimes.responseStatusTag", ResponseStatusTag.SLOW.name()));

            double[] percentiles = appPercentilesList.parallelStream().mapToDouble(c -> c).toArray();

            List<AggregationQuery> aggregationQueries = new ArrayList<>();
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.PERCENTILE).fieldName("responseTimes.responseInMicroseconds").percentileValue(percentiles).build());

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("timeInGMT")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(true)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .timeSliceInMins(timeSliceInMinutes)
                    .matchAllFields(Optional.of(matchFields))
                    .matchAnyOfFields(Optional.of(new ArrayList<List<NameValuePair>>() {{
                        add(matchAnyFields);
                    }}))
                    .aggregationQueries(Optional.of(aggregationQueries))
                    .groupByColumns(Optional.of(groupByColumns))
                    .timeZone(timezoneId)
                    .build();

            log.debug("OS query for fetching service transaction data: {}", queryOptions);

            return OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching aggregated data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
        }
        return null;
    }

    public TabularResults getResponsePercentileDataInstanceLevel(String accountIdentifier, String txnIdentifier, Set<String> agentIdentifiers,
                                                                 Set<Double> appPercentilesList, String responseType, long fromTime, long toTime,
                                                                 int timeSliceInMinutes, String timezoneId) {

        String indexPrefix = Constants.INDEX_PREFIX_RAW_TRANSACTIONS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_RAW_TRANSACTIONS);
            if (elasticClient == null) {
                return null;
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getDailyDatesAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexPrefix + "_" + date));

            List<String> groupByColumns = new ArrayList<>();
            groupByColumns.add("txnIdentifier");

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("txnIdentifier", txnIdentifier));
            matchFields.add(new NameValuePair("responseTimes.responseTimeType", responseType));
            matchFields.add(new NameValuePair("discoveryStatus", "1"));

            List<NameValuePair> matchAnyFieldsAgentIdentifier = new ArrayList<>();
            agentIdentifiers.forEach(c -> matchAnyFieldsAgentIdentifier.add(new NameValuePair("agentIdentifier", c)));
            List<NameValuePair> matchAnyFieldsResponseStatus = new ArrayList<>();
            matchAnyFieldsResponseStatus.add(new NameValuePair("responseTimes.responseStatusTag", ResponseStatusTag.GOOD.name()));
            matchAnyFieldsResponseStatus.add(new NameValuePair("responseTimes.responseStatusTag", ResponseStatusTag.SLOW.name()));

            double[] percentiles = appPercentilesList.parallelStream().mapToDouble(c -> c).toArray();

            List<AggregationQuery> aggregationQueries = new ArrayList<>();
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.PERCENTILE).fieldName("responseTimes.responseInMicroseconds").percentileValue(percentiles).build());

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("timeInGMT")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(true)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .timeSliceInMins(timeSliceInMinutes)
                    .matchAllFields(Optional.of(matchFields))
                    .matchAnyOfFields(Optional.of(new ArrayList<List<NameValuePair>>() {{
                        add(matchAnyFieldsAgentIdentifier);
                        add(matchAnyFieldsResponseStatus);
                    }}))
                    .aggregationQueries(Optional.of(aggregationQueries))
                    .groupByColumns(Optional.of(groupByColumns))
                    .timeZone(timezoneId)
                    .build();

            log.debug("OS query for fetching service transaction data: {}", queryOptions);

            return OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching aggregated data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
        }
        return null;
    }

    public TabularResults getTransactionNtsDataClusterLevel(String accountIdentifier, String serviceIdentifier, String transactionGrpName, String responseType, long fromTime, long toTime) {
        String indexPrefix = Constants.INDEX_PREFIX_RAW_TRANSACTIONS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_RAW_TRANSACTIONS);
            if (elasticClient == null) {
                return null;
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getDailyDatesAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexPrefix + "_" + date));

            List<String> groupByColumns = new ArrayList<>();
            groupByColumns.add("responseTimes.responseStatusTag");

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("serviceIdentifier", serviceIdentifier));
            matchFields.add(new NameValuePair("responseTimes.responseTimeType", responseType));
            matchFields.add(new NameValuePair("discoveryStatus", "1"));

            if (transactionGrpName != null) {
                matchFields.add(new NameValuePair("groupTags", transactionGrpName));
            }

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("@timestamp")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .matchAllFields(Optional.of(matchFields))
                    .groupByColumns(Optional.of(groupByColumns))
                    .build();

            log.debug("OS query for fetching service transaction data: {}", queryOptions);

            return OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching aggregated data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
        }
        return null;
    }

    public TabularResults getTransactionNtsDataInstanceLevel(String accountIdentifier, String serviceIdentifier, String transactionGrpName, Set<String> agentIdentifiers, String responseType, long fromTime, long toTime) {
        String indexPrefix = Constants.INDEX_PREFIX_RAW_TRANSACTIONS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_RAW_TRANSACTIONS);
            if (elasticClient == null) {
                return null;
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getDailyDatesAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexPrefix + "_" + date));

            List<String> groupByColumns = new ArrayList<>();
            groupByColumns.add("responseTimes.responseStatusTag");

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("serviceIdentifier", serviceIdentifier));
            matchFields.add(new NameValuePair("responseTimes.responseTimeType", responseType));
            matchFields.add(new NameValuePair("discoveryStatus", "1"));

            if (transactionGrpName != null) {
                matchFields.add(new NameValuePair("groupTags", transactionGrpName));
            }

            List<NameValuePair> matchAnyFields = new ArrayList<>();
            agentIdentifiers.forEach(c -> matchAnyFields.add(new NameValuePair("agentIdentifier", c)));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("@timestamp")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .matchAllFields(Optional.of(matchFields))
                    .matchAnyOfFields(Optional.of(new ArrayList<List<NameValuePair>>() {{
                        add(matchAnyFields);
                    }}))
                    .groupByColumns(Optional.of(groupByColumns))
                    .build();

            log.debug("OS query for fetching service transaction data: {}", queryOptions);

            return OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching aggregated data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
        }
        return null;
    }

    public TabularResults getTopNTransactionListClusterLevel(String accountIdentifier, String serviceIdentifier, String transactionGrpName,
                                                             int tagId, String responseType,
                                                             long fromTime, long toTime) {
        String indexPrefix = Constants.INDEX_PREFIX_RAW_TRANSACTIONS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_RAW_TRANSACTIONS);
            if (elasticClient == null) {
                return null;
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getDailyDatesAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexPrefix + "_" + date));

            List<String> groupByColumns = new ArrayList<>();
            groupByColumns.add("txnIdentifier");
            groupByColumns.add("responseTimes.responseStatusTag");

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("serviceIdentifier", serviceIdentifier));
            matchFields.add(new NameValuePair("responseTimes.responseTimeType", responseType));
            matchFields.add(new NameValuePair("discoveryStatus", "1"));
            if (tagId != 0)
                matchFields.add(new NameValuePair("groupTags", transactionGrpName));

            List<AggregationQuery> aggregationQueries = new ArrayList<>();
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.UNIQUE).fieldName("agentIdentifier").percentileValue(null).build());

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("@timestamp")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .matchAllFields(Optional.of(matchFields))
                    .groupByColumns(Optional.of(groupByColumns))
                    .aggregationQueries(Optional.of(aggregationQueries))
                    .build();

            log.debug("OS query for fetching service transaction data: {}", queryOptions);

            return OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching aggregated data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
        }
        return null;
    }

    public TabularResults getTopNTransactionListInstanceLevel(String accountIdentifier, String serviceIdentifier, String transactionGrpName,
                                                              int tagId, Set<String> agentIdentifiers,
                                                              String responseType, long fromTime, long toTime) {
        String indexPrefix = Constants.INDEX_PREFIX_RAW_TRANSACTIONS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_RAW_TRANSACTIONS);
            if (elasticClient == null) {
                return null;
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getDailyDatesAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexPrefix + "_" + date));

            List<String> groupByColumns = new ArrayList<>();
            groupByColumns.add("txnIdentifier");
            groupByColumns.add("responseTimes.responseStatusTag");

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("serviceIdentifier", serviceIdentifier));
            matchFields.add(new NameValuePair("responseTimes.responseTimeType", responseType));
            matchFields.add(new NameValuePair("discoveryStatus", "1"));
            if (tagId != 0)
                matchFields.add(new NameValuePair("groupTags", transactionGrpName));

            List<NameValuePair> matchAnyFields = new ArrayList<>();
            agentIdentifiers.forEach(c -> matchAnyFields.add(new NameValuePair("agentIdentifier", c)));

            List<AggregationQuery> aggregationQueries = new ArrayList<>();
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.UNIQUE).fieldName("agentIdentifier").percentileValue(null).build());

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("@timestamp")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .matchAllFields(Optional.of(matchFields))
                    .matchAnyOfFields(Optional.of(new ArrayList<List<NameValuePair>>() {{
                        add(matchAnyFields);
                    }}))
                    .groupByColumns(Optional.of(groupByColumns))
                    .aggregationQueries(Optional.of(aggregationQueries))
                    .build();

            log.debug("OS query for fetching service transaction data: {}", queryOptions);

            return OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching aggregated data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
        }
        return null;
    }

    public TabularResults getTransactionTSDataClusterLevel(String accountIdentifier, String serviceIdentifier, String transactionGrpName,
                                                           String transactionName,
                                                           String responseType, int timeSliceInMin, long fromTime, long toTime, String timezoneId) {
        String indexPrefix = Constants.INDEX_PREFIX_RAW_TRANSACTIONS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_RAW_TRANSACTIONS);
            if (elasticClient == null) {
                return null;
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getDailyDatesAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexPrefix + "_" + date));

            List<String> groupByColumns = new ArrayList<>();
            groupByColumns.add("responseTimes.responseStatusTag");

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("serviceIdentifier", serviceIdentifier));
            matchFields.add(new NameValuePair("responseTimes.responseTimeType", responseType));
            matchFields.add(new NameValuePair("discoveryStatus", "1"));

            if (transactionName != null) {
                matchFields.add(new NameValuePair("txnIdentifier", transactionName));
            }
            if (transactionGrpName != null) {
                matchFields.add(new NameValuePair("groupTags", transactionGrpName));
            }

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("@timestamp")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(true)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .timeSliceInMins(timeSliceInMin)
                    .matchAllFields(Optional.of(matchFields))
                    .groupByColumns(Optional.of(groupByColumns))
                    .timeZone(timezoneId)
                    .build();

            log.debug("OS query for fetching service transaction data: {}", queryOptions);

            return OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching aggregated data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
        }
        return null;
    }

    public TabularResults getTransactionTSDataInstanceLevel(String accountIdentifier, String serviceIdentifier, String transactionGrpName,
                                                            String transactionName, Set<String> agentIdentifiers,
                                                            String responseType, int timeSliceInMin, long fromTime, long toTime, String timezoneId) {
        String indexPrefix = Constants.INDEX_PREFIX_RAW_TRANSACTIONS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_RAW_TRANSACTIONS);
            if (elasticClient == null) {
                return null;
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getDailyDatesAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexPrefix + "_" + date));

            List<String> groupByColumns = new ArrayList<>();
            groupByColumns.add("responseTimes.responseStatusTag");

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("serviceIdentifier", serviceIdentifier));
            matchFields.add(new NameValuePair("responseTimes.responseTimeType", responseType));
            matchFields.add(new NameValuePair("discoveryStatus", "1"));

            if (transactionName != null) {
                matchFields.add(new NameValuePair("txnIdentifier", transactionName));
            }
            if (transactionGrpName != null) {
                matchFields.add(new NameValuePair("groupTags", transactionGrpName));
            }

            List<NameValuePair> matchAnyFields = new ArrayList<>();
            agentIdentifiers.forEach(c -> matchAnyFields.add(new NameValuePair("agentIdentifier", c)));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("@timestamp")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(true)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .timeSliceInMins(timeSliceInMin)
                    .matchAllFields(Optional.of(matchFields))
                    .matchAnyOfFields(Optional.of(new ArrayList<List<NameValuePair>>() {{
                        add(matchAnyFields);
                    }}))
                    .groupByColumns(Optional.of(groupByColumns))
                    .timeZone(timezoneId)
                    .build();

            log.debug("OS query for fetching service transaction data: {}", queryOptions);

            return OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching aggregated data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
        }
        return null;
    }

    public TabularResults getTransactionTSDataForRTKPIClusterLevel(String accountIdentifier, String serviceIdentifier,
                                                                   String transactionName,
                                                                   String responseType, int timeSliceInMin, long fromTime, long toTime,
                                                                   String timezoneId) {
        String indexPrefix = Constants.INDEX_PREFIX_RAW_TRANSACTIONS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_RAW_TRANSACTIONS);
            if (elasticClient == null) {
                return null;
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getDailyDatesAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexPrefix + "_" + date));

            List<String> groupByColumns = new ArrayList<>();
            groupByColumns.add("serviceIdentifier");

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("serviceIdentifier", serviceIdentifier));
            matchFields.add(new NameValuePair("responseTimes.responseTimeType", responseType));
            matchFields.add(new NameValuePair("txnIdentifier", transactionName));
            matchFields.add(new NameValuePair("discoveryStatus", "1"));

            List<NameValuePair> matchAnyFields = new ArrayList<>();
            matchAnyFields.add(new NameValuePair("responseTimes.responseStatusTag", "SLOW"));
            matchAnyFields.add(new NameValuePair("responseTimes.responseStatusTag", "GOOD"));

            List<AggregationQuery> aggregationQueries = new ArrayList<>();
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.AVERAGE).fieldName("responseTimes.responseInMicroseconds").percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.MAX).fieldName("responseTimes.responseInMicroseconds").percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.MIN).fieldName("responseTimes.responseInMicroseconds").percentileValue(null).build());

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("@timestamp")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(true)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .timeSliceInMins(timeSliceInMin)
                    .matchAllFields(Optional.of(matchFields))
                    .groupByColumns(Optional.of(groupByColumns))
                    .aggregationQueries(Optional.of(aggregationQueries))
                    .matchAnyOfFields(Optional.of(new ArrayList<List<NameValuePair>>() {{
                        add(matchAnyFields);
                    }}))
                    .timeZone(timezoneId)
                    .build();

            log.debug("OS query for fetching service transaction data: {}", queryOptions);

            return OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching aggregated data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
        }
        return null;
    }

    public TabularResults getTransactionTSDataForRTKPIInstanceLevel(String accountIdentifier, String serviceIdentifier,
                                                                    String transactionName, Set<String> agentIdentifiers,
                                                                    String responseType, int timeSliceInMin, long fromTime, long toTime,
                                                                    String timezoneId) {
        String indexPrefix = Constants.INDEX_PREFIX_RAW_TRANSACTIONS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_RAW_TRANSACTIONS);
            if (elasticClient == null) {
                return null;
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getDailyDatesAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexPrefix + "_" + date));

            List<String> groupByColumns = new ArrayList<>();
            groupByColumns.add("serviceIdentifier");

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("serviceIdentifier", serviceIdentifier));
            matchFields.add(new NameValuePair("responseTimes.responseTimeType", responseType));
            matchFields.add(new NameValuePair("txnIdentifier", transactionName));
            matchFields.add(new NameValuePair("discoveryStatus", "1"));

            List<AggregationQuery> aggregationQueries = new ArrayList<>();
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.AVERAGE).fieldName("responseTimes.responseInMicroseconds").percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.MAX).fieldName("responseTimes.responseInMicroseconds").percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.MIN).fieldName("responseTimes.responseInMicroseconds").percentileValue(null).build());

            List<NameValuePair> matchAnyFieldsAgentIdentifier = new ArrayList<>();
            agentIdentifiers.forEach(c -> matchAnyFieldsAgentIdentifier.add(new NameValuePair("agentIdentifier", c)));
            List<NameValuePair> matchAnyFieldsResponseStatus = new ArrayList<>();
            matchAnyFieldsResponseStatus.add(new NameValuePair("responseTimes.responseStatusTag", "SLOW"));
            matchAnyFieldsResponseStatus.add(new NameValuePair("responseTimes.responseStatusTag", "GOOD"));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("@timestamp")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(true)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .timeSliceInMins(timeSliceInMin)
                    .matchAllFields(Optional.of(matchFields))
                    .matchAnyOfFields(Optional.of(new ArrayList<List<NameValuePair>>() {{
                        add(matchAnyFieldsAgentIdentifier);
                        add(matchAnyFieldsResponseStatus);
                    }}))
                    .groupByColumns(Optional.of(groupByColumns))
                    .aggregationQueries(Optional.of(aggregationQueries))
                    .timeZone(timezoneId)
                    .build();

            log.debug("OS query for fetching service transaction data: {}", queryOptions);

            return OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching aggregated data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
        }
        return null;
    }

    public TabularResults getTransactionTSDataForKPIClusterLevel(String accountIdentifier, String serviceIdentifier,
                                                                 String transactionName,
                                                                 String responseType, int timeSliceInMin, long fromTime, long toTime,
                                                                 String timezoneId) {
        String indexPrefix = Constants.INDEX_PREFIX_RAW_TRANSACTIONS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_RAW_TRANSACTIONS);
            if (elasticClient == null) {
                return null;
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getDailyDatesAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexPrefix + "_" + date));

            List<String> groupByColumns = new ArrayList<>();
            groupByColumns.add("responseTimes.responseStatusTag");

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("serviceIdentifier", serviceIdentifier));
            matchFields.add(new NameValuePair("responseTimes.responseTimeType", responseType));
            matchFields.add(new NameValuePair("txnIdentifier", transactionName));
            matchFields.add(new NameValuePair("discoveryStatus", "1"));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("@timestamp")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(true)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .timeSliceInMins(timeSliceInMin)
                    .matchAllFields(Optional.of(matchFields))
                    .groupByColumns(Optional.of(groupByColumns))
                    .timeZone(timezoneId)
                    .build();

            log.debug("OS query for fetching service transaction data: {}", queryOptions);

            return OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching aggregated data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
        }
        return null;
    }

    public TabularResults getTransactionTSDataForKPIInstanceLevel(String accountIdentifier, String serviceIdentifier,
                                                                  String transactionName, Set<String> agentIdentifiers,
                                                                  String responseType, int timeSliceInMin, long fromTime, long toTime,
                                                                  String timezoneId) {
        String indexPrefix = Constants.INDEX_PREFIX_RAW_TRANSACTIONS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_RAW_TRANSACTIONS);
            if (elasticClient == null) {
                return null;
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getDailyDatesAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexPrefix + "_" + date));

            List<String> groupByColumns = new ArrayList<>();
            groupByColumns.add("responseTimes.responseStatusTag");

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("serviceIdentifier", serviceIdentifier));
            matchFields.add(new NameValuePair("responseTimes.responseTimeType", responseType));
            matchFields.add(new NameValuePair("txnIdentifier", transactionName));
            matchFields.add(new NameValuePair("discoveryStatus", "1"));


            List<NameValuePair> matchAnyFields = new ArrayList<>();
            agentIdentifiers.forEach(c -> matchAnyFields.add(new NameValuePair("agentIdentifier", c)));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("@timestamp")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(true)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .timeSliceInMins(timeSliceInMin)
                    .matchAllFields(Optional.of(matchFields))
                    .matchAnyOfFields(Optional.of(new ArrayList<List<NameValuePair>>() {{
                        add(matchAnyFields);
                    }}))
                    .groupByColumns(Optional.of(groupByColumns))
                    .timeZone(timezoneId)
                    .build();

            log.debug("OS query for fetching service transaction data: {}", queryOptions);

            return OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching aggregated data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
        }
        return null;
    }

    public TabularResults getInboundOutboundTxnDetails(String accountIdentifier, String responseType, long fromTime, long toTime) {
        String indexPrefix = Constants.INDEX_PREFIX_RAW_TRANSACTIONS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_RAW_TRANSACTIONS);
            if (elasticClient == null) {
                return null;
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getDailyDatesAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexPrefix + "_" + date));

            List<String> groupByColumns = new ArrayList<>();
            groupByColumns.add("serviceIdentifier");
            groupByColumns.add("responseTimes.responseStatusTag");

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("responseTimes.responseTimeType", responseType));
            matchFields.add(new NameValuePair("discoveryStatus", "1"));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("@timestamp")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .matchAllFields(Optional.of(matchFields))
                    .groupByColumns(Optional.of(groupByColumns))
                    .build();

            log.debug("OS query for fetching service transaction data: {}", queryOptions);

            return OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching aggregated data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
        }
        return null;
    }

    public TabularResults getTransactionListUnderInbound(String accountIdentifier, String serviceIdentifier, String responseType,
                                                         long fromTime, long toTime) {
        String indexPrefix = Constants.INDEX_PREFIX_RAW_TRANSACTIONS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_RAW_TRANSACTIONS);
            if (elasticClient == null) {
                return null;
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getDailyDatesAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexPrefix + "_" + date));

            List<String> groupByColumns = new ArrayList<>();
            groupByColumns.add("txnIdentifier");
            groupByColumns.add("responseTimes.responseStatusTag");

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("serviceIdentifier", serviceIdentifier));
            matchFields.add(new NameValuePair("responseTimes.responseTimeType", responseType));
            matchFields.add(new NameValuePair("discoveryStatus", "1"));

            List<AggregationQuery> aggregationQueries = new ArrayList<>();
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.AVERAGE).fieldName("responseTimes.responseInMicroseconds").percentileValue(null).build());

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("@timestamp")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .matchAllFields(Optional.of(matchFields))
                    .groupByColumns(Optional.of(groupByColumns))
                    .aggregationQueries(Optional.of(aggregationQueries))
                    .build();

            log.debug("OS query for fetching service transaction data: {}", queryOptions);

            return OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching aggregated data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
        }
        return null;
    }

    public TabularResults getWorkloadDataHeatMapPage(String accountIdentifier, String serviceIdentifier,
                                                     long fromTime, long toTime) {
        String indexPrefix = Constants.INDEX_PREFIX_RAW_TRANSACTIONS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_RAW_TRANSACTIONS);
            if (elasticClient == null) {
                return null;
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getDailyDatesAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexPrefix + "_" + date));

            List<String> groupByColumns = new ArrayList<>();
            groupByColumns.add("agentIdentifier");

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("serviceIdentifier", serviceIdentifier));
            matchFields.add(new NameValuePair("discoveryStatus", "1"));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("@timestamp")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .matchAllFields(Optional.of(matchFields))
                    .groupByColumns(Optional.of(groupByColumns))
                    .build();

            log.debug("OS query for fetching service transaction data: {}", queryOptions);

            return OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching aggregated data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
        }
        return null;
    }

}
