package com.appnomic.appsone.api.dao.opensearch;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.manager.OpenSearchConnectionManager;
import com.appnomic.appsone.api.pojo.TabularResultsTypePojo;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.opeasearchquery.enumerations.Aggregations;
import com.appnomic.appsone.opeasearchquery.helper.OpenSearchQueryHelper;
import com.appnomic.appsone.opeasearchquery.queryopts.QueryOptions;
import com.appnomic.appsone.opeasearchquery.results.AggregationQuery;
import com.appnomic.appsone.opeasearchquery.results.NameValuePair;
import com.appnomic.appsone.opeasearchquery.results.RawDocumentResults;
import com.appnomic.appsone.opeasearchquery.results.TabularResults;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.enums.KPIAttributes;
import com.heal.configuration.enums.TransactionResponseTypes;
import com.heal.configuration.pojos.ClusterOperationEnum;
import com.heal.configuration.pojos.TimeRangeDetails;
import com.heal.configuration.pojos.TransformedIndexDetails;
import com.heal.configuration.pojos.opensearch.CollatedTransactionAgentData;
import com.heal.configuration.pojos.opensearch.CollatedTransactionData;
import com.heal.configuration.util.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.client.RestHighLevelClient;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 25-02-2022
 */
@Slf4j
public class CollatedTransactionsSearchRepo {

    private final ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();

    private static String indexExtension = ConfProperties.getString(Constants.OPENSEARCH_INDEX_EXTENSION, Constants.OPENSEARCH_INDEX_EXTENSION_DEFAULT);

    public List<TabularResultsTypePojo> getAllTransactionDataByService(String accountIdentifier, String responseType,
                                                                       long fromTime, long toTime, TimeRangeDetails timeRangeDetails,
                                                                       List<TimeRangeDetails> timeRangeDetailsList, List<Long> times) {

        List<TabularResultsTypePojo> tabularResultsBooleanList = new ArrayList<>();
        String indexPrefix = Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS);
            if (elasticClient == null) {
                return new ArrayList<>();
            }

            List<String> groupByColumns = new ArrayList<>();
            groupByColumns.add("serviceId");

            String responseTypeMethodName = responseType.equals(TransactionResponseTypes.DC.name()) ? "dcKpis." :
                    responseType.equals(TransactionResponseTypes.EUM.name()) ? "eumKpis." : "renderingKpis.";

            List<AggregationQuery> aggregationQueries = new ArrayList<>();
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.SLOW_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.FAIL_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.TOTAL_VOLUME).percentileValue(null).build());

            QueryOptions queryOptions = QueryOptions.builder()
                    .epochFieldName("timeInGMT")
                    .timeStampFieldName("timeInGMT")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .build();

            if (timeRangeDetails.getNeedRolledUpIndexes() == 1) {

                List<TransformedIndexDetails> transformedIndicesList = DateHelper.getTransformedIndexAsString(times, timeRangeDetailsList, timeRangeDetails, 0);

                TransformedIndexDetails nonTransformedIndices = transformedIndicesList.parallelStream()
                        .filter(c -> !c.isTransformed()).findAny().orElse(null);
                if (nonTransformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    nonTransformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                    queryOptions.setEpochFromDate(nonTransformedIndices.getFromTime());
                    queryOptions.setEpochToDate(nonTransformedIndices.getToTime());

                    log.debug("OS query for fetching service transaction data: {}", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(false)
                                .build());
                    }
                }

                TransformedIndexDetails transformedIndices = transformedIndicesList.parallelStream()
                        .filter(TransformedIndexDetails::isTransformed).findAny().orElse(null);
                if (transformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    transformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                    queryOptions.setEpochFromDate(transformedIndices.getFromTime());
                    queryOptions.setEpochToDate(transformedIndices.getToTime());

                    log.debug("OS query for fetching service transaction data: {}", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(true)
                                .build());
                    }
                }
            } else {
                List<String> indexNames = new ArrayList<>();
                DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                        indexNames.add(indexPrefix + "_" + date));

                queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                queryOptions.setIndexNames(indexNames);
                queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));

                log.debug("OS query for fetching service transaction data: {}", queryOptions);
                TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                    tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                            .tabularResults(results)
                            .isTransformed(false)
                            .build());
                }
            }

            return tabularResultsBooleanList;

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
            return new ArrayList<>();
        }
    }

    public List<CollatedTransactionData> getTransactionDataByServiceAndGroupName(String accountIdentifier,
                                                                                 String serviceId, String groupName,
                                                                                 long fromTime, long toTime) {

        String indexPrefix = Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS);
            if (elasticClient == null) {
                return Collections.emptyList();
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexPrefix + "_" + date));

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("serviceId", serviceId));
            matchFields.add(new NameValuePair("groupNames", groupName));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("timeInGMT")
                    .timeStampFieldName("timeInGMT")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .matchAllFields(Optional.of(matchFields))
                    .fetchAllRecords(true)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .build();

            log.debug("OS query for fetching service transaction data: {}", queryOptions);

            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);

            if (rawDocuments != null && rawDocuments.getDocuments() != null && !rawDocuments.getDocuments().isEmpty()) {
                return rawDocuments.getDocuments().parallelStream()
                        .map(doc -> {
                            try {
                                return objectMapper.readValue(doc, new TypeReference<CollatedTransactionData>() {
                                });
                            } catch (Exception e) {
                                HealUICache.INSTANCE.updateHealUIErrors(1);
                                log.error("Error occurred while mapping OpenSearch signals data to Collated Transaction for index {}. Details: accountId [{}]", indexPrefix, accountIdentifier, e);
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}] , service id : {} , group name : {}",
                    indexPrefix, accountIdentifier, serviceId, groupName, e);
        }
        return Collections.emptyList();
    }

    public List<CollatedTransactionAgentData> getTransactionDataByAgent(String accountIdentifier, String agentUid,
                                                                        String serviceId,
                                                                        long fromTime, long toTime) {

        String indexPrefix = Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS_AGENT + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS_AGENT);
            if (elasticClient == null) {
                return Collections.emptyList();
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexPrefix + "_" + date));

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("agentUid", agentUid));
            matchFields.add(new NameValuePair("serviceId", serviceId));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("timeInGMT")
                    .timeStampFieldName("timeInGMT")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .matchAllFields(Optional.of(matchFields))
                    .fetchAllRecords(true)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .build();

            log.debug("OS query for fetching agent transaction data: {}", queryOptions);

            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);

            if (rawDocuments != null && rawDocuments.getDocuments() != null && !rawDocuments.getDocuments().isEmpty()) {
                return rawDocuments.getDocuments().parallelStream()
                        .map(doc -> {
                            try {
                                return objectMapper.readValue(doc, new TypeReference<CollatedTransactionAgentData>() {
                                });
                            } catch (Exception e) {
                                HealUICache.INSTANCE.updateHealUIErrors(1);
                                log.error("Error occurred while mapping OpenSearch signals data to Collated Transaction for index {}. Details: accountId [{}]", indexPrefix, accountIdentifier, e);
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
        }
        return Collections.emptyList();
    }

    public List<TabularResultsTypePojo> getTransactionCountByService(String accountIdentifier, String serviceId, long fromTime, long toTime, TimeRangeDetails timeRangeDetails,
                                                                     List<TimeRangeDetails> timeRangeDetailsList, List<Long> times) {

        long st = System.currentTimeMillis();

        List<TabularResultsTypePojo> tabularResultsBooleanList = new ArrayList<>();
        String indexPrefix = Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS);
            if (elasticClient == null) {
                return new ArrayList<>();
            }

            List<NameValuePair> matchAllFields = new ArrayList<>();
            matchAllFields.add(new NameValuePair("serviceId", serviceId));

            QueryOptions queryOptions = QueryOptions.builder()
                    .epochFieldName("timeInGMT")
                    .timeStampFieldName("timeInGMT")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .fetchAllRecords(true)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .matchAllFields(Optional.of(matchAllFields))
                    .build();

            if (timeRangeDetails.getNeedRolledUpIndexes() == 1) {

                List<TransformedIndexDetails> transformedIndicesList = DateHelper.getTransformedIndexAsString(times, timeRangeDetailsList, timeRangeDetails, 0);

                TransformedIndexDetails nonTransformedIndices = transformedIndicesList.parallelStream()
                        .filter(c -> !c.isTransformed()).findAny().orElse(null);
                if (nonTransformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    nonTransformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setEpochFromDate(nonTransformedIndices.getFromTime());
                    queryOptions.setEpochToDate(nonTransformedIndices.getToTime());

                    log.debug("OS query for fetching service transaction data: {}", queryOptions);
                    long count = OpenSearchQueryHelper.getCount(queryOptions, elasticClient);
                    tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                            .count(count)
                            .isTransformed(true)
                            .build());

                }

                TransformedIndexDetails transformedIndices = transformedIndicesList.parallelStream()
                        .filter(TransformedIndexDetails::isTransformed).findAny().orElse(null);
                if (transformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    transformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setEpochFromDate(transformedIndices.getFromTime());
                    queryOptions.setEpochToDate(transformedIndices.getToTime());

                    log.debug("OS query for fetching service transaction data: {}", queryOptions);
                    long count = OpenSearchQueryHelper.getCount(queryOptions, elasticClient);
                    tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                            .count(count)
                            .isTransformed(true)
                            .build());
                }
            } else {
                List<String> indexNames = new ArrayList<>();
                DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                        indexNames.add(indexPrefix + "_" + date));

                queryOptions.setIndexNames(indexNames);
                log.debug("OS query for fetching service transaction data: {}", queryOptions);
                long count = OpenSearchQueryHelper.getCount(queryOptions, elasticClient);
                tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                        .count(count)
                        .isTransformed(false)
                        .build());
            }

            return tabularResultsBooleanList;
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while fetching service wise transaction data check for accountId:{}, serviceId:{}, fromTime:{}, toTime:{}, index:{}",
                    accountIdentifier, serviceId, fromTime, toTime, indexPrefix, e);
            return new ArrayList<>();
        } finally {
            log.debug("Time take for getServiceWiseTransactionCount() method is {} ms.", System.currentTimeMillis() - st);
        }
    }

    public List<TabularResultsTypePojo> getTransactionCountByAgent(String accountIdentifier, String serviceId, Set<String> agentUidList,
                                                                   long fromTime, long toTime, TimeRangeDetails timeRangeDetails,
                                                                   List<TimeRangeDetails> timeRangeDetailsList, List<Long> times) {
        long st = System.currentTimeMillis();

        List<TabularResultsTypePojo> tabularResultsBooleanList = new ArrayList<>();
        String indexPrefix = Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS_AGENT + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS_AGENT);
            if (elasticClient == null) {
                HealUICache.INSTANCE.updateHealUIErrors(1);
                return new ArrayList<>();
            }

            List<NameValuePair> matchAllFields = new ArrayList<>();
            matchAllFields.add(new NameValuePair("serviceId", serviceId));

            List<NameValuePair> matchAnyFields = new ArrayList<>();
            agentUidList.forEach(c -> matchAnyFields.add(new NameValuePair("agentUid", c)));

            QueryOptions queryOptions = QueryOptions.builder()
                    .epochFieldName("timeInGMT")
                    .timeStampFieldName("timeInGMT")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .matchAllFields(Optional.of(matchAllFields))
                    .matchAnyOfFields(Optional.of(new ArrayList<List<NameValuePair>>() {{
                        add(matchAnyFields);
                    }}))
                    .build();

            if (timeRangeDetails.getNeedRolledUpIndexes() == 1) {

                List<TransformedIndexDetails> transformedIndicesList = DateHelper.getTransformedIndexAsString(times, timeRangeDetailsList, timeRangeDetails, 0);

                TransformedIndexDetails nonTransformedIndices = transformedIndicesList.parallelStream()
                        .filter(c -> !c.isTransformed()).findAny().orElse(null);
                if (nonTransformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    nonTransformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setEpochFromDate(nonTransformedIndices.getFromTime());
                    queryOptions.setEpochToDate(nonTransformedIndices.getToTime());

                    log.debug("OS query for fetching service transaction data: {}", queryOptions);
                    long count = OpenSearchQueryHelper.getCount(queryOptions, elasticClient);
                    tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                            .count(count)
                            .isTransformed(true)
                            .build());

                }

                TransformedIndexDetails transformedIndices = transformedIndicesList.parallelStream()
                        .filter(TransformedIndexDetails::isTransformed).findAny().orElse(null);
                if (transformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    transformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setEpochFromDate(transformedIndices.getFromTime());
                    queryOptions.setEpochToDate(transformedIndices.getToTime());

                    log.debug("OS query for fetching service transaction data: {}", queryOptions);
                    long count = OpenSearchQueryHelper.getCount(queryOptions, elasticClient);
                    tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                            .count(count)
                            .isTransformed(true)
                            .build());
                }
            } else {
                List<String> indexNames = new ArrayList<>();
                DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                        indexNames.add(indexPrefix + "_" + date));

                queryOptions.setIndexNames(indexNames);
                log.debug("OS query for fetching service transaction data: {}", queryOptions);
                long count = OpenSearchQueryHelper.getCount(queryOptions, elasticClient);
                tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                        .count(count)
                        .isTransformed(false)
                        .build());
            }

            return tabularResultsBooleanList;

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while fetching transaction data count for accountId:{}, service:{}, agentId:{}, fromTime:{}, toTime:{}, index:{}",
                    accountIdentifier, serviceId, agentUidList, fromTime, toTime, indexPrefix, e);
            return new ArrayList<>();
        } finally {
            log.debug("Time take for getTransactionDataCount() method is {} ms.", System.currentTimeMillis() - st);
        }
    }

    public Map<String, Long> getAgentTransactionCollatedDataByService(String accountIdentifier, String serviceId,
                                                                      long fromTime, long toTime, TimeRangeDetails timeRangeDetails,
                                                                      List<TimeRangeDetails> timeRangeDetailsList, List<Long> times) {
        long st = System.currentTimeMillis();

        Map<String, Long> resultMap = new HashMap<>();
        String indexPrefix = Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS_AGENT + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS_AGENT);
            if (elasticClient == null) {
                return Collections.emptyMap();
            }

            List<NameValuePair> matchAllFields = new ArrayList<>();
            matchAllFields.add(new NameValuePair("serviceId", serviceId));

            List<String> columns = new ArrayList<>();
            columns.add("agentUid");

            QueryOptions queryOptions = QueryOptions.builder()
                    .epochFieldName("timeInGMT")
                    .timeStampFieldName("timeInGMT")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .matchAllFields(Optional.of(matchAllFields))
                    .build();

            if (timeRangeDetails.getNeedRolledUpIndexes() == 1) {

                List<TransformedIndexDetails> transformedIndicesList = DateHelper.getTransformedIndexAsString(times, timeRangeDetailsList, timeRangeDetails, 0);

                TransformedIndexDetails nonTransformedIndices = transformedIndicesList.parallelStream()
                        .filter(c -> !c.isTransformed()).findAny().orElse(null);
                if (nonTransformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    nonTransformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(columns)));
                    queryOptions.setEpochFromDate(nonTransformedIndices.getFromTime());
                    queryOptions.setEpochToDate(nonTransformedIndices.getToTime());

                    log.debug("OS query for fetching service transaction data: {}", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() == null && results.getRowResults().isEmpty()) {
                        Map<String, Long> t1 = results.getRowResults().stream()
                                .collect(Collectors.toMap(c -> c.getListOfRows().get(0).getColumnValue(), TabularResults.ResultRow::getCountValue));
                        t1.forEach((key, value) -> {
                            if (resultMap.containsKey(key)) {
                                resultMap.put(key, resultMap.get(key) + value);
                            } else resultMap.put(key, value);
                        });
                    }
                }

                TransformedIndexDetails transformedIndices = transformedIndicesList.parallelStream()
                        .filter(TransformedIndexDetails::isTransformed).findAny().orElse(null);
                if (transformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    transformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(columns)));
                    queryOptions.setEpochFromDate(transformedIndices.getFromTime());
                    queryOptions.setEpochToDate(transformedIndices.getToTime());

                    log.debug("OS query for fetching service transaction data: {}", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() == null && results.getRowResults().isEmpty()) {
                        Map<String, Long> t1 = results.getRowResults().stream()
                                .collect(Collectors.toMap(c -> c.getListOfRows().get(0).getColumnValue(), TabularResults.ResultRow::getCountValue));
                        t1.forEach((key, value) -> {
                            if (resultMap.containsKey(key)) {
                                resultMap.put(key, resultMap.get(key) + value);
                            } else resultMap.put(key, value);
                        });
                    }
                }
            } else {
                List<String> indexNames = new ArrayList<>();
                DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                        indexNames.add(indexPrefix + "_" + date));

                queryOptions.setIndexNames(indexNames);
                queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(columns)));
                log.debug("OS query for fetching service transaction data: {}", queryOptions);
                TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                    return results.getRowResults().stream()
                            .collect(Collectors.toMap(c -> c.getListOfRows().get(0).getColumnValue(), TabularResults.ResultRow::getCountValue));
                }
            }

            return resultMap;

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while fetching agent wise transaction count for accountId:{}, service:{}, from:{}, to:{}, index:{}"
                    , accountIdentifier, serviceId, fromTime, toTime, indexPrefix, e);
            return Collections.emptyMap();
        } finally {
            log.debug("Time take for getAgentWiseTransactionCount() method is {} ms.", System.currentTimeMillis() - st);
        }
    }

    public List<TabularResultsTypePojo> getTransactionCollatedDataByServiceForAllKpi(String accountIdentifier,
                                                                                     String serviceId, List<String> groupByColumns,
                                                                                     String responseType, long fromTime, long toTime,
                                                                                     TimeRangeDetails timeRangeDetails,
                                                                                     List<TimeRangeDetails> timeRangeDetailsList, List<Long> times) {

        List<TabularResultsTypePojo> tabularResultsBooleanList = new ArrayList<>();
        String indexPrefix = Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS);
            if (elasticClient == null) {
                return new ArrayList<>();
            }

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("serviceId", serviceId));

            String responseTypeMethodName = responseType.equals(TransactionResponseTypes.DC.name()) ? "dcKpis." :
                    responseType.equals(TransactionResponseTypes.EUM.name()) ? "eumKpis." : "renderingKpis.";

            List<AggregationQuery> aggregationQueries = new ArrayList<>();
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.AVERAGE).fieldName(responseTypeMethodName + KPIAttributes.RESPONSE_TIME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.SLOW_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.SUCCESS_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.FAIL_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.TIMEOUT_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.TOTAL_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.UNKNOWN_VOLUME).percentileValue(null).build());

            QueryOptions queryOptions = QueryOptions.builder()
                    .epochFieldName("timeInGMT")
                    .timeStampFieldName("timeInGMT")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .matchAllFields(Optional.of(matchFields))
                    .build();

            if (timeRangeDetails.getNeedRolledUpIndexes() == 1) {

                List<TransformedIndexDetails> transformedIndicesList = DateHelper.getTransformedIndexAsString(times, timeRangeDetailsList, timeRangeDetails, 0);

                TransformedIndexDetails nonTransformedIndices = transformedIndicesList.parallelStream()
                        .filter(c -> !c.isTransformed()).findAny().orElse(null);
                if (nonTransformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    nonTransformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                    queryOptions.setEpochFromDate(nonTransformedIndices.getFromTime());
                    queryOptions.setEpochToDate(nonTransformedIndices.getToTime());

                    log.debug("OS query for fetching service transaction data: {}", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(false)
                                .build());
                    }

                }

                TransformedIndexDetails transformedIndices = transformedIndicesList.parallelStream()
                        .filter(TransformedIndexDetails::isTransformed).findAny().orElse(null);
                if (transformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    transformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                    queryOptions.setEpochFromDate(transformedIndices.getFromTime());
                    queryOptions.setEpochToDate(transformedIndices.getToTime());

                    log.debug("OS query for fetching service transaction data: {}", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(true)
                                .build());
                    }
                }
            } else {
                List<String> indexNames = new ArrayList<>();
                DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                        indexNames.add(indexPrefix + "_" + date));

                queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                queryOptions.setIndexNames(indexNames);
                queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                log.debug("OS query for fetching service transaction data: {}", queryOptions);
                TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                    tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                            .tabularResults(results)
                            .isTransformed(false)
                            .build());
                }
            }

            return tabularResultsBooleanList;

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
            return new ArrayList<>();
        }
    }

    public Map<Long, Map<String, Double>> getTransactionCollatedDataByServiceForSingleKpi(String accountIdentifier,
                                                                                          String serviceId, String kpiIdentifier,
                                                                                          ClusterOperationEnum operationType,
                                                                                          int timeSliceInMinutes,
                                                                                          String responseType,
                                                                                          long fromTime, long toTime,
                                                                                          TimeRangeDetails timeRangeDetails,
                                                                                          List<TimeRangeDetails> timeRangeDetailsList, List<Long> times,
                                                                                          String timezoneId) {

        List<TabularResultsTypePojo> tabularResultsBooleanList = new ArrayList<>();
        String indexPrefix = Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS);
            if (elasticClient == null) {
                return Collections.emptyMap();
            }

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("serviceId", serviceId));

            List<String> groupByColumns = new ArrayList<>();
            groupByColumns.add("serviceId");

            String responseTypeMethodName = responseType.equals(TransactionResponseTypes.DC.name()) ? "dcKpis." :
                    responseType.equals(TransactionResponseTypes.EUM.name()) ? "eumKpis." : "renderingKpis.";

            List<AggregationQuery> aggregationQueries = new ArrayList<>();
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.valueOfLabel(operationType.getOperation())).fieldName(responseTypeMethodName + kpiIdentifier).percentileValue(null).build());

            QueryOptions queryOptions = QueryOptions.builder()
                    .epochFieldName("timeInGMT")
                    .timeStampFieldName("timeInGMT")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(true)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .matchAllFields(Optional.of(matchFields))
                    .timeSliceInMins(timeSliceInMinutes)
                    .timeZone(timezoneId)
                    .build();


            if (timeRangeDetails.getNeedRolledUpIndexes() == 1) {

                List<TransformedIndexDetails> transformedIndicesList = DateHelper.getTransformedIndexAsString(times, timeRangeDetailsList, timeRangeDetails, 0);

                TransformedIndexDetails nonTransformedIndices = transformedIndicesList.parallelStream()
                        .filter(c -> !c.isTransformed()).findAny().orElse(null);
                if (nonTransformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    nonTransformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                    queryOptions.setEpochFromDate(nonTransformedIndices.getFromTime());
                    queryOptions.setEpochToDate(nonTransformedIndices.getToTime());

                    log.debug("OS query for fetching service transaction data: {}", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(false)
                                .build());
                    }

                }

                TransformedIndexDetails transformedIndices = transformedIndicesList.parallelStream()
                        .filter(TransformedIndexDetails::isTransformed).findAny().orElse(null);
                if (transformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    transformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                    queryOptions.setEpochFromDate(transformedIndices.getFromTime());
                    queryOptions.setEpochToDate(transformedIndices.getToTime());

                    log.debug("OS query for fetching service transaction data: {}", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(true)
                                .build());
                    }
                }
            } else {
                List<String> indexNames = new ArrayList<>();
                DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                        indexNames.add(indexPrefix + "_" + date));

                queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                queryOptions.setIndexNames(indexNames);
                queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));

                log.debug("OS query for fetching service transaction data: {}", queryOptions);
                TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                    tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                            .tabularResults(results)
                            .isTransformed(false)
                            .build());
                }
            }

            if (tabularResultsBooleanList.isEmpty()) {
                return Collections.emptyMap();
            }

            Map<Long, Map<String, Double>> resultMap = new HashMap<>();

            tabularResultsBooleanList.forEach(d -> {
                Map<Long, Map<String, Double>> temp = d.getTabularResults().getRowResults().stream()
                        .collect(Collectors.toMap(r -> r.getTimestamp().getTime(), r -> r.getListOfRows().stream()
                                .filter(c -> !c.getColumnDataType().contains("String"))
                                .collect(Collectors.toMap(c -> kpiIdentifier, c -> Double.parseDouble(c.getColumnValue())))))
                        .entrySet().stream()
                        .sorted(Map.Entry.comparingByKey())
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                                (oldValue, newValue) -> oldValue, LinkedHashMap::new));

                temp.forEach((key, value) -> {
                    if (resultMap.containsKey(key)) {
                        Map<String, Double> tMap = new HashMap<>();
                        resultMap.get(key).forEach((k, v) -> tMap.put(k, v + value.getOrDefault(k, 0D)));
                        resultMap.put(key, tMap);
                    } else resultMap.put(key, value);
                });

            });

            return resultMap;
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
            return Collections.emptyMap();
        }
    }

    public List<TabularResultsTypePojo> getTransactionCollatedDataByServiceAndTxnGrpNames(String accountIdentifier,
                                                                                          String serviceId, String kpiIdentifier,
                                                                                          ClusterOperationEnum operationType, String transactionGrpName,
                                                                                          String responseType, long fromTime, long toTime,
                                                                                          TimeRangeDetails timeRangeDetails,
                                                                                          List<TimeRangeDetails> timeRangeDetailsList, List<Long> times) {

        List<TabularResultsTypePojo> tabularResultsBooleanList = new ArrayList<>();
        String indexPrefix = Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS);
            if (elasticClient == null) {
                return new ArrayList<>();
            }

            List<NameValuePair> matchAllFields = new ArrayList<>();
            matchAllFields.add(new NameValuePair("serviceId", serviceId));
            if (transactionGrpName != null) {
                matchAllFields.add(new NameValuePair("groupNames", transactionGrpName));
            }

            List<String> groupByColumns = new ArrayList<>();
            groupByColumns.add("serviceId");

            String responseTypeMethodName = responseType.equals(TransactionResponseTypes.DC.name()) ? "dcKpis." :
                    responseType.equals(TransactionResponseTypes.EUM.name()) ? "eumKpis." : "renderingKpis.";

            List<AggregationQuery> aggregationQueries = new ArrayList<>();
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.valueOfLabel(operationType.getOperation())).fieldName(responseTypeMethodName + kpiIdentifier).percentileValue(null).build());

            QueryOptions queryOptions = QueryOptions.builder()
                    .epochFieldName("timeInGMT")
                    .timeStampFieldName("timeInGMT")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .matchAllFields(Optional.of(matchAllFields))
                    .build();

            if (timeRangeDetails.getNeedRolledUpIndexes() == 1) {

                List<TransformedIndexDetails> transformedIndicesList = DateHelper.getTransformedIndexAsString(times, timeRangeDetailsList, timeRangeDetails, 0);

                TransformedIndexDetails nonTransformedIndices = transformedIndicesList.parallelStream()
                        .filter(c -> !c.isTransformed()).findAny().orElse(null);
                if (nonTransformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    nonTransformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                    queryOptions.setEpochFromDate(nonTransformedIndices.getFromTime());
                    queryOptions.setEpochToDate(nonTransformedIndices.getToTime());

                    log.debug("OS query for fetching service transaction data: {}", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(false)
                                .build());
                    }

                }

                TransformedIndexDetails transformedIndices = transformedIndicesList.parallelStream()
                        .filter(TransformedIndexDetails::isTransformed).findAny().orElse(null);
                if (transformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    transformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                    queryOptions.setEpochFromDate(transformedIndices.getFromTime());
                    queryOptions.setEpochToDate(transformedIndices.getToTime());

                    log.debug("OS query for fetching service transaction data: {}", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(true)
                                .build());
                    }
                }
            } else {
                List<String> indexNames = new ArrayList<>();
                DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                        indexNames.add(indexPrefix + "_" + date));

                queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                queryOptions.setIndexNames(indexNames);
                queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));

                log.debug("OS query for fetching service transaction data: {}", queryOptions);
                TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                    tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                            .tabularResults(results)
                            .isTransformed(false)
                            .build());
                }
            }

            return tabularResultsBooleanList;

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
            return new ArrayList<>();
        }
    }

    public List<TabularResultsTypePojo> getAgentTransactionCollatedDataByServiceAndTxnGrpNames(String accountIdentifier, Set<String> agentUidList,
                                                                                               String serviceId, String kpiIdentifier,
                                                                                               ClusterOperationEnum operationType,
                                                                                               String transactionGrpName,
                                                                                               String responseType, long fromTime,
                                                                                               long toTime, TimeRangeDetails timeRangeDetails,
                                                                                               List<TimeRangeDetails> timeRangeDetailsList, List<Long> times) {

        List<TabularResultsTypePojo> tabularResultsBooleanList = new ArrayList<>();
        String indexPrefix = Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS_AGENT + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS_AGENT);
            if (elasticClient == null) {
                return new ArrayList<>();
            }

            List<NameValuePair> matchAllFields = new ArrayList<>();
            matchAllFields.add(new NameValuePair("serviceId", serviceId));
            if (transactionGrpName != null) {
                matchAllFields.add(new NameValuePair("groupNames", transactionGrpName));
            }

            List<NameValuePair> matchAnyFields = new ArrayList<>();
            agentUidList.forEach(c -> matchAnyFields.add(new NameValuePair("agentUid", c)));

            List<String> groupByColumns = new ArrayList<>();
            groupByColumns.add("serviceId");

            String responseTypeMethodName = responseType.equals(TransactionResponseTypes.DC.name()) ? "dcKpis." :
                    responseType.equals(TransactionResponseTypes.EUM.name()) ? "eumKpis." : "renderingKpis.";

            List<AggregationQuery> aggregationQueries = new ArrayList<>();
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.valueOfLabel(operationType.getOperation())).fieldName(responseTypeMethodName + kpiIdentifier).percentileValue(null).build());

            QueryOptions queryOptions = QueryOptions.builder()
                    .epochFieldName("timeInGMT")
                    .timeStampFieldName("timeInGMT")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .matchAllFields(Optional.of(matchAllFields))
                    .matchAnyOfFields(Optional.of(new ArrayList<List<NameValuePair>>() {{
                        add(matchAnyFields);
                    }}))
                    .build();

            if (timeRangeDetails.getNeedRolledUpIndexes() == 1) {

                List<TransformedIndexDetails> transformedIndicesList = DateHelper.getTransformedIndexAsString(times, timeRangeDetailsList, timeRangeDetails, 0);

                TransformedIndexDetails nonTransformedIndices = transformedIndicesList.parallelStream()
                        .filter(c -> !c.isTransformed()).findAny().orElse(null);
                if (nonTransformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    nonTransformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                    queryOptions.setEpochFromDate(nonTransformedIndices.getFromTime());
                    queryOptions.setEpochToDate(nonTransformedIndices.getToTime());

                    log.debug("OS query for fetching service transaction data: {}", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(false)
                                .build());
                    }
                }

                TransformedIndexDetails transformedIndices = transformedIndicesList.parallelStream()
                        .filter(TransformedIndexDetails::isTransformed).findAny().orElse(null);
                if (transformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    transformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                    queryOptions.setEpochFromDate(transformedIndices.getFromTime());
                    queryOptions.setEpochToDate(transformedIndices.getToTime());

                    log.debug("OS query for fetching service transaction data: {}", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(true)
                                .build());
                    }
                }
            } else {
                List<String> indexNames = new ArrayList<>();
                DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                        indexNames.add(indexPrefix + "_" + date));

                queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                queryOptions.setIndexNames(indexNames);
                queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                log.debug("OS query for fetching service transaction data: {}", queryOptions);
                TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                    tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                            .tabularResults(results)
                            .isTransformed(false)
                            .build());
                }
            }

            return tabularResultsBooleanList;

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
            return new ArrayList<>();
        }
    }

    public List<TabularResultsTypePojo> getTransactionCollatedDataByServiceTimeSeriesTrue(String accountIdentifier,
                                                                                          String serviceId, List<String> groupByColumns,
                                                                                          String transactionGrpName,
                                                                                          String transactionName,
                                                                                          String responseType, int timeSliceInMins,
                                                                                          long fromTime, long toTime, TimeRangeDetails timeRangeDetails,
                                                                                          List<TimeRangeDetails> timeRangeDetailsList,
                                                                                          List<Long> times, String timezoneId) {

        List<TabularResultsTypePojo> tabularResultsBooleanList = new ArrayList<>();
        String indexPrefix = Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS);
            if (elasticClient == null) {
                return new ArrayList<>();
            }

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("serviceId", serviceId));
            if (transactionName != null) {
                matchFields.add(new NameValuePair("txnId", transactionName));
            }
            if (transactionGrpName != null) {
                matchFields.add(new NameValuePair("groupNames", transactionGrpName));
            }

            String responseTypeMethodName = responseType.equals(TransactionResponseTypes.DC.name()) ? "dcKpis." :
                    responseType.equals(TransactionResponseTypes.EUM.name()) ? "eumKpis." : "renderingKpis.";

            List<AggregationQuery> aggregationQueries = new ArrayList<>();
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.AVERAGE).fieldName(responseTypeMethodName + KPIAttributes.RESPONSE_TIME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.SLOW_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.SUCCESS_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.FAIL_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.TIMEOUT_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.TOTAL_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.UNKNOWN_VOLUME).percentileValue(null).build());

            QueryOptions queryOptions = QueryOptions.builder()
                    .epochFieldName("timeInGMT")
                    .timeStampFieldName("timeInGMT")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(true)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .timeSliceInMins(timeSliceInMins)
                    .matchAllFields(Optional.of(matchFields))
                    .timeZone(timezoneId)
                    .build();

            if (timeRangeDetails.getNeedRolledUpIndexes() == 1) {

                List<TransformedIndexDetails> transformedIndicesList = DateHelper.getTransformedIndexAsString(times, timeRangeDetailsList, timeRangeDetails, 0);

                TransformedIndexDetails nonTransformedIndices = transformedIndicesList.parallelStream()
                        .filter(c -> !c.isTransformed()).findAny().orElse(null);
                if (nonTransformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    nonTransformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                    queryOptions.setEpochFromDate(nonTransformedIndices.getFromTime());
                    queryOptions.setEpochToDate(nonTransformedIndices.getToTime());

                    log.debug("OS query for fetching service transaction data: {}", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(false)
                                .build());
                    }
                }

                TransformedIndexDetails transformedIndices = transformedIndicesList.parallelStream()
                        .filter(TransformedIndexDetails::isTransformed).findAny().orElse(null);
                if (transformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    transformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                    queryOptions.setEpochFromDate(transformedIndices.getFromTime());
                    queryOptions.setEpochToDate(transformedIndices.getToTime());

                    log.debug("OS query for fetching service transaction data: {}", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(true)
                                .build());
                    }
                }
            } else {
                List<String> indexNames = new ArrayList<>();
                DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                        indexNames.add(indexPrefix + "_" + date));

                queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                queryOptions.setIndexNames(indexNames);
                queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));

                log.debug("OS query for fetching service transaction data: {}", queryOptions);
                TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                    tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                            .tabularResults(results)
                            .isTransformed(false)
                            .build());
                }
            }

            return tabularResultsBooleanList;

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
            return new ArrayList<>();
        }
    }

    public List<TabularResultsTypePojo> getAgentTransactionCollatedDataByServiceTimeSeriesTrue(String accountIdentifier, Set<String> agentUidList,
                                                                                               String serviceId, List<String> groupByColumns,
                                                                                               String transactionGrpName,
                                                                                               String transactionName, String responseType,
                                                                                               int timeSliceInMins, long fromTime, long toTime,
                                                                                               TimeRangeDetails timeRangeDetails,
                                                                                               List<TimeRangeDetails> timeRangeDetailsList,
                                                                                               List<Long> times, String timezoneId) {

        List<TabularResultsTypePojo> tabularResultsBooleanList = new ArrayList<>();
        String indexPrefix = Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS_AGENT + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS_AGENT);
            if (elasticClient == null) {
                return new ArrayList<>();
            }

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("serviceId", serviceId));
            if (transactionName != null) {
                matchFields.add(new NameValuePair("txnId", transactionName));
            }
            if (transactionGrpName != null) {
                matchFields.add(new NameValuePair("groupNames", transactionGrpName));
            }

            List<NameValuePair> matchAnyFields = new ArrayList<>();
            agentUidList.forEach(c -> matchAnyFields.add(new NameValuePair("agentUid", c)));

            String responseTypeMethodName = responseType.equals(TransactionResponseTypes.DC.name()) ? "dcKpis." :
                    responseType.equals(TransactionResponseTypes.EUM.name()) ? "eumKpis." : "renderingKpis.";

            List<AggregationQuery> aggregationQueries = new ArrayList<>();
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.AVERAGE).fieldName(responseTypeMethodName + KPIAttributes.RESPONSE_TIME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.SLOW_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.SUCCESS_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.FAIL_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.TIMEOUT_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.TOTAL_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.UNKNOWN_VOLUME).percentileValue(null).build());

            QueryOptions queryOptions = QueryOptions.builder()
                    .epochFieldName("timeInGMT")
                    .timeStampFieldName("timeInGMT")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(true)
                    .timeSliceInMins(timeSliceInMins)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .matchAllFields(Optional.of(matchFields))
                    .matchAnyOfFields(Optional.of(new ArrayList<List<NameValuePair>>() {{
                        add(matchAnyFields);
                    }}))
                    .timeZone(timezoneId)
                    .build();

            if (timeRangeDetails.getNeedRolledUpIndexes() == 1) {

                List<TransformedIndexDetails> transformedIndicesList = DateHelper.getTransformedIndexAsString(times, timeRangeDetailsList, timeRangeDetails, 0);

                TransformedIndexDetails nonTransformedIndices = transformedIndicesList.parallelStream()
                        .filter(c -> !c.isTransformed()).findAny().orElse(null);
                if (nonTransformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    nonTransformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                    queryOptions.setEpochFromDate(nonTransformedIndices.getFromTime());
                    queryOptions.setEpochToDate(nonTransformedIndices.getToTime());

                    log.debug("OS query for fetching service transaction data: {}", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(false)
                                .build());
                    }
                }

                TransformedIndexDetails transformedIndices = transformedIndicesList.parallelStream()
                        .filter(TransformedIndexDetails::isTransformed).findAny().orElse(null);
                if (transformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    transformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                    queryOptions.setEpochFromDate(transformedIndices.getFromTime());
                    queryOptions.setEpochToDate(transformedIndices.getToTime());

                    log.debug("OS query for fetching service transaction data: {}", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(true)
                                .build());
                    }
                }
            } else {
                List<String> indexNames = new ArrayList<>();
                DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                        indexNames.add(indexPrefix + "_" + date));

                queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                queryOptions.setIndexNames(indexNames);
                queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                log.debug("OS query for fetching service transaction data: {}", queryOptions);
                TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                    tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                            .tabularResults(results)
                            .isTransformed(false)
                            .build());
                }
            }

            return tabularResultsBooleanList;

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
            return new ArrayList<>();
        }
    }

    public List<TabularResultsTypePojo> getTransactionCollatedDataByServiceForSingleKPITimeSeriesTrue(String accountIdentifier,
                                                                                                      String serviceId,
                                                                                                      String transactionsId,
                                                                                                      String kpiIdentifier,
                                                                                                      ClusterOperationEnum operationType,
                                                                                                      String responseType, int timeSliceInMins,
                                                                                                      long fromTime, long toTime,
                                                                                                      TimeRangeDetails timeRangeDetails,
                                                                                                      List<TimeRangeDetails> timeRangeDetailsList,
                                                                                                      List<Long> times, String timezoneId) {

        List<TabularResultsTypePojo> tabularResultsBooleanList = new ArrayList<>();
        String indexPrefix = Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS);
            if (elasticClient == null) {
                return new ArrayList<>();
            }

            List<String> groupByColumns = new ArrayList<>();
            groupByColumns.add("serviceId");

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("serviceId", serviceId));
            matchFields.add(new NameValuePair("txnId", transactionsId));

            String responseTypeMethodName = responseType.equals(TransactionResponseTypes.DC.name()) ? "dcKpis." :
                    responseType.equals(TransactionResponseTypes.EUM.name()) ? "eumKpis." : "renderingKpis.";

            List<AggregationQuery> aggregationQueries = new ArrayList<>();
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.valueOfLabel(operationType.getOperation())).fieldName(responseTypeMethodName + kpiIdentifier).percentileValue(null).build());

            QueryOptions queryOptions = QueryOptions.builder()
                    .epochFieldName("timeInGMT")
                    .timeStampFieldName("timeInGMT")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(true)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .timeSliceInMins(timeSliceInMins)
                    .matchAllFields(Optional.of(matchFields))
                    .timeZone(timezoneId)
                    .build();

            if (timeRangeDetails.getNeedRolledUpIndexes() == 1) {

                List<TransformedIndexDetails> transformedIndicesList = DateHelper.getTransformedIndexAsString(times, timeRangeDetailsList, timeRangeDetails, 0);

                TransformedIndexDetails nonTransformedIndices = transformedIndicesList.parallelStream()
                        .filter(c -> !c.isTransformed()).findAny().orElse(null);
                if (nonTransformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    nonTransformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                    queryOptions.setEpochFromDate(nonTransformedIndices.getFromTime());
                    queryOptions.setEpochToDate(nonTransformedIndices.getToTime());

                    log.debug("OS query for fetching service transaction data: {}", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(false)
                                .build());
                    }
                }

                TransformedIndexDetails transformedIndices = transformedIndicesList.parallelStream()
                        .filter(TransformedIndexDetails::isTransformed).findAny().orElse(null);
                if (transformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    transformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                    queryOptions.setEpochFromDate(transformedIndices.getFromTime());
                    queryOptions.setEpochToDate(transformedIndices.getToTime());

                    log.debug("OS query for fetching service transaction data: {}", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(true)
                                .build());
                    }
                }
            } else {
                List<String> indexNames = new ArrayList<>();
                DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                        indexNames.add(indexPrefix + "_" + date));

                queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                queryOptions.setIndexNames(indexNames);
                queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                log.debug("OS query for fetching service transaction data: {}", queryOptions);
                TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                    tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                            .tabularResults(results)
                            .isTransformed(false)
                            .build());
                }
            }

            return tabularResultsBooleanList;

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
            return new ArrayList<>();
        }
    }

    public List<TabularResultsTypePojo> getAgentTransactionCollatedDataByServiceForSingleKPITimeSeriesTrue(String accountIdentifier, Set<String> agentUidList,
                                                                                                           String serviceId,
                                                                                                           String transactionId,
                                                                                                           String kpiIdentifier,
                                                                                                           ClusterOperationEnum operationType,
                                                                                                           String responseType, int timeSliceInMins,
                                                                                                           long fromTime, long toTime,
                                                                                                           TimeRangeDetails timeRangeDetails,
                                                                                                           List<TimeRangeDetails> timeRangeDetailsList,
                                                                                                           List<Long> times, String timezoneId) {

        List<TabularResultsTypePojo> tabularResultsBooleanList = new ArrayList<>();
        String indexPrefix = Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS_AGENT + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS_AGENT);
            if (elasticClient == null) {
                return new ArrayList<>();
            }

            List<String> groupByColumns = new ArrayList<>();
            groupByColumns.add("serviceId");

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("serviceId", serviceId));
            matchFields.add(new NameValuePair("txnId", transactionId));

            List<NameValuePair> matchAnyFields = new ArrayList<>();
            agentUidList.forEach(c -> matchAnyFields.add(new NameValuePair("agentUid", c)));

            String responseTypeMethodName = responseType.equals(TransactionResponseTypes.DC.name()) ? "dcKpis." :
                    responseType.equals(TransactionResponseTypes.EUM.name()) ? "eumKpis." : "renderingKpis.";

            List<AggregationQuery> aggregationQueries = new ArrayList<>();
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.valueOfLabel(operationType.getOperation())).fieldName(responseTypeMethodName + kpiIdentifier).percentileValue(null).build());

            QueryOptions queryOptions = QueryOptions.builder()
                    .epochFieldName("timeInGMT")
                    .timeStampFieldName("timeInGMT")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(true)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .timeSliceInMins(timeSliceInMins)
                    .matchAllFields(Optional.of(matchFields))
                    .matchAnyOfFields(Optional.of(new ArrayList<List<NameValuePair>>() {{
                        add(matchAnyFields);
                    }}))
                    .timeZone(timezoneId)
                    .build();

            if (timeRangeDetails.getNeedRolledUpIndexes() == 1) {

                List<TransformedIndexDetails> transformedIndicesList = DateHelper.getTransformedIndexAsString(times, timeRangeDetailsList, timeRangeDetails, 0);

                TransformedIndexDetails nonTransformedIndices = transformedIndicesList.parallelStream()
                        .filter(c -> !c.isTransformed()).findAny().orElse(null);
                if (nonTransformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    nonTransformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                    queryOptions.setEpochFromDate(nonTransformedIndices.getFromTime());
                    queryOptions.setEpochToDate(nonTransformedIndices.getToTime());

                    log.debug("OS query for fetching service transaction data: {}", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(false)
                                .build());
                    }
                }

                TransformedIndexDetails transformedIndices = transformedIndicesList.parallelStream()
                        .filter(TransformedIndexDetails::isTransformed).findAny().orElse(null);
                if (transformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    transformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                    queryOptions.setEpochFromDate(transformedIndices.getFromTime());
                    queryOptions.setEpochToDate(transformedIndices.getToTime());

                    log.debug("OS query for fetching service transaction data: {}", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(true)
                                .build());
                    }
                }
            } else {
                List<String> indexNames = new ArrayList<>();
                DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                        indexNames.add(indexPrefix + "_" + date));

                queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                queryOptions.setIndexNames(indexNames);
                queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                log.debug("OS query for fetching service transaction data: {}", queryOptions);
                TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                    tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                            .tabularResults(results)
                            .isTransformed(false)
                            .build());
                }
            }

            return tabularResultsBooleanList;

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
            return new ArrayList<>();
        }
    }

    public List<TabularResultsTypePojo> getTransactionCollatedDataByServiceForAllKPITimeSeriesTrue(String accountIdentifier,
                                                                                                   String serviceId,
                                                                                                   String transactionsId,
                                                                                                   String responseType, int timeSliceInMins,
                                                                                                   long fromTime, long toTime,
                                                                                                   TimeRangeDetails timeRangeDetails,
                                                                                                   List<TimeRangeDetails> timeRangeDetailsList,
                                                                                                   List<Long> times, String timezoneId) {

        List<TabularResultsTypePojo> tabularResultsBooleanList = new ArrayList<>();
        String indexPrefix = Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS);
            if (elasticClient == null) {
                return new ArrayList<>();
            }

            List<String> groupByColumns = new ArrayList<>();
            groupByColumns.add("serviceId");

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("serviceId", serviceId));
            matchFields.add(new NameValuePair("txnId", transactionsId));

            String responseTypeMethodName = responseType.equals(TransactionResponseTypes.DC.name()) ? "dcKpis." :
                    responseType.equals(TransactionResponseTypes.EUM.name()) ? "eumKpis." : "renderingKpis.";

            List<AggregationQuery> aggregationQueries = new ArrayList<>();
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.SLOW_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.SUCCESS_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.FAIL_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.TIMEOUT_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.TOTAL_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.UNKNOWN_VOLUME).percentileValue(null).build());

            QueryOptions queryOptions = QueryOptions.builder()
                    .epochFieldName("timeInGMT")
                    .timeStampFieldName("timeInGMT")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(true)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .timeSliceInMins(timeSliceInMins)
                    .matchAllFields(Optional.of(matchFields))
                    .timeZone(timezoneId)
                    .build();

            if (timeRangeDetails.getNeedRolledUpIndexes() == 1) {

                List<TransformedIndexDetails> transformedIndicesList = DateHelper.getTransformedIndexAsString(times, timeRangeDetailsList, timeRangeDetails, 0);

                TransformedIndexDetails nonTransformedIndices = transformedIndicesList.parallelStream()
                        .filter(c -> !c.isTransformed()).findAny().orElse(null);
                if (nonTransformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    nonTransformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                    queryOptions.setEpochFromDate(nonTransformedIndices.getFromTime());
                    queryOptions.setEpochToDate(nonTransformedIndices.getToTime());

                    log.debug("OS query for fetching service transaction data: {}", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(false)
                                .build());
                    }
                }

                TransformedIndexDetails transformedIndices = transformedIndicesList.parallelStream()
                        .filter(TransformedIndexDetails::isTransformed).findAny().orElse(null);
                if (transformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    transformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                    queryOptions.setEpochFromDate(transformedIndices.getFromTime());
                    queryOptions.setEpochToDate(transformedIndices.getToTime());

                    log.debug("OS query for fetching service transaction data: {}", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(true)
                                .build());
                    }
                }
            } else {
                List<String> indexNames = new ArrayList<>();
                DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                        indexNames.add(indexPrefix + "_" + date));

                queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                queryOptions.setIndexNames(indexNames);
                queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                log.debug("OS query for fetching service transaction data: {}", queryOptions);
                TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                    tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                            .tabularResults(results)
                            .isTransformed(false)
                            .build());
                }
            }

            return tabularResultsBooleanList;

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
            return new ArrayList<>();
        }
    }

    public List<TabularResultsTypePojo> getAgentTransactionCollatedDataByServiceForAllKPITimeSeriesTrue(String accountIdentifier, Set<String> agentUidList,
                                                                                                        String serviceId,
                                                                                                        String transactionId,
                                                                                                        String responseType, int timeSliceInMins,
                                                                                                        long fromTime, long toTime,
                                                                                                        TimeRangeDetails timeRangeDetails,
                                                                                                        List<TimeRangeDetails> timeRangeDetailsList,
                                                                                                        List<Long> times, String timezoneId) {

        List<TabularResultsTypePojo> tabularResultsBooleanList = new ArrayList<>();
        String indexPrefix = Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS_AGENT + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS_AGENT);
            if (elasticClient == null) {
                return new ArrayList<>();
            }

            List<String> groupByColumns = new ArrayList<>();
            groupByColumns.add("serviceId");

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("serviceId", serviceId));
            matchFields.add(new NameValuePair("txnId", transactionId));

            List<NameValuePair> matchAnyFields = new ArrayList<>();
            agentUidList.forEach(c -> matchAnyFields.add(new NameValuePair("agentUid", c)));

            String responseTypeMethodName = responseType.equals(TransactionResponseTypes.DC.name()) ? "dcKpis." :
                    responseType.equals(TransactionResponseTypes.EUM.name()) ? "eumKpis." : "renderingKpis.";

            List<AggregationQuery> aggregationQueries = new ArrayList<>();
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.SLOW_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.SUCCESS_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.FAIL_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.TIMEOUT_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.TOTAL_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.UNKNOWN_VOLUME).percentileValue(null).build());

            QueryOptions queryOptions = QueryOptions.builder()
                    .epochFieldName("timeInGMT")
                    .timeStampFieldName("timeInGMT")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(true)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .timeSliceInMins(timeSliceInMins)
                    .matchAllFields(Optional.of(matchFields))
                    .matchAnyOfFields(Optional.of(new ArrayList<List<NameValuePair>>() {{
                        add(matchAnyFields);
                    }}))
                    .timeZone(timezoneId)
                    .build();

            if (timeRangeDetails.getNeedRolledUpIndexes() == 1) {

                List<TransformedIndexDetails> transformedIndicesList = DateHelper.getTransformedIndexAsString(times, timeRangeDetailsList, timeRangeDetails, 0);

                TransformedIndexDetails nonTransformedIndices = transformedIndicesList.parallelStream()
                        .filter(c -> !c.isTransformed()).findAny().orElse(null);
                if (nonTransformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    nonTransformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                    queryOptions.setEpochFromDate(nonTransformedIndices.getFromTime());
                    queryOptions.setEpochToDate(nonTransformedIndices.getToTime());

                    log.debug("OS query for fetching service transaction data: {}", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(false)
                                .build());
                    }
                }

                TransformedIndexDetails transformedIndices = transformedIndicesList.parallelStream()
                        .filter(TransformedIndexDetails::isTransformed).findAny().orElse(null);
                if (transformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    transformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                    queryOptions.setEpochFromDate(transformedIndices.getFromTime());
                    queryOptions.setEpochToDate(transformedIndices.getToTime());

                    log.debug("OS query for fetching service transaction data: {}", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(true)
                                .build());
                    }
                }
            } else {
                List<String> indexNames = new ArrayList<>();
                DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                        indexNames.add(indexPrefix + "_" + date));

                queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                queryOptions.setIndexNames(indexNames);
                queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                log.debug("OS query for fetching service transaction data: {}", queryOptions);
                TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                    tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                            .tabularResults(results)
                            .isTransformed(false)
                            .build());
                }
            }

            return tabularResultsBooleanList;

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
            return new ArrayList<>();
        }
    }

    public List<CollatedTransactionData> getTransactionNonCollatedData(String accountIdentifier,
                                                                       String serviceId,
                                                                       String transactionsId,
                                                                       long fromTime, long toTime) {

        String indexPrefix = Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS);
            if (elasticClient == null) {
                return null;
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexPrefix + "_" + date));

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("serviceId", serviceId));
            matchFields.add(new NameValuePair("txnId", transactionsId));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("timeInGMT")
                    .timeStampFieldName("timeInGMT")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .matchAllFields(Optional.of(matchFields))
                    .isTimeSeriesData(false)
                    .fetchAllRecords(true)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .build();

            log.debug("OS query for fetching service transaction data: {}", queryOptions);

            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);

            if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty())
                return Collections.emptyList();

            return rawDocuments.getDocuments().parallelStream()
                    .map(doc -> {
                        try {
                            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(doc, CollatedTransactionData.class);
                        } catch (Exception e) {
                            log.error("Error occurred while mapping OpenSearch signals data to CollatedTransactionData for index {}. Details: accountId [{}]", indexPrefix, accountIdentifier, e);
                            HealUICache.INSTANCE.updateHealUIErrors(1);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
        }
        return Collections.emptyList();
    }

    public List<CollatedTransactionData> getAgentTransactionNonCollatedData(String accountIdentifier,
                                                                            String serviceId, Set<String> agentUidList,
                                                                            String transactionsId,
                                                                            long fromTime, long toTime) {

        String indexPrefix = Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS_AGENT + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS_AGENT);
            if (elasticClient == null) {
                return null;
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexPrefix + "_" + date));

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("serviceId", serviceId));
            matchFields.add(new NameValuePair("txnId", transactionsId));

            List<NameValuePair> matchAnyFields = new ArrayList<>();
            agentUidList.forEach(c -> matchAnyFields.add(new NameValuePair("agentUid", c)));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("timeInGMT")
                    .timeStampFieldName("timeInGMT")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .matchAllFields(Optional.of(matchFields))
                    .matchAnyOfFields(Optional.of(new ArrayList<List<NameValuePair>>() {{
                        add(matchAnyFields);
                    }}))
                    .isTimeSeriesData(false)
                    .fetchAllRecords(true)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .build();

            log.debug("OS query for fetching service agent transaction data: {}", queryOptions);

            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);

            if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty())
                return Collections.emptyList();

            return rawDocuments.getDocuments().parallelStream()
                    .map(doc -> {
                        try {
                            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(doc, CollatedTransactionData.class);
                        } catch (Exception e) {
                            log.error("Error occurred while mapping OpenSearch signals data to CollatedTransactionData for index {}. Details: accountId [{}]", indexPrefix, accountIdentifier, e);
                            HealUICache.INSTANCE.updateHealUIErrors(1);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
        }
        return Collections.emptyList();
    }

    public List<TabularResultsTypePojo> getTransactionCollatedDataByServiceTimeSeriesFalse(String accountIdentifier,
                                                                                           String serviceId,
                                                                                           String transactionGrpName,
                                                                                           String responseType, int tagId,
                                                                                           long numberOfAggregatedRecords,
                                                                                           long fromTime, long toTime,
                                                                                           TimeRangeDetails timeRangeDetails,
                                                                                           List<TimeRangeDetails> timeRangeDetailsList, List<Long> times) {

        List<TabularResultsTypePojo> tabularResultsBooleanList = new ArrayList<>();
        String indexPrefix = Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS);
            if (elasticClient == null) {
                return new ArrayList<>();
            }

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("serviceId", serviceId));
            if (tagId != 0)
                matchFields.add(new NameValuePair("groupNames", transactionGrpName));

            List<String> groupByColumns = new ArrayList<>();
            groupByColumns.add("txnId");

            String responseTypeMethodName = responseType.equals(TransactionResponseTypes.DC.name()) ? "dcKpis." :
                    responseType.equals(TransactionResponseTypes.EUM.name()) ? "eumKpis." : "renderingKpis.";

            List<AggregationQuery> aggregationQueries = new ArrayList<>();
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.AVERAGE).fieldName(responseTypeMethodName + KPIAttributes.RESPONSE_TIME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.SLOW_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.SUCCESS_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.FAIL_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.TIMEOUT_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.TOTAL_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.UNKNOWN_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.UNIQUE).fieldName("agentIds").percentileValue(null).build());

            QueryOptions queryOptions = QueryOptions.builder()
                    .epochFieldName("timeInGMT")
                    .timeStampFieldName("timeInGMT")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .matchAllFields(Optional.of(matchFields))
                    .numberOfAggregateRecords((int) numberOfAggregatedRecords)
                    .build();

            if (timeRangeDetails.getNeedRolledUpIndexes() == 1) {

                List<TransformedIndexDetails> transformedIndicesList = DateHelper.getTransformedIndexAsString(times, timeRangeDetailsList, timeRangeDetails, 0);

                TransformedIndexDetails nonTransformedIndices = transformedIndicesList.parallelStream()
                        .filter(c -> !c.isTransformed()).findAny().orElse(null);
                if (nonTransformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    nonTransformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                    queryOptions.setEpochFromDate(nonTransformedIndices.getFromTime());
                    queryOptions.setEpochToDate(nonTransformedIndices.getToTime());

                    log.debug("OS query for fetching service transaction data: {}", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(false)
                                .build());
                    }
                }

                TransformedIndexDetails transformedIndices = transformedIndicesList.parallelStream()
                        .filter(TransformedIndexDetails::isTransformed).findAny().orElse(null);
                if (transformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    transformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                    queryOptions.setEpochFromDate(transformedIndices.getFromTime());
                    queryOptions.setEpochToDate(transformedIndices.getToTime());

                    log.debug("OS query for fetching service transaction data: {}", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(true)
                                .build());
                    }
                }
            } else {
                List<String> indexNames = new ArrayList<>();
                DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                        indexNames.add(indexPrefix + "_" + date));

                queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                queryOptions.setIndexNames(indexNames);
                queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                log.debug("OS query for fetching service transaction data: {}", queryOptions);
                TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                    tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                            .tabularResults(results)
                            .isTransformed(false)
                            .build());
                }
            }

            return tabularResultsBooleanList;

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
            return new ArrayList<>();
        }
    }

    public List<TabularResultsTypePojo> getAgentTransactionCollatedDataByServiceTimeSeriesFalse(String accountIdentifier, Set<String> agentUidList,
                                                                                                String serviceId,
                                                                                                String groupName, String responseType, int tagId,
                                                                                                long numberOfAggregatedRecords,
                                                                                                long fromTime, long toTime,
                                                                                                TimeRangeDetails timeRangeDetails,
                                                                                                List<TimeRangeDetails> timeRangeDetailsList, List<Long> times) {

        List<TabularResultsTypePojo> tabularResultsBooleanList = new ArrayList<>();
        String indexPrefix = Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS_AGENT + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_COLLATED_TRANSACTIONS_AGENT);
            if (elasticClient == null) {
                return null;
            }

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("serviceId", serviceId));
            if (tagId != 0)
                matchFields.add(new NameValuePair("groupNames", groupName));

            List<String> groupByColumns = new ArrayList<>();
            groupByColumns.add("txnId");

            List<NameValuePair> matchAnyFields = new ArrayList<>();
            agentUidList.forEach(c -> matchAnyFields.add(new NameValuePair("agentUid", c)));

            String responseTypeMethodName = responseType.equals(TransactionResponseTypes.DC.name()) ? "dcKpis." :
                    responseType.equals(TransactionResponseTypes.EUM.name()) ? "eumKpis." : "renderingKpis.";

            List<AggregationQuery> aggregationQueries = new ArrayList<>();
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.AVERAGE).fieldName(responseTypeMethodName + KPIAttributes.RESPONSE_TIME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.SLOW_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.SUCCESS_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.FAIL_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.TIMEOUT_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.TOTAL_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.SUM).fieldName(responseTypeMethodName + KPIAttributes.UNKNOWN_VOLUME).percentileValue(null).build());
            aggregationQueries.add(AggregationQuery.builder().aggregation(Aggregations.UNIQUE).fieldName("agentUid").percentileValue(null).build());

            QueryOptions queryOptions = QueryOptions.builder()
                    .epochFieldName("timeInGMT")
                    .timeStampFieldName("timeInGMT")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .matchAllFields(Optional.of(matchFields))
                    .matchAnyOfFields(Optional.of(new ArrayList<List<NameValuePair>>() {{
                        add(matchAnyFields);
                    }}))
                    .numberOfAggregateRecords((int) numberOfAggregatedRecords)
                    .build();

            if (timeRangeDetails.getNeedRolledUpIndexes() == 1) {

                List<TransformedIndexDetails> transformedIndicesList = DateHelper.getTransformedIndexAsString(times, timeRangeDetailsList, timeRangeDetails, 0);

                TransformedIndexDetails nonTransformedIndices = transformedIndicesList.parallelStream()
                        .filter(c -> !c.isTransformed()).findAny().orElse(null);
                if (nonTransformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    nonTransformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                    queryOptions.setEpochFromDate(nonTransformedIndices.getFromTime());
                    queryOptions.setEpochToDate(nonTransformedIndices.getToTime());

                    log.debug("OS query for fetching service transaction data: {}", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(false)
                                .build());
                    }
                }

                TransformedIndexDetails transformedIndices = transformedIndicesList.parallelStream()
                        .filter(TransformedIndexDetails::isTransformed).findAny().orElse(null);
                if (transformedIndices != null) {
                    List<String> indexNames = new ArrayList<>();
                    transformedIndices.getIndexPrefix().forEach(date ->
                            indexNames.add(indexPrefix + "_" + date));

                    queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                    queryOptions.setIndexNames(indexNames);
                    queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                    queryOptions.setEpochFromDate(transformedIndices.getFromTime());
                    queryOptions.setEpochToDate(transformedIndices.getToTime());

                    log.debug("OS query for fetching service transaction data: {}", queryOptions);
                    TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                    if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                        tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                                .tabularResults(results)
                                .isTransformed(true)
                                .build());
                    }
                }
            } else {
                List<String> indexNames = new ArrayList<>();
                DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                        indexNames.add(indexPrefix + "_" + date));

                queryOptions.setGroupByColumns(Optional.of(new ArrayList<>(groupByColumns)));
                queryOptions.setIndexNames(indexNames);
                queryOptions.setAggregationQueries(Optional.of(new ArrayList<>(aggregationQueries)));
                log.debug("OS query for fetching service transaction data: {}", queryOptions);
                TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
                if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                    tabularResultsBooleanList.add(TabularResultsTypePojo.builder()
                            .tabularResults(results)
                            .isTransformed(false)
                            .build());
                }
            }

            return tabularResultsBooleanList;

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
            return new ArrayList<>();
        }
    }
}
