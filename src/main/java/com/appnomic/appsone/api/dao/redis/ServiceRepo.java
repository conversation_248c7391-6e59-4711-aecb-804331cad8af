package com.appnomic.appsone.api.dao.redis;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.logging.PrintErrorLogs;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.RedisUtilities;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.entities.BasicInstanceBean;
import com.heal.configuration.pojos.*;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 17-03-2022
 */
@Slf4j
public class ServiceRepo {

    public List<BasicEntity> getAllServices(String accountIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/services";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES";

        try {
            String serviceDetails = RedisUtilities.getKey(key, hashKey);
            if (serviceDetails == null) {
                log.debug("No services found for account: [{}]", accountIdentifier);
                return Collections.emptyList();
            }
            return CommonUtils.getObjectMapper().readValue(serviceDetails, new TypeReference<List<BasicEntity>>() {
            });

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting service list for account [{}]. Details: ", accountIdentifier, e);
            return Collections.emptyList();
        }
    }

    public Service getServiceDetailsWithServiceIdentifier(String accountIdentifier, String serviceIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier;
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier;

        try {
            String serviceDetails = RedisUtilities.getKey(key, hashKey);
            if (serviceDetails == null) {
                log.debug("Service details not found for account: [{}], service [{}]", accountIdentifier, serviceIdentifier);
                return null;
            }
            return CommonUtils.getObjectMapper().readValue(serviceDetails, new TypeReference<Service>() {
            });

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting service details for account [{}], service [{}]. Details: ", accountIdentifier, serviceIdentifier, e);
            return null;
        }
    }

    public BasicEntity getBasicServiceDetailsWithServiceId(String accountIdentifier, int serviceId) {
        return getAllServices(accountIdentifier).parallelStream()
                .filter(c -> c.getId() == serviceId)
                .findAny()
                .orElse(null);
    }

    public List<BasicEntity> getConnectionDetails(String accountIdentifier, String serviceIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/connections";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_CONNECTIONS";

        try {
            String connDetails = RedisUtilities.getKey(key, hashKey);
            if (connDetails == null) {
                log.debug("Connection details not found for account: [{}], service [{}]", accountIdentifier, serviceIdentifier);
                return Collections.emptyList();
            }
            return CommonUtils.getObjectMapper().readValue(connDetails, new TypeReference<List<BasicEntity>>() {
            });

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting connection details for account [{}], service [{}]. Details: ", accountIdentifier, serviceIdentifier, e);
            return Collections.emptyList();
        }
    }

    public List<BasicInstanceBean> getAllInstanceDetailsWithServiceIdentifier(String accountIdentifier, String serviceIdentifier, boolean includeCluster) {
        String key = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/instances";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_INSTANCES";

        try {
            String instDetails = RedisUtilities.getKey(key, hashKey);
            if (instDetails == null) {
                log.debug("Instance details not found for account: [{}], service [{}]", accountIdentifier, serviceIdentifier);
                return Collections.emptyList();
            }
            List<BasicInstanceBean> instances = CommonUtils.getObjectMapper().readValue(instDetails, new TypeReference<List<BasicInstanceBean>>() {
            });

            if (!includeCluster) {
                return instances;
            }
            InstanceRepo instanceRepo = new InstanceRepo();
            List<CompInstClusterDetails> accountInstances = instanceRepo.getInstancesByAccount(accountIdentifier);
            Map<String, CompInstClusterDetails> instancesMap = accountInstances.stream()
                    .collect(Collectors.toMap(CompInstClusterDetails::getIdentifier, Function.identity(), (o, n) -> {
                        PrintErrorLogs.print(String.format("Duplicate instances available for account %s, service %s.\nChoosing:\n%s\nRejecting:\n%s\nList:\n%s", accountIdentifier, serviceIdentifier, n, o, accountInstances));
                        log.warn("Duplicate instances available for account {}, service {}.\nChoosing:\n{}\nRejecting:\n{}", accountIdentifier, serviceIdentifier, n, o);
                        return n;
                    }));

            List<BasicInstanceBean> clusterInstances = instances.parallelStream()
                    .filter(c -> c.getClusterIdentifier() != null && !c.getClusterIdentifier().trim().isEmpty())
                    .map(BasicInstanceBean::getClusterIdentifier)
                    .distinct()
                    .map(instancesMap::get)
                    .filter(Objects::nonNull)
                    .map(c -> BasicInstanceBean.builder()
                            .id(c.getId())
                            .name(c.getName())
                            .identifier(c.getIdentifier())
                            .status(c.getStatus())
                            .createdTime(c.getCreatedTime())
                            .updatedTime(c.getUpdatedTime())
                            .lastModifiedBy(c.getLastModifiedBy())
                            .accountId(c.getAccountId())
                            .componentId(c.getComponentId())
                            .componentTypeId(c.getComponentTypeId())
                            .componentVersionId(c.getComponentVersionId())
                            .commonVersionId(c.getCommonVersionId())
                            .build())
                    .collect(Collectors.toList());

            instances.addAll(clusterInstances);

            return instances;
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting instance details for account [{}], service identifier [{}]. Details: ", accountIdentifier, serviceIdentifier, e);
            return Collections.emptyList();
        }
    }

    public List<BasicInstanceBean> getAllInstanceDetailsWithServiceId(String accountIdentifier, int serviceId) {
        try {
            List<BasicEntity> serviceDetails = getAllServices(accountIdentifier);
            return serviceDetails.parallelStream()
                    .filter(c -> c.getId() == serviceId)
                    .map(c -> getAllInstanceDetailsWithServiceIdentifier(accountIdentifier, c.getIdentifier(), true))
                    .flatMap(Collection::parallelStream)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting instance details for account [{}], service id[{}]. Details: ", accountIdentifier, serviceId, e);
            return Collections.emptyList();
        }
    }

    public List<BasicEntity> getApplicationsByServiceIdentifier(String accountIdentifier, String serviceIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/applications";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_APPLICATIONS";

        try {
            String serviceDetails = RedisUtilities.getKey(key, hashKey);
            if (serviceDetails == null) {
                log.debug("Application details not found for account: [{}], service [{}]", accountIdentifier, serviceIdentifier);
                return Collections.emptyList();
            }
            return CommonUtils.getObjectMapper().readValue(serviceDetails, new TypeReference<List<BasicEntity>>() {
            });

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting application details for account [{}], service [{}]. Details: ", accountIdentifier, serviceIdentifier, e);
            return Collections.emptyList();
        }
    }

    public List<BasicEntity> getApplicationsByServiceId(String accountIdentifier, int serviceId) {
        BasicEntity service = new ServiceRepo().getBasicServiceDetailsWithServiceId(accountIdentifier, serviceId);
        if (service == null) {
            return Collections.emptyList();
        }

        return HealUICache.INSTANCE.getServiceApplicationList(accountIdentifier, service.getIdentifier());
    }

    public List<MaintenanceDetails> getServiceMaintenanceDetails(String accountIdentifier, String serviceIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/maintenanceDetails";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_MAINTENANCE_DETAILS";

        try {
            String serviceDetails = RedisUtilities.getKey(key, hashKey);
            if (serviceDetails == null) {
                log.debug("Application details not found for account: [{}], service [{}]", accountIdentifier, serviceIdentifier);
                return Collections.emptyList();
            }
            return CommonUtils.getObjectMapper().readValue(serviceDetails, new TypeReference<List<MaintenanceDetails>>() {
            });

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting application details for account [{}], service [{}]. Details: ", accountIdentifier, serviceIdentifier, e);
            return Collections.emptyList();
        }
    }

    public List<BasicTransactionEntity> getTransactionsByServiceIdentifier(String accountIdentifier, String serviceIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/transactions";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_TRANSACTIONS";

        try {
            String transactionDetails = RedisUtilities.getKey(key, hashKey);
            if (transactionDetails == null) {
                log.debug("Transaction details not found for account: [{}], service [{}]", accountIdentifier, serviceIdentifier);
                return Collections.emptyList();
            }
            return CommonUtils.getObjectMapper().readValue(transactionDetails, new TypeReference<List<BasicTransactionEntity>>() {
            });

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting transaction details for account [{}], service [{}]. Details: ", accountIdentifier, serviceIdentifier, e);
            return Collections.emptyList();
        }
    }

    public List<BasicAgentEntity> getAgents(String accountIdentifier, String serviceIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/agents";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_AGENTS";

        try {
            String jsonData = RedisUtilities.getKey(key, hashKey);
            if (jsonData == null) {
                log.debug("Agent details not found for account: [{}], service [{}]", accountIdentifier, serviceIdentifier);
                return Collections.emptyList();
            }
            return CommonUtils.getObjectMapper().readValue(jsonData, new TypeReference<List<BasicAgentEntity>>() {
            });

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting agents details for account [{}], service [{}]. Details: ", accountIdentifier, serviceIdentifier, e);
            return Collections.emptyList();
        }
    }

    /*
     * Returns all agent ids (unique tokens) for the specified accountId and serviceId
     */
    public List<BasicAgentEntity> getAgents(int accountId, int serviceId) {
        Account account = new AccountRepo().getAccountById(accountId);
        if (account == null) {
            log.error("Account details not found for account id: [{}]", accountId);
            return Collections.emptyList();
        }
        String accountIdentifier = account.getIdentifier();

        BasicEntity service = this.getBasicServiceDetailsWithServiceId(accountIdentifier, serviceId);
        if (service == null) {
            log.error("Service details not found for account identifier: [{}], service id: [{}]", account.getIdentifier(), serviceId);
            return Collections.emptyList();
        }
        String serviceIdentifier = service.getIdentifier();

        return getAgents(accountIdentifier, serviceIdentifier);
    }

    public List<Rule> getServiceRules(String accountIdentifier, String serviceIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/rules";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_RULES";

        try {
            String rulesDetails = RedisUtilities.getKey(key, hashKey);
            if (rulesDetails == null) {
                log.debug("Rules details not found for account: [{}], service [{}]", accountIdentifier, serviceIdentifier);
                return Collections.emptyList();
            }
            return CommonUtils.getObjectMapper().readValue(rulesDetails, new TypeReference<List<Rule>>() {
            });

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting rules details for account [{}], service [{}]. Details: ", accountIdentifier, serviceIdentifier, e);
            return Collections.emptyList();
        }
    }

}
