package com.appnomic.appsone.api.dao.opensearch;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.manager.OpenSearchConnectionManager;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.opeasearchquery.helper.OpenSearchQueryHelper;
import com.appnomic.appsone.opeasearchquery.queryopts.QueryOptions;
import com.appnomic.appsone.opeasearchquery.results.NameValuePair;
import com.appnomic.appsone.opeasearchquery.results.RawDocumentResults;
import com.appnomic.appsone.opeasearchquery.results.TabularResults;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.opensearch.Anomalies;
import com.heal.configuration.util.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.client.RestHighLevelClient;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class AnomalySearchRepo {
    private final ObjectMapper objectMapper = CommonUtils.getObjectMapper();

    private static String indexExtension = ConfProperties.getString(Constants.OPENSEARCH_INDEX_EXTENSION, Constants.OPENSEARCH_INDEX_EXTENSION_DEFAULT);

    public Anomalies getAnomaliesById(String accountIdentifier, String anomalyId) {
        String indexName = Constants.INDEX_PREFIX_ANOMALIES + "_" + accountIdentifier.toLowerCase() + "_*";
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_ANOMALIES);
            if (elasticClient == null) {
                return null;
            }
            List<String> indexNames = new ArrayList<>();
            indexNames.add(indexName);

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("anomalyId", anomalyId));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .isTimeSeriesData(false)
                    .matchAllFields(Optional.of(matchFields))
                    .allIndex(true)
                    .build();

            log.debug("OS query for fetching Anomaly Details: {}", queryOptions);
            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);

            if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty())
                return null;

            return objectMapper.readValue(rawDocuments.getDocuments().get(0), Anomalies.class);
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}], anomalyId [{}]",
                    indexName, accountIdentifier, anomalyId, e);
        }
        return null;
    }

    public List<Anomalies> getAnomaliesBySignal(String accountIdentifier, String signalId) {
        String indexName = Constants.INDEX_PREFIX_ANOMALIES + "_" + accountIdentifier.toLowerCase() + "_*";
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_ANOMALIES);
            if (elasticClient == null) {
                return Collections.emptyList();
            }
            List<String> indexNames = new ArrayList<>();
            indexNames.add(indexName);

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("signalIds", signalId));

            String timeStampFieldName = "anomalyTime";

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .timeStampFieldName(timeStampFieldName)
                    .isTimeSeriesData(false)
                    .matchAllFields(Optional.of(matchFields))
                    .allIndex(true)
                    .fetchAllRecords(true)
                    .build();

            List<Anomalies> anomalyDetails = new ArrayList<>();
            log.debug("OS query for fetching Anomaly Details: {}", queryOptions);
            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);

            if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty()) {
                log.warn("No anomalies available for signal ID {}", signalId);
                return Collections.emptyList();
            }

            for (String hit : rawDocuments.getDocuments()) {
                anomalyDetails.add(objectMapper.readValue(hit, Anomalies.class));
            }

            log.info("{} anomalies available for signal ID {}", anomalyDetails.size(), signalId);

            return anomalyDetails;
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}], anomalyId [{}]",
                    indexName, accountIdentifier, signalId, e);
        }
        return Collections.emptyList();
    }

    public List<Anomalies> getAnomalyDetailsSearchByAnomalyTime(String accountIdentifier, String controllerId, long fromTime, long toTime, int requestPayloadObjectSize, String isInformatic) {

        String indexPrefix = Constants.INDEX_PREFIX_ANOMALIES + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_ANOMALIES);
            if (elasticClient == null) {
                return Collections.emptyList();
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexPrefix + "_" + date));

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("serviceId", controllerId));
            matchFields.add(new NameValuePair("metadata.isInformatic", isInformatic));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("anomalyTime")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .matchAllFields(Optional.of(matchFields))
                    .numberOfRawRecords(requestPayloadObjectSize)
                    .build();

            log.debug("OS query for fetching anomaly data: {}", queryOptions);

            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);

            if (rawDocuments != null && rawDocuments.getDocuments() != null && !rawDocuments.getDocuments().isEmpty()) {
                return rawDocuments.getDocuments().parallelStream()
                        .map(doc -> {
                            try {
                                return objectMapper.readValue(doc, new TypeReference<Anomalies>() {
                                });
                            } catch (Exception e) {
                                HealUICache.INSTANCE.updateHealUIErrors(1);
                                log.error("Error occurred while mapping OpenSearch signals data to Anomalies for index {}. Details: accountId [{}]", indexPrefix, accountIdentifier, e);
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from os index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
        }
        return Collections.emptyList();
    }

    //Send numberOfRecords as -ve in case you want to fetch all records
    public List<Anomalies> getAnomaliesByKpi(String accountIdentifier, String instanceId, int kpiId, String attribute, int numberOfRecords, long fromTime, long toTime) {
        String indexName = Constants.INDEX_PREFIX_ANOMALIES + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_ANOMALIES);
            if (elasticClient == null) {
                return null;
            }
            List<String> indexNames = new ArrayList<>();
            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexName + "_" + date));

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("instanceId", instanceId));
            matchFields.add(new NameValuePair("kpiId", Integer.toString(kpiId)));
            matchFields.add(new NameValuePair("kpiAttribute", attribute));

            QueryOptions queryOptions;
            if (numberOfRecords < 0) {
                queryOptions = QueryOptions.builder()
                        .indexNames(indexNames)
                        .epochFieldName("anomalyTime")
                        .epochFromDate(fromTime)
                        .epochToDate(toTime)
                        .isTimeSeriesData(false)
                        .fetchAllRecords(true)
                        .extensionEnabled(true)
                        .extensionName(indexExtension)
                        .matchAllFields(Optional.of(matchFields))
                        .build();
            } else {
                queryOptions = QueryOptions.builder()
                        .indexNames(indexNames)
                        .epochFieldName("anomalyTime")
                        .epochFromDate(fromTime)
                        .epochToDate(toTime)
                        .isTimeSeriesData(false)
                        .extensionEnabled(true)
                        .extensionName(indexExtension)
                        .numberOfRawRecords(numberOfRecords)
                        .matchAllFields(Optional.of(matchFields))
                        .build();
            }

            List<Anomalies> anomalyDetails = new ArrayList<>();
            log.debug("OS query for fetching Anomaly Details: {}", queryOptions);
            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);

            if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty())
                return Collections.emptyList();

            for (String hit : rawDocuments.getDocuments())
                anomalyDetails.add(objectMapper.readValue(hit, Anomalies.class));

            return anomalyDetails;
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}], kpiId [{}]",
                    indexName, accountIdentifier, kpiId, e);
        }
        return Collections.emptyList();
    }

    public List<Anomalies> getAllAnomaliesByKpi(String accountIdentifier, String instanceIdentifier, String transactionIdentifier, int kpiId, long fromTime, long toTime) {
        String indexName = Constants.INDEX_PREFIX_ANOMALIES + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_ANOMALIES);
            if (elasticClient == null) {
                return null;
            }
            List<String> indexNames = new ArrayList<>();
            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexName + "_" + date));

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("kpiId", Integer.toString(kpiId)));

            QueryOptions queryOptions;
            List<NameValuePair> matchAnyFields = new ArrayList<>();
            if (instanceIdentifier != null) {
                matchFields.add(new NameValuePair("instanceId", instanceIdentifier));
                queryOptions = QueryOptions.builder()
                        .indexNames(indexNames)
                        .epochFieldName("anomalyTime")
                        .epochFromDate(fromTime)
                        .epochToDate(toTime)
                        .isTimeSeriesData(false)
                        .fetchAllRecords(true)
                        .extensionEnabled(true)
                        .extensionName(indexExtension)
                        .matchAllFields(Optional.of(matchFields))
                        .build();

            } else {
                //TODO: We need to check the logic of & issue.
                matchAnyFields.add(new NameValuePair("transactionId", transactionIdentifier));
                matchAnyFields.add(new NameValuePair("transactionId", transactionIdentifier.replace("&", "&amp;")));
                queryOptions = QueryOptions.builder()
                        .indexNames(indexNames)
                        .epochFieldName("anomalyTime")
                        .epochFromDate(fromTime)
                        .epochToDate(toTime)
                        .isTimeSeriesData(false)
                        .fetchAllRecords(true)
                        .extensionEnabled(true)
                        .extensionName(indexExtension)
                        .matchAllFields(Optional.of(matchFields))
                        .matchAnyOfFields(Optional.of(new ArrayList<List<NameValuePair>>() {{
                            add(matchAnyFields);
                        }}))
                        .build();
            }


            List<Anomalies> anomalyDetails = new ArrayList<>();
            log.debug("OS query for fetching Anomaly Details: {}", queryOptions);
            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);

            if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty())
                return Collections.emptyList();

            for (String hit : rawDocuments.getDocuments())
                anomalyDetails.add(objectMapper.readValue(hit, Anomalies.class));

            return anomalyDetails;
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}], kpiId [{}]",
                    indexName, accountIdentifier, kpiId, e);
        }
        return Collections.emptyList();
    }

    public TabularResults getAnomaliesCountInCategories(String accountIdentifier, String instanceId, String categoryIdentifier,
                                                        boolean isAvailabilityKpi, boolean isWatcherKpi, long fromTime, long toTime) {
        String indexName = Constants.INDEX_PREFIX_ANOMALIES + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_ANOMALIES);
            if (elasticClient == null) {
                return null;
            }
            List<String> indexNames = new ArrayList<>();
            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexName + "_" + date));

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("instanceId", instanceId));
            if (isAvailabilityKpi) {
                matchFields.add(new NameValuePair("metadata.kpiType", categoryIdentifier));
            } else {
                matchFields.add(new NameValuePair("categoryId", categoryIdentifier));
            }

            List<NameValuePair> matchAnyFields = new ArrayList<>();
            if (isWatcherKpi) {
                matchAnyFields.add(new NameValuePair("metadata.kpiType", "ConfigWatch"));
                matchAnyFields.add(new NameValuePair("metadata.kpiType", "FileWatch"));
            }

            List<String> columns = new ArrayList<>();
            columns.add("kpiId");
            columns.add("kpiAttribute");

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("anomalyTime")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .matchAllFields(Optional.of(matchFields))
                    .groupByColumns(Optional.of(columns))
                    .build();

            if (!matchAnyFields.isEmpty()) {
                queryOptions.setMatchAnyOfFields(Optional.of(new ArrayList<List<NameValuePair>>() {{
                    add(matchAnyFields);
                }}));
            }

            log.debug("OS query for fetching Anomaly Details: " + queryOptions.toString());

            return OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}], categoryId [{}]",
                    indexName, accountIdentifier, categoryIdentifier, e);
            return null;
        }
    }

    public long getTxnAnomalyCountBySerInst(String accountIdentifier, String serviceId, long fromTime, long toTime) {
        String indexName = Constants.INDEX_PREFIX_ANOMALIES + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_ANOMALIES);
            if (elasticClient == null) {
                return 0;
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexName + "_" + date));

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("serviceId", serviceId));
            matchFields.add(new NameValuePair("instanceId", "null"));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("anomalyTime")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .matchAllFields(Optional.of(matchFields))
                    .fetchAllRecords(true)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .build();

            log.debug("OS query for fetching anomaly data:{} ", queryOptions);
            return OpenSearchQueryHelper.getCount(queryOptions, elasticClient);

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexName, accountIdentifier, e);
        }
        return 0;
    }

    public List<Anomalies> getAnomalyByInstance(String accountIdentifier, String instanceIdentifier, long fromTime, long toTime) {
        String indexName = Constants.INDEX_PREFIX_ANOMALIES + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_ANOMALIES);
            if (elasticClient == null) {
                return Collections.emptyList();
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexName + "_" + date));

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("instanceId", instanceIdentifier));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("anomalyTime")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .matchAllFields(Optional.of(matchFields))
                    .fetchAllRecords(true)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .build();

            log.debug("OS query for fetching anomaly data:{} ", queryOptions);

            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);

            if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty())
                return Collections.emptyList();

            return rawDocuments.getDocuments().parallelStream()
                    .map(doc -> {
                        try {
                            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(doc, Anomalies.class);
                        } catch (Exception e) {
                            log.error("Error occurred while mapping OpenSearch signals data to Anomalies for index {}. Details: accountId [{}]", indexName, accountIdentifier, e);
                            HealUICache.INSTANCE.updateHealUIErrors(1);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexName, accountIdentifier, e);
        }
        return Collections.emptyList();
    }

    public Map<String, Long> getAnomaliesGroupedByCategoryId(String accountIdentifier, String serviceId, String instanceIdentifier, long fromTime, long toTime) {
        String indexName = Constants.INDEX_PREFIX_ANOMALIES + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_ANOMALIES);
            if (elasticClient == null) {
                return Collections.emptyMap();
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexName + "_" + date));

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("instanceId", instanceIdentifier));
            matchFields.add(new NameValuePair("serviceId", serviceId));

            List<String> columns = new ArrayList<>();
            columns.add("categoryId");

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("anomalyTime")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .matchAllFields(Optional.of(matchFields))
                    .groupByColumns(Optional.of(columns))
                    .build();

            log.debug("OS query for fetching Anomaly Details: " + queryOptions);
            TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);

            if (results == null || results.getRowResults() == null || results.getRowResults().isEmpty()) {
                return Collections.emptyMap();
            }

            return results.getRowResults().stream()
                    .collect(Collectors.toMap(c -> c.getListOfRows().get(0).getColumnValue(), TabularResults.ResultRow::getCountValue));

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexName, accountIdentifier, e);
        }
        return Collections.emptyMap();
    }

    public Map<String, Long> getAnomalyCountByService(String accountIdentifier, String serviceId, long fromTime, long toTime) {
        long st = System.currentTimeMillis();
        String indexName = Constants.INDEX_PREFIX_ANOMALIES + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_ANOMALIES);
            if (elasticClient == null) {
                HealUICache.INSTANCE.updateHealUIErrors(1);
                return Collections.emptyMap();
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexName + "_" + date));

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("serviceId", serviceId));

            List<String> columns = new ArrayList<>();
            columns.add("categoryId");
            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("anomalyTime")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .matchAllFields(Optional.of(matchFields))
                    .groupByColumns(Optional.of(columns))
                    .build();

            log.debug("OS query for fetching anomaly data:{} ", queryOptions);

            TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
            if (results == null || results.getRowResults() == null || results.getRowResults().isEmpty()) {
                return Collections.emptyMap();
            }

            return results.getRowResults().stream()
                    .collect(Collectors.toMap(c -> c.getListOfRows().get(0).getColumnValue(), TabularResults.ResultRow::getCountValue));

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while fetching category wise anomaly for accountId:{}, serviceId:{}, from:{}, to:{}, index:{}"
                    , accountIdentifier, serviceId, fromTime, toTime, indexName, e);
            return Collections.emptyMap();
        } finally {
            log.debug("Time taken to execute getAnomalyCountByService() method is {} ms.", System.currentTimeMillis() - st);
        }
    }

    public List<Anomalies> getAnomaliesByServiceInstance(String accountIdentifier, String serviceId, String instanceIdentifier, long fromTime, long toTime) {
        String indexName = Constants.INDEX_PREFIX_ANOMALIES + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_ANOMALIES);
            if (elasticClient == null) {
                return Collections.emptyList();
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexName + "_" + date));

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("instanceId", instanceIdentifier));
            matchFields.add(new NameValuePair("serviceId", serviceId));


            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("anomalyTime")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .matchAllFields(Optional.of(matchFields))
                    .fetchAllRecords(true)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .build();

            log.debug("OS query for fetching Anomaly Details: " + queryOptions);
            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);

            if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty())
                return Collections.emptyList();

            return rawDocuments.getDocuments().parallelStream()
                    .map(doc -> {
                        try {
                            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(doc, Anomalies.class);
                        } catch (Exception e) {
                            log.error("Error occurred while mapping OpenSearch signals data to Anomalies for index {}. Details: accountId [{}]", indexName, accountIdentifier, e);
                            HealUICache.INSTANCE.updateHealUIErrors(1);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexName, accountIdentifier, e);
        }
        return Collections.emptyList();
    }

    public TabularResults getAnomaliesByInstanceCategory(String accountIdentifier, String instanceIdentifier, long fromTime, long toTime) {
        String indexName = Constants.INDEX_PREFIX_ANOMALIES + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_ANOMALIES);
            if (elasticClient == null) {
                return null;
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexName + "_" + date));

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("instanceId", instanceIdentifier));

            List<String> columns = new ArrayList<>();
            columns.add("categoryId");

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("anomalyTime")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .matchAllFields(Optional.of(matchFields))
                    .groupByColumns(Optional.of(columns))
                    .build();

            log.debug("OS query for fetching Anomaly Details: " + queryOptions);
            return OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexName, accountIdentifier, e);
            return null;
        }
    }

    public List<Anomalies> getAnomaliesByServiceCategory(String accountIdentifier, String serviceId, String category, long fromTime, long toTime) {
        String indexName = Constants.INDEX_PREFIX_ANOMALIES + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_ANOMALIES);
            if (elasticClient == null) {
                return Collections.emptyList();
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexName + "_" + date));

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("serviceId", serviceId));
            matchFields.add(new NameValuePair("categoryId", category));


            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("anomalyTime")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .matchAllFields(Optional.of(matchFields))
                    .fetchAllRecords(true)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .build();

            log.debug("OS query for fetching Anomaly Details: " + queryOptions);
            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);

            if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty())
                return Collections.emptyList();

            return rawDocuments.getDocuments().parallelStream()
                    .map(doc -> {
                        try {
                            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(doc, Anomalies.class);
                        } catch (Exception e) {
                            log.error("Error occurred while mapping OpenSearch signals data to Anomalies for index {}. Details: accountId [{}]", indexName, accountIdentifier, e);
                            HealUICache.INSTANCE.updateHealUIErrors(1);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexName, accountIdentifier, e);
        }
        return Collections.emptyList();
    }

    public TabularResults isAnomalousTransaction(String accountIdentifier, long fromTime, long toTime) {
        String indexName = Constants.INDEX_PREFIX_ANOMALIES + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_ANOMALIES);
            if (elasticClient == null) {
                return null;
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexName + "_" + date));

            List<String> groupByColumns = new ArrayList<>();
            groupByColumns.add("transactionId");

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("anomalyTime")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .groupByColumns(Optional.of(groupByColumns))
                    .numberOfAggregateRecords(10000)
                    .isTimeSeriesData(false)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .build();

            log.debug("OS query for fetching anomaly data:{} ", queryOptions);

            return OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexName, accountIdentifier, e);
        }
        return null;
    }

    public Map<String, Map<String, Long>> getAnomaliesCountInstanceCategory(String accountIdentifier, List<String> serviceIdentifierList, long fromTime, long toTime) {
        long start = System.currentTimeMillis();
        String indexName = Constants.INDEX_PREFIX_ANOMALIES + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_ANOMALIES);
            if (elasticClient == null) {
                return Collections.emptyMap();
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexName + "_" + date));

            List<NameValuePair> matchAnyFields = new ArrayList<>();
            for (String serviceIdentifier : serviceIdentifierList) {
                matchAnyFields.add(new NameValuePair("serviceId", serviceIdentifier));
            }

            List<String> columns = new ArrayList<>();
            columns.add("instanceId");
            columns.add("categoryId");

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("anomalyTime")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .matchAnyOfFields(Optional.of(new ArrayList<List<NameValuePair>>() {{
                        add(matchAnyFields);
                    }}))
                    .groupByColumns(Optional.of(columns))
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .build();

            log.debug("OS query for fetching Anomaly Details: " + queryOptions);
            TabularResults tabularResults = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);

            Map<String, Map<String, Long>> instCatAnomalyCountMap = new HashMap<>();

            if (tabularResults != null && tabularResults.getRowResults() != null && !tabularResults.getRowResults().isEmpty()) {

                for (TabularResults.ResultRow resultRow : tabularResults.getRowResults()) {
                    Map<String, Long> categoryAnomalyCountMap = new HashMap<>();

                    String instanceIdentifier = "";
                    String categoryIdentifier = "";

                    for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {
                        if (resultRowColumn.getColumnName().equalsIgnoreCase("instanceId")) {

                            instanceIdentifier = resultRowColumn.getColumnValue();

                        } else if (resultRowColumn.getColumnName().equalsIgnoreCase("categoryId")) {

                            categoryIdentifier = resultRowColumn.getColumnValue();

                        }
                    }

                    categoryAnomalyCountMap.put(categoryIdentifier, resultRow.getCountValue());

                    instCatAnomalyCountMap.getOrDefault(instanceIdentifier, new HashMap<>()).forEach((key, value) -> categoryAnomalyCountMap.merge(key, value, Long::sum));
                    instCatAnomalyCountMap.put(instanceIdentifier, categoryAnomalyCountMap);
                }
            }

            return instCatAnomalyCountMap;
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching instance wise category anomaly count for accountId:{}, from:{}, to:{}, index:{}",
                    accountIdentifier, fromTime, toTime, indexName, e);
            return Collections.emptyMap();
        } finally {
            log.debug("Time taken to execute getDataCheckInstanceCategory() method is {} ms.", (System.currentTimeMillis() - start));
        }
    }

    public Map<String, Long> getAnomalyCountGroupByServiceId(String accountIdentifier, Long fromTime, Long toTime) {
        Map<String, Long> anomalyCountByService = new HashMap<>();
        long st = System.currentTimeMillis();
        String indexName = Constants.INDEX_PREFIX_ANOMALIES + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_ANOMALIES);
            if (elasticClient == null) {
                HealUICache.INSTANCE.updateHealUIErrors(1);
                return null;
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexName + "_" + date));

            List<String> columns = new ArrayList<>();
            columns.add("serviceId");

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("anomalyTime")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .groupByColumns(Optional.of(columns))
                    .numberOfAggregateRecords(10000)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .build();

            log.debug("OS query for fetching getAnomalyCountGroupByServiceId data:{} ", queryOptions);

            TabularResults tabularResults = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
            if (tabularResults != null && tabularResults.getRowResults() != null && !tabularResults.getRowResults().isEmpty()) {
                for (TabularResults.ResultRow resultRow : tabularResults.getRowResults()) {
                    String serviceIdentifier = "";
                    for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {
                        if (resultRowColumn.getColumnName().equalsIgnoreCase("serviceId")) {
                            serviceIdentifier = resultRowColumn.getColumnValue();
                        }
                    }
                    anomalyCountByService.put(serviceIdentifier, resultRow.getCountValue());
                }
                log.trace("Service-wise anomalies count details: {}", anomalyCountByService);

                return anomalyCountByService;
            }
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while fetching getAnomalyCountGroupByServiceId for accountId:{}, index:{}", accountIdentifier, indexName, e);
        } finally {
            log.debug("Time taken to execute getAnomalyCountGroupByServiceId() method is {} ms.", System.currentTimeMillis() - st);
        }
        return Collections.emptyMap();
    }

    public TabularResults getAnomalyCountGroupBySignalId(String accountIdentifier) {
        long st = System.currentTimeMillis();
        String indexName = Constants.INDEX_PREFIX_ANOMALIES + "_" + accountIdentifier.toLowerCase() + "_20*";
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_ANOMALIES);
            if (elasticClient == null) {
                HealUICache.INSTANCE.updateHealUIErrors(1);
                return null;
            }

            List<String> indexNames = new ArrayList<>();
            indexNames.add(indexName);

            List<String> columns = new ArrayList<>();
            columns.add("signalIds");

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("anomalyTime")
                    .isTimeSeriesData(false)
                    .groupByColumns(Optional.of(columns))
                    .numberOfAggregateRecords(10000)
                    .allIndex(true)
                    .build();

            log.debug("OS query for fetching getAnomalyCountGroupBySignalId data:{} ", queryOptions);
            return OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while fetching getAnomalyCountGroupBySignalId for accountId:{}, index:{}", accountIdentifier, indexName, e);
            return null;
        } finally {
            log.debug("Time taken to execute getAnomalyCountGroupBySignalId() method is {} ms.", System.currentTimeMillis() - st);
        }
    }
}
