package com.appnomic.appsone.api.dao;

import com.appnomic.appsone.api.beans.AgentBean;
import org.skife.jdbi.v2.sqlobject.Bind;
import org.skife.jdbi.v2.sqlobject.SqlQuery;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface AgentDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select a.id, a.unique_token uniqueToken, a.physical_agent_id physicalAgentId, a.name, " +
            "a.agent_type_id agentTypeId, a.created_time createdTime, a.updated_time updatedTime, " +
            "a.user_details_id userDetailsId,status, a.host_address hostAddress, a.mode, a.description, " +
            "a.supervisor_id supervisorId, pa.identifier physicalAgentIdentifier, am.account_id accountId " +
            "from agent a, agent_account_mapping am, physical_agent pa where a.id=am.agent_id and " +
            "a.physical_agent_id=pa.id and am.account_id in(1, :accountId)")
    List<AgentBean> getAgentsByAccountId(@Bind("accountId") int accountId);

}
