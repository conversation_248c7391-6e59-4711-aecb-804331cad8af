package com.appnomic.appsone.api.dao;

import com.appnomic.appsone.api.beans.TimezoneDetail;
import com.appnomic.appsone.api.beans.UserAttributeBean;
import com.appnomic.appsone.api.pojo.TagMappingDetails;
import org.skife.jdbi.v2.sqlobject.Bind;
import org.skife.jdbi.v2.sqlobject.BindBean;
import org.skife.jdbi.v2.sqlobject.SqlQuery;
import org.skife.jdbi.v2.sqlobject.SqlUpdate;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface TimezoneDao {
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, time_zone_id timeZoneName, timeoffset timeOffset, created_time createdTime, updated_time updatedTime, user_details_id userDetailsId, account_id accountId, offset_name offsetName, status status, abbreviation abbreviation from mst_timezone where status=1")
    List<TimezoneDetail> getAllTimezones();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, time_zone_id timeZoneId, timeoffset timeOffset, created_time createdTime, updated_time updatedTime, user_details_id userDetailsId, account_id accountId, offset_name offsetName from mst_timezone where id = :id and status = 1")
    TimezoneDetail getTimezonesById(@Bind("id") String id);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id id, user_identifier userIdentifier, contact_number contactNumber, email_address emailAddress, username username, " +
            "user_details_id userDetailsId, created_time createdTime, updated_time updatedTime, status status, is_timezone_mychoice isTimezoneMychoice, " +
            "mst_access_profile_id mstAccessProfileId, mst_role_id mstRoleId, is_notifications_timezone_mychoice isNotificationsTimezoneMychoice from user_attributes where username=:username")
    UserAttributeBean getUserAttributes(@Bind("username") String username);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id id, user_identifier userIdentifier, contact_number contactNumber, email_address emailAddress, username username, " +
            "user_details_id userDetailsId, created_time createdTime, updated_time updatedTime, status status, is_timezone_mychoice isTimezoneMychoice, " +
            "mst_access_profile_id mstAccessProfileId, mst_role_id mstRoleId, is_notifications_timezone_mychoice isNotificationsTimezoneMychoice from user_attributes where user_identifier = :userIdentifier")
    UserAttributeBean getUserAttributesForUserIdentifier(@Bind("userIdentifier") String userIdentifier);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("UPDATE user_attributes SET is_timezone_mychoice=:isTimezoneMychoice, is_notifications_timezone_mychoice=:isNotificationsTimezoneMychoice, " +
            "updated_time= :updatedTime, user_details_id=:updatingUserIdentifier where username= :username")
    int updateUserTimezoneChoice(@Bind("isTimezoneMychoice") Integer isTimezoneMychoice, @Bind("isNotificationsTimezoneMychoice") Integer isNotificationsTimezoneMychoice,
                                 @Bind("username") String username, @Bind("updatingUserIdentifier") String updatingUserIdentifier, @Bind("updatedTime") String timeInGMT);

    @SqlQuery("select id from tag_mapping where object_ref_table = :objectRefTable and object_id=:objectId and tag_id= :tagId")
    int getUserTagMappingId(@Bind("objectRefTable") String objectRefTable, @Bind("objectId") Integer objectId, @Bind("tagId") int tagId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("insert into tag_mapping (tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) values (:tagId,:objectId,:objectRefTable,:tagKey,:tagValue,:createdTime,:updatedTime,:accountId,:userDetailsId)")
    int addUserTagMapping(@BindBean TagMappingDetails tagMappingDetails);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("UPDATE tag_mapping set tag_key=:tagKey, updated_time=:updatedTime, user_details_id=:userDetailsId where id=:id")
    int updateUserTagMapping(@Bind("tagKey") Integer tagKey, @Bind("updatedTime") String updatedTime, @Bind("id") Integer id, @Bind("userDetailsId") String userDetailsId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mtz.id, mtz.time_zone_id timeZoneName, mtz.timeoffset timeOffset, mtz.created_time createdTime, mtz.updated_time updatedTime, mtz.user_details_id userDetailsId, mtz.account_id accountId, mtz.offset_name offsetName, mtz.abbreviation abbreviation " +
            "from mst_timezone mtz, tag_mapping tm where mtz.id=tm.tag_key and tm.object_ref_table=:objRefTable and tm.object_id=:objectId and tm.tag_id=:tagId and mtz.status=1")
    TimezoneDetail getTimezoneByUser(@Bind("objRefTable") String objRefTable, @Bind("objectId") Integer objectId, @Bind("tagId") Integer tagId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from tag_mapping where id=:id")
    int deleteTagMapping(@Bind("id") Integer id);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("UPDATE user_attributes SET last_login_time = :lastLoginTime WHERE user_identifier = :userIdentifier")
    void updateUserLastLoginTime(@Bind("userIdentifier") String userIdentifier, @Bind("lastLoginTime") String lastLoginTime);
}
