package com.appnomic.appsone.api.dao.redis;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.RedisUtilities;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.*;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
public class ParentApplicationRepo {


    private static final String ACCOUNTS_KEY = "/accounts";
    private static final String PARENT_APPLICATIONS_KEY = "/parent_applications";
    private static final String ACCOUNTS_HASH = "ACCOUNTS";
    private static final String PARENT_APPLICATIONS_HASH = "_PARENTAPPLICATIONS";

    public List<ParentApplication> getAllParentApplications(String accountIdentifier) {

        try {
            String key = ACCOUNTS_KEY + "/" + accountIdentifier + PARENT_APPLICATIONS_KEY;
            String hashKey = ACCOUNTS_HASH + "_" + accountIdentifier + PARENT_APPLICATIONS_HASH;

            String parentApplicationDetails = RedisUtilities.getKey(key, hashKey);
            if (parentApplicationDetails == null) {
                log.debug(" Parent Application details not found for account: [{}]", accountIdentifier);
                return Collections.emptyList();
            }

            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(parentApplicationDetails, new TypeReference<List<ParentApplication>>() {
            });
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting Parent Applications for accountId:{}.", accountIdentifier, e);
            return Collections.emptyList();
        }
    }

    public ParentApplication getApplicationDetails(String accountIdentifier, String parentApplicationIdentifier) {

        try {
            String key = ACCOUNTS_KEY + "/" + accountIdentifier + PARENT_APPLICATIONS_KEY + "/" + parentApplicationIdentifier;
            String hashKey = ACCOUNTS_HASH + "_" + accountIdentifier + PARENT_APPLICATIONS_HASH + "_" + parentApplicationIdentifier;

            String parentApplicationDetails = RedisUtilities.getKey(key, hashKey);
            if (parentApplicationDetails == null) {
                log.debug(" Application details not found for Parent Application Identifier: [{}]", parentApplicationIdentifier);
                return null;
            }
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(parentApplicationDetails, new TypeReference<ParentApplication>() {
            });
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting Applications for Parent Application Identifier:{}.", parentApplicationIdentifier, e);
            return null;
        }
    }
}
