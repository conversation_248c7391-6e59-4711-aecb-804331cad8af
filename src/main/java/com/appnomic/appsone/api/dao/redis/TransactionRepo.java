package com.appnomic.appsone.api.dao.redis;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.RedisUtilities;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.BasicTransactionEntity;
import com.heal.configuration.pojos.Transaction;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> on 25-03-2022
 */
@Slf4j
public class TransactionRepo {

    public List<BasicTransactionEntity> getServiceTransactionDetails(String accountIdentifier, String serviceIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/transactions";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_TRANSACTIONS";
        try {
            String txnDetails = RedisUtilities.getKey(key, hashKey);
            if (txnDetails == null) {
                log.debug("No transaction details found for account: [{}], service: [{}]", accountIdentifier, serviceIdentifier);
                return Collections.emptyList();
            }
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(txnDetails, new TypeReference<List<BasicTransactionEntity>>() {
            });

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting transaction list for account [{}], service [{}]. Details: ", accountIdentifier, serviceIdentifier, e);
            return Collections.emptyList();
        }
    }

    public Transaction getTransactionDetails(String accIdentifiers, String txnIdString) {
        String key = "/accounts/" + accIdentifiers + "/transactions/" + txnIdString.hashCode();
        String hashKey = "ACCOUNTS_" + accIdentifiers + "_TRANSACTIONS_" + txnIdString.hashCode();
        try {
            String response = RedisUtilities.getKey(key, hashKey);
            if (response == null)
                return null;

            return CommonUtils.getObjectMapper().readValue(response, new TypeReference<Transaction>() {
            });
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Exception encountered while getting key '{}': {}", key, hashKey, e);
            return null;
        }
    }

    public Transaction getTransactionDetailsById(String accIdentifiers, int txnId) {
        String key = "/accounts/" + accIdentifiers + "/transactions/" + txnId;
        String hashKey = "ACCOUNTS_" + accIdentifiers + "_TRANSACTIONS_" + txnId;
        try {
            String response = RedisUtilities.getKey(key, hashKey);
            if (response == null)
                return null;
            return CommonUtils.getObjectMapper().readValue(response, new TypeReference<Transaction>() {
            });
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Exception encountered while getting key '{}': {}", key, hashKey, e);
            return null;
        }
    }
}
