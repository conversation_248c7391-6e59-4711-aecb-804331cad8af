package com.appnomic.appsone.api.dao;

import org.skife.jdbi.v2.sqlobject.Bind;
import org.skife.jdbi.v2.sqlobject.SqlQuery;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

public interface UserAttributesDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id from user_attributes where user_identifier = :userIdentifier and status = 1")
    int getUserId(@Bind("userIdentifier") String userId);

}