package com.appnomic.appsone.api.dao.opensearch;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.manager.OpenSearchConnectionManager;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.opeasearchquery.helper.OpenSearchQueryHelper;
import com.appnomic.appsone.opeasearchquery.queryopts.QueryOptions;
import com.appnomic.appsone.opeasearchquery.results.NameValuePair;
import com.appnomic.appsone.opeasearchquery.results.RawDocumentResults;
import com.appnomic.appsone.opeasearchquery.results.TabularResults;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.opensearch.InstanceMaintenanceDetails;
import com.heal.configuration.pojos.opensearch.ServiceMaintenanceDetails;
import com.heal.configuration.util.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.client.RestHighLevelClient;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 22-02-2022
 */
@Slf4j
public class MaintenanceWindowSearchRepo {
    private final ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();
    private static String indexExtension = ConfProperties.getString(Constants.OPENSEARCH_INDEX_EXTENSION, Constants.OPENSEARCH_INDEX_EXTENSION_DEFAULT);

    public Map<String, Boolean> isServiceUnderMaintenance(String accountIdentifier, long time, Set<String> serviceIdentifiers) {

        Map<String, Boolean> serviceMaintenanceMap = new HashMap<>();

        String indexPrefix = Constants.INDEX_PREFIX_SERVICE_MAINTENANCE_WINDOW + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_SERVICE_MAINTENANCE_WINDOW);
            if (elasticClient == null) {
                return serviceMaintenanceMap;
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getWeeksAsString(time, System.currentTimeMillis()).forEach(date ->
                    indexNames.add(indexPrefix + "_" + date));

            List<String> column = new ArrayList<>();
            column.add("serviceIdentifier");

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochStartTimeFieldName("startTime")
                    .epochEndTimeFieldName("endTime")
                    .epochFromDate(time)
                    .isTimeSeriesData(false)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .groupByColumns(Optional.of(column))
                    .numberOfRawRecords(1)
                    .build();

            log.debug("OS query for fetching service maintenance data: {}", queryOptions);

            TabularResults tabularResults = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, elasticClient);
            if (tabularResults != null && tabularResults.getRowResults() != null && !tabularResults.getRowResults().isEmpty()) {
                for (TabularResults.ResultRow resultRow : tabularResults.getRowResults()) {
                    for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {
                        if (resultRowColumn.getColumnName().equalsIgnoreCase("serviceIdentifier")) {
                            if (serviceIdentifiers.isEmpty() || serviceIdentifiers.contains(resultRowColumn.getColumnValue())) {
                                serviceMaintenanceMap.put(resultRowColumn.getColumnValue(), true);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
        }
        return serviceMaintenanceMap;
    }

    public List<InstanceMaintenanceDetails> getInstanceMaintenanceDetails(String accountIdentifier, Set<String> instanceIdentifiers, long fromTime, long toTime) {

        String indexPrefix = Constants.INDEX_PREFIX_INSTANCE_MAINTENANCE_WINDOW + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_INSTANCE_MAINTENANCE_WINDOW);
            if (elasticClient == null) {
                return Collections.emptyList();
            }

            List<String> indexNames = new ArrayList<>();
            if (toTime == 0) {
                DateHelper.getWeeksAsString(fromTime, System.currentTimeMillis()).forEach(date -> indexNames.add(indexPrefix + "_" + date));
            } else {
                indexNames.add(indexPrefix + "_*");
            }

            List<NameValuePair> matchAnyFields = new ArrayList<>();
            instanceIdentifiers.forEach(instanceIdentifier -> matchAnyFields.add(new NameValuePair("instanceIdentifier", instanceIdentifier)));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochStartTimeFieldName("startTime")
                    .epochEndTimeFieldName("endTime")
                    .epochFromDate(toTime)
                    .isTimeSeriesData(false)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .matchAnyOfFields(Optional.of(new ArrayList<List<NameValuePair>>() {{
                        add(matchAnyFields);
                    }}))
                    .build();

            if (toTime == 0) {
                queryOptions.setNumberOfRawRecords(1);
            } else {
                queryOptions.setEpochToDate(toTime);
                queryOptions.setFetchAllRecords(true);
                queryOptions.setAllIndex(true);
            }

            log.debug("OS query for fetching instance maintenance data: {}", queryOptions);

            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);
            if (rawDocuments != null && rawDocuments.getDocuments() != null && !rawDocuments.getDocuments().isEmpty()) {
                return rawDocuments.getDocuments().parallelStream()
                        .map(doc -> {
                            try {
                                return objectMapper.readValue(doc, new TypeReference<InstanceMaintenanceDetails>() {
                                });
                            } catch (Exception e) {
                                HealUICache.INSTANCE.updateHealUIErrors(1);
                                log.error("Error occurred while mapping OpenSearch maintenance data to InstanceMaintenanceDetails for index {}. Details: accountId [{}]", indexPrefix, accountIdentifier, e);
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from os index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
        }
        return Collections.emptyList();
    }

    public List<ServiceMaintenanceDetails> getServiceMaintenanceDetails(String accountIdentifier, String serviceId, long fromTime, long toTime) {

        String indexPrefix = Constants.INDEX_PREFIX_SERVICE_MAINTENANCE_WINDOW + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_SERVICE_MAINTENANCE_WINDOW);
            if (elasticClient == null) {
                return Collections.emptyList();
            }

            List<String> indexNames = new ArrayList<>();
            if (toTime == 0) {
                DateHelper.getWeeksAsString(fromTime, System.currentTimeMillis()).forEach(date -> indexNames.add(indexPrefix + "_" + date));
            } else {
                indexNames.add(indexPrefix + "_*");
            }

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("serviceIdentifier", serviceId));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochStartTimeFieldName("startTime")
                    .epochEndTimeFieldName("endTime")
                    .epochFromDate(fromTime)
                    .isTimeSeriesData(false)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .matchAllFields(Optional.of(matchFields))
                    .build();

            if (toTime == 0) {
                queryOptions.setNumberOfRawRecords(1);
            } else {
                queryOptions.setEpochToDate(toTime);
                queryOptions.setFetchAllRecords(true);
                queryOptions.setAllIndex(true);
            }

            log.debug("OS query for fetching instance maintenance data: {}", queryOptions);

            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);
            if (rawDocuments != null && rawDocuments.getDocuments() != null && !rawDocuments.getDocuments().isEmpty()) {
                return rawDocuments.getDocuments().parallelStream()
                        .map(doc -> {
                            try {
                                return objectMapper.readValue(doc, new TypeReference<ServiceMaintenanceDetails>() {
                                });
                            } catch (Exception e) {
                                HealUICache.INSTANCE.updateHealUIErrors(1);
                                log.error("Error occurred while mapping OpenSearch maintenance data to ServiceMaintenanceDetails for index {}. Details: accountId [{}]", indexPrefix, accountIdentifier, e);
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from os index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
        }
        return Collections.emptyList();
    }
}
