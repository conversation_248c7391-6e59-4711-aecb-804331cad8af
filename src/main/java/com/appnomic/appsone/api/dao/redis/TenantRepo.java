package com.appnomic.appsone.api.dao.redis;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.RedisUtilities;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.TenantOpenSearchDetails;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class TenantRepo {

    public List<TenantOpenSearchDetails> getTenantOpenSearchDetails(String tenantIdentifier) {

        String key = "/tenants/" + tenantIdentifier + "/opensearch";
        String hashKey = "TENANTS_" + tenantIdentifier + "_OPENSEARCH";

        try {
            String tenantOpenSearchDetails = RedisUtilities.getKey(key, hashKey);
            if (tenantOpenSearchDetails == null) {
                log.error("Tenant details unavailable for tenant identifier [{}]", tenantIdentifier);
                return Collections.emptyList();
            }
            return CommonUtils.getObjectMapper().readValue(tenantOpenSearchDetails, new TypeReference<List<TenantOpenSearchDetails>>() {
            });

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting to tenant opensearch mapping details for tenant identifier [{}]. ", tenantIdentifier, e);
            return Collections.emptyList();
        }
    }
}
