package com.appnomic.appsone.api.dao.redis;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.RedisUtilities;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.entities.UserAccessDetails;
import com.heal.configuration.pojos.*;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 15-03-2022
 */
@Slf4j
public class ApplicationRepo {

    public List<Application> getAllApplicationDetails(String accountIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/applications";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_APPLICATIONS";
        try {
            String appDetails = RedisUtilities.getKey(key, hashKey);
            if (appDetails == null) {
                log.debug("Application details not found for account: [{}]", accountIdentifier);
                return Collections.emptyList();
            }
            return CommonUtils.getObjectMapper().readValue(appDetails, new TypeReference<List<Application>>() {
            });

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting application details for account [{}]. Details: ", accountIdentifier, e);
            return Collections.emptyList();
        }
    }

    public Application getApplicationDetailWithAppIdentifier(String accIdentifiers, String applicationIdentifier) {
        String key = "/accounts/" + accIdentifiers + "/applications/" + applicationIdentifier;
        String hashKey = "ACCOUNTS_" + accIdentifiers + "_APPLICATIONS_" + applicationIdentifier;
        try {
            String appDetails = RedisUtilities.getKey(key, hashKey);
            if (appDetails == null)
                return null;
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(appDetails, new TypeReference<Application>() {
            });
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Exception encountered while getting key '{}': {}", key, hashKey, e);
            return null;
        }
    }

    public Application getApplicationDetailsWithAppId(String accountIdentifier, int appId) {
        return getAllApplicationDetails(accountIdentifier).parallelStream()
                .filter(c -> c.getId() == appId)
                .findAny()
                .orElse(null);
    }

    public Application getApplicationDetailsWithAppName(String accountIdentifier, String appName) {
        return getAllApplicationDetails(accountIdentifier).parallelStream()
                .filter(c -> c.getName().equals(appName))
                .findAny()
                .orElse(null);
    }

    public List<BasicEntity> getServicesByAppIdentifier(String accountIdentifier, String applicationIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/applications/" + applicationIdentifier + "/services";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_APPLICATIONS_" + applicationIdentifier + "_SERVICES";
        try {
            String serviceDetails = RedisUtilities.getKey(key, hashKey);
            if (serviceDetails == null) {
                log.debug("Service details not found for account: [{}], application [{}]", accountIdentifier, applicationIdentifier);
                return Collections.emptyList();
            }
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(serviceDetails, new TypeReference<List<BasicEntity>>() {
            });

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting service details for account [{}], application [{}]. Details: ", accountIdentifier, applicationIdentifier, e);
            return Collections.emptyList();
        }
    }

    public List<BasicEntity> getServicesByAppId(String accountIdentifier, int applicationId) {
        Application application = getApplicationDetailsWithAppId(accountIdentifier, applicationId);
        if (application == null) {
            return Collections.emptyList();
        }

        return getServicesByAppIdentifier(accountIdentifier, application.getIdentifier());
    }

    public List<User> getAppUsersByAppIdentifier(String accIdentifiers, String applicationIdentifier) {
        String key = "/accounts/" + accIdentifiers + "/applications/" + applicationIdentifier + "/users";
        String hashKey = "ACCOUNTS_" + accIdentifiers + "_APPLICATIONS_" + applicationIdentifier + "_USERS";
        try {
            String appDetails = RedisUtilities.getKey(key, hashKey);
            if (appDetails == null) {
                return Collections.emptyList();
            }

            return CommonUtils.getObjectMapper().readValue(appDetails, new TypeReference<List<User>>() {
            });
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Exception encountered while getting key '{}': {}", key, hashKey, e);
            return Collections.emptyList();
        }
    }

    public Set<BasicEntity> getAccessibleApplicationsByUserId(String userId, String accountIdentifier) {
        try {
            List<UserAccessDetails> userAccessDetailsList = new UserRepo().getUserAccessDetails(userId);
            if (userAccessDetailsList == null || userAccessDetailsList.isEmpty()) {
                HealUICache.INSTANCE.updateHealUIErrors(1);
                log.error("UserAccessDetails does not exists in redis cache, Couldn't get the user accessible applications. UserId:{}", userId);
                return Collections.emptySet();
            }

            boolean accountWiseAccessibility = userAccessDetailsList.parallelStream().anyMatch(UserAccessDetails::isAccountWiseAccessible);

            if (accountWiseAccessibility) {
                return getAllApplicationDetails(accountIdentifier).parallelStream().collect(Collectors.toSet());
            }

            return userAccessDetailsList.parallelStream()
                    .filter(u -> u.getAccount().getIdentifier().equalsIgnoreCase(accountIdentifier))
                    .map(UserAccessDetails::getApplications)
                    .flatMap(Collection::parallelStream)
                    .collect(Collectors.toSet());
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Exception encountered while getting user application details for user {}", userId, e);
            return null;
        }
    }

    public List<BasicEntity> getApplicationForensicsByUserId(String accountId, String userId) {
        try {

            UserRepo userRepo = new UserRepo();
            User user = userRepo.getUser(userId);
            if (user == null) {
                HealUICache.INSTANCE.updateHealUIErrors(1);
                log.error("User does not exists in redis cache, we cant get the user forensic applications. accountId:{}, userId:{}", accountId, userId);
                return Collections.emptyList();
            }
            return getAllApplicationDetails(accountId).parallelStream()
                    .filter(a -> user.getRoleId() == 1 || getAppUsersByAppIdentifier(accountId, a.getIdentifier())
                            .parallelStream()
                            .anyMatch(u -> u.getUserDetailsId().equalsIgnoreCase(userId)))
                    .filter(e -> getForensicNotificationPreference(accountId, e.getIdentifier(), userId) != null)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Exception encountered while getting user application details '{}': {}", accountId, userId, e);
            return Collections.emptyList();
        }
    }

    public ForensicNotificationPreferences getForensicNotificationPreference(String accountIdentifier, String applicationIdentifier, String userId) {
        String key = "/accounts/" + accountIdentifier + "/applications/" + applicationIdentifier + "/forensic/users";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_APPLICATIONS_" + applicationIdentifier + "_FORENSIC_USERS";
        try {
            String preferencesData = RedisUtilities.getKey(key, hashKey);
            if (preferencesData == null) {
                log.debug("Forensic notification preferences not found for accountId:{}, applicationId:{}", accountIdentifier, applicationIdentifier);
                return null;
            }
            List<ForensicNotificationPreferences> preferences = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(preferencesData, new TypeReference<List<ForensicNotificationPreferences>>() {
            });

            return preferences.stream().filter(p -> p.getUserDetailsId().equalsIgnoreCase(userId)).findAny().orElse(null);
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting notification preferences for accountId:{}, application:{}, userId:{}. Details: ", accountIdentifier, applicationIdentifier, userId, e);
            return null;
        }
    }


    public List<SignalNotificationPreferences> getNotificationInRedis(String accountIdentifier, String applicationIdentifier) {
        String ACCOUNTS_KEY = "/accounts";
        String ACCOUNTS_HASH = "ACCOUNTS";
        String APPLICATION_KEY = "/applications";
        String APPLICATION_HASH = "_APPLICATIONS";
        String USERS_KEY = "/users";
        String USERS_HASH = "_USERS";

        try {
            String NotificationDetails = RedisUtilities.getKey(ACCOUNTS_KEY + "/" + accountIdentifier + APPLICATION_KEY + "/" + applicationIdentifier + USERS_KEY,
                    ACCOUNTS_HASH + "_" + accountIdentifier + APPLICATION_HASH + "_" + applicationIdentifier + USERS_HASH);
            if (NotificationDetails == null) {
                log.error(" Could not find services which are mapped to this account: [{}], application: [{}]", accountIdentifier, applicationIdentifier);
                return new ArrayList<>();

            }
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(NotificationDetails, new TypeReference<List<SignalNotificationPreferences>>() {
            });
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting service notification details for accountId:{}, applicationIdentifier:{}.", accountIdentifier, applicationIdentifier, e);
            return new ArrayList<>();
        }
    }

    public void updateNotificationInRedis(String accountIdentifier, String applicationIdentifier, List<SignalNotificationPreferences> existingNotificationDetails) {
        String ACCOUNTS_KEY = "/accounts";
        String ACCOUNTS_HASH = "ACCOUNTS";
        String APPLICATION_KEY = "/applications";
        String APPLICATION_HASH = "_APPLICATIONS";
        String USERS_KEY = "/users";
        String USERS_HASH = "_USERS";
        try {
            RedisUtilities.updateKey(ACCOUNTS_KEY + "/" + accountIdentifier + APPLICATION_KEY + "/" + applicationIdentifier + USERS_KEY,
                    ACCOUNTS_HASH + "_" + accountIdentifier + APPLICATION_HASH + "_" + applicationIdentifier + USERS_HASH, existingNotificationDetails);
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while updating notification details for account: [{}] application: [{}]", accountIdentifier, applicationIdentifier, e);
        }
    }

    public List<ForensicNotificationPreferences> getForensicNotificationInRedis(String accountIdentifier, String applicationIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/applications/" + applicationIdentifier + "/forensic/users";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_APPLICATIONS_" + applicationIdentifier + "_FORENSIC_USERS";
        try {
            String preferencesData = RedisUtilities.getKey(key, hashKey);
            if (preferencesData == null) {
                log.debug("Forensic notification preferences not found for accountId:{}, applicationId:{}", accountIdentifier, applicationIdentifier);
                return new ArrayList<>();
            }
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(preferencesData, new TypeReference<List<ForensicNotificationPreferences>>() {
            });
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting service notification details for accountIdentifier:{}, applicationIdentifier:{}.", accountIdentifier, applicationIdentifier, e);
            return new ArrayList<>();

        }

    }

    public void updateForensicNotificationInRedis(String accountIdentifier, String applicationIdentifier, List<ForensicNotificationPreferences> forensicNotificationPreferencesList) {
        String key = "/accounts/" + accountIdentifier + "/applications/" + applicationIdentifier + "/forensic/users";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_APPLICATIONS_" + applicationIdentifier + "_FORENSIC_USERS";
        try {
            RedisUtilities.updateKey(key, hashKey, forensicNotificationPreferencesList);
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while updating notification details for account: [{}] application: [{}]", accountIdentifier, applicationIdentifier, e);
        }
    }

}
