package com.appnomic.appsone.api.dao.redis;

import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.RedisUtilities;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.BasicKpiEntity;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> on 16-03-2022
 */
@Slf4j
public class ComponentRepo {

    public List<BasicKpiEntity> getComponentKpis(String accountIdentifier, String componentIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/components/" + componentIdentifier + "/kpis";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_COMPONENTS_" + componentIdentifier + "_KPIS";
        try {
            String componentDetails = RedisUtilities.getKey(key, hashKey);
            if (componentDetails == null) {
                log.debug("Kpis details not found for account: [{}], component [{}]", accountIdentifier, componentIdentifier);
                return Collections.emptyList();
            }
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(componentDetails, new TypeReference<List<BasicKpiEntity>>() {
            });

        } catch (Exception e) {
            log.error("Error occurred while Kpis details for account [{}], component [{}]. Details: ", accountIdentifier, componentIdentifier, e);
            return Collections.emptyList();
        }
    }

    public List<BasicEntity> getComponents(String accountIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/components";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_COMPONENTS";
        try {
            String componentDetails = RedisUtilities.getKey(key, hashKey);
            if (componentDetails == null) {
                log.debug("Component details not found for account:{}, key:{}, hashKey:{}", accountIdentifier, key, hashKey);
                return Collections.emptyList();
            }
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(componentDetails, new TypeReference<List<BasicEntity>>() {
            });

        } catch (Exception e) {
            log.error("Error occurred while component details for account:{}. Details: ", accountIdentifier, e);
            return Collections.emptyList();
        }
    }

    public List<BasicEntity> getComponentTypes(String accountIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/components/type";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_COMPONENTS_TYPE";
        try {
            String componentDetails = RedisUtilities.getKey(key, hashKey);
            if (componentDetails == null) {
                log.debug("Component type details not found for account:{}, key:{}, hashKey:{}", accountIdentifier, key, hashKey);
                return Collections.emptyList();
            }
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(componentDetails, new TypeReference<List<BasicEntity>>() {
            });

        } catch (Exception e) {
            log.error("Error occurred while component type details for account:{}. Details: ", accountIdentifier, e);
            return Collections.emptyList();
        }
    }

}
