/* sagalap created on 19/04/22 inside the package - com.appnomic.appsone.api.dao.redis */
package com.appnomic.appsone.api.dao.redis;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.RedisUtilities;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.entities.UserAccessDetails;
import com.heal.configuration.pojos.User;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class UserRepo {

    public User getUser(String userIdentifier) {
        String key = "/users/" + userIdentifier;
        String hashKey = "USERS_" + userIdentifier;
        try {
            String userDetails = RedisUtilities.getKey(key, hashKey);
            if (userDetails == null || userDetails.trim().isEmpty())
                return null;
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(userDetails, new TypeReference<User>() {
            });

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Exception encountered while getting user details from key:{}, hashKey:{}", key, hashKey, e);
            return null;
        }
    }

    public List<UserAccessDetails> getUserAccessDetails(String userIdentifier) {
        String key = "/users/" + userIdentifier + "/accessDetails";
        String hashKey = "USERS_" + userIdentifier + "_ACCESSDETAILS";
        try {
            String userAccessDetails = RedisUtilities.getKey(key, hashKey);
            if (userAccessDetails == null || userAccessDetails.trim().isEmpty())
                return null;
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(userAccessDetails, new TypeReference<List<UserAccessDetails>>() {
            });

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Exception encountered while getting user details from key:{}, hashKey:{}", key, hashKey, e);
            return null;
        }
    }


    public void updateUserInRedis(String userIdentifier, User userDetail) {
        String USERS_KEY = "/users";
        String USERS_HASH = "USERS_";
        try {
            RedisUtilities.updateKey(USERS_KEY + "/" + userIdentifier, USERS_HASH + userIdentifier, userDetail);
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting userId:{}.", userIdentifier, e);
        }
    }
}
