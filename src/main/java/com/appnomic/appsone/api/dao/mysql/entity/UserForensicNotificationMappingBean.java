package com.appnomic.appsone.api.dao.mysql.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserForensicNotificationMappingBean {

    private String applicableUserId;
    private int applicationId;
    private int forensicNotificationSuppression;
    private int accountId;
    private String userDetailsId;
    private int status;
    private Date createdTime;
    private Date updatedTime;
}
