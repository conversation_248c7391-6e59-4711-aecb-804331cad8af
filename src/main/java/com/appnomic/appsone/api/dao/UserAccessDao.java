package com.appnomic.appsone.api.dao;

import com.appnomic.appsone.api.beans.RoutesInformation;
import com.appnomic.appsone.api.beans.UserAccessBean;
import com.appnomic.appsone.api.beans.UserAttributesBean;
import com.appnomic.appsone.api.dao.mysql.entity.UserDetailsBean;
import org.skife.jdbi.v2.sqlobject.Bind;
import org.skife.jdbi.v2.sqlobject.SqlQuery;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface UserAccessDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select access_details accessDetailsJson, user_identifier userId from user_access_details where user_identifier=:user_identifier")
    UserAccessBean getUserAccessDetails(@Bind("user_identifier") String userIdentifier);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mr.name roleName, mr.id roleId, map.id accessProfileId, map.name accessProfileName " +
            "from mst_roles mr join mst_access_profiles map join user_attributes ua on mr.id=ua.mst_role_id and mr.id=map.mst_role_id " +
            "and map.id=mst_access_profile_id where ua.user_identifier=:user_identifier")
    UserAttributesBean getRoleProfileInfoForUserId(@Bind("user_identifier") String userId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select vrd.action_id actionId, vrd.action_identifier actionIdentifier, vrd.route_id routeId, vrd.request_method requestMethod, vrd.base_url baseUrl, " +
            "vrd.path_info pathInfo, vrd.path_info_regex pathInfoInRegex, vrd.big_feature_identifier bigFeatureIdentifier from view_route_details vrd " +
            "join mst_access_profile_mapping mapm on vrd.big_feature_id=mapm.mst_big_feature_id " +
            "where mapm.mst_access_profile_id=:profile_id and vrd.dashboard_name='HealUI'")
    List<RoutesInformation> getAccessibleRoutesForUser(@Bind("profile_id") int profileId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct vrd.action_identifier from mst_access_profile_mapping mapm join view_route_details vrd " +
            "on mst_big_feature_id=vrd.big_feature_id where mst_access_profile_id=:profile_id and dashboard_name='HealUI'")
    List<String> getUserAccessibleActions(@Bind("profile_id") int profileId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select user_identifier from user_attributes where username=:username")
    String getUserIdentifierFromName(@Bind("username") String username);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT u.id id, u.user_identifier userIdentifier, u.username userName, u.user_details_id createdBy, a.name userProfile, " +
            "r.name role, u.status, u.created_time createdOn FROM user_attributes u, mst_roles r, mst_access_profiles a " +
            "where u.mst_access_profile_id = a.id and u.mst_role_id = r.id and a.mst_role_id = r.id")
    List<UserDetailsBean> getUsers();
}

