package com.appnomic.appsone.api.dao.opensearch;

import com.appnomic.appsone.api.beans.WatcherKpiRawBean;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.manager.OpenSearchConnectionManager;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.opeasearchquery.helper.OpenSearchQueryHelper;
import com.appnomic.appsone.opeasearchquery.queryopts.QueryOptions;
import com.appnomic.appsone.opeasearchquery.results.NameValuePair;
import com.appnomic.appsone.opeasearchquery.results.RawDocumentResults;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.opensearch.CollatedKpi;
import com.heal.configuration.pojos.opensearch.KpiData;
import com.heal.configuration.util.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.client.RestHighLevelClient;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Suman - 30-03-2022
 */

@Slf4j
public class ConfigWatcherRepo {

    private final ObjectMapper objectMapper = CommonUtils.getObjectMapper();
    private static String indexExtension = ConfProperties.getString(Constants.OPENSEARCH_INDEX_EXTENSION, Constants.OPENSEARCH_INDEX_EXTENSION_DEFAULT);

    public List<CollatedKpi> getConfigWatcherDetails(String accountIdentifier, String instanceId, int kpiId, String fileName, long fromTime, long toTime) {
        String indexName = Constants.INDEX_PREFIX_COLLATED_KPI + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_COLLATED_KPI);
            if (elasticClient == null) {
                return null;
            }

            List<String> dateList = DateHelper.getWeeksAsString(fromTime, toTime);
            List<String> indexNames = new ArrayList<>();
            dateList.forEach(date -> indexNames.add(indexName + "_" + date));

            List<NameValuePair> matchAnyFields = new ArrayList<>();
            matchAnyFields.add(new NameValuePair("groupAttribute", fileName));
            matchAnyFields.add(new NameValuePair("kpiId", String.valueOf(kpiId)));
            matchAnyFields.add(new NameValuePair("compInstanceIdentifier", instanceId));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("timeInGMT")
                    .epochToDate(toTime)
                    .epochFromDate(fromTime)
                    .isTimeSeriesData(false)
                    .matchAllFields(Optional.of(matchAnyFields))
                    .fetchAllRecords(true)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .build();

            log.debug("OS query for fetching signal data: {}", queryOptions);

            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);
            if (rawDocuments != null && rawDocuments.getDocuments() != null && !rawDocuments.getDocuments().isEmpty()) {
                return rawDocuments.getDocuments().parallelStream().map(doc -> {
                            try {
                                return objectMapper.readValue(doc, new TypeReference<CollatedKpi>() {
                                });
                            } catch (Exception e) {
                                HealUICache.INSTANCE.updateHealUIErrors(1);
                                log.error("Error occurred while mapping OpenSearch config watch data to ConfigWatch for index {}. Details: instanceId [{}]", indexName, instanceId, e);
                                return null;
                            }
                        }).filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }
        } catch (Exception ex) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in getting doc from index {} : ", indexName, ex);
        }
        return Collections.emptyList();
    }

    public WatcherKpiRawBean getWatcherKpisRaw(String accountIdentifier, String instanceId, int kpiId,
                                               String kpiAttributeName, long fromTime, long toTime) {
        String indexName = Constants.INDEX_PREFIX_RAW_KPI + "_" + accountIdentifier.toLowerCase();
        try{
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_RAW_KPI);
            if (elasticClient == null) {
                return null;
            }
            List<String> dateList = DateHelper.getDailyDatesAsString(fromTime, toTime);
            List<String> indexNames = new ArrayList<>();
            dateList.forEach(date -> indexNames.add(indexName + "_" + date));

            List<NameValuePair> matchAllFields = new ArrayList<>();
            matchAllFields.add(new NameValuePair("compInstanceIdentifier", instanceId));
            matchAllFields.add(new NameValuePair("kpiId", String.valueOf(kpiId)));
            matchAllFields.add(new NameValuePair("groupAttribute", kpiAttributeName));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("timeInGMT")
                    .epochToDate(toTime)
                    .epochFromDate(fromTime)
                    .isTimeSeriesData(false)
                    .matchAllFields(Optional.of(matchAllFields))
                    .fetchAllRecords(true)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .build();

            log.debug("OS query for fetching signal data: {}", queryOptions);

            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);
            if (rawDocuments != null && rawDocuments.getDocuments() != null && !rawDocuments.getDocuments().isEmpty()) {
                String s = rawDocuments.getDocuments().get(0);
                KpiData kpiData = objectMapper.readValue(s, new TypeReference<KpiData>() {});
                return WatcherKpiRawBean.builder()
                        .accountId(accountIdentifier)
                        .instanceId(kpiData.getCompInstanceIdentifier())
                        .kpiId(kpiId)
                        .kpiIdentifier(kpiData.getKpiIdentifier())
                        .kpiAttributeName(kpiData.getGroupAttribute())
                        .time(kpiData.getTimeInGMT())
                        .groupId(kpiData.getGroupIdentifier())
                        .kpiType(kpiData.getKpiType())
                        .kpiValue(kpiData.getWatcherValue())
                        .lastUpdatedTime(kpiData.getTimeInGMT())
                        .build();
            }
        } catch (Exception ex) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in getting doc from index prefix {} instance {}: ", indexName, instanceId, ex);
        }
        return null;
    }
}
