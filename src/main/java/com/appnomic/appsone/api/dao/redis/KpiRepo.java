package com.appnomic.appsone.api.dao.redis;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.RedisUtilities;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.BasicKpiEntity;
import com.heal.configuration.pojos.CompInstKpiEntity;
import com.heal.configuration.pojos.KpiDetails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> s kumar on 17-03-2022
 */
public class KpiRepo {
    private static final Logger LOGGER = LoggerFactory.getLogger(KpiRepo.class);

    public CompInstKpiEntity getKpiDetailByAccInstKpiIdentifier(String accountIdentifier, String instanceIdentifier, String kpiIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/instances/" + instanceIdentifier + "/kpis/" + kpiIdentifier;
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_INSTANCES_" + instanceIdentifier + "_KPIS_" + kpiIdentifier;
        try {
            String response = RedisUtilities.getKey(key, hashKey);
            if (response == null)
                return null;
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(response, new TypeReference<CompInstKpiEntity>() {
            });
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            LOGGER.error("Exception encountered while getting key '{}': {}", key, hashKey, e);
            return null;
        }
    }

    public CompInstKpiEntity getKpiDetailByAccInstKpiId(String accountIdentifier, String instanceIdentifier, long kpiId) {
        String key = "/accounts/" + accountIdentifier + "/instances/" + instanceIdentifier + "/kpis/" + kpiId;
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_INSTANCES_" + instanceIdentifier + "_KPIS_" + kpiId;
        try {
            String response = RedisUtilities.getKey(key, hashKey);
            if (response == null)
                return null;
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(response, new TypeReference<CompInstKpiEntity>() {
            });
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            LOGGER.error("Exception encountered while getting key '{}': {}", key, hashKey, e);
            return null;
        }
    }

    public KpiDetails getKpiDetailByServiceKpiId(String accountIdentifier, String serviceIdentifier, String kpiId) {
        String key = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/kpis/" + kpiId;
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_KPIS_" + kpiId;
        try {
            String response = RedisUtilities.getKey(key, hashKey);
            if (response == null)
                return null;
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(response, new TypeReference<KpiDetails>() {
            });
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            LOGGER.error("Exception encountered while getting key '{}': {}", key, hashKey, e);
            return null;
        }
    }

    public List<CompInstKpiEntity> getKpiDetailByAccInst(String accountIdentifier, String instanceIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/instances/" + instanceIdentifier + "/kpis";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_INSTANCES_" + instanceIdentifier + "_KPIS";
        try {
            String response = RedisUtilities.getKey(key, hashKey);
            if (response == null)
                return Collections.emptyList();
            return CommonUtils.getObjectMapper().readValue(response, new TypeReference<List<CompInstKpiEntity>>() {
            });
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            LOGGER.error("Exception encountered while getting key '{}': {}", key, hashKey, e);
            return null;
        }
    }

}
