package com.appnomic.appsone.api.dao;

import com.appnomic.appsone.api.beans.SolutionRecommendationFeedbackBean;
import com.appnomic.appsone.api.pojo.SolutionIsUsefulFeedbackPojo;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface SolutionRecommendationDao {

    @SqlUpdate("INSERT INTO solution_recommendation_feedback (feedback_user_id, signal_id, comments, kpi_vector, created_time, updated_time) VALUES (:feedbackUserId, :signalId, :comments, :vector, :createdTime, :updatedTime)")
    @GetGeneratedKeys
    int createFeedbackEntry(@BindBean SolutionRecommendationFeedbackBean feedbackPojo);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT id, feedback_user_id feedbackUserId, signal_id signalId, comments, kpi_vector vector, created_time createdTime, updated_time updatedTime FROM solution_recommendation_feedback WHERE signal_id = :signalId and feedback_user_id = :feedbackUserId")
    SolutionRecommendationFeedbackBean getUserFeedback(@Bind("signalId") String signalId, @Bind("feedbackUserId") String feedbackUserId);

    @GetGeneratedKeys
    @SqlUpdate("INSERT INTO feedback_soln_mapping (feedback_id, cluster_id, solution_id, is_useful, signal_status, created_time, updated_time) VALUES (:id, :clusterId, :solutionId, :isUseful, :signalStatus, :createdTime, :updatedTime)")
    int createFeedbackMapping(@BindBean SolutionRecommendationFeedbackBean feedback);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("UPDATE solution_recommendation_feedback SET comments = :comments, updated_time = :updatedTime WHERE feedback_user_id = :feedbackUserId and signal_id = :signalId")
    int updateFeedbackComment(@Bind("comments") String comments, @Bind("feedbackUserId") String feedbackUserId, @Bind("signalId") String signalId, @Bind("updatedTime") String updatedTime);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("UPDATE feedback_soln_mapping SET is_useful = :isUseful, updated_time = :updatedTime WHERE feedback_id = :feedbackId and solution_id = :solutionId and cluster_id = :clusterId")
    int updateFeedbackIsUseful(@Bind("isUseful") int isUseful, @Bind("feedbackId") int feedbackId, @Bind("updatedTime") String updatedTime, @Bind("solutionId") int solutionId, @Bind("clusterId") int clusterId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("INSERT INTO feedback_soln_mapping (feedback_id, cluster_id, solution_id, is_useful, signal_status, created_time, updated_time) VALUES (:id, :clusterId, :solutionId, :isUseful, :signalStatus, :createdTime, :updatedTime, :createdTime) ON DUPLICATE KEY UPDATE")
    int upsertFeedbackIsUseful(@Bind("isUseful") int isUseful, @Bind("id") int feedbackId, @Bind("updatedTime") String updatedTime, @Bind("solutionId") int solutionId, @Bind("clusterId") int clusterId, @Bind("signalStatus") int signalStatus, @Bind("createdTime") String createdTime);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT cluster_id clusterId, solution_id solutionId, is_useful isUseful " +
            "FROM feedback_soln_mapping WHERE feedback_id = :feedbackId")
    List<SolutionIsUsefulFeedbackPojo> getFeedbackIsUseful(@Bind("feedbackId") int feedbackId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT solution_id solutionId, is_useful isUseful " +
            "FROM feedback_soln_mapping WHERE feedback_id = :feedbackId and solution_id = :solutionId and cluster_id = :clusterId")
    SolutionIsUsefulFeedbackPojo getFeedbackIsUsefulBySolution(@Bind("feedbackId") int feedbackId, @Bind("solutionId") int solutionId, @Bind("clusterId") int clusterId);
}
