package com.appnomic.appsone.api.dao;

import com.appnomic.appsone.api.beans.*;
import com.appnomic.appsone.api.pojo.*;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.sql.Timestamp;
import java.util.List;

/**
 * Created by Son<PERSON> J on 28/12/2018
 */

public interface MasterDataDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select a.id accountId, a.name accountName, mt.timeoffset timezoneMilli,a.identifier identifier,mt.time_zone_id timeZoneString, " +
            "a.updated_time updatedTimeString, a.user_details_id updatedBy, mt.timeoffset timeOffset, mt.abbreviation abbreviation, offset_name offsetName, " +
            "a.user_details_id userDetailsId " +
            "from account a, tag_mapping tm, mst_timezone mt, tag_details td where tm.object_id = a.id and tm.object_ref_table = :accountTableName " +
            "and tm.tag_id = td.id and td.name = :timezoneKey  and mt.id = tm.tag_key and a.status = 1;")
    List<Account> getAccountList(@Bind("timezoneKey") String timezoneKey, @Bind("accountTableName") String accountTableName);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,mst_type_id mstTypeId,created_time createdTime,updated_time updatedTime,user_details_id userDetailsId,account_id accountId,description,is_custom isCustom,status from mst_sub_type where id = :sub_type_id")
    MasterSubTypeBean getMasterSubTypeForId(@Bind("sub_type_id") int mstSubTypeId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id from account where name = :accountName")
    int getAccountId(@Bind("accountName") String accountName);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select transaction_id,ifnull(group_concat(concat(attribute_1,\"=\",attribute_2)),\" \") value,ST.http_url url,transaction_attribute_id,ifnull(vt.name,\" \")name from sub_transactions ST  Left outer join transaction_matcher_details TM on TM.sub_transaction_id=ST.id Left join view_types vt on TM.transaction_attribute_id = vt.subtypeid where ST.transaction_id= :TxnId group by vt.subtypeid, ST.transaction_id")
    List<RowDetails> getTransactionList(@Bind("TxnId") Integer TxnId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select type typeName, typeid typeId, name subTypeName, subtypeid subTypeId from view_types ")
    List<ViewTypes> getAllTypes();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select d.name label, d.type, d.value from mst_date_component_data d")
    List<DateComponent> getDateComponentList();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, name, is_enabled enabled from mst_features")
    List<MasterFeaturesBean> getMasterFeatures();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select kpi_id kpiId, category_id categoryId, name, category_identifier identifier, kpi_type_id kpiTypeId," +
            "is_informative isInformative from view_kpi_category_details where kpi_id = :kpiId")
    KpiCategoryDetailBean getCategoryDetailsForKpi(@Bind("kpiId") Integer kpiId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id from transaction where account_id=:accountId and status=1")
    List<Integer> getTransactionsIdsForAccount(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,source_id sourceId,source_ref_object sourceRefObject,destination_id destinationId,destination_ref_object destinationRefObject," +
                    "account_id accountId,user_details_id userDetailsId from connection_details where account_id = :accountId")
    List<ConnectionDetails> getConnectionDetails(@Bind("accountId") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,tag_type_id tagTypeId,is_predefined isPredefined,ref_table refTable,created_time createdTime,updated_time updatedTime," +
            "account_id accountId,user_details_id userDetailsId,ref_where_column_name refWhereColumnName,ref_select_column_name refSelectColumnName " +
            "from tag_details where name = :name and account_id = :accountId")
    TagDetailsBean getTagDetails(@Bind("name") String name, @Bind("accountId") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id instanceId,common_version_id commonVersionId,mst_component_id compId,component_name componentName,mst_component_type_id mstComponentTypeId," +
            "component_type_name componentTypeName," +
            "mst_component_version_id compVersionId,component_version_name componentVersionName,name instanceName,host_id hostId,status," +
            "host_name hostName,is_cluster isCluster,identifier,host_address hostAddress,parent_instance_id parentInstanceId from view_component_instance " +
            "where account_id = :accountId")
    List<CompInstClusterDetails> getCompInstClusterList(@Bind("accountId") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id , name, type_id typeId, account_id accountId, status, start_time startTime, end_time endTime, created_time createdTime,"+
            "updated_time updatedTime , user_details_id userDetails  from maintenance_details where id = :maintenanceId and status = 1 "+
            "and ((start_time \\<= :startTime and end_time >= :startTime) or (start_time \\<= :endTime and end_time >= :endTime) or start_time >= :endTime)")
    MaintenanceDetails getMaintenanceWindowDetails(@Bind("maintenanceId") Integer maintenanceId, @Bind("startTime") Timestamp startTime , @Bind("endTime") Timestamp endTime);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id , maintenance_id maintenanceId, recurring_type_id recurringTypeId, start_hr_min startHrMin, end_hr_min endHrMin, duration,"+
            "recurring_data recurringData, user_details_id userDetails, created_time createdTime, updated_time updatedTime "+
            "from recurring_details where maintenance_id = :maintenanceId")
    RecurringDetails getRecurringDetails(@Bind("maintenanceId") Integer maintenanceId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mtz.id, mtz.time_zone_id timeZoneName, mtz.timeoffset timeOffset, mtz.created_time createdTime, mtz.updated_time updatedTime, mtz.user_details_id userDetailsId, mtz.account_id accountId, mtz.offset_name offsetName, mtz.abbreviation abbreviation " +
            "from mst_timezone mtz where mtz.id=:timezoneId")
    TimezoneDetail getTimezoneByTagID(@Bind("timezoneId") int timezoneId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, name, description description from mst_component where account_id in (1, :accountId) and status=1")
    List<MasterComponentBean> getComponentByAccountId(@Bind("accountId") int accountId);

    //Fetch Component master type data
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name from mst_component_type where account_id in (1, :accountId) and status=1")
    List<MasterComponentTypeBean> getMasterComponentTypesData(@Bind("accountId") int accountId);

    /**
     * Checks if a record exists in the mst_category_details table by identifier
     */
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT COUNT(1) > 0 FROM mst_category_details WHERE identifier = :categoryIdentifier")
    boolean existsByIdentifier(@Bind("categoryIdentifier") String categoryIdentifier);
}
