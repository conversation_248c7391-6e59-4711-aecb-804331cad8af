package com.appnomic.appsone.api.dao.opensearch;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.manager.OpenSearchConnectionManager;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.StringUtils;
import com.appnomic.appsone.opeasearchquery.helper.OpenSearchQueryHelper;
import com.appnomic.appsone.opeasearchquery.queryopts.QueryOptions;
import com.appnomic.appsone.opeasearchquery.results.NameValuePair;
import com.appnomic.appsone.opeasearchquery.results.RawDocumentResults;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.opensearch.ForensicPojo;
import com.appnomic.appsone.api.util.ConfProperties;
import com.heal.configuration.util.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.client.RestHighLevelClient;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 24-02-2022
 */
@Slf4j
public class ForensicSearchRepo {

    private static String indexExtension = ConfProperties.getString(Constants.OPENSEARCH_INDEX_EXTENSION, Constants.OPENSEARCH_INDEX_EXTENSION_DEFAULT);

    public List<ForensicPojo> getForensicDataByInstanceCategoryAndTriggerTime(String accountIdentifier, String compInstanceId, String categoryId, Timestamp forensicTriggerTime, String kpiId, String forensicLevel) {
        String indexName = Constants.INDEX_PREFIX_FORENSICS + "_" + accountIdentifier.toLowerCase();
        List<ForensicPojo> forensicPojo = new ArrayList<>();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_FORENSICS);
            if (elasticClient == null) {
                return null;
            }

            List<String> dateList = DateHelper.getWeeksAsString(forensicTriggerTime.getTime(), forensicTriggerTime.getTime() + 60000L);
            List<String> indexNames = new ArrayList<>();
            dateList.forEach(date -> indexNames.add(indexName + "_" + date));

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("instanceId", compInstanceId));
            matchFields.add(new NameValuePair("categoryId", categoryId));

            if (forensicLevel.equalsIgnoreCase("kpi") && !StringUtils.isEmpty(kpiId)) {
                matchFields.add(new NameValuePair("kpiId", kpiId));
            }

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("forensicTriggerTime")
                    .epochFromDate(forensicTriggerTime.getTime())
                    .epochToDate(forensicTriggerTime.getTime() + 60000L)
                    .isTimeSeriesData(false)
                    .fetchAllRecords(true)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .matchAllFields(Optional.of(matchFields))
                    .build();

            log.debug("OS query for fetching forensic data: {}", queryOptions);

            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);
            if (rawDocuments != null && rawDocuments.getDocuments() != null && !rawDocuments.getDocuments().isEmpty()) {
                return rawDocuments.getDocuments().parallelStream()
                        .map(doc -> {
                            try {
                                return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(doc, new TypeReference<ForensicPojo>() {
                                });
                            } catch (Exception e) {
                                HealUICache.INSTANCE.updateHealUIErrors(1);
                                log.error("Error occurred while mapping OpenSearch signals data to ForensicData for index {}. Details: accountId [{}]", indexName, accountIdentifier, e);
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }
        } catch (Exception exception) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in getting doc from index {} : ", indexName, exception);
        }
        return forensicPojo;
    }

    public List<ForensicPojo> getForensicDetails(String accountIdentifier, String instanceId, String categoryId, String kpiId, String kpiAttribute, long fromTime, long toTime, String forensicLevel) {

        String indexPrefix = Constants.INDEX_PREFIX_FORENSICS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_FORENSICS);
            if (elasticClient == null) {
                return Collections.emptyList();
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexPrefix + "_" + date));

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("instanceId", instanceId));
            matchFields.add(new NameValuePair("categoryId", categoryId));

            if (forensicLevel.equalsIgnoreCase("kpi") && !StringUtils.isEmpty(kpiId)) {
                matchFields.add(new NameValuePair("kpiId", kpiId));
            }

            /*
            if (!StringUtils.isEmpty(kpiAttribute)) {
                matchFields.add(new NameValuePair("kpiAttribute", kpiAttribute));
            }*/

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("forensicTriggerTime")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .matchAllFields(Optional.of(matchFields))
                    .fetchAllRecords(true)
                    .extensionEnabled(true)
                    .extensionName(indexExtension)
                    .build();

            log.debug("OS query for fetching anomaly data: {}", queryOptions);

            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);
            if (rawDocuments != null && rawDocuments.getDocuments() != null && !rawDocuments.getDocuments().isEmpty()) {
                return rawDocuments.getDocuments().parallelStream()
                        .map(doc -> {
                            try {
                                return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(doc, new TypeReference<ForensicPojo>() {
                                });
                            } catch (Exception e) {
                                HealUICache.INSTANCE.updateHealUIErrors(1);
                                log.error("Error occurred while mapping OpenSearch signals data to ForensicData for index {}. Details: accountId [{}]", indexPrefix, accountIdentifier, e);
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
        }
        return Collections.emptyList();
    }
}
