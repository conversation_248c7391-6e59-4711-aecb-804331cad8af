package com.appnomic.appsone.api.dao.opensearch;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.manager.OpenSearchConnectionManager;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.opeasearchquery.helper.OpenSearchQueryHelper;
import com.appnomic.appsone.opeasearchquery.queryopts.QueryOptions;
import com.appnomic.appsone.opeasearchquery.results.NameValuePair;
import com.appnomic.appsone.opeasearchquery.results.RawDocumentResults;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.opensearch.InstanceKpiThresholds;
import com.heal.configuration.pojos.opensearch.ServiceKpiThresholds;
import com.heal.configuration.pojos.opensearch.TransactionKpiThresholds;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.client.RestHighLevelClient;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class ThresholdSearchRepo {

    public List<InstanceKpiThresholds> getInstanceLevelThresholdDetails(String accountIdentifier, String instanceIdentifier, String kpiId, String thresholdType, long fromTime, long toTime) {

        String indexPrefix = Constants.INDEX_PREFIX_INSTANCE_KPI_THRESHOLDS + "_" + accountIdentifier.toLowerCase() + "*";
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_INSTANCE_KPI_THRESHOLDS);
            if (elasticClient == null) {
                return Collections.emptyList();
            }

            List<String> indexNames = new ArrayList<>();
            indexNames.add(indexPrefix);

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("kpiId", kpiId));
            matchFields.add(new NameValuePair("instanceId", instanceIdentifier));
            matchFields.add(new NameValuePair("thresholdType", thresholdType));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochStartTimeFieldName("startTime")
                    .epochEndTimeFieldName("endTime")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .matchAllFields(Optional.of(matchFields))
                    .isTimeSeriesData(false)
                    .fetchAllRecords(true)
                    .allIndex(true)
                    .build();

            log.debug("OS query for fetching instance threshold data: {}", queryOptions);

            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);

            if (rawDocuments != null && rawDocuments.getDocuments() != null && !rawDocuments.getDocuments().isEmpty()) {
                return rawDocuments.getDocuments().parallelStream()
                        .map(doc -> {
                            try {
                                return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(doc, new TypeReference<InstanceKpiThresholds>() {
                                });
                            } catch (Exception e) {
                                HealUICache.INSTANCE.updateHealUIErrors(1);
                                log.error("Error occurred while mapping OpenSearch Instance Health data to HealthBean for index {}. Details: accountId [{}]", indexPrefix, accountIdentifier, e);
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
        }
        return Collections.emptyList();
    }

    public List<ServiceKpiThresholds> getServiceLevelThresholdDetails(String accountIdentifier, String serviceIdentifier, String kpiId, String thresholdType, String applicableTo, long fromTime, long toTime) {

        String indexPrefix = Constants.INDEX_PREFIX_SERVICE_KPI_THRESHOLDS + "_" + accountIdentifier.toLowerCase() + "*";
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_SERVICE_KPI_THRESHOLDS);
            if (elasticClient == null) {
                return Collections.emptyList();
            }

            List<String> indexNames = new ArrayList<>();
            indexNames.add(indexPrefix);

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("kpiId", kpiId));
            matchFields.add(new NameValuePair("serviceIdentifier", serviceIdentifier));
            matchFields.add(new NameValuePair("thresholdType", thresholdType));
            matchFields.add(new NameValuePair("applicableTo", applicableTo));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochStartTimeFieldName("startTime")
                    .epochEndTimeFieldName("endTime")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .matchAllFields(Optional.of(matchFields))
                    .isTimeSeriesData(false)
                    .fetchAllRecords(true)
                    .allIndex(true)
                    .build();

            log.debug("OS query for fetching service threshold data: {}", queryOptions);

            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);

            if (rawDocuments != null && rawDocuments.getDocuments() != null && !rawDocuments.getDocuments().isEmpty()) {
                return rawDocuments.getDocuments().parallelStream()
                        .map(doc -> {
                            try {
                                return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(doc, new TypeReference<ServiceKpiThresholds>() {
                                });
                            } catch (Exception e) {
                                HealUICache.INSTANCE.updateHealUIErrors(1);
                                log.error("Error occurred while mapping OpenSearch Instance Health data to HealthBean for index {}. Details: accountId [{}]", indexPrefix, accountIdentifier, e);
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
        }
        return Collections.emptyList();
    }

    public List<TransactionKpiThresholds> getTransactionThresholdDetails(String accountIdentifier, String transactionId, String kpiId, String thresholdType, long fromTime, long toTime) {

        String indexPrefix = Constants.INDEX_PREFIX_TRANSACTION_KPI_THRESHOLDS + "_" + accountIdentifier.toLowerCase() + "*";
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_TRANSACTION_KPI_THRESHOLDS);
            if (elasticClient == null) {
                return Collections.emptyList();
            }

            List<String> indexNames = new ArrayList<>();
            indexNames.add(indexPrefix);

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("kpiId", kpiId));
            matchFields.add(new NameValuePair("thresholdType", thresholdType));
            matchFields.add(new NameValuePair("transactionId", transactionId));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochStartTimeFieldName("startTime")
                    .epochEndTimeFieldName("endTime")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .matchAllFields(Optional.of(matchFields))
                    .isTimeSeriesData(false)
                    .fetchAllRecords(true)
                    .allIndex(true)
                    .build();

            log.debug("OS query for fetching transaction threshold data: {}", queryOptions);

            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);

            if (rawDocuments != null && rawDocuments.getDocuments() != null && !rawDocuments.getDocuments().isEmpty()) {
                return rawDocuments.getDocuments().parallelStream()
                        .map(doc -> {
                            try {
                                return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(doc, new TypeReference<TransactionKpiThresholds>() {
                                });
                            } catch (Exception e) {
                                HealUICache.INSTANCE.updateHealUIErrors(1);
                                log.error("Error occurred while mapping OpenSearch Instance Health data to HealthBean for index {}. Details: accountId [{}]", indexPrefix, accountIdentifier, e);
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexPrefix, accountIdentifier, e);
        }
        return Collections.emptyList();
    }


}
