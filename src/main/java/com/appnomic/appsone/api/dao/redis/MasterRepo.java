package com.appnomic.appsone.api.dao.redis;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.RedisUtilities;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.InstallationAttributes;
import com.heal.configuration.pojos.OSIndexZoneDetails;
import com.heal.configuration.pojos.TagDetails;
import com.heal.configuration.pojos.ViewTypes;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Slf4j
public class MasterRepo {

    public List<ViewTypes> getTypes() {
        String key = "/heal/types";
        String hashKey = "HEAL_TYPES";

        try {
            String typeDetails = RedisUtilities.getKey(key, hashKey);
            if (typeDetails == null) {
                log.debug("View types details not found.");
                return Collections.emptyList();
            }
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(typeDetails, new TypeReference<List<ViewTypes>>() {
            });

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Exception while getting View types details. ", e);
            return Collections.emptyList();
        }
    }

    public List<TagDetails> getTagDetails() {
        String key = "/heal/tag/details";
        String hashKey = "HEAL_TAG_DETAILS";

        try {
            String tagDetails = RedisUtilities.getKey(key, hashKey);
            if (tagDetails == null) {
                log.debug("Tag id not found.");
                return Collections.emptyList();
            }
            return Objects.requireNonNull(CommonUtils.getObjectMapperWithHtmlEncoder().readValue(tagDetails, new TypeReference<List<TagDetails>>() {
            }));

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Exception while getting tag details. ", e);
            return Collections.emptyList();
        }
    }

    public List<InstallationAttributes> getInstallationAttributes() {
        String key = "/installation/attributes";
        String hashKey = "INSTALLATION_ATTRIBUTES";

        try {
            String installationAttributes = RedisUtilities.getKey(key, hashKey);
            if (installationAttributes == null) {
                log.debug("Installation attributes not found.");
                return Collections.emptyList();
            }
            return Objects.requireNonNull(CommonUtils.getObjectMapperWithHtmlEncoder().readValue(installationAttributes, new TypeReference<List<InstallationAttributes>>() {
            }));

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Exception while getting installation attributes details. ", e);
            return Collections.emptyList();
        }
    }

    public List<OSIndexZoneDetails> getHealIndexZones() {
        String key = "/heal/index/zones";
        String hashKey = "HEAL_INDEX_ZONES";

        try {
            String osIndexZoneDetails = RedisUtilities.getKey(key, hashKey);
            if (osIndexZoneDetails == null) {
                log.error("Heal opensearch index to zone mapping unavailable");
                return Collections.emptyList();
            }
            return Objects.requireNonNull(CommonUtils.getObjectMapperWithHtmlEncoder().readValue(osIndexZoneDetails, new TypeReference<List<OSIndexZoneDetails>>() {
            }));

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while getting Heal opensearch index to zone mapping.", e);
            return Collections.emptyList();
        }
    }
}
