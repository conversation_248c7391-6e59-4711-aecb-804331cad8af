package com.appnomic.appsone.api.beans;

import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR> : 16/1/19
 */
@Data
public class MasterComponentVersionBean {
    private int id;
    private String name;
    private int isCustom;
    private int status;
    private int versionFrom;
    private int versionTo;
    private int mstCommonVersionId;
    private int mstComponentId;
    private String createdTime;
    private String updatedTime;
    private String userDetailsId;
    private int accountId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MasterComponentVersionBean that = (MasterComponentVersionBean) o;
        return mstComponentId == that.mstComponentId &&
                Objects.equals(name, that.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, mstComponentId);
    }
}
