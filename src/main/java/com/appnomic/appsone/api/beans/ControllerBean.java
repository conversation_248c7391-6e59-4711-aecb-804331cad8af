package com.appnomic.appsone.api.beans;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by <PERSON><PERSON> on 31/01/2019
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ControllerBean {
    private int id;
    private String name;
    private String identifier;
    private int accountId;
    private String userDetailsId;
    private String createdTime;
    private String updatedTime;
    private int controllerTypeId;
}
