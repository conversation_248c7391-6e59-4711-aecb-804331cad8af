package com.appnomic.appsone.api.beans;

import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR> : 7/3/19
 */
@Data
public class TransactionThresholdDetailsBean {
    private int id;
    private Timestamp createdTime;
    private Timestamp updatedTime;
    private String userDetailsId;
    private int transactionId;
    private int transactionKpiTypeId;
    private int operationId;
    private float minThreshold;
    private float maxThreshold;
    private int responseTimeTypeId;
    private Timestamp startTime;
    private Timestamp endTime;
    private int coverageWindowProfileId;
    private int accountId;

}


