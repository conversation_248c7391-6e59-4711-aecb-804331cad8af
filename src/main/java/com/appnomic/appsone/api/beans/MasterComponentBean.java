package com.appnomic.appsone.api.beans;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_DEFAULT)
public class MasterComponentBean {
    private int id;
    private String name;
    private int isCustom;
    private int status;
    private String createdTime;
    private String updatedTime;
    private String userDetailsId;
    private int accountId;
    private String identifier;
    private String description;
}
