package com.appnomic.appsone.api.beans;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SolutionRecommendationFeedbackBean {

    private int id;
    private String feedbackUserId;
    private String signalId;
    private String comments;
    private Integer clusterId;
    private List kpiVector;
    private int solutionId;
    private int isUseful;
    private int signalStatus;
    private String createdTime;
    private String updatedTime;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String vector;

}