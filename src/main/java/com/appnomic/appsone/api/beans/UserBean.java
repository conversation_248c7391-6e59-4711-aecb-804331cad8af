package com.appnomic.appsone.api.beans;

import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;


@Data
public class UserBean {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserBean.class);
    public static final String ATTRIBUTE_ACCOUNTS = "Accounts";
    public static final String APPLICATIONS = ".Application";

    private String id;
    private String username;
    private String email;
    private Map<String, Object> attributes;
    private Map<String, List<String>> applicationMap;
    private Set<String> accounts;

    public void setAttributes(Map<String, Object> attributes) {

        LOGGER.debug("inside setAttributes: {} ", attributes);

        applicationMap = new HashMap<>();
        if (attributes == null || attributes.isEmpty()
                || !attributes.containsKey(ATTRIBUTE_ACCOUNTS)) {
            LOGGER.warn("Accounts are not available for {} ", this.username);
            return;
        }

        List<String> accountList = (List<String>) this.attributes.get(ATTRIBUTE_ACCOUNTS);
        if (accountList.isEmpty()) {
            LOGGER.warn("Accounts are not available for {} ", this.username);
            return;
        }
        String[] accountNames = accountList.get(0).split(",");
        this.accounts = new HashSet<>();
        for (String accountName : accountNames) {
            this.accounts.add(accountName.trim());
        }

        Set<String> keys = attributes.keySet();

        for (String key : keys) {
            if (!key.endsWith(APPLICATIONS)) {
                continue;
            }
            List<String> applicationList = (List<String>) this.attributes.get(key);
            if (!applicationList.isEmpty()) {
                String[] applicationNames = applicationList.get(0).split(",");
                List<String> applications = Arrays.asList(applicationNames);
                this.applicationMap.put(key, applications);
            }
        }
    }
}
