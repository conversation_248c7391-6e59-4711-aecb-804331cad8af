package com.appnomic.appsone.api.beans;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR> : 22/1/19
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentBean {
    private int id;
    private String uniqueToken;
    private String name;
    private int agentTypeId;
    private Timestamp createdTime;
    private Timestamp updatedTime;
    private String userDetailsId;
    private int status;
    private String hostAddress;
    private String mode;
    private String description;
    private Integer compInstanceId;
    private List<Integer> serviceIds;

    public Integer getCompInstanceId(){
        return compInstanceId == null ? 0 : compInstanceId;
    }
}
