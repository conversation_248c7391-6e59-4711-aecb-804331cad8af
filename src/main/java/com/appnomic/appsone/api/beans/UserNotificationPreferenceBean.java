package com.appnomic.appsone.api.beans;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.sql.Timestamp;

@Data
@JsonIgnoreProperties({"createdTime", "updatedTime"})
public class UserNotificationPreferenceBean {

    private int signalTypeId;
    private String signalType;

    private int signalSeverityId;
    private String signalSeverity;

    private int notificationTypeId;
    private String notificationType;

    private Timestamp createdTime;
    private Timestamp updatedTime;
}