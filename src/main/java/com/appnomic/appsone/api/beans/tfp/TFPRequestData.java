package com.appnomic.appsone.api.beans.tfp;

import com.appnomic.appsone.api.pojo.UserAccessDetails;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.Application;
import com.heal.configuration.pojos.BasicEntity;
import lombok.Data;

import java.util.List;

@Data
public class TFPRequestData {
    private Account account;
    private BasicEntity application;
    private UserAccessDetails userAccessDetails;
    private long fromTime;
    private long toTime;
    private List<Application> applications;
}
