package com.appnomic.appsone.api.beans;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> : 5/2/19
 */
@Data
public class ComponentAgentBean {
    private int id;
    private int agentId;
    private int timeoutMultiplier;
    private Date createdTime;
    private Date updatedTime;
    private String userDetailsId;
    //id from mst_sub_type table
    private int configOperationModeId;
    //id from mst_sub_type table
    private int dataOperationModeId;
    //id from data_communication_details table
    private int dataCommunicationId;
}
