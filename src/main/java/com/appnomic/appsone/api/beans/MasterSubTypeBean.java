package com.appnomic.appsone.api.beans;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.cache.MasterCache;
import lombok.Data;

import java.util.List;
import java.util.Optional;

/**
 * Created by <PERSON><PERSON> on 28/01/2019
 */
@Data
public class MasterSubTypeBean {
    private int id;
    private String name;
    private int mstTypeId;
    private String createdTime;
    private String updatedTime;
    private String userDetailsId;
    private int accountId;
    private String description;
    private int isCustom;
    private int status;
}
