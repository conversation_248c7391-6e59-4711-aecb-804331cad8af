package com.appnomic.appsone.api.beans;

import com.appnomic.appsone.api.pojo.AggregationLevel;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.Application;
import lombok.Data;

import java.util.List;

@Data
public class MleTransactionDataRequestData {
    private Account account;
    private AggregationLevel aggregationLevel = AggregationLevel.MINUTELY;
    private int applicationId;
    private long fromTime;
    private long toTime;
    private Application application;
    private List<Integer> transactionKpi;
    private String responseType;
}
