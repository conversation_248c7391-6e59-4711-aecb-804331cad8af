package com.appnomic.appsone.api.beans;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> : 26/12/19
 */

@Data
public class ServiceKpiThreshold
{
    private int id;
    private int accountId;
    private int serviceId;
    private int kpiId;
    private int operationTypeId;
    private String minThreshold;
    private String maxThreshold;
    private String applicableTo;
    private String userDetailsId;
    private String kpiAttribute;
    private Date createdTime;
    private Date updatedTime ;
}
