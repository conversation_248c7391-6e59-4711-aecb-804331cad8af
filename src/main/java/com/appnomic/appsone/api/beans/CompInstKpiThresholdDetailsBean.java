package com.appnomic.appsone.api.beans;

import lombok.Data;

import java.sql.Timestamp;
import java.util.Objects;

/**
 * <AUTHOR> : 6/3/19
 */
@Data
public class CompInstKpiThresholdDetailsBean {
    private int id;
    private int compInstanceId;
    private int kpiId;
    private int operationId;
    private float minThreshold;
    private float maxThreshold;
    private Timestamp createdTime;
    private Timestamp updatedTime;
    private String userDetailsId;
    private int kpiGroupId;
    private String kpiGroupValue;
    private int coverageWindowProfileId;
    private Timestamp startTime;
    private Timestamp endTime;
    private int accountId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CompInstKpiThresholdDetailsBean that = (CompInstKpiThresholdDetailsBean) o;
        return id == that.id;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}
