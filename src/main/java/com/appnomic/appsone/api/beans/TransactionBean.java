package com.appnomic.appsone.api.beans;


import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Parekaden : 24/1/19
 */
@Data
public class TransactionBean {
    private Integer id;
    private String name;
    private Integer status;
    private Integer auditEnabled;
    private Integer isAutoConfigured;
    private String createdTime;
    private String updatedTime;
    private String userDetailsId;
    private Integer accountId;
    private Integer transactionTypeId;
    private String patternHashcode;
    private String description;
    private String identifier;
    private List<SubTransactionBean> subTransaction = new ArrayList<>();
}
