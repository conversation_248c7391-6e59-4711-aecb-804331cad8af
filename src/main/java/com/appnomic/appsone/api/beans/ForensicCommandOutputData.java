package com.appnomic.appsone.api.beans;

import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

@Data
public class ForensicCommandOutputData {
    private String title;
    private String name;
    private String time;
    //private String endTime;
    //private String returnCode;
    private String contents;
    private boolean moreData;
    private String extensionType;

    public ForensicCommandOutputData(String title, String name, String time, String contents, boolean moreData, String extensionType) {
        this.title = title;
        this.name = name;
        this.time = time;
        //this.endTime = endTime;
        // this.returnCode = returnCode;
        this.contents = contents;
        this.moreData=moreData;
        this.extensionType=extensionType;
    }
}

