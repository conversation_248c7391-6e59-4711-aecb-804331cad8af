package com.appnomic.appsone.api.beans;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class FileUploadDetailsBean {
    private int uploadId;
    private String fileName;
    private long fileSize;
    private String checksum;
    private String fileLocation;
    private String uploadBy;
    private Timestamp uploadTime;
    private int accountId;
    private int isProcessing=0;
    private String status;
    private String processedBy;
    private Timestamp processedTime;
    private int processId;

}
