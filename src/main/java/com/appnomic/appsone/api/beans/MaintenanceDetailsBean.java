package com.appnomic.appsone.api.beans;

import lombok.Builder;
import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR> on 20/7/20
 */

@Data
@Builder
public class MaintenanceDetailsBean {
    private int id;
    private String type;
    private String name;
    private boolean isService;
    private Timestamp startTime;
    private Timestamp endTime;
    private String status;
    private RecurringWindowBean recurring;
}
