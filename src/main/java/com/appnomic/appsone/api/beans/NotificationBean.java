package com.appnomic.appsone.api.beans;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationBean {
    private String applicableUserId;
    private int applicationId;
    private int signalTypeId;
    private String signalType;
    private int severityTypeId;
    private String severityType;
    private int notificationTypeId;
    private int accountId;
    private String userDetailsId;
    private int status;
    private Date createdTime;
    private Date updatedTime;
}
