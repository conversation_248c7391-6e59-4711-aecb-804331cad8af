package com.appnomic.appsone.api.beans;

import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR> : 6/3/19
 */
@Data
public class ApplicationThresholdDetailsBean {
    private int id;
    private Timestamp createdTime;
    private Timestamp updatedTime;
    private String userDetailsId;
    private int applicationId;
    private int transactionKpiTypeId;
    private int operationId;
    private float minThreshold;
    private float maxThreshold;
    private int responseTimeTypeId;
    private Timestamp startTime;
    private Timestamp endTime;
    private int accountId;
    private int coverageWindowProfileId;

}
