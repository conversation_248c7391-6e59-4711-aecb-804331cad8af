package com.appnomic.appsone.api.beans;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Parekaden : 24/1/19
 */
@Data
public class SubTransactionBean {
    private Integer id;
    private String createdTime;
    private String updatedTime;
    private String userDetailsId;
    private String httpMethod;
    private String httpUrl;
    private Integer transactionId;
    private List<TransactionMatcherDetailsBean> transactionMatcherDetailsList = new ArrayList<>();
}
