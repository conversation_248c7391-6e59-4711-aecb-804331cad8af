package com.appnomic.appsone.api.beans;

import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR> : 7/3/19
 */
@Data
public class TransactionResponseThresholdViolationBean {
    private int id;
    private int transactionId;
    private int slowThresholdValue;
    private Timestamp createdTime;
    private Timestamp updatedTime;
    private String userDetailsId;
    private int responseTimeTypeId;
    private Timestamp startTime;
    private Timestamp endTime;
    private int accountId;
    private int coverageWindowProfileId;

}



