package com.appnomic.appsone.api.beans;
import com.appnomic.appsone.api.cache.MasterCache;
import com.appnomic.appsone.api.pojo.KeyCloakUserDetails;
import lombok.Data;

import java.nio.file.Path;
import java.sql.Timestamp;

@Data
public class FileProcessedDetailsBean {
    private String fileName;
    private long fileSize;
    private String checksum;
    private String fileLocation;
    private String uploadBy;
    private Timestamp uploadTime;
    private String processedBy;
    private Timestamp startTime;
    private Timestamp endTime;
    private String status;
    private int accountId;
    private int processId;
    private String progress;

/*
    public void setUploadBy(String uploadBy) {
        if(uploadBy == null || uploadBy.trim().length() ==0) {
            this.uploadBy = uploadBy;
            return;
        }
        KeyCloakUserDetails keyCloakUserDetails = MasterCache.getInstance().getKeycloakUserDetails(uploadBy);
        if(keyCloakUserDetails != null)
            this.uploadBy = keyCloakUserDetails.getUsername();
        else
            this.uploadBy = uploadBy;
    }

    public void setProcessedBy(String processedBy) {
        if(processedBy == null || processedBy.trim().length() == 0) {
            this.processedBy = processedBy;
            return;
        }
        KeyCloakUserDetails keyCloakUserDetails = MasterCache.getInstance().getKeycloakUserDetails(processedBy);
        if(keyCloakUserDetails != null)
            this.processedBy = keyCloakUserDetails.getUsername();
        else
            this.processedBy = processedBy;
    }*/
}


