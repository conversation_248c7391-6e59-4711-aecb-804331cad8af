package com.appnomic.appsone.api.beans;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserNotificationChoiceBean {
    private int notificationChoiceId;
    private int id;
    private String applicableUserId;
    private String componentSelection;
    private String categoryIds;
    private Timestamp createdTime;
    private Timestamp updatedTime;
    private String userDetailsId;
    private String componentTypeIds;
    private String componentIds;
}
