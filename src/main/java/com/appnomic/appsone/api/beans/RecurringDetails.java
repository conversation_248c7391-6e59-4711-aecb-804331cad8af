package com.appnomic.appsone.api.beans;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class RecurringDetails {

    private int id;
    private int maintenanceId;
    private int recurringTypeId;
    private String startHrMin;
    private String endHrMin;
    private int duration;
    private String recurringData;
    private Timestamp createdTime;
    private Timestamp updatedTime;
    private String userDetails;
}
