package com.appnomic.appsone.api.beans;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserNotificationDetails {

    private int smsEnabled;
    private int emailEnabled;
    private int forensicEnabled;
    private int accountId;
    private String applicableUserId;
    private int notificationPreferenceId;
    private int forensicNotificationSuppressionInterval;
    List<Integer> applicationIds;
}
