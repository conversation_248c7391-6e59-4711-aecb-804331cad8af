package com.appnomic.appsone.api.beans;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserNotificationDetailsBean {
    private boolean smsEnabled;
    private boolean emailEnabled;
    private int forensicEnabled;
    private int forensicNotificationSuppression;
    private int accountId;
    private String applicableUserId;
    private Date createdTime;
    private Date updatedTime;
    private String userDetailsId;
    private int notificationPreferenceId;
}