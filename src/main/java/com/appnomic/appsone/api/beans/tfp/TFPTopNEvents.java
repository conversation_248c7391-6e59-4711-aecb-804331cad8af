package com.appnomic.appsone.api.beans.tfp;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class TFPTopNEvents {
    int metricId;
    String metric;
    int instanceId;
    long time;
    String value;
    boolean isForensics;
    long forensicTime;
    int categoryId;
    Double upperThreshold;
    Double lowerThreshold;
    String unit;
    String operation;
    String instanceName;
}
