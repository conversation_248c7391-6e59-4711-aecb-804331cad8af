package com.appnomic.appsone.api.beans;

import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.Service;
import com.heal.configuration.pojos.TimeRangeDetails;
import lombok.Data;

import java.util.List;

@Data
public class HeatMapRequestData {
    private Account account;
    private Service service;
    long from;
    long to;
    private TimeRangeDetails timeRangeDetails;
    private List<TimeRangeDetails> timeRangeDetailsList;

}
