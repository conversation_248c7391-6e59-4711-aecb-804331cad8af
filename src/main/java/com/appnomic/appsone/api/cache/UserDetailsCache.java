package com.appnomic.appsone.api.cache;

import com.appnomic.appsone.api.beans.UserAccessBean;
import com.appnomic.appsone.api.beans.UserAccountIdentifiersBean;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.AppsoneException;
import com.appnomic.appsone.api.pojo.UserAccessDetails;
import com.appnomic.appsone.api.service.mysql.UserAccessDataService;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.api.util.UserValidationUtil;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.NonNull;

import java.util.concurrent.TimeUnit;

public class UserDetailsCache {

    private static final Integer MAX_SIZE = ConfProperties.getInt(Constants.CACHE_MAXIMUM_SIZE_PROPERTY_NAME,
            Constants.CACHE_MAXIMUM_SIZE_DEFAULT_VALUE);
    private static final Integer CACHE_TIMEOUT = ConfProperties.getInt(Constants.USER_CACHE_TIMEOUT_IN_MINUTES_PROPERTY_NAME,
            Constants.USER_CACHE_TIMEOUT_IN_MINUTES_DEFAULT_VALUE);

    private static UserDetailsCache instance = null;

    private UserDetailsCache() {
    }

    public static UserDetailsCache getInstance() {
        if (instance == null) {
            instance = new UserDetailsCache();
        }
        return instance;

    }

    public LoadingCache<UserAccountIdentifiersBean, UserAccessDetails> userApplications = CacheBuilder.newBuilder()
            .maximumSize(MAX_SIZE)
            .expireAfterWrite(CACHE_TIMEOUT, TimeUnit.MINUTES)
            .build(new CacheLoader<UserAccountIdentifiersBean, UserAccessDetails>() {
                @Override
                public UserAccessDetails load(@NonNull UserAccountIdentifiersBean s) {
                    UserAccessBean accessDetails = UserAccessDataService.getUserAccessDetails(s.getUserIdentifier());
                    if (accessDetails != null) {
                        UserAccessDetails userAccessDetails = UserValidationUtil.extractUserAccessDetails(accessDetails.getAccessDetailsJson(), s);
                        if (userAccessDetails != null) {
                            return userAccessDetails;
                        }
                    }

                    throw new AppsoneException("User access details unavailable for account");
                }
            });
}


