package com.appnomic.appsone.api.cache;

import com.appnomic.appsone.api.beans.TimezoneDetail;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.dao.redis.ApplicationRepo;
import com.appnomic.appsone.api.dao.redis.ServiceRepo;
import com.appnomic.appsone.api.dao.redis.TransactionRepo;
import com.appnomic.appsone.api.pojo.ConnectionDetails;
import com.appnomic.appsone.api.service.mysql.MasterDataService;
import com.appnomic.appsone.api.service.mysql.TimezoneDataService;
import com.appnomic.appsone.api.util.ConfProperties;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.heal.configuration.entities.BasicInstanceBean;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.BasicTransactionEntity;
import com.heal.configuration.pojos.MaintenanceDetails;
import com.heal.configuration.pojos.Service;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.lang.management.ManagementFactory;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> Prasad on 1/12/20
 */
@Slf4j
public enum HealUICache {
    INSTANCE;

    private final String defaultSplitValue = "___@#@HeAl@#@___";

    private final Integer maxSize = ConfProperties.getInt(Constants.CACHE_MAXIMUM_SIZE_PROPERTY_NAME, Constants.CACHE_MAXIMUM_SIZE_DEFAULT_VALUE);
    private final Integer cacheTimeout = ConfProperties.getInt(Constants.CACHE_TIMEOUT_IN_MINUTES_PROPERTY_NAME, Constants.CACHE_TIMEOUT_IN_MINUTES_DEFAULT_VALUE);
    private final Integer cacheTimeoutMaintenanceData = ConfProperties.getInt(Constants.CACHE_TIMEOUT_IN_MINUTES_MAINTENANCE_DATA_PROPERTY_NAME, Constants.CACHE_TIMEOUT_IN_MINUTES_MAINTENANCE_DATA_DEFAULT_VALUE);
    @Setter
    @Getter
    private int requestThreshold = ConfProperties.getInt(Constants.REQUEST_THRESHOLD_PROPERTY, Constants.REQUEST_THRESHOLD_PROPERTY_DEFAULT_VALUE);

    private final long schedulerPeriodInMS = ConfProperties.getInt(Constants.HEAL_METRICS_SCHEDULER_PROPERTY_NAME, Constants.HEAL_METRICS_SCHEDULER_PROPERTY_NAME_DEFAULT_VALUE);

    @Getter
    private int requests = 0;
    @Getter
    private int unauthorizedRequests = 0;
    @Getter
    private int accessDeniedRequests = 0;
    @Getter
    private int skipValidationRequests = 0;
    @Getter
    private int slowRequests = 0;
    @Getter
    private int healUIErrors = 0;
    @Getter
    private Map<String, Double> slowRequestDetails = new HashMap<>();
    @Getter
    private double maxRespTimeInMillSecs = 0.0;
    @Getter
    private Map<Integer, Integer> statusCodes = new HashMap<>();

    public void updateRequests(int count) {
        this.requests += count;
    }

    public void updateUnauthorizedRequests(int count) {
        this.unauthorizedRequests += count;
    }

    public void updateAccessDeniedRequests(int count) {
        this.accessDeniedRequests += count;
    }

    public void updateSkipValidationRequests(int count) {
        this.skipValidationRequests += count;
    }

    public void updateSlowRequests(int count) {
        this.slowRequests += count;
    }

    public void resetSlowRequestDetails() {
        slowRequestDetails = new HashMap<>();
    }

    public void addSlowRequestDetails(String api, Double timeInMillSecs) {
        slowRequestDetails.put(api, timeInMillSecs);
    }

    public void resetStatusCodes() {
        statusCodes = new HashMap<>();
    }

    public void addStatusCodes(Integer statusCode, Integer counter) {
        statusCodes.put(statusCode, statusCodes.getOrDefault(statusCode, 0) + counter);
    }

    public void updateResponseTime(String url, double respTimeInMillSecs) {
        if (respTimeInMillSecs > this.maxRespTimeInMillSecs) {
            this.maxRespTimeInMillSecs = respTimeInMillSecs;
        }
        if (respTimeInMillSecs >= requestThreshold * 1000) {
            this.updateSlowRequests(1);
            this.addSlowRequestDetails(url, maxRespTimeInMillSecs);
        }
    }

    public void updateHealUIErrors(int healUIErrors) {
        this.healUIErrors += healUIErrors;
    }

    public void refreshCache() {
        long start = System.currentTimeMillis();
        long st = System.currentTimeMillis();

        accountConnectionList.asMap().keySet().forEach(accountConnectionList::refresh);
        log.info("Cache name: 'accountConnectionList', size: " + accountConnectionList.size() + ", time taken: " + (System.currentTimeMillis() - st) + "ms");
        st = System.currentTimeMillis();

        allTimezones.asMap().keySet().forEach(allTimezones::refresh);
        log.info("Cache name: 'allTimezones', size: " + allTimezones.size() + ", time taken: " + (System.currentTimeMillis() - st) + "ms");
        st = System.currentTimeMillis();

        accountServiceMaintenanceMap.asMap().keySet().forEach(accountServiceMaintenanceMap::refresh);
        log.info("Cache name: 'accountServiceMaintenanceMap', size: " + accountServiceMaintenanceMap.size() + ", time taken: " + (System.currentTimeMillis() - st) + "ms");
        st = System.currentTimeMillis();

        applicationServiceListMap.asMap().keySet().forEach(applicationServiceListMap::refresh);
        log.info("Cache name: 'applicationServiceListMap', size: " + applicationServiceListMap.size() + ", time taken: " + (System.currentTimeMillis() - st) + "ms");
        st = System.currentTimeMillis();

        serviceInstanceListMap.asMap().keySet().forEach(serviceInstanceListMap::refresh);
        log.info("Cache name: 'serviceInstanceListMap', size: " + serviceInstanceListMap.size() + ", time taken: " + (System.currentTimeMillis() - st) + "ms");
        st = System.currentTimeMillis();

        serviceApplicationListMap.asMap().keySet().forEach(serviceApplicationListMap::refresh);
        log.info("Cache name: 'serviceApplicationListMap', size: " + serviceApplicationListMap.size() + ", time taken: " + (System.currentTimeMillis() - st) + "ms");
        st = System.currentTimeMillis();

        serviceDetailsMap.asMap().keySet().forEach(serviceDetailsMap::refresh);
        log.info("Cache name: 'serviceDetailsMap', size: " + serviceDetailsMap.size() + ", time taken: " + (System.currentTimeMillis() - st) + "ms");
        st = System.currentTimeMillis();

        serviceConnectionDetailsMap.asMap().keySet().forEach(serviceConnectionDetailsMap::refresh);
        log.info("Cache name: 'serviceConnectionDetailsMap', size: " + serviceConnectionDetailsMap.size() + ", time taken: " + (System.currentTimeMillis() - st) + "ms");

        log.info("Total time taken for cache refresh is {}ms", System.currentTimeMillis() - start);
    }

    private final LoadingCache<Integer, Optional<List<ConnectionDetails>>> accountConnectionList = CacheBuilder.newBuilder()
            .maximumSize(maxSize)
            .expireAfterWrite(cacheTimeout, TimeUnit.MINUTES)
            .build(new CacheLoader<Integer, Optional<List<ConnectionDetails>>>() {

                @Override
                public Optional<List<ConnectionDetails>> load(@NonNull Integer accountId) {
                    return Optional.ofNullable(MasterDataService.getConnectionDetails(accountId));
                }
            });

    public List<ConnectionDetails> getAccountConnectionList(int accountId) {
        try {
            return accountConnectionList.get(accountId).orElseGet(ArrayList::new);
        } catch (Exception e) {
            log.error("Exception encountered while fetching connection list. Account: {}", accountId, e);
        }
        return Collections.emptyList();
    }

    /**
     * key: 'allTimezones'
     * value: list of all time zones available in DB
     */
    private final LoadingCache<String, Optional<List<TimezoneDetail>>> allTimezones = CacheBuilder.newBuilder()
            .maximumSize(maxSize)
            .expireAfterWrite(cacheTimeout, TimeUnit.MINUTES)
            .build(new CacheLoader<String, Optional<List<TimezoneDetail>>>() {
                @Override
                public Optional<List<TimezoneDetail>> load(@NonNull String key) {
                    return Optional.ofNullable(new TimezoneDataService().getAllTimezones());
                }
            });

    public List<TimezoneDetail> getTimezones() {
        try {
            return allTimezones.get("allTimezones").orElseGet(ArrayList::new);
        } catch (Exception e) {
            log.error("Error occurred while fetching timezones from database.", e);
        }
        return Collections.emptyList();
    }

    /**
     * key: 'accountServiceMaintenanceMap'
     * value: Map of Service Under Maintenance Data
     */
    private final LoadingCache<String, Optional<List<MaintenanceDetails>>> accountServiceMaintenanceMap = CacheBuilder.newBuilder()
            .maximumSize(maxSize)
            .expireAfterWrite(cacheTimeoutMaintenanceData, TimeUnit.MINUTES)
            .build(new CacheLoader<String, Optional<List<MaintenanceDetails>>>() {
                @Override
                public Optional<List<MaintenanceDetails>> load(@NonNull String key) {
                    String[] keyArray = key.split(defaultSplitValue);
                    return Optional.ofNullable(new ServiceRepo().getServiceMaintenanceDetails(keyArray[0], keyArray[1]));
                }
            });

    public List<MaintenanceDetails> getServiceMaintenanceDetails(String accountIdentifier, String serviceIdentifier) {
        try {
            return accountServiceMaintenanceMap.get(accountIdentifier + defaultSplitValue + serviceIdentifier).orElseGet(ArrayList::new);
        } catch (Exception e) {
            log.error("Exception encountered while fetching service maintenance data for Account: {}, Service {}", accountIdentifier, serviceIdentifier, e);
        }
        return Collections.emptyList();
    }

    private final LoadingCache<String, Optional<List<BasicEntity>>> applicationServiceListMap = CacheBuilder.newBuilder()
            .maximumSize(maxSize)
            .expireAfterWrite(cacheTimeout, TimeUnit.MINUTES)
            .build(new CacheLoader<String, Optional<List<BasicEntity>>>() {
                @Override
                public Optional<List<BasicEntity>> load(@NonNull String key) {
                    String[] keyArray = key.split(defaultSplitValue);
                    return Optional.ofNullable(new ApplicationRepo().getServicesByAppIdentifier(keyArray[0], keyArray[1]));
                }
            });

    public List<BasicEntity> getApplicationServiceList(String accountIdentifier, String applicationIdentifier) {
        try {
            return applicationServiceListMap.get(accountIdentifier + defaultSplitValue + applicationIdentifier).orElseGet(ArrayList::new);
        } catch (Exception e) {
            log.error("Exception encountered while fetching services mapped to Account: {}, Application {}", accountIdentifier, applicationIdentifier, e);
        }
        return Collections.emptyList();
    }

    private final LoadingCache<String, Optional<List<BasicInstanceBean>>> serviceInstanceListMap = CacheBuilder.newBuilder()
            .maximumSize(maxSize)
            .expireAfterWrite(cacheTimeout, TimeUnit.MINUTES)
            .build(new CacheLoader<String, Optional<List<BasicInstanceBean>>>() {
                @Override
                public Optional<List<BasicInstanceBean>> load(@NonNull String key) {
                    String[] keyArray = key.split(defaultSplitValue);
                    return Optional.ofNullable(new ServiceRepo().getAllInstanceDetailsWithServiceIdentifier(keyArray[0], keyArray[1], Boolean.parseBoolean(keyArray[2])));
                }
            });

    public List<BasicInstanceBean> getServiceInstanceList(String accountIdentifier, String serviceIdentifier, boolean includeCluster) {
        try {
            return serviceInstanceListMap.get(accountIdentifier + defaultSplitValue + serviceIdentifier + defaultSplitValue + includeCluster).orElseGet(ArrayList::new);
        } catch (Exception e) {
            log.error("Exception encountered while fetching instances mapped to Account: {}, service {}", accountIdentifier, serviceIdentifier, e);
        }
        return Collections.emptyList();
    }

    private final LoadingCache<String, Optional<List<BasicEntity>>> serviceApplicationListMap = CacheBuilder.newBuilder()
            .maximumSize(maxSize)
            .expireAfterWrite(cacheTimeout, TimeUnit.MINUTES)
            .build(new CacheLoader<String, Optional<List<BasicEntity>>>() {
                @Override
                public Optional<List<BasicEntity>> load(@NonNull String key) {
                    String[] keyArray = key.split(defaultSplitValue);
                    return Optional.ofNullable(new ServiceRepo().getApplicationsByServiceIdentifier(keyArray[0], keyArray[1]));
                }
            });

    public List<BasicEntity> getServiceApplicationList(String accountIdentifier, String serviceIdentifier) {
        try {
            return serviceApplicationListMap.get(accountIdentifier + defaultSplitValue + serviceIdentifier).orElseGet(ArrayList::new);
        } catch (Exception e) {
            log.error("Exception encountered while fetching applications mapped to Account: {}, service {}", accountIdentifier, serviceIdentifier, e);
        }
        return Collections.emptyList();
    }

    private final LoadingCache<String, Optional<List<BasicTransactionEntity>>> serviceTransactionListMap = CacheBuilder.newBuilder()
            .maximumSize(maxSize)
            .expireAfterWrite(cacheTimeout, TimeUnit.MINUTES)
            .build(new CacheLoader<String, Optional<List<BasicTransactionEntity>>>() {
                @Override
                public Optional<List<BasicTransactionEntity>> load(@NonNull String key) {
                    String[] keyArray = key.split(defaultSplitValue);
                    return Optional.ofNullable(new TransactionRepo().getServiceTransactionDetails(keyArray[0], keyArray[1]));
                }
            });

    public List<BasicTransactionEntity> getServiceTransactionList(String accountIdentifier, String serviceIdentifier) {
        try {
            return serviceTransactionListMap.get(accountIdentifier + defaultSplitValue + serviceIdentifier).orElseGet(ArrayList::new);
        } catch (Exception e) {
            log.error("Exception encountered while fetching transactions mapped to Account: {}, service {}", accountIdentifier, serviceIdentifier, e);
        }
        return Collections.emptyList();
    }

    private final LoadingCache<String, Optional<Service>> serviceDetailsMap = CacheBuilder.newBuilder()
            .maximumSize(maxSize)
            .expireAfterWrite(cacheTimeout, TimeUnit.MINUTES)
            .build(new CacheLoader<String, Optional<Service>>() {
                @Override
                public Optional<Service> load(@NonNull String key) {
                    String[] keyArray = key.split(defaultSplitValue);
                    return Optional.ofNullable(new ServiceRepo().getServiceDetailsWithServiceIdentifier(keyArray[0], keyArray[1]));
                }
            });

    public Service getServiceDetails(String accountIdentifier, String serviceIdentifier) {
        try {
            return serviceDetailsMap.get(accountIdentifier + defaultSplitValue + serviceIdentifier).orElseGet(() -> null);
        } catch (Exception e) {
            log.error("Exception encountered while fetching transactions mapped to Account: {}, service {}", accountIdentifier, serviceIdentifier, e);
        }
        return null;
    }

    private final LoadingCache<String, Optional<List<BasicEntity>>> serviceConnectionDetailsMap = CacheBuilder.newBuilder()
            .maximumSize(maxSize)
            .expireAfterWrite(cacheTimeout, TimeUnit.MINUTES)
            .build(new CacheLoader<String, Optional<List<BasicEntity>>>() {
                @Override
                public Optional<List<BasicEntity>> load(@NonNull String key) {
                    String[] keyArray = key.split(defaultSplitValue);
                    return Optional.ofNullable(new ServiceRepo().getConnectionDetails(keyArray[0], keyArray[1]));
                }
            });

    public List<BasicEntity> getServiceConnectionDetails(String accountIdentifier, String serviceIdentifier) {
        try {
            return serviceConnectionDetailsMap.get(accountIdentifier + defaultSplitValue + serviceIdentifier).orElseGet(ArrayList::new);
        } catch (Exception e) {
            log.error("Exception encountered while fetching connection details for Account: {}, service {}", accountIdentifier, serviceIdentifier, e);
        }
        return new ArrayList<>();
    }

    private Map<String, Long> classRedisKeyCountMap = new HashMap<>();

    public void updateClassRedisKeyCountMap(String className) {
        classRedisKeyCountMap.put(className, classRedisKeyCountMap.getOrDefault(className, 0L) + 1);
    }

    public void resetRedisCallCount() {
        classRedisKeyCountMap = new HashMap<>();
    }

    public void logHealthMetrics() {

        log.info("Memory statistics : ");
        printUsage(Runtime.getRuntime());
        log.info("Available Processors : {}", ManagementFactory.getOperatingSystemMXBean().getAvailableProcessors());
        log.info("System Load Average : {}", ManagementFactory.getOperatingSystemMXBean().getSystemLoadAverage());

        StringBuilder sb = new StringBuilder();
        sb.append("Count of Requests : ").append(getRequests()).append(", ");
        sb.append("Count of Slow Requests : ").append(getSlowRequests()).append(", ");
        sb.append("Count of Unauthorized Requests : ").append(getUnauthorizedRequests()).append(", ");
        sb.append("Count of Access Denied Requests : ").append(getAccessDeniedRequests()).append(", ");
        sb.append("Count of Validation skip Requests : ").append(getSkipValidationRequests()).append(", ");
        sb.append("Errors Count : ").append(getHealUIErrors()).append(", ");
        sb.append("Max response time in ms : ").append(getMaxRespTimeInMillSecs()).append(", ");
        sb.append("Request Threshold : ").append(getRequestThreshold()).append(", ");

        sb.append("400 Status Count : ").append(getStatusCodes().getOrDefault(400, 0)).append(", ");
        sb.append("401 Status Count : ").append(getStatusCodes().getOrDefault(401, 0)).append(", ");
        sb.append("403 Status Count : ").append(getStatusCodes().getOrDefault(403, 0)).append(", ");
        sb.append("404 Status Count : ").append(getStatusCodes().getOrDefault(404, 0)).append(", ");
        sb.append("500 Status Count : ").append(getStatusCodes().getOrDefault(500, 0)).append(", ");
        sb.append("200 Status Count : ").append(getStatusCodes().getOrDefault(200, 0)).append(", ");

        sb.append("Number of Slow Requests in last ").append(schedulerPeriodInMS).append(" ms : ").append(getSlowRequestDetails().size()).append("\n");

        classRedisKeyCountMap.forEach((k, v) -> sb.append("Class: ").append(k).append(", Count: ").append(v).append("\n"));
        log.info("Health metrics details : {}", sb);
    }

    public static void printUsage(Runtime runtime) {
        long total, free, used;
        int mb = 1024 * 1024;

        total = runtime.totalMemory();
        free = runtime.freeMemory();
        used = total - free;
        log.info("Total Memory: {} MB", total / mb);
        log.info("Memory Used: {} MB", used / mb);
        log.info("Memory Free: {} MB", free / mb);
        log.info("Memory Percent Used: {} %", ((double) used / (double) total) * 100);
        log.info("Memory Percent Free: {} %", ((double) free / (double) total) * 100);
    }
}
