package com.appnomic.appsone.api.cache;

import com.appnomic.appsone.api.pojo.*;
import com.appnomic.appsone.api.service.mysql.*;

import java.util.*;

/**
 * <AUTHOR> : 15/1/19
 */
public class MasterCache {
    private MasterCache() {
    }

    /**
     * Implementing <PERSON>ton Implementation
     * ref: {<a href="https://www.journaldev.com/1377/java-singleton-design-pattern-best-practices-examples">...</a>}
     */
    private static class SingletonHelper {
        private static final MasterCache INSTANCE = new MasterCache();
    }

    public static MasterCache getInstance() {
        return SingletonHelper.INSTANCE;

    }

    /**
     * @return
     */
    public List<Account> getAccounts() {
        try {
            return MasterDataService.getAccountList();
        } catch (Exception e) {

        }
        return null;
    }
}
