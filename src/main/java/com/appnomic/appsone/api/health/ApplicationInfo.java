package com.appnomic.appsone.api.health;

import com.appnomic.appsone.api.cache.HealUICache;

import java.util.Map;

public class <PERSON>Info implements ApplicationInfoMBean {


    @Override
    public Map<Integer, Integer> getStatusCodes() {
        return HealUICache.INSTANCE.getStatusCodes();
    }

    @Override
    public int getHttpRequests() {
        return HealUICache.INSTANCE.getRequests();
    }

    @Override
    public int getUnauthorizedRequests() {
        return HealUICache.INSTANCE.getUnauthorizedRequests();
    }

    @Override
    public int getAccessDeniedRequests() {
        return HealUICache.INSTANCE.getAccessDeniedRequests();
    }

    @Override
    public int getSkipValidationRequests() {
        return HealUICache.INSTANCE.getSkipValidationRequests();
    }

    @Override
    public Map<String, Double> getSlowRequest() {
        return HealUICache.INSTANCE.getSlowRequestDetails();
    }

    @Override
    public double getMaxResponseTime() {
        return HealUICache.INSTANCE.getMaxRespTimeInMillSecs();
    }

    @Override
    public int getSlowRequests() {
        return HealUICache.INSTANCE.getSlowRequests();
    }

    @Override
    public int getRequestThreshold() {
        return HealUICache.INSTANCE.getRequestThreshold();
    }

    @Override
    public void setRequestThreshold(int thresholdInSecs) {
        HealUICache.INSTANCE.setRequestThreshold(thresholdInSecs);
    }

    @Override
    public int getHealUIErrors() {
        return HealUICache.INSTANCE.getHealUIErrors();
    }
}
