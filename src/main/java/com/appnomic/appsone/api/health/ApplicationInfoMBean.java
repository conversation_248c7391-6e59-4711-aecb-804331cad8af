package com.appnomic.appsone.api.health;

import java.util.Map;

public interface ApplicationInfoMBean {
    Map<Integer, Integer> getStatusCodes();
    int getHttpRequests();
    int getUnauthorizedRequests();
    int getAccessDeniedRequests();
    int getSkipValidationRequests();
    Map<String, Double> getSlowRequest();

    double getMaxResponseTime();
    int getSlowRequests();
    int getRequestThreshold();
    void setRequestThreshold(int thresholdInSecs);
    int getHealUIErrors();
}
