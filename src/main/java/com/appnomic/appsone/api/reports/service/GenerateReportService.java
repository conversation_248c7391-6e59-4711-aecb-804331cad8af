package com.appnomic.appsone.api.reports.service;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.reports.businesslogic.TriggerReportsBL;
import com.appnomic.appsone.api.reports.pojo.Report;
import com.appnomic.appsone.api.util.CommonUtils;
import com.heal.configuration.pojos.report.TriggeredReportsPojo;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

@Slf4j
public class GenerateReportService {

    public GenericResponse<String> triggerReport(Request request, Response response) {
        GenericResponse<String> reportsResponse = new GenericResponse<>();

        try {
            RequestObject requestObject = new RequestObject(request);

            TriggerReportsBL getReportsBL = new TriggerReportsBL();
            UtilityBean<Report> utilityBean = getReportsBL.clientValidation(requestObject);
            TriggeredReportsPojo triggeredReportsPojo = getReportsBL.serverValidation(utilityBean);
            String syncMessage = getReportsBL.processData(triggeredReportsPojo);

            reportsResponse.setData(syncMessage);
            reportsResponse.setResponseStatus(StatusResponse.SUCCESS.name());
            reportsResponse.setMessage(StatusResponse.SUCCESS.name());

            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (ClientException | ServerException | DataProcessingException e) {
            log.error("Request validation failed. Details: ", e);
            CommonUtils.populateErrorResponse(reportsResponse, response,
                    e.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (Exception e) {
            log.error("Error in retrieving configured reports information. Details: ", e);
            CommonUtils.populateErrorResponse(reportsResponse, response,
                    e.getMessage(), Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
        }

        return reportsResponse;
    }
}
