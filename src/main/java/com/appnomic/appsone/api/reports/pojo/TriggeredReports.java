package com.appnomic.appsone.api.reports.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TriggeredReports {

    private int reportId;
    private String reportName;
    private String fileName;
    private String userTriggered;
    private long triggerTime;
    private long completedTime;
    private long reportStartDate;
    private long reportEndDate;
    private String status;
    private String statusDescription;
    private String emailStatus;
    private String emailAddress;
    private boolean shouldDownload;
    private String applicationName;
    private String fileFormat;
    private String whyDownloadFailed;
    private boolean shouldDelete;
    private String kpiNames;

}
