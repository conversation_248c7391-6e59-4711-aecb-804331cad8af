package com.appnomic.appsone.api.reports.service;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.reports.businesslogic.DeleteReportBL;
import com.appnomic.appsone.api.util.CommonUtils;
import com.heal.configuration.pojos.opensearch.TriggeredReportDetails;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class DeleteReportService {

    public GenericResponse<String> deleteReport(Request request, Response response) {
        GenericResponse<String> reportsResponse = new GenericResponse<>();

        try {
            RequestObject requestObject = new RequestObject(request);

            DeleteReportBL deleteReportBL = new DeleteReportBL();
            UtilityBean<List<String>> utilityBean = deleteReportBL.clientValidation(requestObject);
            List<TriggeredReportDetails> triggeredReportDetails = deleteReportBL.serverValidation(utilityBean);
            String message = deleteReportBL.processData(triggeredReportDetails);

            reportsResponse.setData(message);
            reportsResponse.setResponseStatus(StatusResponse.SUCCESS.name());
            reportsResponse.setMessage(StatusResponse.SUCCESS.name());

            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (ClientException | ServerException | DataProcessingException e) {
            log.error("Request validation failed. Details: ", e);
            CommonUtils.populateErrorResponse(reportsResponse, response,
                    e.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (Exception e) {
            log.error("Error while deleting generated report. Details: ", e);
            CommonUtils.populateErrorResponse(reportsResponse, response,
                    e.getMessage(), Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
        }

        return reportsResponse;
    }

}
