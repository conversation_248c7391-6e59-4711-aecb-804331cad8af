package com.appnomic.appsone.api.reports.businesslogic;

import com.appnomic.appsone.api.businesslogic.BusinessLogic;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.ApplicationRepo;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.reports.dao.opensearch.ReportsSearchRepo;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.api.util.StringUtils;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.Application;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.opensearch.TriggeredReportDetails;
import lombok.extern.slf4j.Slf4j;

import java.nio.file.FileSystems;

/**
 * <AUTHOR>
 */
@Slf4j
public class DownloadReportBL implements BusinessLogic<String, TriggeredReportDetails, String[]> {

    @Override
    public UtilityBean<String> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        //GET AuthToken from cookies for Download API
        String authKey = requestObject.getCookies().get("token");
        if (StringUtils.isEmpty(authKey)) {
            log.error(Constants.MESSAGE_INVALID_AUTH_TOKEN);
            throw new ClientException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(identifier)) {
            log.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        String fileNameString = requestObject.getParams().get(":fileName");
        if (StringUtils.isEmpty(fileNameString)) {
            log.error("Invalid generated file name");
            throw new ClientException("Invalid generated file name");
        }

        String fromTimeString = requestObject.getQueryParams().get(Constants.REQUEST_PARAM_FROM_TIME)[0];
        if (StringUtils.isEmpty(fromTimeString)) {
            log.error("FromTime can't be null/empty.");
            throw new ClientException("FromTime can't be null/empty.");
        }

        String toTimeString = requestObject.getQueryParams().get(Constants.REQUEST_PARAM_TO_TIME)[0];
        if (StringUtils.isEmpty(toTimeString)) {
            log.error("ToTime can't be null/empty.");
            throw new ClientException("ToTime can't be null/empty.");
        }

        long fromTime;
        long toTime;
        try {
            fromTime = Long.parseLong(fromTimeString);
            toTime = Long.parseLong(toTimeString);
        } catch (Exception e) {
            log.error("Exception while parsing time.");
            throw new ClientException("Exception while parsing time.");
        }

        if (fromTime > toTime) {
            log.error("From Time can't be greater than To Time.");
            throw new ClientException("From Time can't be greater than To Time.");
        }

        return UtilityBean.<String>builder()
                .requestPayloadObject(fileNameString)
                .authToken(authKey)
                .accountIdString(identifier)
                .fromTime(fromTime)
                .toTime(toTime)
                .build();
    }

    @Override
    public TriggeredReportDetails serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ServerException(UIMessages.AUTH_KEY_INVALID);
        }

        Account account = new AccountRepo().getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            log.error(UIMessages.INVALID_ACCOUNT_MESSAGE);
            throw new ServerException(UIMessages.INVALID_ACCOUNT_MESSAGE);
        }
        utilityBean.setAccount(account);

        String fileName = utilityBean.getRequestPayloadObject();

        TriggeredReportDetails triggeredReportDetails = new ReportsSearchRepo().getTriggeredReportWithGeneratedFileName(account.getIdentifier(),
                fileName, utilityBean.getFromTime(), utilityBean.getToTime());
        if (triggeredReportDetails == null) {
            log.error("Invalid generated file name {} received", fileName);
            throw new ServerException("Invalid generated file name received");
        }

        if (!triggeredReportDetails.getStatus().equalsIgnoreCase("Completed")) {
            log.error("Report status is {} for file {}. Can't download file unless status is 'Completed'.", triggeredReportDetails.getStatus(), fileName);
            throw new ServerException(String.format("Report status is [%s] for file [%s]. Can't download file unless status is 'Completed'.", triggeredReportDetails.getStatus(), fileName));
        }

        if (triggeredReportDetails.getArguments().containsKey("application_name")) {
            String applicationName = triggeredReportDetails.getArguments().get("application_name");
            ApplicationRepo applicationRepo = new ApplicationRepo();
            Application application = applicationRepo.getApplicationDetailsWithAppName(account.getIdentifier(), applicationName);

            boolean isApplicationAccessible = applicationRepo.getAccessibleApplicationsByUserId(userId, account.getIdentifier())
                    .parallelStream()
                    .map(BasicEntity::getIdentifier)
                    .anyMatch(c -> c.equals(application.getIdentifier()));

            if (!isApplicationAccessible) {
                log.error("User doesn't have access to download the report file.");
                throw new ServerException("User doesn't have access to download the report file.");
            }

        }

        return triggeredReportDetails;
    }

    @Override
    public String[] processData(TriggeredReportDetails triggeredReportDetails) throws DataProcessingException {

        String fileName = triggeredReportDetails.getOutputFileName();
        String filePath = ConfProperties.getString(com.appnomic.appsone.api.reports.common.Constants.FILE_PATH_OUTSIDE_CONTAINER, com.appnomic.appsone.api.reports.common.Constants.DEFAULT_FILE_PATH_OUTSIDE_CONTAINER);

        String completeFileName;

        if (filePath.endsWith(FileSystems.getDefault().getSeparator())) {
            completeFileName = filePath + fileName;
        } else {
            completeFileName = filePath + FileSystems.getDefault().getSeparator() + fileName;
        }

        return new String[]{fileName, completeFileName};
    }

}
