package com.appnomic.appsone.api.reports.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ConfiguredReport {

    private int id;
    private String name;
    private String identifier;
    private List<String> parameters;
    private Map<String, Long> timeRanges;

}
