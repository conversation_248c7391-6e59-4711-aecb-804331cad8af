package com.appnomic.appsone.api.reports.service;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.reports.businesslogic.AccountApplicationKpisBL;
import com.appnomic.appsone.api.reports.pojo.KpiDetailsPojo;
import com.appnomic.appsone.api.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class AccountApplicationKpiService {

    public GenericResponse<List<KpiDetailsPojo>> getAccountApplicationKpis(Request request, Response response) {

        GenericResponse<List<KpiDetailsPojo>> kpiDetailsResponse = new GenericResponse<>();

        try {
            RequestObject requestObject = new RequestObject(request);

            AccountApplicationKpisBL accountKpisBL = new AccountApplicationKpisBL();
            UtilityBean<Object> utilityBean = accountKpisBL.clientValidation(requestObject);
            utilityBean = accountKpisBL.serverValidation(utilityBean);
            List<KpiDetailsPojo> kpiDetailsPojo = accountKpisBL.processData(utilityBean);

            kpiDetailsResponse.setData(kpiDetailsPojo);
            kpiDetailsResponse.setResponseStatus(StatusResponse.SUCCESS.name());
            kpiDetailsResponse.setMessage(StatusResponse.SUCCESS.name());

            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (ClientException | ServerException | DataProcessingException e) {
            log.error("Request validation failed. Details: ", e);
            CommonUtils.populateErrorResponse(kpiDetailsResponse, response,
                    e.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (Exception e) {
            log.error("Error in retrieving kpi details mapped to account. Details: ", e);
            CommonUtils.populateErrorResponse(kpiDetailsResponse, response,
                    e.getMessage(), Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
        }

        return kpiDetailsResponse;
    }
}
