package com.appnomic.appsone.api.reports.businesslogic;

import com.appnomic.appsone.api.businesslogic.BusinessLogic;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.ApplicationRepo;
import com.appnomic.appsone.api.dao.redis.UserRepo;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.reports.dao.opensearch.ReportsSearchRepo;
import com.appnomic.appsone.api.reports.pojo.TriggeredReports;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.api.util.StringUtils;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.User;
import com.heal.configuration.pojos.opensearch.TriggeredReportDetails;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.nio.file.FileSystems;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class GetTriggeredReportsBL implements BusinessLogic<String, UtilityBean<String>, List<TriggeredReports>> {

    private String userIdentifier;
    private String accountIdentifier;

    private static final String adminUserIdentifier = ConfProperties.getString(Constants.ADMIN_USER_IDENTIFIER, Constants.DEFAULT_ADMIN_USER_IDENTIFIER);

    @Override
    public UtilityBean<String> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION_HEADER);
        if (StringUtils.isEmpty(authKey)) {
            log.error(Constants.MESSAGE_INVALID_AUTH_TOKEN);
            throw new ClientException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(identifier)) {
            log.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        String fromTimeString = requestObject.getQueryParams().get(Constants.REQUEST_PARAM_FROM_TIME)[0];
        if (StringUtils.isEmpty(fromTimeString)) {
            log.error("FromTime can't be null/empty.");
            throw new ClientException("FromTime can't be null/empty.");
        }

        String toTimeString = requestObject.getQueryParams().get(Constants.REQUEST_PARAM_TO_TIME)[0];
        if (StringUtils.isEmpty(toTimeString)) {
            log.error("ToTime can't be null/empty.");
            throw new ClientException("ToTime can't be null/empty.");
        }

        long fromTime;
        long toTime;
        try {
            fromTime = Long.parseLong(fromTimeString);
            toTime = Long.parseLong(toTimeString);
        } catch (Exception e) {
            log.error("Exception while parsing time.");
            throw new ClientException("Exception while parsing time.");
        }

        if (fromTime > toTime) {
            log.error("From Time can't be greater than To Time.");
            throw new ClientException("From Time can't be greater than To Time.");
        }

        return UtilityBean.<String>builder()
                .authToken(authKey)
                .requestPayloadObject(identifier)
                .fromTime(fromTime)
                .toTime(toTime)
                .build();
    }

    @Override
    public UtilityBean<String> serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        AccountRepo accountRepo = new AccountRepo();
        userIdentifier = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userIdentifier == null) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ServerException(UIMessages.AUTH_KEY_INVALID);
        }

        Account account = accountRepo.getAccount(utilityBean.getRequestPayloadObject());
        if (account == null) {
            log.error(UIMessages.INVALID_ACCOUNT_MESSAGE);
            throw new ServerException(UIMessages.INVALID_ACCOUNT_MESSAGE);
        }
        accountIdentifier = account.getIdentifier();

        utilityBean.setAccount(account);
        return utilityBean;
    }

    @Override
    public List<TriggeredReports> processData(UtilityBean<String> utilityBean) throws DataProcessingException {
        List<TriggeredReports> triggeredReportsList = new ArrayList<>();

        List<TriggeredReportDetails> triggeredReports = new ReportsSearchRepo().getTriggeredReports(utilityBean.getAccount().getIdentifier(),
                utilityBean.getFromTime(), utilityBean.getToTime());
        if (triggeredReports.isEmpty()) {
            log.warn("Reports yet to be triggered.");
            return triggeredReportsList;
        }

        Set<BasicEntity> accessibleApplicationList = new ApplicationRepo().getAccessibleApplicationsByUserId(userIdentifier, accountIdentifier);

        try {
            triggeredReportsList = triggeredReports.parallelStream()
                    .map(r -> {
                        String applicationName = "";
                        String kpiNames = "";
                        boolean canDownloadReport = true;
                        String whyDownloadFailed = null;

                        Map<String, String> argsMap = r.getArguments();
                        //Check user has access to download file or not wrt the application-user mapping
                        if (argsMap.containsKey("application_name")) {
                            applicationName = argsMap.get("application_name");
                            canDownloadReport = accessibleApplicationList.parallelStream()
                                    .anyMatch(a -> a.getName().equalsIgnoreCase(argsMap.get("application_name")));
                            if (!canDownloadReport) {
                                whyDownloadFailed = com.appnomic.appsone.api.reports.common.Constants.FILE_DOWNLOAD_USER_ACCESS_DENIED;
                            }
                            if (argsMap.containsKey("kpi_names")) {
                                kpiNames = argsMap.get("kpi_names");
                            }
                        }

                        long startTime = Long.parseLong(argsMap.getOrDefault("start_time", "0"));
                        long endTime = Long.parseLong(argsMap.getOrDefault("end_time", "0"));


                        User user = new UserRepo().getUser(r.getTriggeredUserDetailsId());
                        String userName;
                        if (user != null)
                            userName = user.getUserName();
                        else
                            userName = r.getTriggeredUserDetailsId();

                        //Check file exist or not
                        String fileName = r.getOutputFileName();
                        String filePath = ConfProperties.getString(com.appnomic.appsone.api.reports.common.Constants.FILE_PATH_OUTSIDE_CONTAINER, com.appnomic.appsone.api.reports.common.Constants.DEFAULT_FILE_PATH_OUTSIDE_CONTAINER);
                        String completeFileName;
                        if (filePath.endsWith(FileSystems.getDefault().getSeparator())) {
                            completeFileName = filePath + fileName;
                        } else {
                            completeFileName = filePath + FileSystems.getDefault().getSeparator() + fileName;
                        }

                        boolean shouldDelete = r.getTriggeredUserDetailsId().equalsIgnoreCase(userIdentifier)
                                || userIdentifier.equalsIgnoreCase(adminUserIdentifier);
                        File f = new File(completeFileName);
                        if (!f.exists()) {
                            canDownloadReport = false;
                            whyDownloadFailed = com.appnomic.appsone.api.reports.common.Constants.FILE_DOWNLOAD_FILE_MISSING;
                            shouldDelete = false;
                        }

                        return TriggeredReports.builder()
                                .reportId(r.getReportId())
                                .reportName(r.getReportName())
                                .fileName(fileName)
                                .userTriggered(userName)
                                .triggerTime(r.getTriggeredTime())
                                .completedTime(r.getCompletedTime())
                                .status(r.getStatus())
                                .statusDescription(r.getStatusDescription() == null ? "" : r.getStatusDescription())
                                .emailStatus(r.getEmailAddress() == null || r.getEmailAddress().isEmpty() ? "Not Configured" : "Configured")
                                .emailAddress(r.getEmailAddress().parallelStream().collect(Collectors.joining(", ")))
                                .shouldDownload(canDownloadReport)
                                .applicationName(applicationName)
                                .fileFormat(r.getOutputFileName().split("\\.")[1])
                                .reportStartDate(startTime)
                                .reportEndDate(endTime)
                                .whyDownloadFailed(whyDownloadFailed)
                                .shouldDelete(shouldDelete)
                                .kpiNames(kpiNames)
                                .build();
                    }).filter(Objects::nonNull).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Exception while making triggered reports list.", e);
            throw new DataProcessingException(e.getMessage());
        }

        return triggeredReportsList;
    }
}
