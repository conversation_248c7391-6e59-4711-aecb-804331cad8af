package com.appnomic.appsone.api.reports.dao.opensearch;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.manager.OpenSearchConnectionManager;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.opeasearchquery.helper.OpenSearchQueryHelper;
import com.appnomic.appsone.opeasearchquery.queryopts.QueryOptions;
import com.appnomic.appsone.opeasearchquery.results.NameValuePair;
import com.appnomic.appsone.opeasearchquery.results.RawDocumentResults;
import com.heal.configuration.pojos.opensearch.TriggeredReportDetails;
import com.heal.configuration.util.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.action.search.SearchRequest;
import org.opensearch.action.search.SearchResponse;
import org.opensearch.client.RequestOptions;
import org.opensearch.client.RestHighLevelClient;
import org.opensearch.client.indices.GetIndexRequest;
import org.opensearch.client.indices.GetIndexResponse;
import org.opensearch.index.query.TermQueryBuilder;
import org.opensearch.index.reindex.BulkByScrollResponse;
import org.opensearch.index.reindex.DeleteByQueryRequest;
import org.opensearch.search.aggregations.AggregationBuilders;
import org.opensearch.search.aggregations.BucketOrder;
import org.opensearch.search.aggregations.bucket.terms.ParsedTerms;
import org.opensearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.opensearch.search.builder.SearchSourceBuilder;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class ReportsSearchRepo {

    public List<TriggeredReportDetails> getTriggeredReports(String accountIdentifier, long fromTime, long toTime) {
        String indexName = Constants.INDEX_PREFIX_REPORTS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_REPORTS);
            if (elasticClient == null) {
                return null;
            }
            List<String> indexNames = new ArrayList<>();
            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexName + "_" + date));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("triggeredTime")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .fetchAllRecords(true)
                    .build();

            log.debug("OS query for fetching triggered reports: {}", queryOptions);
            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);

            if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty())
                return Collections.emptyList();

            return rawDocuments.getDocuments().parallelStream()
                    .map(doc -> {
                        try {
                            return CommonUtils.getObjectMapper().readValue(doc, TriggeredReportDetails.class);
                        } catch (Exception e) {
                            log.error("Error occurred while mapping OpenSearch reports data to TriggeredReportDetails pojo for index {}.", indexName, e);
                            HealUICache.INSTANCE.updateHealUIErrors(1);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}", indexName, e);
        }
        return Collections.emptyList();
    }

    public List<TriggeredReportDetails> getTriggeredReportsWithReportId(String accountIdentifier, int reportId, long fromTime, long toTime) {
        String indexName = Constants.INDEX_PREFIX_REPORTS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_REPORTS);
            if (elasticClient == null) {
                return null;
            }
            List<String> indexNames = new ArrayList<>();
            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexName + "_" + date));

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("reportId", String.valueOf(reportId)));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("triggeredTime")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .fetchAllRecords(true)
                    .matchAllFields(Optional.of(matchFields))
                    .build();

            log.debug("OS query for fetching triggered reports: {}", queryOptions);
            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);

            if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty())
                return Collections.emptyList();

            return rawDocuments.getDocuments().parallelStream()
                    .map(doc -> {
                        try {
                            return CommonUtils.getObjectMapper().readValue(doc, TriggeredReportDetails.class);
                        } catch (Exception e) {
                            log.error("Error occurred while mapping OpenSearch reports data to TriggeredReportDetails pojo for index {}.", indexName, e);
                            HealUICache.INSTANCE.updateHealUIErrors(1);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}", indexName, e);
        }
        return Collections.emptyList();
    }

    public TriggeredReportDetails getTriggeredReportWithGeneratedFileName(String accountIdentifier, String generatedFileName, long fromTime, long toTime) {
        String indexName = Constants.INDEX_PREFIX_REPORTS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_REPORTS);
            if (elasticClient == null) {
                return null;
            }
            List<String> indexNames = new ArrayList<>();
            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexName + "_" + date));

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("outputFileName", generatedFileName));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("triggeredTime")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .fetchAllRecords(true)
                    .matchAllFields(Optional.of(matchFields))
                    .build();

            log.debug("OS query for fetching triggered report: {}", queryOptions);
            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);

            if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty())
                return null;

            return CommonUtils.getObjectMapper().readValue(rawDocuments.getDocuments().get(0), TriggeredReportDetails.class);
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}", indexName, e);
        }
        return null;
    }

    public boolean deleteTriggeredReportWithGeneratedFileName(String accountIdentifier, String generatedFileName, long fromTime, long toTime) {
        String indexName = Constants.INDEX_PREFIX_REPORTS + "_" + accountIdentifier.toLowerCase();
        try {
            RestHighLevelClient elasticClient = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, Constants.INDEX_PREFIX_REPORTS);
            if (elasticClient == null) {
                return false;
            }
            List<String> indexNames = new ArrayList<>();
            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexName + "_" + date));

            List<String> indicesPresent = OpenSearchQueryHelper.checkAndGetExistingIndex(QueryOptions.builder().indexNames(indexNames).build(), elasticClient);
            if (indicesPresent.size() < indexNames.size()) {
                List<String> differences = indexNames.stream().filter((element) -> !indexNames.contains(element)).collect(Collectors.toList());
                log.warn("{} indexes removed from list of indexes as they do not exist in OpenSearch cluster.", differences);
            }

            if (indicesPresent.isEmpty()) {
                log.error("No {} indices found for selected time range in OpenSearch.", indexNames);
                return false;
            }

            DeleteByQueryRequest deleteByQueryRequest =
                    new DeleteByQueryRequest(indicesPresent.toArray(new String[0]));
            deleteByQueryRequest.setQuery(new TermQueryBuilder("_id", generatedFileName));
            deleteByQueryRequest.setRefresh(true);

            BulkByScrollResponse bulkByScrollResponse = elasticClient.deleteByQuery(deleteByQueryRequest, RequestOptions.DEFAULT);

            long deletedDocs = bulkByScrollResponse.getDeleted();
            if (deletedDocs != 1) {
                log.error("No report with doc id {} found in OpenSearch for delete operation.", generatedFileName);
                return false;
            } else return true;
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching data from index {}", indexName, e);
        }
        return false;
    }

    public List<String> getAvailableIndices(String accountIdentifier) {
        try {
            RestHighLevelClient client = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, "");
            if (client == null) {
                return new ArrayList<>();
            }

            GetIndexRequest request = new GetIndexRequest("*");
            GetIndexResponse response = client.indices().get(request, RequestOptions.DEFAULT);
            List<String> allIndices = Arrays.asList(response.getIndices());
            return allIndices.parallelStream().filter(c -> c.contains(accountIdentifier.toLowerCase())).collect(Collectors.toList());
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching all available index details");
        }
        return new ArrayList<>();
    }

    public long getStartTimeStamp(String indexName, String aggFieldName, String accountIdentifier) {
        try {
            RestHighLevelClient client = OpenSearchConnectionManager.INSTANCE.getElasticClient(accountIdentifier, indexName);
            if (client == null) {
                return 0L;
            }

            SearchRequest searchReq = new SearchRequest(indexName);
            TermsAggregationBuilder kpiAgg = AggregationBuilders
                    .terms(aggFieldName)
                    .field(aggFieldName)
                    .order(BucketOrder.key(true))
                    .size(1);

            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.aggregation(kpiAgg);
            searchSourceBuilder.size(0);
            searchReq.source(searchSourceBuilder);

            log.debug("OS query for fetching index start timeStamp: {}", searchReq);

            SearchResponse response = client.search(searchReq, RequestOptions.DEFAULT);
            ParsedTerms kpiAggregation = response.getAggregations().get(aggFieldName);

            if (!kpiAggregation.getBuckets().isEmpty() && kpiAggregation.getBuckets().get(0).getKey() != null) {
                return (long) kpiAggregation.getBuckets().get(0).getKey();
            }
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error in fetching index start timeStamp. ", e);
        }
        return 0L;
    }

}
