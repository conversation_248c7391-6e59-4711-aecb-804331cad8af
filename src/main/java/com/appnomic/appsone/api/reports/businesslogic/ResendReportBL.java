package com.appnomic.appsone.api.reports.businesslogic;

import com.appnomic.appsone.api.businesslogic.BusinessLogic;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.reports.dao.opensearch.ReportsSearchRepo;
import com.appnomic.appsone.api.reports.pojo.EmailFilePojo;
import com.appnomic.appsone.api.util.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.gson.Gson;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.opensearch.TriggeredReportDetails;
import com.heal.configuration.pojos.report.ResendMailPojo;
import com.heal.configuration.pojos.report.TriggeredReportsPojo;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.bouncycastle.crypto.InvalidCipherTextException;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class ResendReportBL implements BusinessLogic<EmailFilePojo, List<ResendMailPojo>, String> {

    private static final String adminUserIdentifier = ConfProperties.getString(Constants.ADMIN_USER_IDENTIFIER, Constants.DEFAULT_ADMIN_USER_IDENTIFIER);

    @Override
    public UtilityBean<EmailFilePojo> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION_HEADER);
        if (StringUtils.isEmpty(authKey)) {
            log.error(Constants.MESSAGE_INVALID_AUTH_TOKEN);
            throw new ClientException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(identifier)) {
            log.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        String fromTimeString = requestObject.getQueryParams().get(Constants.REQUEST_PARAM_FROM_TIME)[0];
        if (StringUtils.isEmpty(fromTimeString)) {
            log.error("FromTime can't be null/empty.");
            throw new ClientException("FromTime can't be null/empty.");
        }

        String toTimeString = requestObject.getQueryParams().get(Constants.REQUEST_PARAM_TO_TIME)[0];
        if (StringUtils.isEmpty(toTimeString)) {
            log.error("ToTime can't be null/empty.");
            throw new ClientException("ToTime can't be null/empty.");
        }

        long fromTime;
        long toTime;
        try {
            fromTime = Long.parseLong(fromTimeString);
            toTime = Long.parseLong(toTimeString);
        } catch (Exception e) {
            log.error("Exception while parsing time.");
            throw new ClientException("Exception while parsing time.");
        }

        if (fromTime > toTime) {
            log.error("From Time can't be greater than To Time.");
            throw new ClientException("From Time can't be greater than To Time.");
        }
        String decryptedRequestBody;
        try {
            decryptedRequestBody = new AECSBouncyCastleUtil().decrypt(requestObject.getBody());
        } catch (InvalidCipherTextException e) {
            throw new ClientException(e, e.getMessage());
        }
        EmailFilePojo emailFilePojo;
        try {
            emailFilePojo = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(decryptedRequestBody, new TypeReference<EmailFilePojo>() {
            });
        } catch (IOException e) {
            log.error("Exception encountered while parsing the JSON request body. Details: " + e);
            throw new ClientException("Error in parsing the JSON request body");
        }

        return UtilityBean.<EmailFilePojo>builder()
                .authToken(authKey)
                .accountIdString(identifier)
                .requestPayloadObject(emailFilePojo)
                .fromTime(fromTime)
                .toTime(toTime)
                .build();
    }

    @Override
    public List<ResendMailPojo> serverValidation(UtilityBean<EmailFilePojo> utilityBean) throws ServerException {

        String userIdentifier = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userIdentifier == null) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ServerException(UIMessages.AUTH_KEY_INVALID);
        }

        Account account = new AccountRepo().getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            log.error(UIMessages.INVALID_ACCOUNT_MESSAGE);
            throw new ServerException(UIMessages.INVALID_ACCOUNT_MESSAGE);
        }
        utilityBean.setAccount(account);

        EmailFilePojo emailFilePojo = utilityBean.getRequestPayloadObject();
        List<String> resendFileNames = emailFilePojo.getFileNames();
        List<String> mailIds = emailFilePojo.getEmailIds();

        List<TriggeredReportDetails> triggeredReportDetails = new ReportsSearchRepo().getTriggeredReports(utilityBean.getAccount().getIdentifier(),
                utilityBean.getFromTime(), utilityBean.getToTime());
        if (triggeredReportDetails.isEmpty()) {
            log.error("Reports yet to be triggered.");
            throw new ServerException("No triggered reports found in db.");
        }

        List<String> allFilesAccessibleForUser = triggeredReportDetails.parallelStream()
                .filter(c -> c.getStatus().equalsIgnoreCase("Completed"))
                .filter(c -> c.getTriggeredUserDetailsId().equalsIgnoreCase(userIdentifier)
                        || userIdentifier.equalsIgnoreCase(adminUserIdentifier))
                .map(TriggeredReportDetails::getOutputFileName)
                .collect(Collectors.toList());

        List<String> validFileNames = resendFileNames.parallelStream()
                .filter(allFilesAccessibleForUser::contains).collect(Collectors.toList());
        if (validFileNames.isEmpty()) {
            log.error("User doesn't have access to resend the selected reports.");
            throw new ServerException("User doesn't have access to resend the selected reports.");
        }

        ArrayList<String> uniqueFileNames = new ArrayList<>(resendFileNames);
        uniqueFileNames.removeAll(validFileNames);
        if (!uniqueFileNames.isEmpty()) {
            log.warn("No records found for file names [{}]. Hence skipping these.", uniqueFileNames);
        }

        List<ResendMailPojo> resendMailPojoList = new ArrayList<>();
        try {
            triggeredReportDetails.parallelStream()
                    .filter(c -> validFileNames.contains(c.getOutputFileName()))
                    .forEach(c -> resendMailPojoList.add(ResendMailPojo.builder()
                            .triggeredReportsPojo(TriggeredReportsPojo.builder()
                                    .reportId(c.getReportId())
                                    .reportName(c.getReportName())
                                    .outputFileName(c.getOutputFileName())
                                    .outputFilePath(c.getOutputFilePath())
                                    .triggeredTime(c.getTriggeredTime())
                                    .completedTime(c.getCompletedTime())
                                    .emailAddress(c.getEmailAddress().isEmpty() ? null : c.getEmailAddress().parallelStream().collect(Collectors.joining(",")))
                                    .accountIdentifier(account.getIdentifier())
                                    .arguments(c.getArguments())
                                    .build())
                            .mailIds(mailIds.parallelStream().map(String::trim).collect(Collectors.toList())).build()));
        } catch (Exception e) {
            log.error("Exception while making Resend mail pojo. ", e);
            throw new ServerException("Exception while making Resend mail pojo.");
        }

        return resendMailPojoList;
    }

    @Override
    public String processData(List<ResendMailPojo> configData) throws DataProcessingException {

        Gson gson = new Gson();
        String gString = gson.toJson(configData, List.class);

        String url = ConfProperties.getString(Constants.RESEND_MAIL_DATA_SERVICE_URL, Constants.DEFAULT_RESEND_MAIL_DATA_SERVICE_URL);

        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(url);

            StringEntity entity = new StringEntity(gString);
            httpPost.setEntity(entity);
            httpPost.setHeader("Accept", "application/json");
            httpPost.setHeader("Content-type", "application/json");

            CloseableHttpResponse response = client.execute(httpPost);
            if (response.getStatusLine().getStatusCode() != 200) {
                log.error("Received response with status code {} from Report data service", response.getStatusLine().getStatusCode());
                throw new DataProcessingException(String.format("Received response with status code [%s] during rest call to Report data service url [%s]. Please look into the Report data service logs.", response.getStatusLine().getStatusCode(), url));
            }
        } catch (IOException e) {
            log.error("Exception while rest call to Report data service url [{}]. Please look into the Report data service logs.", url);
            throw new DataProcessingException(e, String.format("Exception while rest call to Report data service url [%s]. Please look into the Report data service logs.", url));
        }

        return "Reports resent successfully to the given mail IDs.";
    }
}
