package com.appnomic.appsone.api.reports.service;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.reports.businesslogic.GetReportsBL;
import com.appnomic.appsone.api.reports.pojo.ConfiguredReport;
import com.appnomic.appsone.api.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

import java.util.List;

@Slf4j
public class GetReportsService {

    public GenericResponse<List<ConfiguredReport>> getReports(Request request, Response response) {
        GenericResponse<List<ConfiguredReport>> reportsResponse = new GenericResponse<>();
        try {
            RequestObject requestObject = new RequestObject(request);

            GetReportsBL getReportsBL = new GetReportsBL();
            UtilityBean<Object> utilityBean = getReportsBL.clientValidation(requestObject);
            utilityBean = getReportsBL.serverValidation(utilityBean);
            List<ConfiguredReport> configuredReports = getReportsBL.processData(utilityBean);

            reportsResponse.setData(configuredReports);
            reportsResponse.setResponseStatus(StatusResponse.SUCCESS.name());
            reportsResponse.setMessage(StatusResponse.SUCCESS.name());

            response.status(Constants.SUCCESS_STATUS_CODE);
        }
         catch (ClientException | ServerException | DataProcessingException e) {
            log.error("Request validation failed. Details: ", e);
            CommonUtils.populateErrorResponse(reportsResponse, response,
                    e.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (Exception e) {
            log.error("Error in retrieving configured reports information. Details: ", e);
            CommonUtils.populateErrorResponse(reportsResponse, response,
                    e.getMessage(), Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
        }

        return reportsResponse;
    }
}
