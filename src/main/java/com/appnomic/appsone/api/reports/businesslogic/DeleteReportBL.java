package com.appnomic.appsone.api.reports.businesslogic;

import com.appnomic.appsone.api.businesslogic.BusinessLogic;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.reports.dao.opensearch.ReportsSearchRepo;
import com.appnomic.appsone.api.reports.pojo.EmailFilePojo;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.api.util.StringUtils;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.opensearch.TriggeredReportDetails;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.nio.file.FileSystems;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class DeleteReportBL implements BusinessLogic<List<String>, List<TriggeredReportDetails>, String> {

    private static final String adminUserIdentifier = ConfProperties.getString(Constants.ADMIN_USER_IDENTIFIER, Constants.DEFAULT_ADMIN_USER_IDENTIFIER);
    private String accountIdentifier;

    private long fromTime;
    private long toTime;

    @Override
    public UtilityBean<List<String>> clientValidation(RequestObject requestObject) throws ClientException {

        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION_HEADER);
        if (StringUtils.isEmpty(authKey)) {
            log.error(Constants.MESSAGE_INVALID_AUTH_TOKEN);
            throw new ClientException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(identifier)) {
            log.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        String fromTimeString = requestObject.getQueryParams().get(Constants.REQUEST_PARAM_FROM_TIME)[0];
        if (StringUtils.isEmpty(fromTimeString)) {
            log.error("FromTime can't be null/empty.");
            throw new ClientException("FromTime can't be null/empty.");
        }

        String toTimeString = requestObject.getQueryParams().get(Constants.REQUEST_PARAM_TO_TIME)[0];
        if (StringUtils.isEmpty(toTimeString)) {
            log.error("ToTime can't be null/empty.");
            throw new ClientException("ToTime can't be null/empty.");
        }

        try {
            fromTime = Long.parseLong(fromTimeString);
            toTime = Long.parseLong(toTimeString);
        } catch (Exception e) {
            log.error("Exception while parsing time.");
            throw new ClientException("Exception while parsing time.");
        }

        if (fromTime > toTime) {
            log.error("From Time can't be greater than To Time.");
            throw new ClientException("From Time can't be greater than To Time.");
        }

        EmailFilePojo emailFilePojo;
        try {
            emailFilePojo = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(requestObject.getBody(), new TypeReference<EmailFilePojo>() {
            });
        } catch (IOException e) {
            log.error("Exception encountered while parsing the JSON request body. Details: " + e);
            throw new ClientException("Error in parsing the JSON request body");
        }


        return UtilityBean.<List<String>>builder()
                .authToken(authKey)
                .accountIdString(identifier)
                .requestPayloadObject(emailFilePojo.getFileNames())
                .build();
    }

    @Override
    public List<TriggeredReportDetails> serverValidation(UtilityBean<List<String>> utilityBean) throws ServerException {

        String userIdentifier = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userIdentifier == null) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ServerException(UIMessages.AUTH_KEY_INVALID);
        }

        Account account = new AccountRepo().getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            log.error(UIMessages.INVALID_ACCOUNT_MESSAGE);
            throw new ServerException(UIMessages.INVALID_ACCOUNT_MESSAGE);
        }
        accountIdentifier = account.getIdentifier();

        List<String> deleteFileNames = utilityBean.getRequestPayloadObject();

        List<TriggeredReportDetails> triggeredReportDetails = new ReportsSearchRepo().getTriggeredReports(accountIdentifier,
                fromTime, toTime);
        if (triggeredReportDetails.isEmpty()) {
            log.error("No generated reports found.");
            throw new ServerException("No generated reports found.");
        }

        List<String> allFilesAccessibleForUser = triggeredReportDetails.parallelStream()
                .filter(c -> c.getStatus().equalsIgnoreCase("Completed"))
                .filter(c -> c.getTriggeredUserDetailsId().equalsIgnoreCase(userIdentifier)
                        || userIdentifier.equalsIgnoreCase(adminUserIdentifier))
                .map(TriggeredReportDetails::getOutputFileName)
                .collect(Collectors.toList());

        List<String> validFileNames = deleteFileNames.parallelStream()
                .filter(allFilesAccessibleForUser::contains).collect(Collectors.toList());
        if (validFileNames.isEmpty()) {
            log.error("User doesn't have access to delete the selected reports.");
            throw new ServerException("User doesn't have access to delete the selected reports.");
        }

        ArrayList<String> uniqueFileNames = new ArrayList<>(deleteFileNames);
        uniqueFileNames.removeAll(validFileNames);
        if (!uniqueFileNames.isEmpty()) {
            log.warn("No records found for file names [{}]. Hence skipping these.", uniqueFileNames);
        }

        return triggeredReportDetails.parallelStream().filter(c -> validFileNames.contains(c.getOutputFileName())).collect(Collectors.toList());
    }

    @Override
    public String processData(List<TriggeredReportDetails> triggeredReportDetails) throws DataProcessingException {
        ReportsSearchRepo reportsSearchRepo = new ReportsSearchRepo();
        try {
            triggeredReportDetails.forEach(triggeredReportsBean -> {
                String fileName = triggeredReportsBean.getOutputFileName();
                String filePath = ConfProperties.getString(com.appnomic.appsone.api.reports.common.Constants.FILE_PATH_OUTSIDE_CONTAINER, com.appnomic.appsone.api.reports.common.Constants.DEFAULT_FILE_PATH_OUTSIDE_CONTAINER);

                String completeFileName;
                if (filePath.endsWith(FileSystems.getDefault().getSeparator())) {
                    completeFileName = filePath + fileName;
                } else {
                    completeFileName = filePath + FileSystems.getDefault().getSeparator() + fileName;
                }

                File file = new File(completeFileName);
                if (file.delete()) {
                    log.info("{} file is deleted successfully from host!", completeFileName);
                    boolean reportDeletedFromOs = reportsSearchRepo.deleteTriggeredReportWithGeneratedFileName(accountIdentifier,
                            triggeredReportsBean.getOutputFileName(), fromTime, toTime);
                    if (!reportDeletedFromOs) {
                        log.error("Couldn't delete document {} from OpenSearch.", triggeredReportsBean.getOutputFileName());
                        throw new RuntimeException(String.format("Couldn't delete document %s from OpenSearch. Check logs.", triggeredReportsBean.getOutputFileName()));
                    } else {
                        log.info("{} document is deleted successfully from OpenSearch!", triggeredReportsBean.getOutputFileName());
                    }
                } else {
                    log.error("Unable to delete the file {}.", completeFileName);
                    throw new RuntimeException(String.format("Unable to delete the file %s. Check logs.", completeFileName));
                }
            });
        } catch (Exception e) {
            log.error("Exception while deleting generated report files. ", e);
            throw new DataProcessingException("Exception while deleting generated report files. " + e);
        }
        return "Files deleted successfully.";
    }
}
