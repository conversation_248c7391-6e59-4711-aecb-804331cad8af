package com.appnomic.appsone.api.reports.businesslogic;

import com.appnomic.appsone.api.businesslogic.BusinessLogic;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.redis.*;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.reports.pojo.KpiDetailsPojo;
import com.appnomic.appsone.api.util.StringUtils;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.heal.configuration.pojos.*;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class AccountApplicationKpisBL implements BusinessLogic<Object, UtilityBean<Object>, List<KpiDetailsPojo>> {

    @Override
    public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION_HEADER);
        if (StringUtils.isEmpty(authKey)) {
            log.error(Constants.MESSAGE_INVALID_AUTH_TOKEN);
            throw new ClientException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(identifier)) {
            log.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        String appIdString = requestObject.getParams().get(Constants.REQUEST_PARAM_APPLICATION_ID);
        if (StringUtils.isEmpty(appIdString)) {
            log.error("Request Exception : Application Id is null or empty.");
            throw new ClientException("Request Exception : Application Id is null or empty.");
        }

        int appId;
        try {
            appId = Integer.parseInt(appIdString);
        } catch (Exception e) {
            log.error("Exception while parsing application id.");
            throw new ClientException("Exception while parsing application id.");
        }

        return UtilityBean.builder()
                .authToken(authKey)
                .accountIdString(identifier)
                .applicationId(appId)
                .build();
    }

    @Override
    public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ServerException(UIMessages.AUTH_KEY_INVALID);
        }

        Account account = new AccountRepo().getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            log.error(UIMessages.INVALID_ACCOUNT_MESSAGE);
            throw new ServerException(UIMessages.INVALID_ACCOUNT_MESSAGE);
        }
        utilityBean.setAccount(account);

        Application application = new ApplicationRepo().getApplicationDetailsWithAppId(account.getIdentifier(), utilityBean.getApplicationId());
        utilityBean.setApplication(application);

        return utilityBean;
    }

    @Override
    public List<KpiDetailsPojo> processData(UtilityBean<Object> configData) throws DataProcessingException {
        String accountIdentifier = configData.getAccount().getIdentifier();
        String appIdentifier = configData.getApplication().getIdentifier();

        List<BasicEntity> serviceList = HealUICache.INSTANCE.getApplicationServiceList(accountIdentifier, appIdentifier);
        if (serviceList.isEmpty()) {
            log.error("No services mapped to selected application {} found in redis.", configData.getApplication().getName());
            throw new DataProcessingException(String.format("No services mapped to selected application {%s} found in redis.", configData.getApplication().getName()));
        }

        Set<String> clusterIdentifiers = new HashSet<>();
        serviceList.forEach(c -> HealUICache.INSTANCE.getServiceInstanceList(accountIdentifier, c.getIdentifier(), true)
                .forEach(d -> {
                    if (d.getClusterId() == 0) {
                        clusterIdentifiers.add(d.getIdentifier());
                    }
                }));
        if (clusterIdentifiers.isEmpty()) {
            log.error("No Clusters mapped to selected application {}'s services found in redis.", configData.getApplication().getName());
            throw new DataProcessingException(String.format("No services mapped to selected application {%s}'s services found in redis.", configData.getApplication().getName()));
        }

        InstanceRepo instanceRepo = new InstanceRepo();
        Map<String, String> componentNameTypeMap = new HashMap<>();
        clusterIdentifiers.forEach(c -> {
            CompInstClusterDetails compInstClusterDetails = instanceRepo.getInstanceDetailsWithInstIdentifier(accountIdentifier, c);
            componentNameTypeMap.put(compInstClusterDetails.getComponentName(), compInstClusterDetails.getComponentTypeName());
        });
        if (componentNameTypeMap.isEmpty()) {
            log.error("No Components mapped to selected application {}'s instances found in redis.", configData.getApplication().getName());
            throw new DataProcessingException(String.format("No Components mapped to selected application {%s}'s instances found in redis.", configData.getApplication().getName()));
        }


        ComponentRepo componentRepo = new ComponentRepo();
        Map<String, Set<BasicKpiEntity>> componentTypeKpiMapping = new HashMap<>();
        componentNameTypeMap.forEach((componentName, componentTypeName) -> {
            List<BasicKpiEntity> kpiEntities = componentRepo.getComponentKpis(accountIdentifier, componentName);
            if (kpiEntities.isEmpty()) {
                log.warn("No Kpis mapped to component {}'s found in redis.", componentName);
                return;
            }

            kpiEntities.addAll(componentTypeKpiMapping.getOrDefault(componentTypeName, new HashSet<>()));
            componentTypeKpiMapping.put(componentTypeName, new HashSet<>(kpiEntities));
        });

        List<KpiDetailsPojo> result = new ArrayList<>();
        componentTypeKpiMapping.forEach((componentTypeName, kpiList) -> result.addAll(kpiList.parallelStream().map(c -> KpiDetailsPojo.builder()
                        .id(c.getId())
                        .name(c.getName())
                        .identifier(c.getIdentifier())
                        .kpiComponentType(componentTypeName)
                        .categoryId(c.getCategoryDetails().getId())
                        .categoryName(c.getCategoryDetails().getName())
                        .categoryIdentifier(c.getCategoryDetails().getIdentifier())
                        .kpiType(c.getType())
                        .build())
                .collect(Collectors.toList())));

        return result;
    }
}
