package com.appnomic.appsone.api.reports.service;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.reports.businesslogic.DownloadReportBL;
import com.appnomic.appsone.api.util.CommonUtils;
import com.heal.configuration.pojos.opensearch.TriggeredReportDetails;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.OutputStream;

/**
 * <AUTHOR>
 */
@Slf4j
public class DownloadReportService {

    public GenericResponse<String> downloadReport(Request request, Response response) {

        GenericResponse<String> genericResponse = new GenericResponse<>();
        try {
            RequestObject requestObject = new RequestObject(request);

            DownloadReportBL downloadReportBL = new DownloadReportBL();

            UtilityBean<String> utilityBean = downloadReportBL.clientValidation(requestObject);
            TriggeredReportDetails triggeredReportDetails = downloadReportBL.serverValidation(utilityBean);
            String[] fileDetails = downloadReportBL.processData(triggeredReportDetails);

            response.header("Content-disposition", "attachment; filename=" + fileDetails[0] + ";");

            OutputStream outputStream = response.raw().getOutputStream();
            try {
                FileInputStream input = new FileInputStream(fileDetails[1]);
                ByteArrayOutputStream output = new ByteArrayOutputStream();
                byte[] buffer = new byte[65536];

                int l;
                while ((l = input.read(buffer)) > 0)
                    output.write(buffer, 0, l);

                input.close();
                outputStream.write(output.toByteArray());
            } catch (Exception e) {
                log.error("Exception while processing file.");
                throw new Exception(e);
            }
            outputStream.flush();

            genericResponse.setData("File Downloaded successfully.");
            genericResponse.setResponseStatus(StatusResponse.SUCCESS.name());
            genericResponse.setMessage(StatusResponse.SUCCESS.name());

            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (ClientException | ServerException | DataProcessingException e) {
            log.error("Request validation failed. Details: ", e);
            CommonUtils.populateErrorResponse(genericResponse, response,
                    e.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (Exception e) {
            log.error("Error in downloading file. Details: ", e);
            CommonUtils.populateErrorResponse(genericResponse, response,
                    e.getMessage(), Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
        }
        return genericResponse;
    }

}
