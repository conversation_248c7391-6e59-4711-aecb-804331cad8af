package com.appnomic.appsone.api.reports.service;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.reports.businesslogic.GetTriggeredReportsBL;
import com.appnomic.appsone.api.reports.pojo.TriggeredReports;
import com.appnomic.appsone.api.util.AECSBouncyCastleUtil;
import com.appnomic.appsone.api.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

import java.util.List;

@Slf4j
public class GetTriggeredReportsService {

    public GenericResponse<String> getTriggeredReports(Request request, Response response) {
        GenericResponse<String> reportsResponse = new GenericResponse<>();

        try {
            RequestObject requestObject = new RequestObject(request);

            GetTriggeredReportsBL getTriggeredReportsBL = new GetTriggeredReportsBL();
            UtilityBean<String> utilityBean = getTriggeredReportsBL.clientValidation(requestObject);
            utilityBean = getTriggeredReportsBL.serverValidation(utilityBean);
            List<TriggeredReports> reports = getTriggeredReportsBL.processData(utilityBean);
            String reportsToString = CommonUtils.getObjectMapper().writeValueAsString(reports);
            String encryptedReportString = new AECSBouncyCastleUtil().encrypt(reportsToString);
            reportsResponse.setData(encryptedReportString);
            reportsResponse.setResponseStatus(StatusResponse.SUCCESS.name());
            reportsResponse.setMessage(StatusResponse.SUCCESS.name());

            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (ClientException | ServerException | DataProcessingException e) {
            log.error("Request validation failed. Details: ", e);
            CommonUtils.populateErrorResponse(reportsResponse, response,
                    e.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (Exception e) {
            log.error("Error in retrieving triggered reports information. Details: ", e);
            CommonUtils.populateErrorResponse(reportsResponse, response,
                    e.getMessage(), Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
        }

        return reportsResponse;
    }
}
