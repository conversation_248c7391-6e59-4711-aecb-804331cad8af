package com.appnomic.appsone.api.reports.businesslogic;

import com.appnomic.appsone.api.businesslogic.BusinessLogic;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.ReportRepo;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.reports.dao.opensearch.ReportsSearchRepo;
import com.appnomic.appsone.api.reports.pojo.Report;
import com.appnomic.appsone.api.util.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Throwables;
import com.google.gson.Gson;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.ReportArguments;
import com.heal.configuration.pojos.ReportDetails;
import com.heal.configuration.pojos.opensearch.TriggeredReportDetails;
import com.heal.configuration.pojos.report.TriggeredReportsPojo;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.HttpHostConnectException;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

import java.io.IOException;
import java.text.ParseException;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Slf4j
public class TriggerReportsBL implements BusinessLogic<Report, TriggeredReportsPojo, String> {

    @Override
    public UtilityBean<Report> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION_HEADER);
        if (StringUtils.isEmpty(authKey)) {
            log.error(Constants.MESSAGE_INVALID_AUTH_TOKEN);
            throw new ClientException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(identifier)) {
            log.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        String reportIdString = requestObject.getParams().get(":reportId");
        if (StringUtils.isEmpty(reportIdString)) {
            log.error("Invalid reportId");
            throw new ClientException("Invalid reportId");
        }

        int reportId;
        try {
            reportId = Integer.parseInt(reportIdString);
        } catch (Exception e) {
            log.error("Invalid reportId. It is not a valid integer.");
            throw new ClientException("Invalid reportId");
        }

        String requestBody = requestObject.getBody();
        Report report;
        try {
            report = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(requestBody, new TypeReference<Report>() {
            });
        } catch (IOException e) {
            log.error("Exception encountered while parsing the JSON request body. Details: " + e);
            throw new ClientException("Error in parsing the JSON request body");
        }

        report.setReportId(reportId);

        try {
            if (!report.validate()) {
                log.error("Error while validating request body");
                throw new ClientException("Error while validating request body");
            }
        } catch (ParseException e) {
            throw new ClientException(e.getMessage());
        }

        return UtilityBean.<Report>builder()
                .authToken(authKey)
                .accountIdString(identifier)
                .requestPayloadObject(report)
                .build();
    }

    @Override
    public TriggeredReportsPojo serverValidation(UtilityBean<Report> utilityBean) throws ServerException {
        AccountRepo accountRepo = new AccountRepo();
        String userIdentifier = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userIdentifier == null) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ServerException(UIMessages.AUTH_KEY_INVALID);
        }

        Account account = accountRepo.getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            log.error(UIMessages.INVALID_ACCOUNT_MESSAGE);
            throw new ServerException(UIMessages.INVALID_ACCOUNT_MESSAGE);
        }
        utilityBean.setAccount(account);

        String accountIdentifier = account.getIdentifier();
        int accountId = account.getId();

        int timezoneOffset = account.getTags().stream()
                .filter(t -> t.getType().equalsIgnoreCase("Timezone"))
                .map((timezoneTag -> Integer.parseInt(timezoneTag.getValue())))
                .findAny().orElse(********);
        String accountTimeZone = ZoneId.ofOffset("GMT", ZoneOffset.ofTotalSeconds(timezoneOffset / 1000)).getId();
        String accountName = account.getName();
        Report report = utilityBean.getRequestPayloadObject();

        //Validate the reportID in the request
        ReportDetails reportDetails = new ReportRepo().getReportDetails(accountIdentifier)
                .parallelStream().filter(c -> c.getStatus() == 1 && c.getId() == report.getReportId()).findAny().orElse(null);
        if (Objects.isNull(reportDetails)) {
            log.error("Report with ID [{}] is unavailable for account [{}]", report.getReportId(), accountName);
            throw new ServerException(String.format("Report with ID [%d] is unavailable for account [%s]", report.getReportId(), accountName));
        }

        List<ReportArguments> reportArguments = reportDetails.getReportArguments();
        Optional<ReportArguments> appNameArgBean = reportArguments.parallelStream()
                .filter(c -> c.getArgumentName().equalsIgnoreCase("application_name")).findFirst();

        Calendar now = Calendar.getInstance(TimeZone.getTimeZone("GMT"));
        List<TriggeredReportDetails> triggeredReportDetails = new ReportsSearchRepo().getTriggeredReportsWithReportId(accountIdentifier,
                        report.getReportId(), report.getFrom() - Constants.DAY, now.getTimeInMillis())
                .parallelStream()
                .filter(c -> !c.getStatus().equalsIgnoreCase("Failed"))
                .collect(Collectors.toList());

        try {
            if (appNameArgBean.isPresent()) {
                Optional<ReportArguments> kpiIdArgBean = reportArguments.parallelStream().filter(c -> c.getArgumentName().equalsIgnoreCase("kpi_ids")).findFirst();
                if (kpiIdArgBean.isPresent()) {

                    Map<String, Map<String, String>> triggeredReportArgumentsBeanMap = triggeredReportDetails
                            .parallelStream()
                            .filter(c -> c.getArguments().containsKey("start_time") || c.getArguments().containsKey("end_time")
                                    || c.getArguments().containsKey("application_name") || c.getArguments().containsKey("kpi_ids"))
                            .collect(Collectors.toMap(TriggeredReportDetails::getOutputFileName, TriggeredReportDetails::getArguments));

                    triggeredReportArgumentsBeanMap.forEach((key, value) -> {
                        AtomicBoolean startMatch = new AtomicBoolean(false);
                        AtomicBoolean endMatch = new AtomicBoolean(false);
                        AtomicBoolean applicationMatch = new AtomicBoolean(false);
                        AtomicBoolean kpiMatch = new AtomicBoolean(false);
                        value.forEach((k, v) -> {
                            if (k.equalsIgnoreCase("start_time") && report.getFrom() == Long.parseLong(v)) {
                                startMatch.set(true);
                            } else if (k.equalsIgnoreCase("end_time") && report.getTo() == Long.parseLong(v)) {
                                endMatch.set(true);
                            } else if (k.equalsIgnoreCase("application_name") && report.getApplicationName().equalsIgnoreCase(v)) {
                                applicationMatch.set(true);
                            } else if (k.equalsIgnoreCase("kpi_ids")) {
                                ArrayList<String> uniqueKpiIds = new ArrayList<>(report.getKpiIds());
                                uniqueKpiIds.removeAll(Arrays.stream(v.split(",")).map(String::trim).collect(Collectors.toList()));
                                if (uniqueKpiIds.isEmpty()) {
                                    kpiMatch.set(true);
                                }
                            }
                            if (startMatch.get() && endMatch.get() && applicationMatch.get() && kpiMatch.get()) {
                                if (key.split("\\.")[1].equalsIgnoreCase(report.getOutputFormat())) {
                                    log.error("A report is already triggered for the selected time range and application [{}] and selected kpis and same output format for account [{}]", report.getApplicationName(), accountName);
                                    try {
                                        throw new ServerException(String.format("A report is already triggered for the selected time range and application [%s] and selected kpis and same output format for account [%s]", report.getApplicationName(), accountName));
                                    } catch (ServerException e) {
                                        throw new RuntimeException(e);
                                    }
                                }
                            }
                        });
                    });

                } else {

                    Map<String, Map<String, String>> triggeredReportArgumentsBeanMap = triggeredReportDetails
                            .parallelStream()
                            .filter(c -> c.getArguments().containsKey("start_time") || c.getArguments().containsKey("end_time")
                                    || c.getArguments().containsKey("application_name"))
                            .collect(Collectors.toMap(TriggeredReportDetails::getOutputFileName, TriggeredReportDetails::getArguments));

                    triggeredReportArgumentsBeanMap.forEach((key, value) -> {
                        AtomicBoolean startMatch = new AtomicBoolean(false);
                        AtomicBoolean endMatch = new AtomicBoolean(false);
                        AtomicBoolean applicationMatch = new AtomicBoolean(false);
                        value.forEach((k, v) -> {
                            if (k.equalsIgnoreCase("start_time") && report.getFrom() == Long.parseLong(v)) {
                                startMatch.set(true);
                            } else if (k.equalsIgnoreCase("end_time") && report.getTo() == Long.parseLong(v)) {
                                endMatch.set(true);
                            } else if (k.equalsIgnoreCase("application_name") && report.getApplicationName().equalsIgnoreCase(v)) {
                                applicationMatch.set(true);
                            }
                            if (startMatch.get() && endMatch.get() && applicationMatch.get()) {
                                if (key.split("\\.")[1].equalsIgnoreCase(report.getOutputFormat())) {
                                    log.error("A report is already triggered for the selected time range and application [{}] and same output format for account [{}]", report.getApplicationName(), accountName);
                                    try {
                                        throw new ServerException(String.format("A report is already triggered for the selected time range and application [%s] and same output format for account [%s]", report.getApplicationName(), accountName));
                                    } catch (ServerException e) {
                                        throw new RuntimeException(e);
                                    }
                                }
                            }
                        });
                    });

                }
            } else {

                Map<String, Map<String, String>> triggeredReportArgumentsBeanMap = triggeredReportDetails
                        .parallelStream()
                        .filter(c -> c.getArguments().containsKey("start_time") || c.getArguments().containsKey("end_time"))
                        .collect(Collectors.toMap(TriggeredReportDetails::getOutputFileName, TriggeredReportDetails::getArguments));

                triggeredReportArgumentsBeanMap.forEach((key, value) -> {
                    AtomicBoolean startMatch = new AtomicBoolean(false);
                    AtomicBoolean endMatch = new AtomicBoolean(false);
                    value.forEach((k, v) -> {
                        if (k.equalsIgnoreCase("start_time") && report.getFrom() == Long.parseLong(v)) {
                            startMatch.set(true);
                        } else if (k.equalsIgnoreCase("end_time") && report.getTo() == Long.parseLong(v)) {
                            endMatch.set(true);
                        }
                        if (startMatch.get() && endMatch.get()) {
                            if (key.split("\\.")[1].equalsIgnoreCase(report.getOutputFormat())) {
                                log.error("A report is already triggered for the selected time range and same output format for account [{}]", accountName);
                                try {
                                    throw new ServerException(String.format("A report is already triggered for the selected time range and same output format for account [%s]", accountName));
                                } catch (ServerException e) {
                                    throw new RuntimeException(e);
                                }
                            }
                        }
                    });
                });
            }
        } catch (Exception e) {
            log.error("Exception while validating the report to be triggered. ", e);
            throw new ServerException(Throwables.getRootCause(e).getMessage());
        }

        try {
            Map<String, String> reportArgumentsMap = reportArguments.parallelStream()
                    .collect(Collectors.toMap(ReportArguments::getArgumentName, ReportArguments::getArgumentValue));
            List<String> distinctReportArguments = reportArguments.parallelStream()
                    .map(ReportArguments::getArgumentName).collect(Collectors.toList());

            //Replace the placeholders (which ever is present and applicable)
            Map<String, String> argumentsBeans = new HashMap<>();
            if (report.getApplicationName() != null && !report.getApplicationName().trim().isEmpty()) {
                Optional<ReportArguments> reportArgumentsBean = reportArguments.parallelStream()
                        .filter(r -> r.getArgumentName().equalsIgnoreCase("application_name")).findFirst();
                distinctReportArguments.remove("application_name");
                if (!reportArgumentsBean.isPresent()) {
                    log.warn("'application_name' is not an argument for report with ID [{}]", report.getReportId());
                } else {
                    argumentsBeans.put(reportArgumentsBean.get().getArgumentName(), report.getApplicationName());
                }
            }


            if (report.getServiceId() != null && !report.getServiceId().isEmpty()) {
                Optional<ReportArguments> reportArgumentsBean = reportArguments.parallelStream()
                        .filter(r -> r.getArgumentName().equalsIgnoreCase("service_id")).findFirst();
                distinctReportArguments.remove("service_id");
                if (!reportArgumentsBean.isPresent()) {
                    log.trace("'service_id' is not an argument for report with ID [{}]", report.getReportId());
                } else {
                    argumentsBeans.put(reportArgumentsBean.get().getArgumentName(), String.join(",", report.getServiceId()));
                }
            }

            boolean isKpiOptional = Arrays.stream(reportDetails.getParameters().split(","))
                    .map(String::trim)
                    .anyMatch(c -> c.equalsIgnoreCase("optionalKpis"));
            if (isKpiOptional) {
                argumentsBeans.put("kpi_ids", "ALL");
                argumentsBeans.put("kpi_names", "ALL");
            }

            if (report.getKpiIds() != null && !report.getKpiIds().isEmpty()) {
                Optional<ReportArguments> reportArgumentsBean = reportArguments.parallelStream()
                        .filter(r -> r.getArgumentName().equalsIgnoreCase("kpi_ids")).findFirst();
                distinctReportArguments.remove("kpi_ids");
                distinctReportArguments.remove("kpi_names");
                if (!reportArgumentsBean.isPresent()) {
                    log.error("'kpi_ids' is not an argument for report with ID [{}]", report.getReportId());
                    throw new ServerException("'kpi_ids' field missing in report arguments for report with ID" + report.getReportId());
                } else {
                    if (reportArgumentsMap.getOrDefault("shouldSplitKpi", "0").equals("1")) {
                        for (int i = 0; i < report.getKpiIds().size(); i++) {
                            argumentsBeans.put(reportArgumentsBean.get().getArgumentName() + "_" + (i + 1), report.getKpiIds().get(i));
                            argumentsBeans.put("kpi_names" + "_" + (i + 1), report.getKpiNames().get(i));
                        }
                        distinctReportArguments.remove("shouldSplitKpi");
                    } else {
                        argumentsBeans.put(reportArgumentsBean.get().getArgumentName(), report.getKpiIds().parallelStream().collect(Collectors.joining(",")));

                        if (report.getKpiNames() == null || report.getKpiNames().isEmpty()) {
                            argumentsBeans.put("kpi_names", report.getKpiIds().parallelStream().collect(Collectors.joining(",")));
                        } else {
                            argumentsBeans.put("kpi_names", report.getKpiNames().parallelStream().collect(Collectors.joining(",")));
                        }
                    }
                }
            }


            Optional<ReportArguments> accountNameArgs = reportArguments.parallelStream()
                    .filter(r -> r.getArgumentName().equalsIgnoreCase("account_name")).findFirst();
            distinctReportArguments.remove("account_name");
            if (!accountNameArgs.isPresent()) {
                log.warn("'account_name' is not an argument for report with ID [{}]", report.getReportId());
            } else {
                argumentsBeans.put(accountNameArgs.get().getArgumentName(), accountName);
            }


            Optional<ReportArguments> fromArg = reportArguments.parallelStream()
                    .filter(r -> r.getArgumentName().equalsIgnoreCase("start_time")).findFirst();
            distinctReportArguments.remove("start_time");
            if (!fromArg.isPresent()) {
                log.error("'start_time' is not an argument for report with ID [{}]", report.getReportId());
                throw new ServerException("'start_time' field missing in report arguments for report with ID" + report.getReportId());
            } else {
                argumentsBeans.put(fromArg.get().getArgumentName(), String.valueOf(report.getFrom()));
            }


            Optional<ReportArguments> toArg = reportArguments.parallelStream()
                    .filter(r -> r.getArgumentName().equalsIgnoreCase("end_time")).findFirst();
            distinctReportArguments.remove("end_time");
            if (!toArg.isPresent()) {
                log.error("'end_time' is not an argument for report with ID [{}]", report.getReportId());
                throw new ServerException("'end_time' field missing in report arguments for report with ID" + report.getReportId());
            } else {
                argumentsBeans.put(toArg.get().getArgumentName(), String.valueOf(report.getTo()));
            }


            Optional<ReportArguments> isInternalReport = reportArguments.parallelStream()
                    .filter(r -> r.getArgumentName().equalsIgnoreCase("isInternalReport")).findFirst();
            distinctReportArguments.remove("isInternalReport");
            if (!isInternalReport.isPresent()) {
                log.error("'isInternalReport' is not an argument for report with ID [{}]", report.getReportId());
                throw new ServerException("'isInternalReport' field missing in report arguments for report with ID" + report.getReportId());
            } else {
                argumentsBeans.put(isInternalReport.get().getArgumentName(), isInternalReport.get().getArgumentValue());
            }

            Optional<ReportArguments> reportGenerationTime = reportArguments.parallelStream()
                    .filter(r -> r.getArgumentName().equalsIgnoreCase("report_start_time")).findFirst();
            distinctReportArguments.remove("report_start_time");
            if (!reportGenerationTime.isPresent()) {
                log.error("'report_start_time' is not an argument for report with ID [{}]", report.getReportId());
                throw new ServerException("'report_start_time' field missing in report arguments for report with ID" + report.getReportId());
            } else {
                argumentsBeans.put(reportGenerationTime.get().getArgumentName(), String.valueOf(now.getTimeInMillis()));
            }

            Optional<ReportArguments> accountIdArgs = reportArguments.parallelStream()
                    .filter(r -> r.getArgumentName().equalsIgnoreCase("account_id")).findFirst();
            distinctReportArguments.remove("account_id");
            if (!accountIdArgs.isPresent()) {
                log.trace("'account_id' is not an argument for report with ID [{}]", report.getReportId());
            } else {
                argumentsBeans.put(accountIdArgs.get().getArgumentName(), String.valueOf(accountId));
            }

            List<ReportArguments> indexNameArgs = reportArguments.parallelStream()
                    .filter(r -> r.getArgumentName().startsWith("index_name")).collect(Collectors.toList());
            distinctReportArguments.removeAll(distinctReportArguments.parallelStream().filter(r -> r.startsWith("index_name")).collect(Collectors.toList()));
            if (indexNameArgs.isEmpty()) {
                log.trace("No argument with name 'index_name' is present for report with ID [{}]", report.getReportId());
            } else {
                indexNameArgs.forEach(c -> {
                    if (!c.getArgumentValue().contains("{ACCOUNT_IDENTIFIER}_{YEAR}.w{WEEK_NUMBER}")
                            && !c.getArgumentValue().contains("{ACCOUNT_IDENTIFIER}_{YEAR}.{MONTH}.{DAY}")
                            && !c.getArgumentValue().contains("{ACCOUNT_IDENTIFIER}{DELTA_STORE}")) {
                        log.error("No proper index name pattern found in argument value for argument name '{}' for report id {} in report_arguments table of Percona." +
                                " Please make sure index name contains {ACCOUNT_IDENTIFIER}_{YEAR}.w{WEEK_NUMBER} or" +
                                "{ACCOUNT_IDENTIFIER}_{YEAR}.{MONTH}.{DAY} or {ACCOUNT_IDENTIFIER}{DELTA_STORE} in argument value.", c.getArgumentName(), report.getReportId());
                        throw new RuntimeException(String.format("Invalid index pattern found for report %s", reportDetails.getName()));
                    }
                    if (c.getArgumentValue().contains("{ACCOUNT_IDENTIFIER}{DELTA_STORE}")) {
                        c.setArgumentValue(c.getArgumentValue().replace("{DELTA_STORE}", ""));
                    }
                    argumentsBeans.put(c.getArgumentName(), c.getArgumentValue());
                });
            }

            Optional<ReportArguments> shouldCreateIndexArgs = reportArguments.parallelStream()
                    .filter(r -> r.getArgumentName().startsWith("shouldCreateIndex")).findFirst();
            distinctReportArguments.remove("shouldCreateIndex");
            if (!shouldCreateIndexArgs.isPresent()) {
                log.warn("'shouldCreateIndex' is not an argument for report with ID [{}]", report.getReportId());
            } else if (!(shouldCreateIndexArgs.get().getArgumentValue().equals("0") || shouldCreateIndexArgs.get().getArgumentValue().equals("1"))) {
                log.error("'createIndexArgument' field value should be either '0' or '1'");
                throw new ServerException("Report arguments contains 'shouldCreateIndex' field with value " + shouldCreateIndexArgs.get().getArgumentValue() + ".It should be either '0' or '1'");
            } else {
                Optional<ReportArguments> createIndexArgs = reportArguments.parallelStream()
                        .filter(r -> r.getArgumentName().startsWith("createIndexArgument")).findFirst();
                distinctReportArguments.remove("createIndexArgument");
                if (!createIndexArgs.isPresent()) {
                    log.error("'createIndexArgument' field is absent for report with ID [{}], where 'shouldCreateIndex' argument is present.", report.getReportId());
                    throw new ServerException("Report arguments contains 'shouldCreateIndex' field without 'createIndexArgument' field.");
                } else if (createIndexArgs.get().getArgumentValue() == null || createIndexArgs.get().getArgumentValue().trim().isEmpty()) {
                    log.error("'createIndexArgument' field contains no values for report with ID [{}]", report.getReportId());
                    throw new ServerException("Report arguments contains 'shouldCreateIndex' field with no values.");
                } else {
                    argumentsBeans.put(shouldCreateIndexArgs.get().getArgumentName(), shouldCreateIndexArgs.get().getArgumentValue());
                    argumentsBeans.put(createIndexArgs.get().getArgumentName(), createIndexArgs.get().getArgumentValue());
                }
            }


            distinctReportArguments.remove("output_path");
            distinctReportArguments.remove("output_timestamp");
            distinctReportArguments.remove("rolled_up_index_start_time");
            distinctReportArguments.remove("rolled_up_index_end_time");
            distinctReportArguments.remove("non_rolled_up_index_start_time");
            distinctReportArguments.remove("non_rolled_up_index_end_time");
            distinctReportArguments.remove("rolled_up_index_priority");
            distinctReportArguments.remove("calculateTimeLimit");
            distinctReportArguments.remove("index_priority");
            distinctReportArguments.forEach(c -> {
                if (StringUtils.isEmpty(reportArgumentsMap.get(c))) {
                    throw new RuntimeException(String.format("No value found for attribute [%s] in 'report_arguments' table. Please provide a value for this new attribute.", c));
                }
                argumentsBeans.put(c, reportArgumentsMap.get(c));
            });

//          TODO:- Enable this line after RollUps come into Heal
//            argumentsBeans.put("rollup", report.getRollup() == 1 ? "YES" : "NO");
            argumentsBeans.put("rollup", "NO");

            Optional<ReportArguments> outputFileArg = reportArguments.parallelStream()
                    .filter(r -> r.getArgumentName().equalsIgnoreCase("output_timestamp")).findFirst();

            String outputFileName = "";
            if (!outputFileArg.isPresent()) {
                log.warn("'output_timestamp' is not an argument for report with ID [{}]", report.getReportId());
            } else {
                outputFileName = outputFileArg.get().getArgumentValue()
                        .replace("{output_timestamp}", DateTimeUtil.getTimeInRequiredTimezone(now.getTimeInMillis(), accountTimeZone)
                                .replace(" ", "_").replace(":", "-"))
                        .replace("{format}", report.getOutputFormat());
            }

            String templateName = reportDetails.getTemplateName();

            //Create and return a new object bean to push into DB
            return TriggeredReportsPojo.builder()
                    .reportId(report.getReportId())
                    .reportName(reportDetails.getName())
                    .scriptName(reportDetails.getScriptName())
                    .scriptPath(reportDetails.getScriptPath())
                    .outputFileName(outputFileName)
                    .triggeredUserDetailsId(userIdentifier)
                    .triggeredTime(now.getTimeInMillis())
                    .status("In Progress")
                    .statusDescription("Generating Report....")
                    .emailAddress(report.getEmailIds() == null || report.getEmailIds().isEmpty() ? null : report.getEmailIds().parallelStream().map(String::trim).collect(Collectors.joining(",")))
                    .outputFormat(report.getOutputFormat())
                    .templateName(templateName)
                    .arguments(argumentsBeans)
                    .accountId(accountId)
                    .accountIdentifier(accountIdentifier)
                    .build();
        } catch (Exception e) {
            log.error("Exception while server validation for trigger reports.", e);
            throw new ServerException(Throwables.getRootCause(e).getMessage());
        }
    }

    @Override
    public String processData(TriggeredReportsPojo reportBean) throws DataProcessingException {

        Gson gson = new Gson();
        String gString = gson.toJson(reportBean, TriggeredReportsPojo.class);

        String url = ConfProperties.getString(Constants.REPORT_DATA_SERVICE_URL, Constants.DEFAULT_REPORT_DATA_SERVICE_URL);

        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(url);

            StringEntity entity = new StringEntity(gString);
            httpPost.setEntity(entity);
            httpPost.setHeader("Accept", "application/json");
            httpPost.setHeader("Content-type", "application/json");

            CloseableHttpResponse response = client.execute(httpPost);
            if (response.getStatusLine().getStatusCode() != 200) {
                log.error("Received response with status code {} from Report data service", response.getStatusLine().getStatusCode());
                throw new DataProcessingException(String.format("Received response with status code [%s] during rest call to Report data service url [%s]. Please look into the Report data service logs.", response.getStatusLine().getStatusCode(), url));
            }
        } catch (HttpHostConnectException e) {
            log.error("Exception while rest call to Report data service url [{}]. Please look into the Report data service logs.", url);
            throw new DataProcessingException("Connection not available. Reporting data Service is down.");
        } catch (Exception e) {
            log.error("Exception while rest call to Report data service url [{}]. Please look into the Report data service logs.", url);
            throw new DataProcessingException(e, String.format("Exception while rest call to Report data service url [%s]. Please look into the Report data service logs.", url));
        }

        return "Reports triggered.";
    }
}
