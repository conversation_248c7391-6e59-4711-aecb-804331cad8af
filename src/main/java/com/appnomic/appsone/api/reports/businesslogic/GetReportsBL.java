package com.appnomic.appsone.api.reports.businesslogic;

import com.appnomic.appsone.api.businesslogic.BusinessLogic;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.ReportRepo;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.reports.dao.opensearch.ReportsSearchRepo;
import com.appnomic.appsone.api.reports.pojo.ConfiguredReport;
import com.appnomic.appsone.api.util.DateTimeUtil;
import com.appnomic.appsone.api.util.StringUtils;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.google.common.base.Throwables;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.ReportArguments;
import com.heal.configuration.pojos.ReportDetails;
import lombok.extern.slf4j.Slf4j;

import java.time.*;
import java.time.temporal.IsoFields;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
public class GetReportsBL implements BusinessLogic<Object, UtilityBean<Object>, List<ConfiguredReport>> {

    @Override
    public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION_HEADER);
        if (StringUtils.isEmpty(authKey)) {
            log.error(Constants.MESSAGE_INVALID_AUTH_TOKEN);
            throw new ClientException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(identifier)) {
            log.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }


        return UtilityBean.builder()
                .authToken(authKey)
                .accountIdString(identifier)
                .build();
    }

    @Override
    public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ServerException(UIMessages.AUTH_KEY_INVALID);
        }

        Account account = new AccountRepo().getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            log.error(UIMessages.INVALID_ACCOUNT_MESSAGE);
            throw new ServerException(UIMessages.INVALID_ACCOUNT_MESSAGE);
        }
        utilityBean.setAccount(account);

        return utilityBean;
    }

    @Override
    public List<ConfiguredReport> processData(UtilityBean<Object> configData) throws DataProcessingException {
        try {
            String accountIdentifier = configData.getAccount().getIdentifier();

            int timezoneOffset = configData.getAccount().getTags().stream()
                    .filter(t -> t.getType().equalsIgnoreCase("Timezone"))
                    .map((timezoneTag -> Integer.parseInt(timezoneTag.getValue())))
                    .findAny().orElse(********);
            String accountTimeZone = ZoneId.ofOffset("GMT", ZoneOffset.ofTotalSeconds(timezoneOffset / 1000)).getId();

            List<ReportDetails> reportDetails = new ReportRepo().getReportDetails(accountIdentifier);

            if (reportDetails.isEmpty()) {
                log.warn("Reports are unavailable in db.");
                return new ArrayList<>();
            }

            Map<Integer, Map<String, Long>> timeRangesMap = new HashMap<>();
            List<String> availableIndices = new ReportsSearchRepo().getAvailableIndices(accountIdentifier);

            Map<String, Long> indexStartTimeMap = new HashMap<>();

            reportDetails.forEach(report -> {
                Map<String, String> reportArgsMap = report.getReportArguments().stream().collect(Collectors.toMap(ReportArguments::getArgumentName, ReportArguments::getArgumentValue));
                if (reportArgsMap.get("isInternalReport").equalsIgnoreCase("1")) {
                    return;
                }

                Map<String, Long> timeRanges = new HashMap<>();
                if (reportArgsMap.getOrDefault("calculateTimeLimit", "0").equalsIgnoreCase("0")) {

                    //Non Rolled UP Start Time
                    String startTime = reportArgsMap.getOrDefault("non_rolled_up_index_start_time", null);
                    String endTime = reportArgsMap.getOrDefault("non_rolled_up_index_end_time", null);

                    long st = DateTimeUtil.getGMTToLongTimeInRequiredTimeZone(startTime, accountTimeZone);
                    if (st != 0L) {
                        timeRanges.put("nonRolledUpStartTime", st);
                    }

                    long et = DateTimeUtil.getGMTToLongTimeInRequiredTimeZone(endTime, accountTimeZone);
                    if (st != 0L && et != 0L) {
                        timeRanges.put("nonRolledUpEndTime", et + Constants.DAY - 1);
                    }

                    //Rolled UP Start Time
                    startTime = reportArgsMap.getOrDefault("rolled_up_index_start_time", null);
                    endTime = reportArgsMap.getOrDefault("rolled_up_index_end_time", null);

                    st = DateTimeUtil.getGMTToLongTimeInRequiredTimeZone(startTime, accountTimeZone);
                    if (st != 0L) {
                        timeRanges.put("rolledUpStartTime", st);
                    }

                    et = DateTimeUtil.getGMTToLongTimeInRequiredTimeZone(endTime, accountTimeZone);
                    if (st != 0L && et != 0L) {
                        timeRanges.put("rolledUpEndTime", et + Constants.DAY - 1);
                    }

                } else {

                    String indexPriority = reportArgsMap.getOrDefault("index_priority", "index_name");
                    String index_pattern = reportArgsMap.getOrDefault(indexPriority, "heal_collated_kpi_{ACCOUNT_IDENTIFIER}_{YEAR}.w{WEEK_NUMBER}_1440mins");
                    if (!index_pattern.contains("{ACCOUNT_IDENTIFIER}_{YEAR}.w{WEEK_NUMBER}")
                            && !index_pattern.contains("{ACCOUNT_IDENTIFIER}_{YEAR}.{MONTH}.{DAY}")
                            && !index_pattern.contains("{ACCOUNT_IDENTIFIER}{DELTA_STORE}")) {
                        log.error("No proper index name pattern found in argument value for argument name '{}' for report id {} in report_arguments table of Percona." +
                                " Please make sure index name contains {ACCOUNT_IDENTIFIER}_{YEAR}.w{WEEK_NUMBER} or " +
                                " {ACCOUNT_IDENTIFIER}_{YEAR}.{MONTH}.{DAY} or {ACCOUNT_IDENTIFIER}{DELTA_STORE} in argument value.", indexPriority, report.getId());
                        throw new RuntimeException(String.format("Invalid index pattern found for report id %s", report.getId()));
                    }
                    String index_pattern_initials = index_pattern.split("\\{ACCOUNT_IDENTIFIER}")[0];

                    String[] indexPrefixArr = reportArgsMap.get("index_name").split("_");
                    String aggregationInMin = indexPrefixArr[indexPrefixArr.length - 1].contains("mins") ? "_" + indexPrefixArr[indexPrefixArr.length - 1] : "";

                    String applicableNonRolledUpIndex = availableIndices.stream()
                            .filter(c -> c.startsWith(index_pattern_initials) && !c.contains("mins"))
                            .sorted()
                            .findFirst()
                            .orElse(null);
                    if (!StringUtils.isEmpty(applicableNonRolledUpIndex)) {
                        long startTimeInEpoch;
                        if (indexStartTimeMap.containsKey(applicableNonRolledUpIndex)) {
                            startTimeInEpoch = indexStartTimeMap.get(applicableNonRolledUpIndex);
                        } else {
                            startTimeInEpoch = new ReportsSearchRepo().getStartTimeStamp(applicableNonRolledUpIndex, reportArgsMap.getOrDefault("priority_index_time_field", "timeInGMT"), accountIdentifier);
                            indexStartTimeMap.put(applicableNonRolledUpIndex, startTimeInEpoch);
                        }

                        if (startTimeInEpoch != 0L) {
                            timeRanges.put("nonRolledUpStartTime", startTimeInEpoch + timezoneOffset);
                        } else {
                            timeRanges.put("nonRolledUpStartTime", calculateIndexStartTimeFromIndexName(index_pattern, applicableNonRolledUpIndex) + timezoneOffset);
                        }
                        timeRanges.put("nonRolledUpEndTime", 0L);
                    }

                    String applicableRolledUpIndex = availableIndices.stream()
                            .filter(c -> c.startsWith(index_pattern_initials) && !StringUtils.isEmpty(aggregationInMin) && c.contains(aggregationInMin))
                            .sorted()
                            .findFirst()
                            .orElse(null);
                    if (!StringUtils.isEmpty(applicableRolledUpIndex)) {
                        long startTimeInEpoch;
                        if (indexStartTimeMap.containsKey(applicableRolledUpIndex)) {
                            startTimeInEpoch = indexStartTimeMap.get(applicableRolledUpIndex);
                        } else {
                            startTimeInEpoch = new ReportsSearchRepo().getStartTimeStamp(applicableRolledUpIndex, reportArgsMap.getOrDefault("priority_index_time_field", "timeInGMT"), accountIdentifier);
                            indexStartTimeMap.put(applicableRolledUpIndex, startTimeInEpoch);
                        }

                        if (startTimeInEpoch != 0L) {
                            timeRanges.put("rolledUpStartTime", startTimeInEpoch + timezoneOffset);
                        } else {
                            timeRanges.put("rolledUpStartTime", calculateIndexStartTimeFromIndexName(index_pattern, applicableRolledUpIndex) + timezoneOffset);
                        }
                        timeRanges.put("rolledUpEndTime", 0L);
                    }
                }

                if (reportArgsMap.containsKey("rolled_up_index_priority")) {
                    timeRanges.put("rolledUpIndexPriority", Long.valueOf(reportArgsMap.get("rolled_up_index_priority")));
                }

                if (!timeRanges.isEmpty()) {
                    timeRangesMap.put(report.getId(), timeRanges);
                }
            });


            return reportDetails.parallelStream()
                    .filter(c -> c.getStatus() == 1)
                    .map(r -> ConfiguredReport.builder()
                            .id(r.getId())
                            .name(r.getName())
                            .identifier(r.getIdentifier())
                            .parameters(Arrays.asList(r.getParameters().split(",")).parallelStream().map(String::trim).collect(Collectors.toList()))
                            .timeRanges(timeRangesMap.get(r.getId()))
                            .build())
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Exception while getting reports details.", e);
            throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
        }
    }

    private long calculateIndexStartTimeFromIndexName(String indexPattern, String applicableIndex) {
        String[] tempArr;
        String[] tempArr2;
        if (indexPattern.contains("{ACCOUNT_IDENTIFIER}_{YEAR}.w{WEEK_NUMBER}")) {
            tempArr = applicableIndex.split("\\.w");
            tempArr2 = tempArr[0].split("_");
            return getStartTimeFromWeekNumber(Integer.parseInt(tempArr[1]), Integer.parseInt(tempArr2[tempArr2.length - 1]));
        } else if (indexPattern.contains("{ACCOUNT_IDENTIFIER}_{YEAR}.{MONTH}.{DAY}")) {
            tempArr = applicableIndex.split("\\.");
            tempArr2 = tempArr[0].split("_");
            return getStartTimeFromDateNumber(Integer.parseInt(tempArr[2]), Integer.parseInt(tempArr[1]), Integer.parseInt(tempArr2[tempArr2.length - 1]));
        } else {
            return LocalDate.now().atStartOfDay().toInstant(ZoneOffset.UTC).toEpochMilli() - TimeUnit.DAYS.toMillis(365);
        }
    }

    private long getStartTimeFromDateNumber(int date, int month, int year) {
        LocalDate desiredDate = LocalDate.of(year, month, date);
        return desiredDate.atStartOfDay().toInstant(ZoneOffset.UTC).toEpochMilli();
    }

    private long getStartTimeFromWeekNumber(int weekNumber, int yearNumber) {
        LocalDate desiredDate = LocalDate.now()
                .with(IsoFields.WEEK_OF_WEEK_BASED_YEAR, weekNumber)
                .with(IsoFields.WEEK_BASED_YEAR, yearNumber)
                .with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));

        return desiredDate.atStartOfDay().toInstant(ZoneOffset.UTC).toEpochMilli();
    }
}
