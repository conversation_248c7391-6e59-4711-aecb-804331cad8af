package com.appnomic.appsone.api.reports.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Slf4j
public class Report {

    private int reportId;
    private String applicationName;
    private long from;
    private long to;
    private String serviceId;
    private List<String> kpiIds = new ArrayList<>();
    private List<String> kpiNames = new ArrayList<>();
    private List<String> emailIds;
    private String outputFormat;
    private int rollup;

    public boolean validate() throws ParseException {
        if (reportId < 1) {
            log.error("ReportId is incorrect. It is not a valid integer.");
            return false;
        }
        if (from == 0) {
            from = System.currentTimeMillis() - (1440 * 60 * 1000);
        }

        if (to == 0) {
            to = System.currentTimeMillis();
        }

        if (outputFormat == null || outputFormat.trim().isEmpty()
                || (!outputFormat.equalsIgnoreCase("pdf")
                && !outputFormat.equalsIgnoreCase(("csv"))
                && !outputFormat.equalsIgnoreCase("xls")
                && !outputFormat.equalsIgnoreCase("xlsx")
                && !outputFormat.equalsIgnoreCase("html")
                && !outputFormat.equalsIgnoreCase("rtf"))) {
            log.error("Output format is incorrect. It should be one of 'pdf', 'csv', 'xlsx' or 'html'");
            return false;
        }

        return true;
    }
}
