package com.appnomic.appsone.api.businesslogic;

import java.util.ArrayList;
import java.util.List;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.mobile.model.CrashOverview;
import com.appnomic.appsone.api.mobile.util.BigQueryService;
import com.appnomic.appsone.api.mobile.util.BigQueryUtil;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.FieldValueList;
import com.google.cloud.bigquery.QueryJobConfiguration;
import com.google.cloud.bigquery.TableResult;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CrashMonitoringOverviewBL implements BusinessLogic<Object, UtilityBean<Object>, CrashOverview> {

	@Override
	public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
		List<String> errorMessages = new ArrayList<>();
		if (!BigQueryUtil.appOSStringValidation(requestObject, errorMessages)
			|| !BigQueryUtil.appDisplayVersionValidation(requestObject, errorMessages)
			|| !BigQueryUtil.fromTimeToTimeValidation(requestObject, errorMessages)) {
			throw new ClientException(String.join(";", errorMessages));
		}
		String appOSString = requestObject.getQueryParams().get("app-os")[0];
		String appDisplayVersions = requestObject.getQueryParams().get("app-build-version")[0];
		String fromTime = requestObject.getQueryParams().get("from-time")[0];
		String toTime = requestObject.getQueryParams().get("to-time")[0];
		return UtilityBean.<Object>builder()
				.appOSString(appOSString)
				.appDisplayVersions(appDisplayVersions)
                .fromTimeString(fromTime)
                .toTimeString(toTime)
                .build();
	}

	@Override
	public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
		return utilityBean;
	}

	@Override
	public CrashOverview processData(UtilityBean<Object> configData) throws DataProcessingException {
		String appOSString = configData.getAppOSString();
		BigQuery bigQuery = BigQueryService.getInstance(appOSString.split("\\.")[0]);
		log.debug("bigQuery Obj: {}", bigQuery);

		String appDisplayVersionInParam = BigQueryUtil.getInParameter(configData.getAppDisplayVersions());
		String fromTime = configData.getFromTimeString();
		String toTime = configData.getToTimeString();
		long timeDiff = Long.parseLong(toTime) - Long.parseLong(fromTime);
		String pastTime = String.valueOf(Long.parseLong(fromTime) - timeDiff);

        String query = "SELECT issue_id AS issueId, COUNT(issue_id) AS issueCount, COUNTIF(is_fatal=true) AS fatalCount, (COUNT(issue_id)-COUNTIF(is_fatal=true)) AS nonFatalCount" +
                "		FROM " + Constants.getCrashlyticsTable(appOSString) +
                "		WHERE event_timestamp > TIMESTAMP_MILLIS("+ fromTime +") " +
                "		AND event_timestamp < TIMESTAMP_MILLIS(" + toTime +") "+
                "		AND application.display_version IN " + appDisplayVersionInParam +
                "		GROUP BY issueId "+
                " 		UNION ALL "+
                "		SELECT 'previousIssueCount' AS issueId, COUNT(issue_id) AS issueCount, 0 AS fatalCount, 0 AS nonFatalCount " +
                "		FROM " + Constants.getCrashlyticsTable(appOSString) +
                "		WHERE event_timestamp > TIMESTAMP_MILLIS("+ pastTime +") " +
                "		AND event_timestamp < TIMESTAMP_MILLIS(" + fromTime +") " + 
                "		AND application.display_version IN " + appDisplayVersionInParam;

		QueryJobConfiguration queryConfig = QueryJobConfiguration.newBuilder(query).setUseLegacySql(false).build();

		long uniqueCount = 0L;
		long fatalCount = 0L;
		long nonFatalCount = 0L;
		long previousCrashCount = 0L;
		CrashOverview crashOverview = new CrashOverview();

		TableResult result;
		try {
			result = bigQuery.query(queryConfig);
		} catch(InterruptedException inExc) {
			throw new DataProcessingException("Error in fetching data from BigQuery");
		}

		for (FieldValueList value : result.iterateAll()) {
			if (null != value.get("issueId").getValue() && value.get("issueId").getStringValue().equalsIgnoreCase("previousIssueCount")) {
				previousCrashCount = value.get("issueCount").getLongValue();
				continue;
			}
			uniqueCount++;
			if(null == value.get("fatalCount").getValue() || null == value.get("nonFatalCount").getValue()) {
				throw new DataProcessingException("Fatal/Non Fatal Crashes not found in system");
			}
			fatalCount += value.get("fatalCount").getLongValue();
			nonFatalCount += value.get("nonFatalCount").getLongValue();
		}
		long crashCount = fatalCount + nonFatalCount;

		long crashCountChange = crashCount - previousCrashCount;
        String percentageChange = (previousCrashCount != 0) ? String.format("%.2f", (double) crashCountChange / (double) previousCrashCount *100.00) : "0";

		crashOverview.setFatalCrashes(fatalCount);
		crashOverview.setNonFatalCrashes(nonFatalCount);
		crashOverview.setUniqueCrashes(uniqueCount);
		crashOverview.setTotalCrashes(crashCount);
		crashOverview.setPercentageChange(percentageChange + "%");
		return crashOverview;
	}

}
