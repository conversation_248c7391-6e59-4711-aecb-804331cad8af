package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.opensearch.CollatedTransactionsSearchRepo;
import com.appnomic.appsone.api.dao.opensearch.RawTransactionSearchRepo;
import com.appnomic.appsone.api.dao.redis.*;
import com.appnomic.appsone.api.pojo.*;
import com.appnomic.appsone.api.pojo.request.TransactionPerformanceDataRequest;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.*;
import com.appnomic.appsone.opeasearchquery.results.TabularResults;
import com.heal.configuration.enums.KPIAttributes;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.CompInstClusterDetails;
import com.heal.configuration.pojos.Transaction;
import com.heal.configuration.pojos.*;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 17/05/22
 */
@Slf4j
public class TransactionPerformanceDataBL implements BusinessLogic<TransactionPerformanceDataRequest, UtilityBean<TransactionPerformanceDataRequest>, UIData> {

    @Override
    public UtilityBean<TransactionPerformanceDataRequest> clientValidation(RequestObject request) throws ClientException {

        if (request == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);

        if (authToken == null || authToken.trim().isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        TransactionPerformanceDataRequest transactionPerformanceDataRequest = new TransactionPerformanceDataRequest(request);

        if (!transactionPerformanceDataRequest.validateParameters(transactionPerformanceDataRequest)) {
            log.error("Client validation failed for GET TRANSACTION DATA request.");
            throw new ClientException(UIMessages.CLIENT_VALIDATION_FAILED);
        }

        return UtilityBean.<TransactionPerformanceDataRequest>builder()
                .requestPayloadObject(transactionPerformanceDataRequest)
                .fromTime(Long.valueOf(transactionPerformanceDataRequest.getFromTimeString()))
                .toTime(Long.valueOf(transactionPerformanceDataRequest.getToTimeString()))
                .accountIdString(transactionPerformanceDataRequest.getAccountIdentifierString())
                .serviceIdString(transactionPerformanceDataRequest.getServiceIdString())
                .componentInstanceIdString(transactionPerformanceDataRequest.getInstanceIdString())
                .responseType(transactionPerformanceDataRequest.getTransactionResponseTypeString())
                .requestTypeString(transactionPerformanceDataRequest.getRequestTypeString())
                .authToken(authToken)
                .build();
    }

    @Override
    public UtilityBean<TransactionPerformanceDataRequest> serverValidation(UtilityBean<TransactionPerformanceDataRequest> utilityBean) throws ServerException {

        String accountIdentifier = utilityBean.getAccountIdString();
        int serviceId = Integer.parseInt(utilityBean.getServiceIdString());
        int instanceId = Integer.parseInt(utilityBean.getComponentInstanceIdString());
        int txnIdentifier = Integer.parseInt(utilityBean.getRequestPayloadObject().getTransactionIdString());

        AccountRepo accountsRepo = new AccountRepo();
        ServiceRepo serviceRepo = new ServiceRepo();
        InstanceRepo instanceRepo = new InstanceRepo();

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token.");
            throw new ServerException("Error while extracting user details from authorization token.");
        }

        Account account = accountsRepo.getAccount(accountIdentifier);
        if (account == null) {
            log.error("Account identifier is invalid.");
            throw new ServerException("Account identifier is invalid.");
        }
        utilityBean.setAccount(account);

        TimeRangeDetailsDao timeRangeDetailsDao = new TimeRangeDetailsDao();
        List<TimeRangeDetails> timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(accountIdentifier);
        if (timeRangeDetailsList == null) {
            log.error("Exception occurred while fetching time range details for account {}", accountIdentifier);
            throw new ServerException("Exception occurred while fetching time range details for account " + accountIdentifier);
        }

        if (timeRangeDetailsList.isEmpty()) {
            log.trace("No time range details found for account {}. Searching for Global account.", accountIdentifier);
            timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }
        if (timeRangeDetailsList == null) {
            log.error("Exception occurred while fetching time range details for account {}", Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
            throw new ServerException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }

        if (timeRangeDetailsList.isEmpty()) {
            log.error("No time range details found");
            throw new ServerException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }
        TimeRangeDetails timeRangeDetails = ValidationUtils.getRollupPojo(utilityBean.getFromTime(), utilityBean.getToTime(), timeRangeDetailsList);
        if (timeRangeDetails == null) {
            log.error("Time range details are invalid.");
            throw new ServerException("Time range details are invalid.");
        }
        utilityBean.setTimeRangeDetails(timeRangeDetails);
        utilityBean.setTimeRangeDetailsList(timeRangeDetailsList);

        BasicEntity serviceDetails = serviceRepo.getBasicServiceDetailsWithServiceId(accountIdentifier, serviceId);
        if (serviceDetails == null) {
            String error = "Service id is invalid";
            log.error(error);
            throw new ServerException(error);
        }
        utilityBean.setServiceIdString(serviceDetails.getIdentifier());

        CompInstClusterDetails instanceDetails = new CompInstClusterDetails();
        if (utilityBean.getRequestTypeString().trim().equalsIgnoreCase(Constants.COM_INSTANCE_TYPE)) {
            instanceDetails = instanceRepo.getInstancesByAccount(accountIdentifier)
                    .parallelStream().filter(c -> c.getId() == instanceId).findAny().orElse(null);
            if (instanceDetails == null) {
                log.error("Instance id is invalid.");
                throw new ServerException("Instance id is invalid.");
            }
            utilityBean.setComponentInstanceIdString(instanceDetails.getIdentifier());
        }

        //GET Tagged transactions
        TransactionRepo transactionRepo = new TransactionRepo();

        if (txnIdentifier == 0) {
            List<BasicTransactionEntity> transactions = serviceRepo.getTransactionsByServiceIdentifier(accountIdentifier, serviceDetails.getIdentifier())
                    .parallelStream()
                    .filter(t -> t.getStatus() == 1)
                    .collect(Collectors.toList());
            if (utilityBean.getRequestPayloadObject().getTagIdString().equals("0")) {
                utilityBean.getRequestPayloadObject().setTaggedTransaction(transactions);
            } else {
                utilityBean.getRequestPayloadObject().setTaggedTransaction(transactions.parallelStream()
                        .filter(txn -> (txn.getTransactionGroups().parallelStream().anyMatch(it -> it.getTransactionGroupId() == Integer.parseInt(utilityBean.getRequestPayloadObject().getTagIdString()))))
                        .collect(Collectors.toList()));

                utilityBean.getRequestPayloadObject().getTaggedTransaction().parallelStream()
                        .map(BasicTransactionEntity::getTransactionGroups)
                        .flatMap(Collection::stream)
                        .filter(Objects::nonNull)
                        .filter(t -> t.getTransactionGroupName() != null)
                        .findAny().ifPresent(group -> utilityBean.getRequestPayloadObject().setGroupName(group.getTransactionGroupName()));
            }

        } else {
            Transaction transaction = transactionRepo.getTransactionDetailsById(accountIdentifier, txnIdentifier);
            utilityBean.getRequestPayloadObject().setTaggedTransaction(Collections.singletonList(transaction));
        }


        //GET Agent Data
        List<String> agentList;
        if (utilityBean.getRequestTypeString().trim().equalsIgnoreCase(Constants.SERVICES_CONTROLLER_TYPE)) {
            return utilityBean;
        } else if (utilityBean.getRequestTypeString().trim().equalsIgnoreCase(Constants.COM_INSTANCE_TYPE)) {
            agentList = instanceDetails.getAgentIds();
            if (agentList == null || agentList.isEmpty()) {
                log.error("Unable to fetch agents for given service/comp instance: {}", utilityBean.getServiceIdString());
                return utilityBean;
            }
            utilityBean.getRequestPayloadObject().setAgentList(agentList);
        } else {
            log.error("Invalid request type provided: {}", utilityBean.getRequestPayloadObject().getRequestTypeString());
            throw new ServerException(String.format("Invalid request type provided: %s", utilityBean.getRequestPayloadObject().getRequestTypeString()));
        }

        return utilityBean;
    }

    @Override
    public UIData processData(UtilityBean<TransactionPerformanceDataRequest> configData) throws DataProcessingException {
        log.debug("{} processData(), with params: {}", Constants.INVOKED_METHOD, configData);

        if (DateTimeUtil.inRange(configData.getFromTime())) {
            return processRawTxnData(configData);
        }

        long start = System.currentTimeMillis();
        CollatedTransactionsSearchRepo collatedTxnRepo = new CollatedTransactionsSearchRepo();

        String accountIdentifier = configData.getAccountIdString();
        String serviceIdentifier = configData.getServiceIdString();
        String responseType = configData.getResponseType();
        String requestType = configData.getRequestTypeString();
        String txnId = configData.getRequestPayloadObject().getTransactionIdString();
        long fromTime = configData.getFromTime();
        long toTime = configData.getToTime();
        String txnGroupName = configData.getRequestPayloadObject().getGroupName();

        NewTimeIntervalGenerationUtility t = new NewTimeIntervalGenerationUtility();
        t.setTimezoneOffset(accountIdentifier);
        t.processTimeRange(fromTime, toTime);
        t.addEndTimeInOSTime();

        String timezoneId = t.getTimeZoneId();
        List<Long> times = t.getOSTimes();
        AggregationLevel aggregationLevel = t.getNewTimeRangeDefinition().getAggregationLevel();
        int aggregationValue = aggregationLevel.getAggregrationValue();

        UIData uiData = new UIData();
        uiData.setAggregationLevel(aggregationLevel.getAggregrationValue());
        uiData.setDateFormat(aggregationLevel.getDataTimePattern());
        uiData.setTime(t.getDisplayTimes());

        List<String> columns = new ArrayList<>();
        columns.add("serviceId");

        List<TabularResultsTypePojo> tabularResults;
        if (requestType.trim().equalsIgnoreCase(Constants.SERVICES_CONTROLLER_TYPE)) {
            if (txnId.equalsIgnoreCase("0")) {
                tabularResults = collatedTxnRepo.getTransactionCollatedDataByServiceTimeSeriesTrue(accountIdentifier,
                        serviceIdentifier, columns, txnGroupName, null, responseType, aggregationValue,
                        fromTime, toTime, configData.getTimeRangeDetails(),
                        configData.getTimeRangeDetailsList(), times, timezoneId);
            } else {
                tabularResults = collatedTxnRepo.getTransactionCollatedDataByServiceTimeSeriesTrue(accountIdentifier,
                        serviceIdentifier, columns, null, configData.getRequestPayloadObject().getTaggedTransaction().get(0).getIdentifier(),
                        responseType, aggregationValue, fromTime, toTime, configData.getTimeRangeDetails(),
                        configData.getTimeRangeDetailsList(), times, timezoneId);
            }


        } else {
            Set<String> agentList = configData.getRequestPayloadObject().getAgentList().parallelStream().collect(Collectors.toSet());

            if (txnId.equalsIgnoreCase("0")) {
                tabularResults = collatedTxnRepo.getAgentTransactionCollatedDataByServiceTimeSeriesTrue(accountIdentifier, agentList,
                        serviceIdentifier, columns, txnGroupName, null, responseType, aggregationValue,
                        fromTime, toTime, configData.getTimeRangeDetails(),
                        configData.getTimeRangeDetailsList(), times, timezoneId);
            } else {
                tabularResults = collatedTxnRepo.getAgentTransactionCollatedDataByServiceTimeSeriesTrue(accountIdentifier, agentList,
                        serviceIdentifier, columns, null, configData.getRequestPayloadObject().getTaggedTransaction().get(0).getIdentifier(),
                        responseType, aggregationValue, fromTime, toTime, configData.getTimeRangeDetails(),
                        configData.getTimeRangeDetailsList(), times, timezoneId);
            }
        }
        if (tabularResults == null || tabularResults.isEmpty()) {
            return uiData;
        }

        times.remove(times.size() - 1);

        List<AttributeKpiValuePojo> attributeKpiValuePojoList = extractDataFromCollatedTabularResult(tabularResults);
        Set<String> kpiNamesList = attributeKpiValuePojoList.parallelStream().map(AttributeKpiValuePojo::getAttributeName).collect(Collectors.toSet());
        Map<String, List<Double>> attributeKpiValueMap = getCollatedKpiValue(times, attributeKpiValuePojoList, kpiNamesList);

        List<BasicKpiEntity> basicKpiEntityList = new ComponentRepo().getComponentKpis(Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT, Constants.TRANSACTION_IDENTIFIER_DEFAULT);
        Map<String, List<BasicKpiEntity>> kpiDetailsMap = basicKpiEntityList.parallelStream().filter(c -> c.getStatus() == 1).collect(Collectors.groupingBy(BasicKpiEntity::getIdentifier));

        List<KpiData> kpiDataList = kpiNamesList.parallelStream().map(c -> {
            String kpiName = null;
            if (kpiDetailsMap.containsKey(c)) {
                kpiName = KPIAttributes.getDisplayName(kpiDetailsMap.get(c).get(0).getIdentifier());
            }

            KpiData kpiData = new KpiData();
            kpiData.setKpiId(kpiDetailsMap.containsKey(c) ? kpiDetailsMap.get(c).get(0).getId() : 0);
            kpiData.setGroupId(kpiDetailsMap.containsKey(c) ? kpiDetailsMap.get(c).get(0).getGroupId() : 0);
            kpiData.setKpi_name(kpiName);
            kpiData.setUnit(kpiDetailsMap.containsKey(c) ? kpiDetailsMap.get(c).get(0).getUnit() : null);
            if (!configData.getRequestPayloadObject().getTransactionIdString().equalsIgnoreCase("0")) {
                kpiData.setGroupDisplayValue(kpiData.getKpi_name());
            }

            kpiData.setValue(attributeKpiValueMap.get(c));
            return kpiData;
        }).collect(Collectors.toList());


        uiData.setChart_data(kpiDataList);

        log.debug("Time taken to process transaction performance time-series data is {} ms.", (System.currentTimeMillis() - start));

        return uiData;
    }

    private UIData processRawTxnData(UtilityBean<TransactionPerformanceDataRequest> configData) {

        long start = System.currentTimeMillis();
        RawTransactionSearchRepo rawTransactionSearchRepo = new RawTransactionSearchRepo();

        String accountIdentifier = configData.getAccountIdString();
        String serviceIdentifier = configData.getServiceIdString();
        String responseType = configData.getResponseType();
        String requestType = configData.getRequestTypeString();
        String txnId = configData.getRequestPayloadObject().getTransactionIdString();
        long fromTime = configData.getFromTime();
        long toTime = configData.getToTime();
        String txnGroupName = configData.getRequestPayloadObject().getGroupName();

        NewTimeIntervalGenerationUtility t = new NewTimeIntervalGenerationUtility();
        t.setTimezoneOffset(accountIdentifier);
        t.processTimeRange(fromTime, toTime);
        t.addEndTimeInOSTime();

        String timezoneId = t.getTimeZoneId();
        List<Long> times = t.getOSTimes();
        times.remove(times.size() - 1);

        AggregationLevel aggregationLevel = t.getNewTimeRangeDefinition().getAggregationLevel();
        int aggregationValue = aggregationLevel.getAggregrationValue();

        UIData uiData = new UIData();
        uiData.setAggregationLevel(aggregationValue);
        uiData.setDateFormat(aggregationLevel.getDataTimePattern());
        uiData.setTime(t.getDisplayTimes());

        TabularResults tabularResults;
        if (requestType.trim().equalsIgnoreCase(Constants.SERVICES_CONTROLLER_TYPE)) {
            if (txnId.equalsIgnoreCase("0")) {
                tabularResults = rawTransactionSearchRepo.getTransactionTSDataClusterLevel(accountIdentifier,
                        serviceIdentifier, txnGroupName, null, responseType, aggregationValue,
                        fromTime, toTime, timezoneId);
            } else {
                tabularResults = rawTransactionSearchRepo.getTransactionTSDataClusterLevel(accountIdentifier,
                        serviceIdentifier, null, configData.getRequestPayloadObject().getTaggedTransaction().get(0).getIdentifier(),
                        responseType, aggregationValue, fromTime, toTime, timezoneId);
            }


        } else {
            Set<String> agentList = configData.getRequestPayloadObject().getAgentList().parallelStream().collect(Collectors.toSet());

            if (txnId.equalsIgnoreCase("0")) {
                tabularResults = rawTransactionSearchRepo.getTransactionTSDataInstanceLevel(accountIdentifier,
                        serviceIdentifier, txnGroupName, null, agentList, responseType, aggregationValue,
                        fromTime, toTime, timezoneId);
            } else {
                tabularResults = rawTransactionSearchRepo.getTransactionTSDataInstanceLevel(accountIdentifier,
                        serviceIdentifier, null, configData.getRequestPayloadObject().getTaggedTransaction().get(0).getIdentifier(),
                        agentList, responseType, aggregationValue, fromTime, toTime, timezoneId);
            }
        }
        if (tabularResults == null || tabularResults.getRowResults() == null || tabularResults.getRowResults().isEmpty()) {
            return uiData;
        }

        List<BasicKpiEntity> basicKpiEntityList = new ComponentRepo().getComponentKpis(Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT, Constants.TRANSACTION_IDENTIFIER_DEFAULT);
        Map<String, List<BasicKpiEntity>> kpiDetailsMap = basicKpiEntityList.parallelStream().filter(c -> c.getStatus() == 1).collect(Collectors.groupingBy(BasicKpiEntity::getIdentifier));
        Set<String> kpiNamesList = kpiDetailsMap.keySet().parallelStream().map(KPIAttributes::getOpenSearchName).collect(Collectors.toSet());

        List<AttributeKpiValuePojo> attributeKpiValuePojoList = extractDataFromRawTabularResult(tabularResults);

        Map<String, List<Double>> attributeKpiValueMap = getRawKpiValue(times, attributeKpiValuePojoList, kpiNamesList);

        List<KpiData> kpiDataList = kpiDetailsMap.entrySet().parallelStream().map(c -> {

            String kpiName = KPIAttributes.getOpenSearchName(c.getKey()).toLowerCase();

            String kpiDisplayName = KPIAttributes.getDisplayName(c.getKey());

            KpiData kpiData = new KpiData();
            kpiData.setKpiId(c.getValue().get(0).getId());
            kpiData.setGroupId(c.getValue().get(0).getGroupId());
            kpiData.setKpi_name(kpiDisplayName);
            kpiData.setUnit(c.getValue().get(0).getUnit());
            if (!configData.getRequestPayloadObject().getTransactionIdString().equalsIgnoreCase("0")) {
                kpiData.setGroupDisplayValue(kpiData.getKpi_name());
            }

            kpiData.setValue(attributeKpiValueMap.get(kpiName));
            return kpiData;
        }).collect(Collectors.toList());

        uiData.setChart_data(kpiDataList);

        log.debug("Time taken to process transaction performance time-series data is {} ms.", (System.currentTimeMillis() - start));

        return uiData;

    }

    private Map<String, List<Double>> getCollatedKpiValue(List<Long> times, List<AttributeKpiValuePojo> attributeKpiValuePojoList, Set<String> kpiNamesList) {

        Map<String, List<Double>> attributeKpiValueMap = new LinkedHashMap<>();

        Map<String, List<AttributeKpiValuePojo>> attributeKpiPojoMap = attributeKpiValuePojoList.parallelStream()
                .collect(Collectors.groupingBy(AttributeKpiValuePojo::getAttributeName));

        for (String kpiName : kpiNamesList) {
            List<Double> values = new ArrayList<>();
            if (!attributeKpiPojoMap.containsKey(kpiName)) {
                times.forEach(t -> values.add(0D));
            } else {
                Map<Long, String> valuesMapFinal = new LinkedHashMap<>();
                attributeKpiPojoMap.get(kpiName).forEach(c -> valuesMapFinal.putAll(c.getValuesMap()));
                times.forEach(t -> values.add(!valuesMapFinal.containsKey(t) ? 0D : Double.parseDouble(valuesMapFinal.get(t))));
            }
            attributeKpiValueMap.put(kpiName, values);
        }

        return attributeKpiValueMap;

    }

    private Map<String, List<Double>> getRawKpiValue(List<Long> times, List<AttributeKpiValuePojo> attributeKpiValuePojoList, Set<String> kpiNamesList) {

        Map<String, List<Double>> attributeKpiValueMap = new LinkedHashMap<>();

        Map<String, List<AttributeKpiValuePojo>> attributeKpiPojoMap = attributeKpiValuePojoList.parallelStream()
                .collect(Collectors.groupingBy(c -> c.getAttributeName().toLowerCase()));

        for (String kpiName : kpiNamesList) {
            List<Double> values = new ArrayList<>();
            if (!attributeKpiPojoMap.containsKey(kpiName.toLowerCase())) {
                times.forEach(t -> values.add(0D));
            } else {
                Map<Long, String> valuesMapFinal = new LinkedHashMap<>();
                attributeKpiPojoMap.get(kpiName).forEach(c -> valuesMapFinal.putAll(c.getValuesMap()));
                times.forEach(t -> values.add(!valuesMapFinal.containsKey(t) ? 0D : Double.parseDouble(valuesMapFinal.get(t))));
            }
            attributeKpiValueMap.put(kpiName, values);
        }

        return attributeKpiValueMap;
    }

    private List<AttributeKpiValuePojo> extractDataFromCollatedTabularResult(List<TabularResultsTypePojo> tabularResultsTypePojoList) {

        List<AttributeKpiValuePojo> attributeKpiValuePojoList = new ArrayList<>();

        String pattern = ".+\\..*";
        for (TabularResultsTypePojo tabularResultsTypePojo : tabularResultsTypePojoList) {
            TabularResults tabularResults = tabularResultsTypePojo.getTabularResults();

            List<AttributeKpiValuePojo> tempAttributeKpiValuePojoList = new ArrayList<>();
            for (TabularResults.ResultRow resultRow : tabularResults.getRowResults()) {
                long time = resultRow.getTimestamp().getTime();
                for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {
                    AttributeKpiValuePojo attributeKpiValuePojo = new AttributeKpiValuePojo();
                    if (resultRowColumn.getColumnName().matches(pattern)) {
                        Map<Long, String> timeValueMap = new LinkedHashMap<>();

                        attributeKpiValuePojo.setAttributeName(resultRowColumn.getColumnName().split("\\.")[1]);
                        timeValueMap.put(time, resultRowColumn.getColumnValue());
                        attributeKpiValuePojo.setValuesMap(timeValueMap);
                        tempAttributeKpiValuePojoList.add(attributeKpiValuePojo);
                    }
                }
            }

            tempAttributeKpiValuePojoList.forEach(t -> {
                Map<String, List<AttributeKpiValuePojo>> attributeKpiPojoMap = attributeKpiValuePojoList.parallelStream()
                        .collect(Collectors.groupingBy(AttributeKpiValuePojo::getAttributeName));

                if (attributeKpiPojoMap.containsKey(t.getAttributeName())) {
                    Map<Long, String> tMap = new HashMap<>();
                    attributeKpiPojoMap.get(t.getAttributeName()).stream().map(AttributeKpiValuePojo::getValuesMap)
                            .flatMap(map -> map.entrySet().stream())
                            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue))
                            .forEach((key, value) -> {
                                if (t.getValuesMap().containsKey(key)) {
                                    String v1 = t.getValuesMap().get(key);
                                    tMap.put(key, String.valueOf(Double.parseDouble(v1) + Double.parseDouble(value)));
                                } else {
                                    tMap.putAll(t.getValuesMap());
                                }

                            });
                    attributeKpiValuePojoList.add(AttributeKpiValuePojo.builder()
                            .attributeName(t.getAttributeName())
                            .valuesMap(tMap).build());
                } else attributeKpiValuePojoList.add(t);
            });

        }
        return attributeKpiValuePojoList;
    }

    private List<AttributeKpiValuePojo> extractDataFromRawTabularResult(TabularResults tabularResults) {

        List<AttributeKpiValuePojo> attributeKpiValuePojoList = new ArrayList<>();

        for (TabularResults.ResultRow resultRow : tabularResults.getRowResults()) {
            long time = resultRow.getTimestamp().getTime();
            Map<Long, String> timeValueMap = new LinkedHashMap<>();
            AttributeKpiValuePojo attributeKpiValuePojo = new AttributeKpiValuePojo();
            for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {
                if (resultRowColumn.getColumnName().equalsIgnoreCase("responseTimes.responseStatusTag")) {
                    attributeKpiValuePojo.setAttributeName(resultRowColumn.getColumnValue());
                }
            }
            timeValueMap.put(time, String.valueOf(resultRow.getCountValue()));
            attributeKpiValuePojo.setValuesMap(timeValueMap);
            attributeKpiValuePojoList.add(attributeKpiValuePojo);
        }
        return attributeKpiValuePojoList;
    }

}
