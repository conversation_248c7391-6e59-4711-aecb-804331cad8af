package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.beans.TimezoneDetail;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.SignalType;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.AppsoneException;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.opensearch.SignalSearchRepo;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.SignalData;
import com.appnomic.appsone.api.pojo.SignalTimezonePojo;
import com.appnomic.appsone.api.pojo.TimezonePojo;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.api.util.StringUtils;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.Service;
import com.heal.configuration.pojos.Tags;
import com.heal.configuration.pojos.opensearch.SignalDetails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class TimezoneBySignalBL implements BusinessLogic<SignalData, UtilityBean<SignalData>, List<SignalTimezonePojo>> {
    private static final Logger LOGGER = LoggerFactory.getLogger(TimezoneBySignalBL.class);
    private static final String timezoneKey = ConfProperties.getString(Constants.TIMEZONE_TAG_DETAILS_IDETIFIER,
            Constants.TIMEZONE_TAG_DETAILS_IDETIFIER_DEFAULT);
    private static final String TYPE_ENTRY_NODE = "entryNode";
    private static final String TYPE_RCA_NODE = "rcaNode";

    public TimezoneBySignalBL() {
    }

    @Override
    public UtilityBean<SignalData> clientValidation(RequestObject requestObject) throws ClientException {
        LOGGER.debug("Inside Client validation");

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION_HEADER.toLowerCase());
        String signalId = requestObject.getParams().get(Constants.REQUEST_PARAM_SIGNAL_ID.toLowerCase());
        String identifier = requestObject.getParams().get(Constants.REQUEST_PARAM_IDENTIFIER);

        if(StringUtils.isEmpty(authKey)){
            LOGGER.error(Constants.MESSAGE_EMPTY_AUTH_TOKEN);
            throw new ClientException(Constants.MESSAGE_EMPTY_AUTH_TOKEN);
        }

        if(StringUtils.isEmpty(identifier)){
            LOGGER.error(Constants.MESSAGE_EMPTY_ACCOUNT);
            throw new ClientException(Constants.MESSAGE_EMPTY_ACCOUNT);
        }

        if(StringUtils.isEmpty(signalId)){
            LOGGER.error(UIMessages.ERROR_INVALID_INPUT);
            throw new ClientException(MessageFormat.format(UIMessages.ERROR_INVALID_INPUT_PARAM, Constants.REQUEST_PARAM_SIGNAL_ID, signalId));
        }
        SignalData signalData =  new SignalData();
        signalData.setId(signalId);

        return UtilityBean.<SignalData>builder()
                .authToken(authKey)
                .accountIdString(identifier)
                .requestPayloadObject(signalData)
                .build();
    }

    @Override
    public UtilityBean<SignalData> serverValidation(UtilityBean<SignalData> utilityBean) throws ServerException {
        LOGGER.debug("Inside Server validation");
        AccountRepo accountRepo = new AccountRepo();
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if(userId == null){
            throw new AppsoneException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }
        utilityBean.setUserId(userId);

        com.heal.configuration.pojos.Account account = accountRepo.getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            throw new AppsoneException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
        }
        utilityBean.setAccount(account);

        //Validate signal id
        SignalDetails signalDet = new SignalSearchRepo().getSignalsBySignalId(utilityBean.getRequestPayloadObject().getId(), utilityBean.getAccountIdString());

        if( signalDet == null ) {
            throw new ServerException(MessageFormat.format("Unable to fetch details for signal with id: {0}", utilityBean.getRequestPayloadObject().getId()));
        }else {
            SignalData signalData = utilityBean.getRequestPayloadObject();
            signalData.setId(signalDet.getSignalId());
            signalData.setType(getDisplayName(signalDet));
            signalData.setEntryServices(signalDet.getEntryServiceId());
            signalData.setRootCauseServiceSet(signalDet.getRootCauseServiceIds());
            signalData.setAffectedServices(signalDet.getServiceIds());
        }
        return utilityBean;
    }

    @Override
    public List<SignalTimezonePojo> processData(UtilityBean<SignalData> configData) throws DataProcessingException {

        AccountRepo accountRepo = new AccountRepo();
        String signalId = configData.getRequestPayloadObject().getId();
        String signalType = configData.getRequestPayloadObject().getType();
        Set<String> entryServiceIds = configData.getRequestPayloadObject().getEntryServices();
        Set<String> rootCauseServiceSet = configData.getRequestPayloadObject().getRootCauseServiceSet();
        Set<String> affectedServicesSet = configData.getRequestPayloadObject().getAffectedServices();

        Set<String> allServicesSet = Stream.of(rootCauseServiceSet, affectedServicesSet, entryServiceIds)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        LOGGER.debug("All Service: {}", allServicesSet);
        List<TimezoneDetail> listOfTimeZone = HealUICache.INSTANCE.getTimezones();

        Account account = accountRepo.getAccount(configData.getAccountIdString());
        Tags accountTimezoneTag = account.getTags().stream().filter((t) -> t.getType().equals(timezoneKey)).findAny().orElse(null);
        TimezoneDetail accountTimezone = accountTimezoneTag != null ? (listOfTimeZone.parallelStream()
                .filter((timeZone) -> timeZone.getId() == Integer.parseInt(accountTimezoneTag.getKey()))
                .findAny()
                .orElse(null)) : null;

        return allServicesSet.parallelStream()
                .map(serviceIdentifier -> {
                    Service service = HealUICache.INSTANCE.getServiceDetails(configData.getAccountIdString(), serviceIdentifier);
                    if(service == null) {
                        return null;
                    }
                    Tags serviceTimezoneTag = service.getTags().stream().filter((t) -> t.getType().equals(timezoneKey)).findAny().orElse(null);
                    if (serviceTimezoneTag == null) {
                        LOGGER.info("Timezone does not exists for service id: {} for signal id: {}. Will consider for account timezone.", serviceIdentifier, signalId);
                    }

                    TimezoneDetail serviceTimezone = serviceTimezoneTag != null ? listOfTimeZone.parallelStream()
                            .filter((timeZone) -> timeZone.getId() == Integer.parseInt(serviceTimezoneTag.getKey()))
                            .findAny()
                            .orElse(null) : null;

                    if(serviceTimezone == null && accountTimezone == null) {
                        LOGGER.error("Timezone does not exists for serviceId: {} and accountId:{} for signalId: {}. Will consider for account timezone.", serviceIdentifier, configData.getAccount().getIdentifier(), signalId);
                        return null;
                    }

                    if(serviceTimezone == null) {
                        serviceTimezone = accountTimezone;
                    }

                    String type = "";
                    if (signalType.equals(SignalType.PROBLEM.getDisplayName()) && null != entryServiceIds
                            && entryServiceIds.contains(serviceIdentifier)) {
                        type = TYPE_ENTRY_NODE;
                    } else if (null != rootCauseServiceSet && rootCauseServiceSet.contains(serviceIdentifier)) {
                        type = TYPE_RCA_NODE;
                    }


                    return SignalTimezonePojo.builder()
                            .signalType(signalType)
                            .serviceIdentifier(serviceIdentifier)
                            .type(type)
                            .timezoneDetails(TimezonePojo.builder()
                                    .id(serviceTimezone.getId())
                                    .timeZoneName(serviceTimezone.getTimeZoneName())
                                    .offsetName(serviceTimezone.getOffsetName())
                                    .timeOffset(serviceTimezone.getTimeOffset())
                                    .abbreviation(serviceTimezone.getAbbreviation())
                                    .build())
                            .build();
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private String getDisplayName(SignalDetails signal){
        String dispName = null;
        try {
            dispName = SignalType.valueOf(signal.getSignalType()).getDisplayName();
        }
        catch (Exception e){
            LOGGER.error("Error occurred in enum SignalType: values{}", signal.getSignalType(), e);
        }
        return dispName;
    }
}
