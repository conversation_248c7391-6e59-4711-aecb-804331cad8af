package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.beans.ControllerBean;
import com.appnomic.appsone.api.beans.keys.AccountServiceKey;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.AppsoneException;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.opensearch.HealthSearchRepo;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.InstanceRepo;
import com.appnomic.appsone.api.dao.redis.ServiceRepo;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.ServiceInstanceHealthDetails;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.service.mysql.ControllerDataService;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.api.util.DateTimeUtil;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.heal.configuration.entities.BasicInstanceBean;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.CompInstClusterDetails;
import com.heal.configuration.pojos.InstanceAttributes;
import com.heal.configuration.pojos.opensearch.CompInstanceHealthData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.Comparator.nullsFirst;

public class GetServiceInstanceHealth implements BusinessLogic<String, AccountServiceKey, List<ServiceInstanceHealthDetails>> {
    private static final Logger LOGGER = LoggerFactory.getLogger(GetServiceInstanceHealth.class);
    private static final int TIME_IN_MIN = ConfProperties.getInt(Constants.INSTANCE_HEALTH_INTERVAL_PROP, Constants.INSTANCE_HEALTH_INTERVAL_MIN_DEFAULT);

    @Override
    public UtilityBean<String> clientValidation(RequestObject request) throws ClientException {
        if (request == null) {
            LOGGER.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION_HEADER);

        if (authToken == null || authToken.trim().isEmpty()) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String identifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            LOGGER.error(UIMessages.ACCOUNT_IDENTIFIER_NULL_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_IDENTIFIER_NULL_EMPTY);
        }

        String serviceIdString = request.getParams().get(Constants.REQUEST_PARAM_SERVICE_ID);
        if (serviceIdString == null || serviceIdString.trim().isEmpty()) {
            LOGGER.error("Service ID is null or empty.");
            throw new ClientException("Service ID is null or empty.");
        }
        int serviceId;
        try {
            serviceId = Integer.parseInt(serviceIdString);
        } catch (NumberFormatException n) {
            LOGGER.error("Service ID should be a positive integer.");
            throw new ClientException("Service ID should be a positive integer.");
        }
        if (serviceId < 1) {
            LOGGER.error("Service ID should be a greater than zero.");
            throw new ClientException("Service ID should be a greater than zero.");
        }
        return UtilityBean.<String>builder()
                .accountIdString(identifier)
                .serviceId(serviceId)
                .authToken(authToken)
                .build();
    }

    @Override
    public AccountServiceKey serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        AccountRepo accountRepo = new AccountRepo();
        ServiceRepo serviceRepo = new ServiceRepo();
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID);
            throw new ServerException(UIMessages.AUTH_KEY_INVALID);
        }

        Account account = accountRepo.getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            LOGGER.error(UIMessages.INVALID_ACCOUNT_MESSAGE);
            throw new ServerException(UIMessages.INVALID_ACCOUNT_MESSAGE);
        }

        BasicEntity service = serviceRepo.getBasicServiceDetailsWithServiceId(account.getIdentifier(), utilityBean.getServiceId());
        if (service == null) {
            LOGGER.error("Invalid service ID [{}]", utilityBean.getServiceId());
            throw new ServerException(UIMessages.ERROR_INVALID_SERVICE_ID);
        }

        return AccountServiceKey.builder()
                .accountId(account.getId())
                .accountIdentifier(utilityBean.getAccountIdString())
                .serviceId(utilityBean.getServiceId())
                .build();
    }

    @Override
    public List<ServiceInstanceHealthDetails> processData(AccountServiceKey accountServiceKey) throws DataProcessingException {
        try {
            InstanceRepo instanceRepo = new InstanceRepo();

            List<BasicInstanceBean> basicInstanceBeanList =
                    new ServiceRepo().getAllInstanceDetailsWithServiceId(accountServiceKey.getAccountIdentifier(), accountServiceKey.getServiceId());

            Map<String, Long> instanceHealthBeanMap = basicInstanceBeanList.parallelStream()
                    .map(c -> new HealthSearchRepo().getInstanceHealthDetails(accountServiceKey.getAccountIdentifier(), c.getIdentifier()))
                    .flatMap(Collection::stream)
                    .collect(Collectors.toMap(CompInstanceHealthData::getCompInstanceIdentifier, CompInstanceHealthData::getLastDataReceivedTimeInGMT));

            int endTimeMilli = TIME_IN_MIN * 60 * 1000;
            Timestamp timestamp = DateTimeUtil.getCurrentTimestampInGMT();
            if (timestamp == null) {
                throw new DataProcessingException("Error while fetching current timestamp");
            }
            long currentTimeGmtMilli = timestamp.getTime();
            long timeBracketDiff = currentTimeGmtMilli - endTimeMilli;

            List<ServiceInstanceHealthDetails> serviceInstanceHealthDetails = basicInstanceBeanList.parallelStream()
                    .filter(inst-> inst.getClusterId()>0)
                    .map(basicInstanceBean ->
                    {
                        Long dataCollectionTimeMilli = instanceHealthBeanMap.getOrDefault(basicInstanceBean.getIdentifier(), 0L);
                        CompInstClusterDetails compDetails = instanceRepo.getInstanceDetailsWithInstIdentifier(accountServiceKey.getAccountIdentifier(), basicInstanceBean.getIdentifier());
                        Map<String, String> instanceAttributesMap = instanceRepo.getInstanceAttributesWithInstanceIdentifier(accountServiceKey.getAccountIdentifier(), basicInstanceBean.getIdentifier())
                                .parallelStream().distinct()
                                .collect(Collectors.toMap(InstanceAttributes::getAttributeName, InstanceAttributes::getAttributeValue));
                        return ServiceInstanceHealthDetails.builder()
                                .id(basicInstanceBean.getId())
                                .name(basicInstanceBean.getName())
                                .componentName(compDetails != null ? compDetails.getComponentName() : "")
                                .hostAddress(instanceAttributesMap.getOrDefault("HostAddress", ""))
                                .availabilityStatus(getDataCollectionStatus(dataCollectionTimeMilli, timeBracketDiff))
                                .availableLastTime(dataCollectionTimeMilli)
                                .build();
                    })
                    .collect(Collectors.toList());

            return getSortedList(serviceInstanceHealthDetails);
        } catch (AppsoneException e) {
            LOGGER.error("Error while fetching the instances health information. Details: ", e);
            throw new DataProcessingException(e.getMessage());
        }
    }

    protected int getDataCollectionStatus(Long dataCollectionTimeMilli, long timeBracketDiff) {
        int availabilityStatus = 1;
        if (dataCollectionTimeMilli < timeBracketDiff) {
            availabilityStatus = 0;
        }
        return availabilityStatus;
    }

    List<ServiceInstanceHealthDetails> getSortedList(List<ServiceInstanceHealthDetails> serviceInstanceHealthDetails) {
        if (Objects.isNull(serviceInstanceHealthDetails)) {
            return new ArrayList<>();
        }
        serviceInstanceHealthDetails.sort(Comparator.comparing(ServiceInstanceHealthDetails::getAvailabilityStatus)
                .thenComparing(ServiceInstanceHealthDetails::getAvailableLastTime, nullsFirst(Comparator.naturalOrder()))
                .thenComparing(ServiceInstanceHealthDetails::getName));
        return serviceInstanceHealthDetails;
    }
}