package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.beans.*;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.AppsoneException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.opensearch.MaintenanceWindowSearchRepo;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.MasterRepo;
import com.appnomic.appsone.api.dao.redis.ServiceRepo;
import com.appnomic.appsone.api.pojo.*;
import com.appnomic.appsone.api.service.mysql.MaintenanceWindowService;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.api.util.StringUtils;
import com.google.gson.Gson;
import com.heal.configuration.entities.BasicInstanceBean;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.Service;
import com.heal.configuration.pojos.opensearch.InstanceMaintenanceDetails;
import com.heal.configuration.pojos.opensearch.ServiceMaintenanceDetails;
import com.heal.configuration.pojos.ViewTypes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;

import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

public class MaintenanceWindowBL {
    private static final Logger logger = LoggerFactory.getLogger(MaintenanceWindowBL.class);
    private static final long offsetGmt = ConfProperties.getInt(Constants.OFFSET_FROM_GMT, Constants.OFFSET_FROM_GMT_DEFAULT);


    public UtilityBean<Object> clientValidations(Request request) {

        String startTimeStr = request.queryParams("fromTime");
        String endTimeStr = request.queryParams("toTime");
        String accountIdStr = request.params(Constants.REQUEST_PARAM_IDENTIFIER);
        String serviceIdStr = request.params(Constants.REQUEST_PARAM_SERVICE_ID);
        long st, et;
        int serviceId;

        if (StringUtils.isEmpty(startTimeStr)) {
            throw new AppsoneException(MessageFormat.format(UIMessages.ERROR_INVALID_INPUT_PARAM, "startTime", startTimeStr));
        }

        if (StringUtils.isEmpty(endTimeStr)) {
            throw new AppsoneException(MessageFormat.format(UIMessages.ERROR_INVALID_INPUT_PARAM, "endTime", endTimeStr));
        }

        if (StringUtils.isEmpty(accountIdStr)) {
            throw new AppsoneException(MessageFormat.format(UIMessages.ERROR_INVALID_INPUT_PARAM, Constants.REQUEST_PARAM_IDENTIFIER, accountIdStr));
        }

        if (StringUtils.isEmpty(serviceIdStr)) {
            throw new AppsoneException(MessageFormat.format(UIMessages.ERROR_INVALID_INPUT_PARAM, Constants.REQUEST_PARAM_SERVICE_ID, serviceIdStr));
        }

        try {
            serviceId = Integer.parseInt(serviceIdStr);
            st = Long.parseLong(startTimeStr);
            et = Long.parseLong(endTimeStr);
            Timestamp startTime = new Timestamp(st);
            Timestamp endTime = new Timestamp(et);

            if (endTime.before(startTime)) {
                logger.error("Start time {} cannot be greater than end time {}", startTimeStr, endTimeStr);
                throw new AppsoneException(MessageFormat.format(UIMessages.INVALID_TIME_INTERVAL, startTimeStr, endTimeStr));
            }
        } catch (NumberFormatException e) {
            throw new AppsoneException(e, "Error while parsing the maintenance window request.");
        }

        return UtilityBean.builder().accountIdentifier(accountIdStr).serviceId(serviceId).startTime(st).endTime(et).build();
    }

    public void serverValidations(UtilityBean<Object> utilityBean) throws ServerException {
        AccountRepo accountRepo = new AccountRepo();
        ServiceRepo serviceRepo = new ServiceRepo();

        com.heal.configuration.pojos.Account account = accountRepo.getAccount(utilityBean.getAccountIdentifier());
        if (account == null) {
            throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID + ":" + utilityBean.getAccountIdentifier());
        }
        utilityBean.setAccount(account);

        BasicEntity basicEntity = serviceRepo.getBasicServiceDetailsWithServiceId(account.getIdentifier(), utilityBean.getServiceId());
        if (basicEntity == null) {
            logger.error("Invalid service id input. No details found in redis for account {}, service id {}.", account.getIdentifier(), utilityBean.getServiceId());
            throw new ServerException(UIMessages.ERROR_INVALID_SERVICE_ID + ":" + utilityBean.getServiceId());
        }
        utilityBean.setService(Service.builder().id(basicEntity.getId()).identifier(basicEntity.getIdentifier()).name(basicEntity.getName()).build());
    }

    public List<MaintenanceDetailsBean> getMaintenanceWindowDetailsPerService(UtilityBean<Object> utilityBean) throws DataProcessingException {
        Gson gson = new Gson();
        List<MaintenanceDetailsBean> maintenanceDetailsBeanList = new ArrayList<>();

        long fromTime = utilityBean.getStartTime();
        long toTime = utilityBean.getEndTime();
        Service service = utilityBean.getService();

        List<ViewTypes> viewTypesList = new MasterRepo().getTypes();
        if (viewTypesList == null || viewTypesList.isEmpty()) {
            logger.error("No view types found in Redis.");
            throw new DataProcessingException("No view types found in Redis.");
        }

        ViewTypes scheduledType = viewTypesList.stream()
                .filter(v -> v.getTypeName().equalsIgnoreCase(Constants.MST_TYPE_MAINTENANCE))
                .filter(v -> v.getSubTypeName().equalsIgnoreCase(Constants.MST_SUB_TYPE_SCHEDULED))
                .findAny().orElseThrow(() -> {
                    logger.error("No {} view types found in redis.", Constants.MST_SUB_TYPE_SCHEDULED);
                    return new DataProcessingException(String.format("No '%s' view types found in Redis.", Constants.MST_SUB_TYPE_SCHEDULED));
                });
        ViewTypes recurringType = viewTypesList.stream()
                .filter(v -> v.getTypeName().equalsIgnoreCase(Constants.MST_TYPE_MAINTENANCE))
                .filter(v -> v.getSubTypeName().equalsIgnoreCase(Constants.MST_SUB_TYPE_RECURRING))
                .findAny().orElseThrow(() -> {
                    logger.error("No {} view types found in redis.", Constants.MST_SUB_TYPE_RECURRING);
                    return new DataProcessingException(String.format("No '%s' view types found in Redis.", Constants.MST_SUB_TYPE_RECURRING));
                });

        ViewTypes recurringDailyType = viewTypesList.stream()
                .filter(v -> v.getTypeName().equalsIgnoreCase(Constants.MST_TYPE_RECURRING))
                .filter(v -> v.getSubTypeName().equalsIgnoreCase(Constants.MST_SUB_TYPE_RECURRING_DAILY))
                .findAny().orElseThrow(() -> {
                    logger.error("No {} view types found in redis.", Constants.MST_SUB_TYPE_RECURRING_DAILY);
                    return new DataProcessingException(String.format("No '%s' view types found in Redis.", Constants.MST_SUB_TYPE_RECURRING_DAILY));
                });
        ViewTypes recurringWeeklyType = viewTypesList.stream()
                .filter(v -> v.getTypeName().equalsIgnoreCase(Constants.MST_TYPE_RECURRING))
                .filter(v -> v.getSubTypeName().equalsIgnoreCase(Constants.MST_SUB_TYPE_RECURRING_WEEKLY))
                .findAny().orElseThrow(() -> {
                    logger.error("No {} view types found in redis.", Constants.MST_SUB_TYPE_RECURRING_WEEKLY);
                    return new DataProcessingException(String.format("No '%s' view types found in Redis.", Constants.MST_SUB_TYPE_RECURRING_WEEKLY));
                });
        ViewTypes recurringMonthlyType = viewTypesList.stream()
                .filter(v -> v.getTypeName().equalsIgnoreCase(Constants.MST_TYPE_RECURRING))
                .filter(v -> v.getSubTypeName().equalsIgnoreCase(Constants.MST_SUB_TYPE_RECURRING_MONTHLY))
                .findAny().orElseThrow(() -> {
                    logger.error("No {} view types found in redis.", Constants.MST_SUB_TYPE_RECURRING_MONTHLY);
                    return new DataProcessingException(String.format("No '%s' view types found in Redis.", Constants.MST_SUB_TYPE_RECURRING_MONTHLY));
                });


        List<Integer> maintenanceList = getServiceMaintenanceList(service.getId());
        if (maintenanceList != null && !maintenanceList.isEmpty()) {
            logger.debug("Getting maintenance scheduled for service id:{}", service.getId());

            Object[] maintenanceDetailsLists = getMaintenanceDetails(maintenanceList, new Timestamp(fromTime), new Timestamp(toTime),
                    scheduledType, recurringType, recurringDailyType, recurringWeeklyType, recurringMonthlyType);
            if (maintenanceDetailsLists != null) {

                List<MaintenanceWindowBean> maintenanceDetails = (List<MaintenanceWindowBean>) maintenanceDetailsLists[0];

                Map<Integer, RecurringDetailsBean> recurringDetails = (Map<Integer, RecurringDetailsBean>) maintenanceDetailsLists[1];

                if (maintenanceDetails != null) {
                    maintenanceDetails.forEach(maintenanceWindow -> {
                        if (maintenanceWindow.getType().equals(Constants.MST_SUB_TYPE_SCHEDULED)) {
                            if (new Timestamp(toTime).before(new Timestamp(maintenanceWindow.getStartTime())))
                                maintenanceDetailsBeanList.add(MaintenanceDetailsBean.builder()
                                        .id(maintenanceWindow.getId())
                                        .isService(true)
                                        .type(maintenanceWindow.getType())
                                        .endTime(new Timestamp(maintenanceWindow.getEndTime()))
                                        .startTime(new Timestamp(maintenanceWindow.getStartTime()))
                                        .name(maintenanceWindow.getName())
                                        .status(Constants.MAINTENANCE_UPCOMING)
                                        .build());
                        } else {
                            if (recurringDetails != null) {
                                RecurringDetailsBean recurringDetailsBean = recurringDetails.get(maintenanceWindow.getId());
                                if (recurringDetailsBean != null) {
                                    RecurringDataBean recurringData = gson.fromJson(recurringDetailsBean.getRecurringData(), RecurringDataBean.class);

                                    maintenanceDetailsBeanList.add(MaintenanceDetailsBean.builder()
                                            .name(maintenanceWindow.getName())
                                            .isService(true)
                                            .startTime(new Timestamp(maintenanceWindow.getStartTime()))
                                            .endTime(new Timestamp(maintenanceWindow.getEndTime()))
                                            .id(maintenanceWindow.getId())
                                            .type(maintenanceWindow.getType())
                                            .recurring(RecurringWindowBean.builder()
                                                    .recurringType(recurringDetailsBean.getRecurringType())
                                                    .startHrMin(recurringDetailsBean.getStartHrMin())
                                                    .endHrMin(recurringDetailsBean.getEndHrMin())
                                                    .day(recurringData.getDay())
                                                    .week(recurringData.getWeek())
                                                    .month(recurringData.getMonth())
                                                    .weekNames(recurringData.getWeeknames())
                                                    .build())
                                            .status(Constants.MAINTENANCE_UPCOMING)
                                            .build());
                                }
                            }
                        }
                    });
                }

                MaintenanceWindowSearchRepo maintenanceWindowSearchRepo = new MaintenanceWindowSearchRepo();
                //GET Completed Service maintenance windows in the selected times range
                List<ServiceMaintenanceDetails> completedServiceMaintenanceWindows = maintenanceWindowSearchRepo.getServiceMaintenanceDetails(utilityBean.getAccount().getIdentifier(),
                        service.getIdentifier(), fromTime, toTime);
                completedServiceMaintenanceWindows.forEach(maintenanceWindow -> {
                    if (maintenanceWindow.getEndTime() <= System.currentTimeMillis()) {
                        maintenanceDetailsBeanList.add(MaintenanceDetailsBean.builder()
                                .name(service.getIdentifier())
                                .isService(true)
                                .startTime(new Timestamp(maintenanceWindow.getStartTime()))
                                .endTime(new Timestamp(maintenanceWindow.getEndTime()))
                                .status(Constants.MAINTENANCE_COMPLETED)
                                .build());
                    }
                });

                //GET OnGoing Service maintenance windows in the selected times range
                List<ServiceMaintenanceDetails> onGoingServiceMaintenanceWindows = maintenanceWindowSearchRepo.getServiceMaintenanceDetails(utilityBean.getAccount().getIdentifier(),
                        service.getIdentifier(), toTime, 0);
                onGoingServiceMaintenanceWindows.forEach(maintenanceWindow ->
                        maintenanceDetailsBeanList.add(MaintenanceDetailsBean.builder()
                                .name(service.getIdentifier())
                                .isService(true)
                                .startTime(new Timestamp(maintenanceWindow.getStartTime()))
                                .endTime(new Timestamp(maintenanceWindow.getEndTime()))
                                .status(Constants.MAINTENANCE_ONGOING)
                                .build()));

            }
        }

        //Instance maintenance check
        List<BasicInstanceBean> serviceInstances = HealUICache.INSTANCE.getServiceInstanceList(utilityBean.getAccount().getIdentifier(),
                service.getIdentifier(), false);

        Set<String> instanceIdentifiers = serviceInstances.stream().map(BasicInstanceBean::getIdentifier).collect(Collectors.toSet());
        maintenanceDetailsBeanList.addAll(getInstanceMaintenance(utilityBean.getAccount().getIdentifier(), instanceIdentifiers, fromTime, toTime));

        return maintenanceDetailsBeanList;
    }


    private List<MaintenanceDetailsBean> getInstanceMaintenance(String accountIdentifier, Set<String> instanceIdentifierSet, long fromTime, long toTime) {

        List<MaintenanceDetailsBean> maintenanceDetailsBeans = new ArrayList<>();

        MaintenanceWindowSearchRepo maintenanceWindowSearchRepo = new MaintenanceWindowSearchRepo();
        //GET Completed Instance maintenance windows in the selected times range
        List<InstanceMaintenanceDetails> completedInstanceMaintenanceWindows = maintenanceWindowSearchRepo.getInstanceMaintenanceDetails(accountIdentifier,
                instanceIdentifierSet, fromTime, toTime);
        completedInstanceMaintenanceWindows.forEach(maintenanceWindow ->
                maintenanceDetailsBeans.add(MaintenanceDetailsBean.builder()
                        .name(maintenanceWindow.getInstanceIdentifier())
                        .isService(false)
                        .type(Constants.MST_SUB_TYPE_SCHEDULED)
                        .startTime(new Timestamp(maintenanceWindow.getStartTime()))
                        .endTime(new Timestamp(maintenanceWindow.getEndTime()))
                        .status(Constants.MAINTENANCE_COMPLETED)
                        .build()));

        //GET OnGoing Instance maintenance windows in the selected times range
        List<InstanceMaintenanceDetails> onGoingInstanceMaintenanceWindows = maintenanceWindowSearchRepo.getInstanceMaintenanceDetails(accountIdentifier,
                instanceIdentifierSet, toTime, 0);
        onGoingInstanceMaintenanceWindows.forEach(maintenanceWindow ->
                maintenanceDetailsBeans.add(MaintenanceDetailsBean.builder()
                        .name(maintenanceWindow.getInstanceIdentifier())
                        .type(Constants.MST_SUB_TYPE_SCHEDULED)
                        .isService(false)
                        .startTime(new Timestamp(maintenanceWindow.getStartTime()))
                        .endTime(new Timestamp(maintenanceWindow.getEndTime()))
                        .status(Constants.MAINTENANCE_ONGOING)
                        .build()));

        return maintenanceDetailsBeans;
    }

    private static List<Integer> getServiceMaintenanceList(int serviceId) {
        List<ServiceMaintenanceMapping> maintenanceMappingList = MaintenanceWindowService.getMaintenanceWindowsByServiceId(serviceId);
        if (maintenanceMappingList == null) {
            logger.error("Error occurred while getting getServiceMaintenanceList");
            return null;
        } else if (maintenanceMappingList.isEmpty()) {
            logger.info("No maintenance scheduled for service id:{}", serviceId);
            return new ArrayList<>();
        }

        return maintenanceMappingList
                .stream()
                .map(ServiceMaintenanceMapping::getMaintenanceId)
                .collect(Collectors.toList());
    }

    private static Object[] getMaintenanceDetails(List<Integer> maintenanceList, Timestamp startTime, Timestamp endTime,
                                                  ViewTypes scheduledType, ViewTypes recurringType,
                                                  ViewTypes recurringDailyType, ViewTypes recurringWeeklyType, ViewTypes recurringMonthlyType) {
        //for querying from db, remove time. use only date
        Calendar tempStartCal = Calendar.getInstance();
        tempStartCal.setTimeInMillis(startTime.getTime());
        tempStartCal.set(Calendar.HOUR_OF_DAY, 0);
        tempStartCal.set(Calendar.MINUTE, 0);
        tempStartCal.set(Calendar.SECOND, 0);
        tempStartCal.set(Calendar.MILLISECOND, 0);

        List<MaintenanceDetails> maintenanceDetailsList = MaintenanceWindowService.getMaintenanceWindowDetailsList(maintenanceList, new Timestamp(tempStartCal.getTimeInMillis()), endTime);
        if (maintenanceDetailsList == null) {
            logger.warn("getMaintenanceScheduled: maintenanceDetailsList is null");
            return null;
        }

        List<MaintenanceWindowBean> maintenanceWindowBeanList = maintenanceDetailsList
                .stream()
                .map(i -> {
                    if (i.getTypeId() == scheduledType.getSubTypeId())
                        return new MaintenanceWindowBean(i.getId(), Constants.MST_SUB_TYPE_SCHEDULED, i.getName(), i.getStartTime().getTime(), i.getEndTime().getTime());
                    else if (i.getTypeId() == recurringType.getSubTypeId())
                        return new MaintenanceWindowBean(i.getId(), Constants.MST_SUB_TYPE_RECURRING, i.getName(), i.getStartTime().getTime(), i.getEndTime().getTime());
                    else
                        return new MaintenanceWindowBean(i.getId(), String.valueOf(i.getTypeId()), i.getName(), i.getStartTime().getTime(), i.getEndTime().getTime());
                })
                .collect(Collectors.toList());

        List<RecurringDetails> recurringDetailsList = MaintenanceWindowService.getRecurringDetailsList(maintenanceList);
        Map<Integer, RecurringDetailsBean> recurringDetailsMap = new HashMap<>();
        if (recurringDetailsList != null && !recurringDetailsList.isEmpty()) {
            recurringDetailsMap = recurringDetailsList
                    .stream()
                    .collect(Collectors.toMap(RecurringDetails::getMaintenanceId,
                            r -> {
                                if (r.getRecurringTypeId() == recurringDailyType.getSubTypeId())
                                    return new RecurringDetailsBean(r.getMaintenanceId(), Constants.MST_SUB_TYPE_RECURRING_DAILY, r.getStartHrMin(), r.getEndHrMin(), r.getRecurringData());
                                else if (r.getRecurringTypeId() == recurringWeeklyType.getSubTypeId())
                                    return new RecurringDetailsBean(r.getMaintenanceId(), Constants.MST_SUB_TYPE_RECURRING_WEEKLY, r.getStartHrMin(), r.getEndHrMin(), r.getRecurringData());
                                else if (r.getRecurringTypeId() == recurringMonthlyType.getSubTypeId())
                                    return new RecurringDetailsBean(r.getMaintenanceId(), Constants.MST_SUB_TYPE_RECURRING_MONTHLY, r.getStartHrMin(), r.getEndHrMin(), r.getRecurringData());
                                else
                                    return new RecurringDetailsBean(r.getMaintenanceId(), String.valueOf(r.getRecurringTypeId()), r.getStartHrMin(), r.getEndHrMin(), r.getRecurringData());
                            }));
        }
        return new Object[]{maintenanceWindowBeanList, recurringDetailsMap};
    }

    public boolean isUnderMaintenance(List<com.heal.configuration.pojos.MaintenanceDetails> maintenanceDetailsList, long timeInMilliSecs) {
        Timestamp timestamp;
        try {
            timestamp = new Timestamp(timeInMilliSecs + offsetGmt);
        } catch (Exception e) {
            logger.error("Error while timestamp conversion, gmtTimeInMilliSecs:{}", timeInMilliSecs, e);
            return false;
        }
        boolean isUnderMaintenance;
        for (com.heal.configuration.pojos.MaintenanceDetails maintenanceDetails : maintenanceDetailsList) {
            if (maintenanceDetails == null) continue;
            logger.debug("maintenanceDetails== {}", maintenanceDetails);
            Timestamp startTime = maintenanceDetails.getStartTime();
            Timestamp endTime = maintenanceDetails.getEndTime();
            if (startTime == null) return false;
            logger.debug("isUnderMaintenance() : data time : {}, maintenance start time : {}, maintenance end time : {}.", timestamp.getTime(), startTime.getTime(), endTime != null ? endTime.getTime() : null);
            if (endTime == null) {
                isUnderMaintenance = (startTime.before(timestamp) || startTime.equals(timestamp));
            } else {
                isUnderMaintenance =
                        ((startTime.before(timestamp) || startTime.equals(timestamp)) && (endTime.after(timestamp) || endTime.equals(timestamp)));
            }
            if (isUnderMaintenance) return isUnderMaintenance;
        }
        return false;
    }

}