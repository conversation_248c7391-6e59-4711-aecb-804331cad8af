package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.ApplicationRepo;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.ServiceInstanceDetailsResponse;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.StringUtils;
import com.appnomic.appsone.api.util.ValidationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.MessageFormat;
import java.util.List;
import java.util.stream.Collectors;

public class ApplicationServicesBL implements BusinessLogic<Object, UtilityBean<Object>, List<ServiceInstanceDetailsResponse>> {
    private static final Logger LOGGER = LoggerFactory.getLogger(ApplicationServicesBL.class);

    @Override
    public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
        LOGGER.debug("Inside Client validation");

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION_HEADER);
        String identifier = requestObject.getParams().get(Constants.REQUEST_PARAM_IDENTIFIER);
        String applicationIdStr = requestObject.getParams().get(Constants.REQUEST_PARAM_APPLICATION_ID);
        int applicationId;

        if (StringUtils.isEmpty(authKey)) {
            LOGGER.error(Constants.MESSAGE_EMPTY_AUTH_TOKEN);
            throw new ClientException(Constants.MESSAGE_EMPTY_AUTH_TOKEN);
        }

        if (StringUtils.isEmpty(identifier)) {
            LOGGER.error(Constants.MESSAGE_EMPTY_ACCOUNT);
            throw new ClientException(Constants.MESSAGE_EMPTY_ACCOUNT);
        }

        if (applicationIdStr == null || applicationIdStr.trim().isEmpty()) {
            LOGGER.error("Application id should not be empty or null.");
            throw new ClientException(UIMessages.SERVICE_EMPTY_ERROR);
        } else {
            try {
                applicationId = Integer.parseInt(applicationIdStr);
            } catch (NumberFormatException e) {
                LOGGER.error("Error occurred while converting the application id {}. Reason: {}", applicationIdStr, e.getMessage());
                throw new ClientException(UIMessages.INVALID_SERVICE);
            }
        }

        return UtilityBean.builder()
                .accountIdString(identifier)
                .authToken(authKey)
                .applicationId(applicationId)
                .build();
    }

    @Override
    public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
        LOGGER.debug("Inside Server validation");
        AccountRepo accountRepo = new AccountRepo();
        ApplicationRepo applicationRepo = new ApplicationRepo();
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            throw new ServerException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }
        utilityBean.setUserId(userId);

        com.heal.configuration.pojos.Account account = accountRepo.getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
        }
        utilityBean.setAccount(account);

        com.heal.configuration.pojos.Application application = applicationRepo.getApplicationDetailsWithAppId(account.getIdentifier(), utilityBean.getApplicationId());
        if (application == null) {
            LOGGER.error("Error occurred while getting application list for account: {}", account.getIdentifier());
            throw new ServerException(MessageFormat.format("Invalid application id: {0}", utilityBean.getApplicationId()));
        }
        utilityBean.setApplication(application);

        return utilityBean;
    }

    @Override
    public List<ServiceInstanceDetailsResponse> processData(UtilityBean<Object> configData) throws DataProcessingException {
        LOGGER.debug("Inside Process Data");

        return HealUICache.INSTANCE.getApplicationServiceList(configData.getAccount().getIdentifier(),
                        configData.getApplication().getIdentifier())
                .stream()
                .map(basicEntity -> ServiceInstanceDetailsResponse.builder()
                        .id(basicEntity.getId())
                        .identifier(basicEntity.getIdentifier())
                        .name(basicEntity.getName())
                        .build()
                )
                .collect(Collectors.toList());
    }
}
