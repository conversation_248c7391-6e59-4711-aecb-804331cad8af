package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.beans.*;
import com.appnomic.appsone.api.cache.UserDetailsCache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.AppsoneException;
import com.appnomic.appsone.api.dao.redis.*;
import com.appnomic.appsone.api.manager.MySQLConnectionManager;
import com.appnomic.appsone.api.pojo.*;
import com.appnomic.appsone.api.pojo.IdPojo;
import com.appnomic.appsone.api.pojo.NotificationChoice;
import com.appnomic.appsone.api.service.mysql.ControllerDataService;
import com.appnomic.appsone.api.service.mysql.MasterDataService;
import com.appnomic.appsone.api.service.mysql.NotificationPreferencesDataService;
import com.appnomic.appsone.api.util.DateTimeUtil;
import com.google.common.base.Throwables;
import com.heal.configuration.enums.NotificationPreferencesType;
import com.heal.configuration.pojos.*;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.ViewTypes;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;

import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class NotificationBL {
    public NotificationsPojo processRequestAndGetNotificationTypeList(Account account, String userIdentifierFromQuery, String userIdentifierFromAuthToken) throws AppsoneException {
        int accountId = account.getId();

        UserAccessDetails accessDetails;
        try {
            accessDetails = UserDetailsCache.getInstance().userApplications.get(new UserAccountIdentifiersBean(userIdentifierFromQuery, account.getIdentifier()));
        } catch (ExecutionException e) {
            log.error("Exception while fetching user access details. Reason: {}", e.getMessage(), e);
            throw new AppsoneException("Exception while fetching user access details");
        }

        if (accessDetails == null) {
            log.error("User does not have access to given account. Please contact admin");
            throw new AppsoneException(UIMessages.NOTIFICATION_ACCOUNT_ERROR);
        }

        NotificationsPojo notificationsPojo = new NotificationsPojo();

        List<ViewTypes> allViewTypes = new MasterRepo().getTypes();

        Map<String, List<NotificationTypePojo>> metaData = getNotificationType(account.getId(), allViewTypes);
        notificationsPojo.setMetaData(metaData);

        Set<Integer> appIds = new HashSet<>();
        List<Controller> applicationList = getControllersByTypeAndIdentifier(accountId, accessDetails.getApplicationIdentifiers(), allViewTypes);
        if (Objects.nonNull(applicationList)) {
            applicationList.forEach(c -> appIds.add(Integer.valueOf(c.getAppId())));
        }

        DBI dbi = MySQLConnectionManager.getInstance().getHandle();
        dbi.inTransaction((handle, status) -> {
            Set<NotificationBean> userNotificationMappings = NotificationPreferencesDataService.getUserNotificationMapping(accountId, userIdentifierFromQuery, null);

            Set<NotificationBean> applicationNotificationPreferences = NotificationPreferencesDataService.getApplicationNotificationPreferences(accountId, handle);
            if (!applicationNotificationPreferences.isEmpty()) {
                applicationNotificationPreferences = applicationNotificationPreferences.stream().filter(c -> appIds.contains(c.getApplicationId())).collect(Collectors.toSet());
                createDefaultNotificationsPreferences(accountId, userIdentifierFromQuery, userIdentifierFromAuthToken, applicationNotificationPreferences,
                        userNotificationMappings, handle);
            }

            List<UserPreferencesPojo> userPreferencesList = setNotification(userNotificationMappings.stream()
                    .filter(c -> appIds.contains(c.getApplicationId())).collect(Collectors.toSet()), applicationList, allViewTypes);
            notificationsPojo.setPreferences(userPreferencesList);

            UserNotificationDetailsBean userNotificationDetailsBean = NotificationPreferencesDataService.getUserNotificationDetails(userIdentifierFromQuery, handle);
            if (Objects.isNull(userNotificationDetailsBean)) {
                userNotificationDetailsBean = createAndAddDefaultNotificationsDetails(userIdentifierFromQuery, userIdentifierFromAuthToken, handle);
            }
            notificationsPojo.setEmailNotification(userNotificationDetailsBean.isEmailEnabled());
            notificationsPojo.setSmsNotification(userNotificationDetailsBean.isSmsEnabled());

            NotificationChoice notificationChoice = new NotificationChoice();

            UserNotificationDetailsBean finalUserNotificationDetailsBean = userNotificationDetailsBean;
            ViewTypes notificationChoiceType = allViewTypes.stream()
                    .filter(t -> t.getSubTypeId() == finalUserNotificationDetailsBean.getNotificationPreferenceId())
                    .findAny()
                    .orElse(null);
            notificationChoice.setType(notificationChoiceType == null ? null : notificationChoiceType.getSubTypeName());

            UserNotificationChoiceBean userNotificationChoiceBean = NotificationPreferencesDataService.getUserNotificationChoice(userIdentifierFromQuery, handle);
            if (Objects.nonNull(userNotificationChoiceBean)) {
                notificationChoice.setComponent(userNotificationChoiceBean.getComponentSelection());

                String ids = getListOfIds(notificationChoice, userNotificationChoiceBean);

                notificationChoice.setCategories((null == ids) ? null : Arrays.stream(ids.substring(1, ids.length() - 1).split(","))
                        .map(String::trim)
                        .mapToInt(Integer::parseInt)
                        .boxed()
                        .collect(Collectors.toList()));
            }
            notificationsPojo.setNotificationChoice(notificationChoice);

            UserAttributesPojo userAttributesPojo = NotificationPreferencesDataService.getEmailNotificationDetails(userIdentifierFromQuery, handle);
            notificationsPojo.setUserAttributes(userAttributesPojo);

            return notificationsPojo;
        });

        return notificationsPojo;
    }

    private Map<String, List<NotificationTypePojo>> getNotificationType(int accountId, List<ViewTypes> allViewTypes) throws AppsoneException {
        Map<String, List<NotificationTypePojo>> metaData = new HashMap<>();
        List<NotificationTypePojo> notificationTypeList = new ArrayList<>();

        List<ViewTypes> type = allViewTypes.stream()
                .filter(t -> t.getTypeName().equalsIgnoreCase("NotificationType"))
                .collect(Collectors.toList());
        if (type.isEmpty()) {
            log.error("Notification type unavailable.");
            throw new AppsoneException("Type notificationType is not found in DB");
        }
        List<NotificationSettingsBean> notificationSetting = NotificationPreferencesDataService.getNotificationSettingsForAccount(accountId);

        type.forEach(a -> {
            NotificationTypePojo notificationTypePojo = new NotificationTypePojo();

            notificationTypePojo.setId(a.getSubTypeId());
            notificationTypePojo.setName(a.getSubTypeName());

            NotificationSettingsBean notificationSettingsBean = notificationSetting.stream().filter(s -> s.getTypeId() == a.getSubTypeId()).findAny().orElse(null);
            if (Objects.nonNull(notificationSettingsBean)) {
                notificationTypePojo.setDuration(notificationSettingsBean.getDurationInMin());
            }

            notificationTypeList.add(notificationTypePojo);
        });

        metaData.put("notificationType", notificationTypeList);

        return metaData;
    }

    private List<Controller> getControllersByTypeAndIdentifier(int accountId, List<String> appIdentifiers, List<ViewTypes> allViewTypes) {
        List<Controller> filtratedControllerList = new ArrayList<>();
        try {
            ViewTypes subTypeBean = allViewTypes.stream()
                    .filter(t -> t.getTypeName().equalsIgnoreCase(Constants.CONTROLLER_TYPE_NAME_DEFAULT))
                    .filter(t -> t.getSubTypeName().equalsIgnoreCase(Constants.APPLICATION_CONTROLLER_TYPE))
                    .findAny()
                    .orElse(null);

            if (null == subTypeBean) {
                log.error("There are type name:{}, sub type:{}", Constants.CONTROLLER_TYPE_NAME_DEFAULT, Constants.APPLICATION_CONTROLLER_TYPE);
                return filtratedControllerList;
            }

            List<Controller> controllerList = ControllerDataService.getControllerList(accountId);
            if (null == controllerList) {
                log.error("There are no controllers mapped to account [{}]", accountId);
                return filtratedControllerList;
            }

            filtratedControllerList = controllerList.stream().
                    filter(t -> t.getControllerTypeId() == subTypeBean.getSubTypeId()).
                    filter(t -> appIdentifiers.contains(t.getIdentifier())).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Error occurred while fetching controller details for service name: {} account id: {}", Constants.APPLICATION_CONTROLLER_TYPE, accountId, e);
        }
        return filtratedControllerList;
    }

    private void createDefaultNotificationsPreferences(int accountId, String userIdentifierFromQuery, String userIdentifierFromAuthToken,
                                                       Set<NotificationBean> applicationNotificationPreferences, Set<NotificationBean> userNotificationMappings,
                                                       Handle handle) throws AppsoneException {
        List<NotificationBean> addNotificationBeanList = new ArrayList<>();
        applicationNotificationPreferences.forEach(nb -> {
            if (userNotificationMappings.stream().noneMatch(n -> n.getApplicationId() == nb.getApplicationId()
                    && n.getSeverityTypeId() == nb.getSeverityTypeId()
                    && n.getSignalTypeId() == nb.getSignalTypeId())) {
                NotificationBean addNotificationBean = createNotificationPreferencesBean(nb.getNotificationTypeId(), nb.getSignalTypeId(),
                        nb.getSeverityTypeId(), nb.getApplicationId(), userIdentifierFromQuery, userIdentifierFromAuthToken, accountId);

                addNotificationBeanList.add(addNotificationBean);
            }
        });

        if (!addNotificationBeanList.isEmpty()) {
            int[] notificationId = NotificationPreferencesDataService.addNotificationDetails(addNotificationBeanList, handle);
            if (notificationId.length == 0) {
                log.error(UIMessages.LIST_NOTIFICATION_ERROR);
                throw new AppsoneException(UIMessages.NOTIFICATION_DETAILS_ERROR);
            }
        }

        log.info("[{}] notification preferences added for user [{}].", addNotificationBeanList.size(), userIdentifierFromQuery);
    }

    private List<UserPreferencesPojo> setNotification(Set<NotificationBean> userNotificationMappings, List<Controller> appList, List<ViewTypes> allViewTypes) {
        List<UserPreferencesPojo> userPreferencesList = new ArrayList<>();

        Map<Integer, String> viewTypesMap = allViewTypes.stream().collect(Collectors.toMap(ViewTypes::getSubTypeId, ViewTypes::getSubTypeName));

        userNotificationMappings.forEach(c -> userPreferencesList.add(userList(c, appList, viewTypesMap)));

        return userPreferencesList;
    }

    private UserPreferencesPojo userList(NotificationBean nb, List<Controller> appList, Map<Integer, String> viewTypesMap) {
        UserPreferencesPojo userPreferencesPojo = new UserPreferencesPojo();
        String appName = "";

        Optional<Controller> data = appList.stream()
                .filter(c -> c.getAppId().equals(String.valueOf(nb.getApplicationId())))
                .findAny();

        if (data.isPresent()) {
            appName = data.get().getName();
        }

        userPreferencesPojo.setApplicationName(appName);
        userPreferencesPojo.setApplicationId(nb.getApplicationId());
        userPreferencesPojo.setNotificationTypeId(nb.getNotificationTypeId());
        userPreferencesPojo.setSeverityTypeId(nb.getSeverityTypeId());
        userPreferencesPojo.setSeverityType(viewTypesMap.getOrDefault(nb.getSeverityTypeId(), "Default"));
        userPreferencesPojo.setSignalTypeId(nb.getSignalTypeId());
        userPreferencesPojo.setSignalType(viewTypesMap.getOrDefault(nb.getSignalTypeId(), "Early Warning"));

        return userPreferencesPojo;
    }

    private UserNotificationDetailsBean createAndAddDefaultNotificationsDetails(String userIdentifierFromQuery, String userIdentifierFromAuthToken, Handle handle) throws AppsoneException {
        UserNotificationDetailsBean createNotificationDetails = createNotificationDetails(userIdentifierFromQuery, userIdentifierFromAuthToken);

        int notificationUserId = NotificationPreferencesDataService.addNotificationUserDetails(createNotificationDetails, handle);
        if (notificationUserId == -1) {
            log.error(UIMessages.LOG_NOTIFICATION_ERROR);
            throw new AppsoneException(UIMessages.ADD_NOTIFICATION_ERROR);
        }

        return createNotificationDetails;
    }

    private UserNotificationDetailsBean createNotificationDetails(String userIdentifierFromQuery, String userIdentifierFromAuthToken) {
        Date startDate = DateTimeUtil.getCurrentTimestampInGMT();
        return UserNotificationDetailsBean.builder()
                .emailEnabled(true)
                .smsEnabled(true)
                .forensicEnabled(Constants.STATUS_INACTIVE)
                .forensicNotificationSuppression(-1)
                .accountId(Constants.DEFAULT_ACCOUNT_ID)
                .applicableUserId(userIdentifierFromQuery)
                .userDetailsId(userIdentifierFromAuthToken)
                .createdTime(startDate)
                .updatedTime(startDate)
                .notificationPreferenceId(308)
                .build();
    }

    private String getListOfIds(NotificationChoice notificationChoice, UserNotificationChoiceBean userNotificationChoiceBean) {
        String ids;
        if (notificationChoice.getType().equalsIgnoreCase(NotificationPreferencesType.COMPONENT_NAME.getPreference())) {
            ids = userNotificationChoiceBean.getComponentIds();
        } else if (notificationChoice.getType().equalsIgnoreCase(NotificationPreferencesType.COMPONENT_TYPE.getPreference())) {
            ids = userNotificationChoiceBean.getComponentTypeIds();
        } else {
            ids = userNotificationChoiceBean.getCategoryIds();
        }

        return ids;
    }

    private static NotificationBean createNotificationPreferencesBean(int notificationTypeId, int signalTypeId, int severityTypeId, int appId,
                                                                      String userIdentifierFromQuery, String userIdentifierFromAuthToken, int accountId) {
        Date startDate = DateTimeUtil.getCurrentTimestampInGMT();
        return NotificationBean.builder()
                .accountId(accountId)
                .notificationTypeId(notificationTypeId)
                .severityTypeId(severityTypeId)
                .signalTypeId(signalTypeId)
                .applicationId(appId)
                .applicableUserId(userIdentifierFromQuery)
                .userDetailsId(userIdentifierFromAuthToken)
                .status(Constants.STATUS_ACTIVE)
                .createdTime(startDate)
                .updatedTime(startDate)
                .build();
    }

    /*Update notification preference part----------------------------------------------------*/
    public void createNotifications(PreferencesPojo userPreferencesPojo, String applicableUser, String userId, com.heal.configuration.pojos.Account account)
            throws AppsoneException {
        DBI dbi = MySQLConnectionManager.getInstance().getHandle();
        int accountId = account.getId();

        try {
            dbi.inTransaction((handle, status) -> {
                List<NotificationBean> updateNotificationBeanList = new ArrayList<>();
                List<UserPreferencesPojo> preferencesList = userPreferencesPojo.getPreferences();

                preferencesList.forEach(nb -> {

                    int count = NotificationPreferencesDataService.getNotificationPreferencesForUser(nb.getSignalTypeId(), nb.getSeverityTypeId(), nb.getApplicationId(),
                            applicableUser, accountId, handle);

                    if (count > 0) {
                        NotificationBean updateNotificationBean = createNotificationPreferencesBean(nb.getNotificationTypeId(), nb.getSignalTypeId(),
                                nb.getSeverityTypeId(), nb.getApplicationId(), applicableUser, userId, accountId);
                        updateNotificationBeanList.add(updateNotificationBean);
                    }
                });

                if (!updateNotificationBeanList.isEmpty()) {
                    NotificationPreferencesDataService.updateNotifications(updateNotificationBeanList);
                }

                ViewTypes level = new MasterRepo().getTypes().stream()
                        .filter(v -> v.getTypeName().equalsIgnoreCase(Constants.MST_TYPE_NOTIFICATION_PREFERENCES))
                        .filter(v -> v.getSubTypeName().equalsIgnoreCase(userPreferencesPojo.getNotificationChoice().getType()))
                        .findAny().orElse(null);

                if (level == null) {
                    throw new AppsoneException(MessageFormat.format("NotificationPreferences- {0}:{1} is not found.",
                            Constants.MST_TYPE_NOTIFICATION_PREFERENCES, userPreferencesPojo.getNotificationChoice().getType()));
                }

                userPreferencesPojo.setNotificationPreferenceId(level.getSubTypeId());

                NotificationPreferencesDataService.updateNotificationUserDetails(userPreferencesPojo.isSmsNotification(),
                        userPreferencesPojo.isEmailNotification(), applicableUser, DateTimeUtil.getCurrentTimestampInGMT(), level.getSubTypeId(), handle);

                UserNotificationChoiceBean userNotificationChoiceBean = NotificationPreferencesDataService.getUserNotificationChoice(applicableUser, handle);
                NotificationChoice userNotificationChoice = userPreferencesPojo.getNotificationChoice();

                if (userNotificationChoiceBean == null) {
                    // this is a new Notification Choice entry for new user
                    UserNotificationChoiceBean bean = UserNotificationChoiceBean.builder()
                            .applicableUserId(applicableUser)
                            .componentSelection(userNotificationChoice.getComponent() == null ? null : userNotificationChoice.getComponent())
                            .notificationChoiceId(level.getSubTypeId())
                            .createdTime(DateTimeUtil.getCurrentTimestampInGMT())
                            .updatedTime(DateTimeUtil.getCurrentTimestampInGMT())
                            .componentIds((userNotificationChoice.getType().equalsIgnoreCase(NotificationPreferencesType.COMPONENT_NAME.getPreference())
                                    && userNotificationChoice.getCategories() != null) ? userNotificationChoice.getCategories().toString() : null)
                            .componentTypeIds((userNotificationChoice.getType().equalsIgnoreCase(NotificationPreferencesType.COMPONENT_TYPE.getPreference())
                                    && userNotificationChoice.getCategories() != null) ? userNotificationChoice.getCategories().toString() : null)
                            .userDetailsId(userId)
                            .build();

                    if ((userNotificationChoice.getType().equalsIgnoreCase(NotificationPreferencesType.CATEGORY.getPreference()) &&
                            userNotificationChoice.getCategories() != null)) {
                        bean.setCategoryIds(userNotificationChoice.getCategories().toString());
                    } else if (userNotificationChoice.getType().equalsIgnoreCase(NotificationPreferencesType.ALL.getPreference()) && userNotificationChoice.getCategories() == null) {
                        bean.setCategoryIds(null);
                    }

                    NotificationPreferencesDataService.addNotificationUserChoice(bean, handle);
                } else {
                    // this is an updating of an existing Notification Choice entry
                    if (userNotificationChoice.getType().equalsIgnoreCase(NotificationPreferencesType.CATEGORY.getPreference())
                            || userNotificationChoice.getType().equalsIgnoreCase(NotificationPreferencesType.COMPONENT.getPreference())
                            || userNotificationChoice.getType().equalsIgnoreCase(NotificationPreferencesType.ALL.getPreference())) {
                        NotificationPreferencesDataService.updateNotificationUserChoice(handle, applicableUser, userNotificationChoice.getComponent(),
                                (userNotificationChoice.getCategories() == null ? null : userNotificationChoice.getCategories().toString()),
                                DateTimeUtil.getCurrentTimestampInGMT(), userId, null, null);
                    } else if (userNotificationChoice.getType().equalsIgnoreCase(NotificationPreferencesType.COMPONENT_NAME.getPreference())) {
                        NotificationPreferencesDataService.updateNotificationUserChoice(handle, applicableUser, userNotificationChoice.getComponent(),
                                null,
                                DateTimeUtil.getCurrentTimestampInGMT(), userId, null,
                                (userNotificationChoice.getCategories() == null ? null : userNotificationChoice.getCategories().toString()));
                    } else if (userNotificationChoice.getType().equalsIgnoreCase(NotificationPreferencesType.COMPONENT_TYPE.getPreference())) {
                        NotificationPreferencesDataService.updateNotificationUserChoice(handle, applicableUser, userNotificationChoice.getComponent(),
                                null,
                                DateTimeUtil.getCurrentTimestampInGMT(), userId, (userNotificationChoice.getCategories() == null ? null
                                        : userNotificationChoice.getCategories().toString()), null);
                    }
                }

                userPreferencesPojo.getUserAttributes().setUserIdentifier(applicableUser);
                NotificationPreferencesDataService.updateEmailNotificationDetails(userPreferencesPojo.getUserAttributes(), handle);

                return "User notification preferences and/or details successfully updated.";
            });
            updateUsersInRedis(userPreferencesPojo, userId, account);
            addSignalNotificationDetailsInRedis(userPreferencesPojo, applicableUser, account.getIdentifier());
        } catch (Exception e) {
            if (Throwables.getRootCause(e) instanceof AppsoneException) {
                throw (AppsoneException) Throwables.getRootCause(e);
            } else {
                throw e;
            }
        }
    }

    /*Signal preference In Redis*/
    private void addSignalNotificationDetailsInRedis(PreferencesPojo userPreferencesPojo, String applicableUser, String accountIdentifier) {
        ApplicationRepo applicationRepo = new ApplicationRepo();

        if (!userPreferencesPojo.getPreferences().isEmpty()) {
            List<ViewTypes> viewTypes = new MasterRepo().getTypes();
            Map<Integer, com.heal.configuration.pojos.Application> existingAllApplications =
                    applicationRepo.getAllApplicationDetails(accountIdentifier).parallelStream().collect(Collectors.toMap(BasicEntity::getId, Function.identity()));
            for (UserPreferencesPojo preference : userPreferencesPojo.getPreferences()) {
                String appIdentifier = existingAllApplications.get(preference.getApplicationId()).getIdentifier();
                List<SignalNotificationPreferences> existingNotificationDetails = applicationRepo.getNotificationInRedis(accountIdentifier, appIdentifier);
                Map<Integer, ViewTypes> viewTypesMap = viewTypes.parallelStream()
                        .filter(type -> type.getSubTypeId() == preference.getSignalTypeId()
                                || type.getSubTypeId() == preference.getSeverityTypeId()
                                || type.getSubTypeId() == preference.getNotificationTypeId())
                        .collect(Collectors.toMap(ViewTypes::getSubTypeId, Function.identity()));

                SignalNotificationPreferences signalNotificationPreferences = SignalNotificationPreferences.builder()
                        .userDetailsId(applicableUser)
                        .signalSeverity(viewTypes.get(preference.getSeverityTypeId()).getSubTypeName())
                        .signalSeverityId(preference.getSeverityTypeId())
                        .signalTypeId(preference.getSignalTypeId())
                        .signalType(viewTypesMap.get(preference.getSignalTypeId()).getSubTypeName())
                        .notificationType(viewTypesMap.get(preference.getNotificationTypeId()).getSubTypeName())
                        .notificationTypeId(preference.getNotificationTypeId())
                        .status(1)
                        .build();
                existingNotificationDetails.add(signalNotificationPreferences);
                applicationRepo.updateNotificationInRedis(accountIdentifier, appIdentifier, existingNotificationDetails);

            }
        }
    }

    /*User Notification Preference In Redis*/
    private void updateUsersInRedis(PreferencesPojo user, String userId, Account account) {
        UserRepo userRepo = new UserRepo();
        try {
            User existingUser = userRepo.getUser(userId);

            if (existingUser == null) {
                log.error("No existing user found for UserId: {}", userId);
                return;
            }

            ComponentRepo componentRepo = new ComponentRepo();
            Map<Integer, Category> existingCategory = new CategoryRepo().getCategoryDetails(account.getIdentifier())
                    .parallelStream().collect(Collectors.toMap(Category::getId, Function.identity()));
            List<String> categoryIdentifiers = new ArrayList<>();

            if (existingUser.getNotificationChoice() != null) {
                if (user.getNotificationChoice().getType().equals(Constants.ALL)) {
                    existingUser.getNotificationChoice().setCategories(null);
                    existingUser.getNotificationChoice().setComponent(null);
                } else if (user.getNotificationChoice().getCategories() != null && !existingCategory.isEmpty()) {
                    for (Integer categoryId : user.getNotificationChoice().getCategories()) {
                        categoryIdentifiers.add(existingCategory.get(categoryId).getIdentifier());
                    }
                }
            }
            com.heal.configuration.pojos.NotificationChoice notificationChoice =
                    com.heal.configuration.pojos.NotificationChoice.builder().component(user.getNotificationChoice().getComponent() != null ?
                            user.getNotificationChoice().getComponent() : null).build();
            notificationChoice.setType(user.getNotificationChoice().getType());

            if (user.getNotificationChoice().getType().equalsIgnoreCase(NotificationPreferencesType.COMPONENT_NAME.getPreference())) {

                List<String> componentNames = new ArrayList<>();
                if(user.getNotificationChoice().getCategories() != null && !user.getNotificationChoice().getCategories().isEmpty()) {
                    Map<Integer, String> idVsName = componentRepo.getComponents(account.getIdentifier()).stream().filter(b -> b.getStatus() == 1).collect(Collectors.toMap(BasicEntity::getId, BasicEntity::getName));
                    componentNames = user.getNotificationChoice().getCategories().stream().map(idVsName::get).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                } else {
                    log.error("Notification choice for {} type, component ids does not exists, userId:{}, userName:{}.", user.getNotificationChoice().getType(), existingUser.getUserDetailsId(), existingUser.getUserName());
                }

                log.debug("Notification choice for component, component ids:{}, component names:{}", user.getNotificationChoice().getCategories(), componentNames);
                notificationChoice.setComponentNames(componentNames);
            } else if (user.getNotificationChoice().getType().equalsIgnoreCase(NotificationPreferencesType.COMPONENT_TYPE.getPreference())) {

                List<String> componentTypeNames = new ArrayList<>();
                if(user.getNotificationChoice().getCategories() != null) {
                    Map<Integer, String> idVsName = componentRepo.getComponentTypes(account.getIdentifier()).stream().filter(b -> b.getStatus() == 1).collect(Collectors.toMap(BasicEntity::getId, BasicEntity::getName));
                    componentTypeNames = user.getNotificationChoice().getCategories().stream().map(idVsName::get).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                 }else {
                    log.error("Notification choice for {} type, component type ids does not exists, userId:{}, userName:{}.", user.getNotificationChoice().getType(), existingUser.getUserDetailsId(), existingUser.getUserName());
                }
                log.debug("Notification choice for component type, component ids:{}, component names:{}", user.getNotificationChoice().getCategories(), componentTypeNames);
                notificationChoice.setComponentTypes(componentTypeNames);
            } else {

                if (user.getNotificationChoice().getCategories() != null) {
                    notificationChoice.setCategories(categoryIdentifiers);
                } else if (existingUser.getNotificationChoice() != null) {
                    notificationChoice.setCategories(existingUser.getNotificationChoice().getCategories());
                }
            }

            existingUser.setSmsEnabled(user.isSmsNotification() ? 1 : 0);
            existingUser.setEmailEnabled(user.isEmailNotification() ? 1 : 0);
            existingUser.setCcEmails(user.getUserAttributes().getCcEmails());
            existingUser.setToEmails(user.getUserAttributes().getToEmails());
            existingUser.setNotificationChoice(notificationChoice);
            existingUser.setTimezoneMychoice(true);
            existingUser.setNotificationsTimezoneMychoice(true);
            existingUser.setForensicSuppression(1);
            existingUser.setNotificationPreferenceId(user.getNotificationPreferenceId());

            userRepo.updateUserInRedis(userId, existingUser);
        } catch (Exception e) {
            log.error("Error while updating the redis key /users with user notification changes {}", user, e);
        }
    }
}
