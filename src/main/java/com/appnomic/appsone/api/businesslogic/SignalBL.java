package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.beans.UserAccountIdentifiersBean;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.cache.UserDetailsCache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.SignalStatus;
import com.appnomic.appsone.api.common.SignalType;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.opensearch.AnomalySearchRepo;
import com.appnomic.appsone.api.dao.opensearch.SignalSearchRepo;
import com.appnomic.appsone.api.dao.redis.*;
import com.appnomic.appsone.api.pojo.Controller;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.SignalData;
import com.appnomic.appsone.api.pojo.UserAccessDetails;
import com.appnomic.appsone.api.pojo.request.SignalDataRequest;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.appnomic.appsone.opeasearchquery.results.TabularResults;
import com.heal.configuration.pojos.*;
import com.heal.configuration.pojos.opensearch.SignalDetails;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class SignalBL implements BusinessLogic<SignalDataRequest, UtilityBean<SignalDataRequest>, Set<SignalData>> {
    private static int SIGNAL_CLOSE_WINDOW_TIME = ConfProperties.getInt(Constants.SIGNAL_CLOSE_WINDOW_TIME, Constants.SIGNAL_CLOSE_WINDOW_DEFAULT_TIME);
    private static final boolean SIGNAL_CLOSE_WINDOW_TIME_CHECK_ENABLED = ConfProperties.getBoolean(Constants.SIGNAL_CLOSE_WINDOW_TIME_CHECK_ENABLED, Constants.SIGNAL_CLOSE_WINDOW_TIME_CHECK_ENABLED_DEFAULT);
    private static final String PROBLEM_DESC_TEMPLATE = ConfProperties.getString(Constants.SIGNAL_PROBLEM_DESCRIPTION, Constants.SIGNAL_PROBLEM_DESCRIPTION_DEFAULT);
    private static final String WARNING_DESC_TEMPLATE = ConfProperties.getString(Constants.SIGNAL_WARNING_DESCRIPTION, Constants.SIGNAL_WARNING_DESCRIPTION_DEFAULT);
    private static final String INFO_DESC_TEMPLATE = ConfProperties.getString(Constants.SIGNAL_INFO_DESCRIPTION, Constants.SIGNAL_INFO_DESCRIPTION_DEFAULT);
    private static final String BATCH_DESC_TEMPLATE = ConfProperties.getString(Constants.SIGNAL_BATCH_PROCESS_DESCRIPTION, Constants.SIGNAL_BATCH_PROCESS_DESCRIPTION_DEFAULT);

    @Override
    public UtilityBean<SignalDataRequest> clientValidation(RequestObject request) throws ClientException {
        log.trace("Inside Client validation");

        if (request == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (authToken == null || authToken.trim().isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        SignalDataRequest signalDataRequest = new SignalDataRequest(request);
        if (!signalDataRequest.validateParameters()) {
            log.error("Client validation failed for GET SIGNAL DATA request.");
            throw new ClientException(UIMessages.CLIENT_VALIDATION_FAILED);
        }

        signalDataRequest.setActualFromTime(signalDataRequest.getFromTime());
        signalDataRequest.setFromTime(checkSignalWindowTime(signalDataRequest.getFromTime(), signalDataRequest.getToTime()));

        return UtilityBean.<SignalDataRequest>builder()
                .accountIdString(signalDataRequest.getAccountIdentifier())
                .serviceId(signalDataRequest.getServiceId())
                .requestPayloadObject(signalDataRequest)
                .authToken(authToken)
                .build();
    }

    private Long checkSignalWindowTime(Long fromTime, Long toTime) {
        try {
            List<InstallationAttributes> installationAttributesList = new MasterRepo().getInstallationAttributes();
            if (!installationAttributesList.isEmpty()) {
                installationAttributesList.parallelStream().filter(attrs -> attrs.getName().equals(Constants.SIGNAL_CLOSE_WINDOW_TIME))
                        .findAny().ifPresent(installationAttributeBean -> SIGNAL_CLOSE_WINDOW_TIME = Integer.parseInt(installationAttributeBean.getValue()));
            }
        } catch (Exception ex) {
            log.error("Exception encountered while getting Signal Window Closing Time based on attributes. Reason: {}", ex.getMessage(), ex);
        }
        if ((null != fromTime && null != toTime)) {
            long signalWindowTime = toTime - TimeUnit.MINUTES.toMillis(SIGNAL_CLOSE_WINDOW_TIME);
            if (fromTime > signalWindowTime) {
                fromTime = signalWindowTime;
            }
        }
        return fromTime;
    }

    @Override
    public UtilityBean<SignalDataRequest> serverValidation(UtilityBean<SignalDataRequest> utilityBean) throws ServerException {
        AccountRepo accountRepo = new AccountRepo();
        ServiceRepo serviceRepo = new ServiceRepo();

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error(Constants.MESSAGE_INVALID_AUTH_TOKEN);
            throw new ServerException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }
        utilityBean.setUserId(userId);

        Account account = accountRepo.getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            log.error(UIMessages.ERROR_INVALID_ACCOUNT_ID);
            throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
        }
        utilityBean.setAccount(account);

        if (utilityBean.getServiceId() > 0) {
            BasicEntity serviceDetails = serviceRepo.getBasicServiceDetailsWithServiceId(utilityBean.getAccountIdString(), utilityBean.getServiceId());
            if (serviceDetails == null) {
                String error = "Service id is invalid.";
                log.error(error);
                throw new ServerException(error);
            }
            utilityBean.setServiceIdString(serviceDetails.getIdentifier());
        }
        return utilityBean;
    }

    @Override
    public Set<SignalData> processData(UtilityBean<SignalDataRequest> configData) throws DataProcessingException {
        Set<SignalData> responseData = new HashSet<>();
        SignalDataRequest requestPojo = configData.getRequestPayloadObject();
        SignalSearchRepo signalSearchRepo = new SignalSearchRepo();
        try {
            long st = System.currentTimeMillis();
            Set<SignalDetails> signalList;
            if (requestPojo.getSignalId() == null && requestPojo.getSignalType() != null) {
                signalList = signalSearchRepo.getOpenSignals(configData.getAccountIdString(), configData.getRequestPayloadObject().getSignalTypeSet());
                signalList.addAll(signalSearchRepo.getAllSignals(configData.getAccountIdString(), configData.getRequestPayloadObject().getSignalTypeSet(), Collections.emptySet(), requestPojo.getFromTime(), requestPojo.getToTime()));
            } else if (requestPojo.getSignalId() == null) {
                signalList = signalSearchRepo.getOpenSignals(configData.getAccountIdString(), Collections.emptySet());
                signalList.addAll(signalSearchRepo.getAllSignals(configData.getAccountIdString(), Collections.emptySet(), Collections.emptySet(), requestPojo.getFromTime(), requestPojo.getToTime()));
            } else {
                signalList = new SignalSearchRepo().getSignals(Collections.singletonList(requestPojo.getSignalId()), configData.getAccountIdString());
            }
            log.trace("Time taken to get Signals data from OpenSearch {}", System.currentTimeMillis() - st);
            log.debug("Signals fetched from OS: {}", signalList.size());

            st = System.currentTimeMillis();
            if (!signalList.isEmpty()) {
                responseData = processSignalData(signalList, configData);
            }
            log.trace("Time taken to get response data {}", System.currentTimeMillis() - st);

        } catch (Exception ex) {
            log.error("Error in fetching signal details for account id : {} - ", configData.getAccountIdString(), ex);
        }
        return responseData;
    }

    protected Set<SignalData> processSignalData(Set<SignalDetails> signalList, UtilityBean<SignalDataRequest> configData) throws DataProcessingException {
        Set<SignalData> responseData;
        Account account = configData.getAccount();
        String userId = configData.getUserId();
        String status = configData.getRequestPayloadObject().getStatus();
        String signalId = configData.getRequestPayloadObject().getSignalId();
        int serviceId = configData.getRequestPayloadObject().getServiceId();
        String serviceIdentifier = configData.getServiceIdString();
        long actualFromTime = configData.getRequestPayloadObject().getActualFromTime();


        long st = System.currentTimeMillis();
        UserAccessDetails accessDetails = getUserAccessDetails(userId, account.getIdentifier());
        log.trace("Time taken to get UserAccessDetails data {}", System.currentTimeMillis() - st);
        st = System.currentTimeMillis();

        ServiceRepo serviceRepo = new ServiceRepo();
        Set<String> serviceIdList = signalList.parallelStream().map(SignalDetails::getServiceIds).flatMap(Collection::parallelStream).collect(Collectors.toSet());
        log.trace("Time taken to get serviceIdList data {}", System.currentTimeMillis() - st);
        st = System.currentTimeMillis();

        Map<String, List<BasicEntity>> serviceApplicationsMap = serviceIdList.parallelStream().collect(Collectors.toMap(s -> s, s -> HealUICache.INSTANCE.getServiceApplicationList(account.getIdentifier(), s)));
        log.trace("Time taken to get serviceApplicationsMap data {}", System.currentTimeMillis() - st);
        st = System.currentTimeMillis();

        Map<String, Controller> controllerMap = serviceRepo.getAllServices(account.getIdentifier())
                .parallelStream()
                .map((entity) -> Controller.builder().appId(String.valueOf(entity.getId()))
                        .identifier(entity.getIdentifier())
                        .name(entity.getName())
                        .status(entity.getStatus())
                        .build())
                .collect(Collectors.toMap(Controller::getIdentifier, Function.identity()));
        log.trace("Time taken to get controllerMap data {}", System.currentTimeMillis() - st);
        st = System.currentTimeMillis();

        ParentApplicationRepo parentApplicationRepo = new ParentApplicationRepo();
        List<ParentApplication> parentApplicationList = parentApplicationRepo.getAllParentApplications(account.getIdentifier());

        MasterRepo masterViewTypesRepo = new MasterRepo();
        List<ViewTypes> typeDetailsList = masterViewTypesRepo.getTypes();
        log.trace("Time taken to get typeDetailsList data {}", System.currentTimeMillis() - st);
        st = System.currentTimeMillis();

        Map<String, Long> signalAnomalyCountMap = new HashMap<>();
        AnomalySearchRepo anomalySearchRepo = new AnomalySearchRepo();
        TabularResults tabularResults = anomalySearchRepo.getAnomalyCountGroupBySignalId(account.getIdentifier());
        if (tabularResults != null && tabularResults.getRowResults() != null && !tabularResults.getRowResults().isEmpty()) {
            for (TabularResults.ResultRow resultRow : tabularResults.getRowResults()) {
                String tempSignalId = "";
                for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {
                    if (resultRowColumn.getColumnName().equalsIgnoreCase("signalIds")) {
                        tempSignalId = resultRowColumn.getColumnValue();
                    }
                }
                signalAnomalyCountMap.put(tempSignalId, resultRow.getCountValue());
            }
        }

        responseData = signalList.parallelStream()
                .filter(signal -> null == status || signal.getCurrentStatus().equals(status))
                .filter(signal -> signalId == null || signal.getSignalId().trim().equals(signalId.trim()))
                .map(signal -> getSignalData(signal, serviceId, serviceIdentifier, serviceApplicationsMap, account,
                        accessDetails.getApplicationIdentifiers(), actualFromTime, controllerMap, typeDetailsList, signalAnomalyCountMap, parentApplicationList))
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        log.trace("Time taken to get responseData data {}", System.currentTimeMillis() - st);
        return responseData;
    }

    /**
     * This method decides whether given problem can be added to the list of problems to be returned, based on service
     * level filter or if the problem id already exists, if the response is null it means problem is not to be added
     * else add the problem.
     */
    private SignalData getSignalData(SignalDetails signalPojo, Integer serviceId, String serviceIdentifier, Map<String, List<BasicEntity>> serviceApplicationsMap, Account account,
                                     List<String> assignedApplicationsToCurrentUser, long actualFromTime,
                                     Map<String, Controller> controllerMap, List<ViewTypes> typeDetailsList,
                                     Map<String, Long> signalAnomalyCountMap, List<ParentApplication> parentApplicationList) {
        long st = System.currentTimeMillis();
        Set<String> serviceList = signalPojo.getServiceIds();
        if (signalPojo.getSignalType().equalsIgnoreCase(SignalType.INFO.name()) && signalPojo.getStartedTime() < actualFromTime) {
            log.warn("Signal Skipped Type: {}, startTime: {}, actualFromTime: {}",
                    SignalType.INFO.name(), signalPojo.getStartedTime(), actualFromTime);
            return null;
        }

        log.trace("Fetching application details for services {}", serviceList.toArray());
        List<Controller> affectedApplications = new ArrayList<>();
        serviceList.forEach(id -> {
            List<BasicEntity> entity = serviceApplicationsMap.get(id);
            affectedApplications.addAll(entity.parallelStream()
                    .filter(application -> assignedApplicationsToCurrentUser.contains(application.getIdentifier()))
                    .map(application -> Controller.builder()
                            .identifier(application.getIdentifier())
                            .name(application.getName())
                            .appId(String.valueOf(application.getId())).build())
                    .collect(Collectors.toList()));
        });

        if (affectedApplications.isEmpty()) {
            log.warn("User does not have access to signal {}", signalPojo.getSignalId());
            return null;
        }
        log.trace("Time taken to get affectedApplications.addAll(mappedToController) data {}", System.currentTimeMillis() - st);
        st = System.currentTimeMillis();

        if (serviceId == 0 || serviceList.contains(serviceIdentifier)) {

            SignalData signalData = populateSignalData(signalPojo, account, controllerMap, typeDetailsList, signalAnomalyCountMap);
            log.trace("Time taken to get signal data pojo {} ms", System.currentTimeMillis() - st);

            if (null == signalData) {
                return null;
            }

            serviceList.forEach(svc -> {
                if (controllerMap.containsKey(svc)) {
                    signalData.addServices(controllerMap.get(svc));
                }
            });

            if (signalData.getType().equals("BP")) {
                log.info("Signal type is batch job");
                ApplicationRepo applicationRepo = new ApplicationRepo();
                Map<String, Application> applicationMap = applicationRepo.getAllApplicationDetails(account.getIdentifier()).parallelStream().collect(Collectors.toMap(BasicEntity::getIdentifier, f -> f));
                signalPojo.getServiceIds().forEach(appIdentifier -> {
                    Application application = applicationMap.get(appIdentifier);
                    affectedApplications.add(Controller.builder()
                            .identifier(application.getIdentifier())
                            .name(application.getName())
                            .appId(String.valueOf(application.getId())).build());
                });

            }
            signalData.addApplications(affectedApplications);
            if (signalData.getApplications() == null) {
                signalData.setParentApplications(new HashSet<>());
            } else {
                Set<ParentApplication> parentApplications = signalData.getApplications()
                        .parallelStream()
                        .map(application -> parentApplicationList
                                .parallelStream()
                                .filter(parentApplication -> parentApplication.getApplicationIdentifiers().contains(application.getIdentifier()))
                                .findFirst()
                                .orElse(null)
                        )
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
                signalData.setParentApplications(parentApplications);
            }
            return signalData;
        }
        return null;
    }

    protected SignalData populateSignalData(SignalDetails signalPojo, Account account, Map<String, Controller> controllerMap,
                                            List<ViewTypes> typeDetailsList, Map<String, Long> signalAnomalyCountMap) {
        SignalData signalData = new SignalData();
        Map<String, String> metaData = signalPojo.getMetadata();

        if (signalPojo.getSignalType().equalsIgnoreCase(com.heal.configuration.enums.SignalType.INFO.name())) {
            signalData.setMetricCategory(metaData.getOrDefault("category_id", null));
            log.trace("kpi category for signalId: {} is {}", signalPojo.getSignalId(), metaData.getOrDefault("category_id", null));
        } else if (signalPojo.getSignalType().equalsIgnoreCase(com.heal.configuration.enums.SignalType.BATCH_JOB.name())) {
            signalData.setMetricCategory(metaData.getOrDefault("KPICategoryName", null));
            log.trace("kpi category for signalId: {} is {}", signalPojo.getSignalId(), metaData.getOrDefault("KPICategoryName", null));
        } else if (signalPojo.getSignalType().equalsIgnoreCase(com.heal.configuration.enums.SignalType.EARLY_WARNING.name())) {
            signalData.setMetricCategory(metaData.getOrDefault("KPICategoryName", null));
            log.trace("kpi category for signalId: {} is {}", signalPojo.getSignalId(), metaData.getOrDefault("KPICategoryName", null));
        } else if (signalPojo.getSignalType().equalsIgnoreCase(com.heal.configuration.enums.SignalType.PROBLEM.name())) {
            signalData.setMetricCategory(metaData.getOrDefault("KPICategoryName", null));
            log.trace("kpi category for signalId: {} is {}", signalPojo.getSignalId(), metaData.getOrDefault("KPICategoryName", null));
        }

        signalData.setId(signalPojo.getSignalId());
        signalData.setType(getDisplayName(signalPojo));

        if (shouldProblemBeClosed(signalPojo) && !SignalType.BATCH_JOB.getDisplayName().equals(signalData.getType())) {
            signalData.setCurrentStatus(SignalStatus.CLOSED.getReturnType());
        } else {
            signalData.setCurrentStatus(SignalStatus.valueOf(signalPojo.getCurrentStatus()).getReturnType());
        }

        ViewTypes typeDetails = typeDetailsList.parallelStream().filter(type -> type.getSubTypeId() == signalPojo.getSeverityId()).findAny().orElse(null);
        //If No severity type found, then Severity Name is marked as "NA here" instead of Severe or Default
        String severityName = typeDetails == null ? "NA" : typeDetails.getSubTypeName();
        signalData.setSeverity(severityName);
        signalData.setDescription(getProblemDescription(signalPojo, account, controllerMap, severityName));

        signalData.setStartTimeMilli(signalPojo.getStartedTime());
        signalData.setUpdatedTimeMilli((metaData.get("end_time") == null) ? 0 : Long.parseLong(signalPojo.getMetadata().get("end_time")));

        //Added to check count mismatch for the anomalies
        long count = signalAnomalyCountMap.getOrDefault(signalPojo.getSignalId(), 0L);
        long anomalyCount = (signalPojo.getAnomalies() == null || signalPojo.getAnomalies().isEmpty()) ? count : count > signalPojo.getAnomalies().size() ? count : signalPojo.getAnomalies().size();
        if (anomalyCount == 0 || count == 0) {
            log.info("Number of anomalies in the Signal Pojo: {} and in the map: {} for signal {}", signalPojo.getAnomalies().size(), count, signalData.getId());
        } else {
            log.trace("Number of anomalies in the Signal Pojo: {} and in the map: {} for signal {}", signalPojo.getAnomalies().size(), count, signalData.getId());
        }
        signalData.setEventCount(anomalyCount);

        Long txnAnomalyTime = signalPojo.getTxnAnomalyTime();
        Long kpiAnomalyTime = signalPojo.getUpdatedTime();
        if (txnAnomalyTime != null || kpiAnomalyTime != null) {
            if (txnAnomalyTime == null) {
                signalData.setLastEventTime(kpiAnomalyTime);
            } else if (kpiAnomalyTime == null) {
                signalData.setLastEventTime(txnAnomalyTime);
            } else {
                signalData.setLastEventTime((txnAnomalyTime >= kpiAnomalyTime) ? txnAnomalyTime : kpiAnomalyTime);
            }
        }

        return signalData;
    }

    private String getDisplayName(SignalDetails signal) {
        String displayName = null;
        try {
            displayName = SignalType.valueOf(signal.getSignalType()).getDisplayName();
        } catch (Exception e) {
            log.error("Error occurred in getting SignalType display name.: values{}", signal.getSignalType(), e);
        }
        return displayName;
    }

    private boolean shouldProblemBeClosed(SignalDetails signal) {
        log.trace("SIGNAL_CLOSE_WINDOW_TIME: {}, id: {}", SIGNAL_CLOSE_WINDOW_TIME, signal.getSignalId());
        try {
            if (!SignalStatus.valueOf(signal.getCurrentStatus()).getReturnType().equalsIgnoreCase(SignalStatus.OPEN.getReturnType())) {
                return false;
            }

            if (SIGNAL_CLOSE_WINDOW_TIME_CHECK_ENABLED) {
                long updatedTime = (null == signal.getUpdatedTime()) ? 0 : signal.getUpdatedTime();
                long txnAnomalyTime = (null == signal.getTxnAnomalyTime()) ? 0 : signal.getTxnAnomalyTime();
                long lastUpdatedTime = Math.max(updatedTime, txnAnomalyTime);

                Calendar cal = Calendar.getInstance(TimeZone.getTimeZone("GMT"));
                long signalWindowTime = cal.getTimeInMillis() - TimeUnit.MINUTES.toMillis(SIGNAL_CLOSE_WINDOW_TIME);

                log.trace("shouldProblemBeClosed() signalId: {}, updated_time: {}, txn_anomaly_time: {}, windowTime: {}",
                        signal.getSignalId(), updatedTime, txnAnomalyTime, SIGNAL_CLOSE_WINDOW_TIME);
                if (lastUpdatedTime < signalWindowTime) {
                    log.info("Signal marked as closed in the signal list.");
                    return true;
                }
            }

            return false;
        } catch (Exception e) {
            log.error("Error occurred in shouldProblemBeClosed() method. signalId:{}", signal.getSignalId(), e);
            return false;
        }
    }

    public static String getProblemDescription(SignalDetails signal, Account account, Map<String, Controller> controllerMap, String severityName) {
        String entryServiceId = (signal.getEntryServiceId() == null || signal.getEntryServiceId().isEmpty()) ? null : new ArrayList<>(signal.getEntryServiceId()).get(0);
        String raw = "";
        String signalType = signal.getSignalType();
        String kpiName = "";
        if (signalType != null) {
            SignalType st = SignalType.valueOf(signalType);
            log.trace("Signal type: {}", st.name());
            if (SignalType.BATCH_JOB.equals(st) && entryServiceId != null) {
                raw = BATCH_DESC_TEMPLATE;
                String kpiIdString = signal.getMetadata().get("kpi_id");
                KpiDetails kpiDetails = new KpiRepo().getKpiDetailByServiceKpiId(account.getIdentifier(), entryServiceId, kpiIdString);
                kpiName = kpiDetails != null ? kpiDetails.getName() : "NA";
            } else if (st.equals(SignalType.INFO)) {
                raw = INFO_DESC_TEMPLATE;
            } else if (entryServiceId != null) {
                raw = PROBLEM_DESC_TEMPLATE;
            } else {
                raw = WARNING_DESC_TEMPLATE;
            }
        }

        Map<String, String> placeHolders = extractPlaceholderData(signal, controllerMap, severityName, kpiName);
        for (Map.Entry<String, String> entry : placeHolders.entrySet()) {
            raw = raw.replaceAll(entry.getKey(), entry.getValue());
        }
        return raw;
    }

    public static Map<String, String> extractPlaceholderData(SignalDetails signal, Map<String, Controller> controllerMap, String severityName, String name) {
        String KPI = "<kpi>";
        String JOB_ID = "<job_id>";
        String CUR_BATCH_STATUS = "<current_batch_status>";
        String CUR_STATUS = "<current_status>";
        String ENTRY_SVC_NAME = "<entry_service_name>";
        String META_DATA = "<meta_data>";
        String RELATED_SIGNALS = "<related_signals>";
        String RC_SVC_LIST = "<root_cause_service_list>";
        String AFFECTED_SVC_LIST = "<affected_service_list>";
        String SEVERITY = "<severity>";
        String SIGNAL_TYPE = "<signal_type>";
        String NA = "NA";
        Map<String, String> result = new HashMap<>();
        Map<String, String> metaData = signal.getMetadata();
        String jobId = metaData.get("batch_job_id");
        String currentBatchStatus = metaData.get("batch_job_status");
        String status = signal.getCurrentStatus();
        String entrySvcId = (signal.getEntryServiceId() == null || signal.getEntryServiceId().isEmpty()) ? null : new ArrayList<>(signal.getEntryServiceId()).get(0);
        Set<String> relatedSignals = signal.getRelatedSignals();
        Set<String> rootCauseServiceIdSet = signal.getRootCauseServiceIds();
        Set<String> affectedSvcList = signal.getServiceIds();
        String signalType = signal.getSignalType();

        result.put(KPI, name);
        String rootCauseServiceNames = rootCauseServiceIdSet.stream()
                .map(identifier -> controllerMap.getOrDefault(identifier, null))
                .filter(Objects::nonNull)
                .map(Controller::getName)
                .collect(Collectors.joining(", "));
        String affectedServiceNames = affectedSvcList.stream()
                .map(identifier -> controllerMap.getOrDefault(identifier, null))
                .filter(Objects::nonNull)
                .map(Controller::getName)
                .collect(Collectors.joining(", "));
        String entryServiceName = (entrySvcId == null) ? NA : controllerMap.containsKey(entrySvcId) ? controllerMap.get(entrySvcId).getName() : NA;
        String severity = NA;

        if (severityName != null) severity = severityName;

        result.put(CUR_BATCH_STATUS, currentBatchStatus != null ? currentBatchStatus : NA);
        result.put(JOB_ID, jobId != null ? jobId : NA);
        result.put(CUR_STATUS, status);
        result.put(ENTRY_SVC_NAME, entryServiceName);
        result.put(META_DATA, metaData.toString());
        result.put(RELATED_SIGNALS, (relatedSignals == null) ? NA : String.join(",", relatedSignals));
        result.put(RC_SVC_LIST, rootCauseServiceNames);
        result.put(AFFECTED_SVC_LIST, affectedServiceNames);
        result.put(SEVERITY, severity);
        result.put(SIGNAL_TYPE, (signalType == null) ? NA : signalType);

        return result;
    }

    protected UserAccessDetails getUserAccessDetails(String userId, String accountIdentifier) throws DataProcessingException {
        try {
            return UserDetailsCache.getInstance().userApplications
                    .get(new UserAccountIdentifiersBean(userId, accountIdentifier));
        } catch (ExecutionException e) {
            log.error("Exception while fetching user access details. Reason: {}", e.getMessage(), e);
            throw new DataProcessingException("Error in fetching user access details");
        }
    }

}
