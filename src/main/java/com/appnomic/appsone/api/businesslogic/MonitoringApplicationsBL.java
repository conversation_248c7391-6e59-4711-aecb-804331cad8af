package com.appnomic.appsone.api.businesslogic;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.mobile.model.MonitoringApplication;
import com.appnomic.appsone.api.mobile.util.BigQueryService;
import com.appnomic.appsone.api.mobile.util.BigQueryUtil;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.FieldValueList;
import com.google.cloud.bigquery.QueryJobConfiguration;
import com.google.cloud.bigquery.TableResult;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MonitoringApplicationsBL implements BusinessLogic<Object, UtilityBean<Object>, MonitoringApplication> {

	@Override
	public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
		List<String> errorMessages = new ArrayList<>();
		if (!BigQueryUtil.appNameValidation(requestObject, errorMessages)
			|| !BigQueryUtil.fromTimeToTimeValidation(requestObject, errorMessages)) {
				throw new ClientException(String.join(",", errorMessages));
		}
		String appName = requestObject.getQueryParams().get("app-name")[0];
		String fromTime = requestObject.getQueryParams().get("from-time")[0];
		String toTime = requestObject.getQueryParams().get("to-time")[0];
		return  UtilityBean.<Object>builder()
				.appName(appName)
				.fromTimeString(fromTime)
				.toTimeString(toTime)
				.build();
	}

	@Override
	public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
		return utilityBean;
	}

	@Override
	public MonitoringApplication processData(UtilityBean<Object> configData) throws DataProcessingException {
		String appName = configData.getAppName();
		BigQuery bigQuery = BigQueryService.getInstance(appName);
		log.debug("bigQuery Obj: {}", bigQuery.toString());
		
		String fromTime = configData.getFromTimeString();
		String toTime = configData.getToTimeString();
		String intervalBucket = BigQueryUtil.getIntervalBucket(fromTime, toTime);
		String intervalBucketUnit = intervalBucket.split(" ")[1];
		
		List<String> osListFromAppName = Constants.getOSListFromAppName(appName);
		String appOSString = "";
		String aggregateQuery = "";
		String unionSubQueryForAggregateQuery = "";
		
		for(String os : osListFromAppName) {
			appOSString = appName+"."+os;
			aggregateQuery += unionSubQueryForAggregateQuery + 
							  " ("
							  + "SELECT  " + 
							  "    COUNT(issue_id) AS totalCrashes, " + 
							  "    COUNT(DISTINCT user.id) AS impactedUsers, " + 
							  "    '"+os+"' AS appType " + 
							  "FROM " + Constants.getCrashlyticsTable(appOSString) +
							  "WHERE  " + 
							  "    event_timestamp > TIMESTAMP_MILLIS("+fromTime+") " + 
							  "    AND event_timestamp < TIMESTAMP_MILLIS("+toTime+") "
							  + ") ";
			unionSubQueryForAggregateQuery = " UNION ALL ";
		}
		appOSString = appName+"."+osListFromAppName.get(0);
		QueryJobConfiguration aggregateQueryConfig = QueryJobConfiguration.newBuilder(aggregateQuery).setUseLegacySql(false).build();
		TableResult aggregateQueryResult;
		
		String timeSeriesQuery = 	"SELECT " + 
									"  'ActiveUser' AS userType, " + 
									"  COUNT(DISTINCT user_id) AS userCount, " + 
									"  TIMESTAMP_TRUNC(TIMESTAMP_MILLIS("+fromTime+"), "+intervalBucketUnit+") AS truncatedFromTime," +
									"  TIMESTAMP_TRUNC(TIMESTAMP_MICROS(event_timestamp), "+intervalBucketUnit+") AS time " +
									"FROM " + Constants.getAnalyticsTable(appOSString) +  " AS T " +
									"    CROSS JOIN " + 
									"      T.event_params " + 
									"WHERE " + 
									"  event_params.key = 'engagement_time_msec' AND event_params.value.int_value > 0 " + 
									"  AND event_timestamp > ("+ fromTime +"000) " +
					                "  AND event_timestamp < (" + toTime +"000) "  +
									"GROUP BY  " + 
									"  time " + 
									" " + 
									"UNION ALL  " + 
									" " + 
									"(SELECT " + 
									"  'NewUser' AS userType, " + 
									"  COUNTIF(event_name='first_open') AS userCount, " + 
									"  TIMESTAMP_TRUNC(TIMESTAMP_MILLIS("+fromTime+"), "+intervalBucketUnit+") AS truncatedFromTime," +
									"  TIMESTAMP_TRUNC(TIMESTAMP_MICROS(event_timestamp), "+intervalBucketUnit+") AS time " +
									"FROM " + Constants.getAnalyticsTable(appOSString) +  " AS T " +
									"    CROSS JOIN " + 
									"      T.event_params " + 
									"WHERE " + 
									"  event_params.key = 'previous_first_open_count' AND event_params.value.int_value = 0 " + 
									"  AND event_timestamp > ("+ fromTime +"000) " +
					                "  AND event_timestamp < (" + toTime +"000) "  +
									"GROUP BY  " + 
									"  time) ";
		
		QueryJobConfiguration timeSeriesQueryConfig = QueryJobConfiguration.newBuilder(timeSeriesQuery).setUseLegacySql(false).build();
		TableResult timeSeriesQueryResult;
		try {
			aggregateQueryResult = bigQuery.query(aggregateQueryConfig);
			timeSeriesQueryResult = bigQuery.query(timeSeriesQueryConfig);
		} catch(InterruptedException inExc) {
			throw new DataProcessingException("Error in fetching data from BigQuery");
		}
		MonitoringApplication monitoringApplicationResponse = new MonitoringApplication();
		monitoringApplicationResponse.setAppName(appName);
		Long impactedUsers = 0L;
		Long totalCrashes = 0L;
		
		for (FieldValueList value : aggregateQueryResult.iterateAll()) {
			if(null == value.get("impactedUsers").getValue() || null == value.get("totalCrashes").getValue()) {
				throw new DataProcessingException("No impacted or crashed users found in system");
			}
			impactedUsers += value.get("impactedUsers").getLongValue();
			totalCrashes += value.get("totalCrashes").getLongValue();
		}
		monitoringApplicationResponse.setImpactedUsers(impactedUsers);
		monitoringApplicationResponse.setTotalCrashes(totalCrashes);
		
		Map<String, Long> activeUsersTrendBucket = new HashMap<>();
		Map<String, Long> newUsersTrendBucket = new HashMap<>();
		
		for (FieldValueList value : timeSeriesQueryResult.iterateAll()) {
            if (activeUsersTrendBucket.isEmpty() || newUsersTrendBucket.isEmpty()) {
                activeUsersTrendBucket = BigQueryUtil.createTimeBucket(value.get("truncatedFromTime").getTimestampValue() / 1000L, Long.parseLong(toTime), intervalBucket);
                newUsersTrendBucket = BigQueryUtil.createTimeBucket(value.get("truncatedFromTime").getTimestampValue() / 1000L, Long.parseLong(toTime), intervalBucket);
            }
            if (!value.get("userType").isNull() && value.get("userType").getStringValue().equalsIgnoreCase("ActiveUser")) {
            	if (activeUsersTrendBucket.containsKey(String.valueOf(value.get("time").getTimestampValue() / 1000L))) {
                    activeUsersTrendBucket.put(String.valueOf(value.get("time").getTimestampValue() / 1000L), value.get("userCount").getLongValue());
                }
            } else {
                if (newUsersTrendBucket.containsKey(String.valueOf(value.get("time").getTimestampValue() / 1000L))) {
                	newUsersTrendBucket.put(String.valueOf(value.get("time").getTimestampValue() / 1000L), value.get("userCount").getLongValue());
                }
            }
        }
		Map<String, Map<String, Long>> chartData = new HashMap<>();
		chartData.put("activeUsersTrend", activeUsersTrendBucket);
		chartData.put("newUsersTrend", newUsersTrendBucket);
		
		monitoringApplicationResponse.setChartData(chartData);
		
		return monitoringApplicationResponse;
	}

}
