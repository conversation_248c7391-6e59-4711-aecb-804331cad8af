package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.ApplicationRepo;
import com.appnomic.appsone.api.dao.redis.ParentApplicationRepo;
import com.appnomic.appsone.api.dao.redis.UserRepo;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.StringUtils;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.heal.configuration.pojos.Application;
import com.heal.configuration.pojos.ParentApplication;
import com.heal.configuration.pojos.User;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class ParentApplicationServiceBL implements BusinessLogic<Object, UtilityBean<Object>, List<Application>> {

    String accountIdentifier;
    ParentApplicationRepo parentApplicationRepo = new ParentApplicationRepo();


    public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
        log.info("Client Validation for fetching Application mapped to Parent Application");

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION_HEADER);
        accountIdentifier = requestObject.getParams().get(Constants.REQUEST_PARAM_IDENTIFIER);
        String parentApplicationIdentifier = requestObject.getParams().get(Constants.REQUEST_PARAM_PARENT_APPLICATION_ID);

        if (StringUtils.isEmpty(authKey)) {
            log.error(Constants.MESSAGE_EMPTY_AUTH_TOKEN);
            throw new ClientException(Constants.MESSAGE_EMPTY_AUTH_TOKEN);
        }

        if (StringUtils.isEmpty(accountIdentifier)) {
            log.error(Constants.MESSAGE_EMPTY_ACCOUNT);
            throw new ClientException(Constants.MESSAGE_EMPTY_ACCOUNT);
        }
        if (StringUtils.isEmpty(parentApplicationIdentifier)) {
            log.error(Constants.MESSAGE_EMPTY_PARENT_APPLICATION);
            throw new ClientException(Constants.MESSAGE_EMPTY_PARENT_APPLICATION);
        }
        return UtilityBean.builder()
                .accountIdString(accountIdentifier)
                .authToken(authKey)
                .requestTypeString(parentApplicationIdentifier)
                .build();
    }

    public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
        log.info("Server Validation for fetching Application mapped to Parent Application {}", utilityBean.getRequestTypeString());
        AccountRepo accountRepo = new AccountRepo();

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error(Constants.MESSAGE_INVALID_AUTH_TOKEN);
            throw new ServerException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }

        com.heal.configuration.pojos.Account account = accountRepo.getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            log.error(UIMessages.ERROR_INVALID_ACCOUNT_ID + ": {}", utilityBean.getAccountIdString());
            throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
        }
        String parentApplicationIdentifier = utilityBean.getRequestTypeString();
        ParentApplication parentApplication = parentApplicationRepo.getAllParentApplications(accountIdentifier)
                .stream()
                .filter(element -> element.getIdentifier().equals(parentApplicationIdentifier))
                .findAny().orElse(null);

        if (parentApplication == null) {
            log.error(UIMessages.ERROR_INVALID_PARENT_APPLICATION_IDENTIFIER + ": {}", utilityBean.getRequestTypeString());
            throw new ServerException(UIMessages.ERROR_INVALID_PARENT_APPLICATION_IDENTIFIER);
        }

        return UtilityBean.builder()
                .accountIdString(accountIdentifier)
                .parentApplication(parentApplication)
                .parentApplicationIdString(parentApplicationIdentifier)
                .userId(userId)
                .build();
    }


    public List<Application> processData(UtilityBean<Object> configData) throws DataProcessingException {
        log.info("Fetching Applications mapped to Parent Application : {}", configData.getParentApplicationIdString());
        log.info("Getting user Accessible Applications for the User id {} and Account id {}", configData.getUserId(), configData.getAccountIdString());
        List<Application> accessibleApps = getApplicationsForUser(configData.getUserId(), configData.getAccountIdString());
        try {
            List<String> applicationList = configData.getParentApplication().getApplicationIdentifiers();
            List<String> accessibleAppIdentifiers = accessibleApps.stream().map(Application::getIdentifier).collect(Collectors.toList());
            List<Application> applicationDetails = new ApplicationRepo().getAllApplicationDetails(accountIdentifier).parallelStream()
                    .filter(app -> applicationList.contains(app.getIdentifier()) && accessibleAppIdentifiers.contains(app.getIdentifier()))
                    .peek(app -> {
                        if (app.getTags() != null && app.getTags().parallelStream().anyMatch(tag -> tag.getValue().equalsIgnoreCase(Constants.MICROSERVICE))) {
                            app.setIsMicroService(1);
                        }
                    })
                    .map(a -> Application.builder()
                            .id(a.getId())
                            .name(a.getName())
                            .identifier(a.getIdentifier())
                            .tags(a.getTags()).build())
                    .collect(Collectors.toList());
            log.info("Applications mapped to Parent Application {} fetched successfully", configData.getParentApplication().getName());
            return applicationDetails;
        } catch (Exception e) {
            log.error("Error while fetching Applications mapped tp Parent Application : {}", configData.getParentApplicationIdString(), e);
            throw new DataProcessingException(UIMessages.ERROR_GETTING_APPLICATION);
        }
    }

    public List<Application> getApplicationsForUser(String userIdentifier, String accountIdentifier) {

        User user = new UserRepo().getUser(userIdentifier);
        if (user == null) {
            log.error("Could not get the user details from redis, userId:{}", userIdentifier);
            return Collections.emptyList();
        }

        ApplicationRepo applicationRepo = new ApplicationRepo();
        List<Application> applications = applicationRepo.getAllApplicationDetails(accountIdentifier);
        if (user.getRoleId() == 1) {
            return applications;
        }

        return applications.parallelStream()
                .filter(application -> applicationRepo.getAppUsersByAppIdentifier(accountIdentifier, application.getIdentifier())
                        .stream()
                        .anyMatch(u -> u.getUserDetailsId().equalsIgnoreCase(userIdentifier))
                ).collect(Collectors.toList());
    }
}
