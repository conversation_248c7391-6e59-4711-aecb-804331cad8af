package com.appnomic.appsone.api.businesslogic;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.mobile.model.UsageSummary;
import com.appnomic.appsone.api.mobile.util.BigQueryService;
import com.appnomic.appsone.api.mobile.util.BigQueryUtil;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.FieldValueList;
import com.google.cloud.bigquery.QueryJobConfiguration;
import com.google.cloud.bigquery.TableResult;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class GetUsageSummaryBL implements BusinessLogic<Object, UtilityBean<Object>, UsageSummary> {

	@Override
	public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
		List<String> errorMessages = new ArrayList<>();
		if (!BigQueryUtil.appOSStringValidation(requestObject, errorMessages)
			|| !BigQueryUtil.appDisplayVersionValidation(requestObject, errorMessages)
			|| !BigQueryUtil.fromTimeToTimeValidation(requestObject, errorMessages)) {
				throw new ClientException(String.join(";", errorMessages));
		}
		String appOSString = requestObject.getQueryParams().get("app-os")[0];
		String appDisplayVersions = requestObject.getQueryParams().get("app-build-version")[0];
		String fromTime = requestObject.getQueryParams().get("from-time")[0];
		String toTime = requestObject.getQueryParams().get("to-time")[0];
		
		return UtilityBean.<Object>builder()
				.appOSString(appOSString)
				.appDisplayVersions(appDisplayVersions)
				.fromTimeString(fromTime)
				.toTimeString(toTime)
				.build();
	}

	@Override
	public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
		return utilityBean;
	}

	@Override
	public UsageSummary processData(UtilityBean<Object> configData) throws DataProcessingException {
		String appOSString = configData.getAppOSString();
		BigQuery bigQuery = BigQueryService.getInstance(appOSString.split("\\.")[0]);
		log.debug("bigQuery Obj: {}", bigQuery);

		List<String> appDisplayVersions = Arrays.asList(configData.getAppDisplayVersions().split(","));
		String appDisplayVersionInParam = BigQueryUtil.getInParameter(configData.getAppDisplayVersions());
		String fromTime = configData.getFromTimeString();
		String toTime = configData.getToTimeString();
		String intervalBucket = BigQueryUtil.getIntervalBucket(fromTime, toTime);
		String intervalBucketUnit = intervalBucket.split(" ")[1];
		String joinCondition = BigQueryUtil.getTruncatedEventTimestamp(intervalBucket, intervalBucketUnit, Constants.CRASHLYTICS_ALIAS);
		
		Map<String, Long> activeUserTrendBucket = Collections.emptyMap();
		Map<String, Long> affectedUserTrendBucket = new HashMap<>();
        // Getting affected User and Affected User trend

        String affectedUsersQuery = "(SELECT" +
                "    'AffectedUsers' AS userType," +
                "    COUNT(DISTINCT user.id) AS count," +
                "    null AS time," +
                "    0 AS affectedUserCount," +
                "    null AS appDisplayVersions " +
                "FROM " + Constants.getCrashlyticsTable(appOSString) +  " AS crashlytics " +
                "WHERE " +
                " 		event_timestamp > TIMESTAMP_MILLIS("+ fromTime +") " +
                "		AND event_timestamp < TIMESTAMP_MILLIS(" + toTime +") "  +
                "		AND application.display_version IN " + appDisplayVersionInParam + " " +
                ")" +
                " UNION ALL " +
                "(" +
                " WITH hours AS (" +
                "	SELECT *" +
                "	FROM UNNEST(GENERATE_TIMESTAMP_ARRAY(TIMESTAMP_TRUNC(TIMESTAMP_MILLIS("+fromTime+"), "+intervalBucketUnit+"), TIMESTAMP_MILLIS("+toTime+"), INTERVAL "+intervalBucket+")" +
                "	) AS time ) " +
                "SELECT " +
                "    '' AS userType," +
                "    0 AS count," +
                "    hours.time AS time," +
                "    COUNT(DISTINCT user.id) AS affectedUserCount," +
                "    STRING_AGG(DISTINCT(application.display_version)) AS appDisplayVersions " +
                "FROM " + Constants.getCrashlyticsTable(appOSString) +  " AS crashlytics " +
                "	 RIGHT JOIN hours ON "+joinCondition+" = hours.time " +
                "GROUP BY time " +
                "ORDER BY 1" +
                ")";

		QueryJobConfiguration affectedUsersQueryConfig = QueryJobConfiguration.newBuilder(affectedUsersQuery)
														.setUseLegacySql(false).build();
		TableResult affectedUsersResult;
		
		String truncatedEventTimestamp = BigQueryUtil.getTruncatedEventTimestamp(intervalBucket, intervalBucketUnit, Constants.ANALYTICS_ALIAS);
        String activeUsersQuery = "SELECT " +
                "    null AS userType, " +
                "    null AS totalUserCount, " +
                "    TIMESTAMP_TRUNC(TIMESTAMP_MILLIS("+fromTime+"), "+intervalBucketUnit+") AS truncatedFromTime," +
                "  	 COUNT(DISTINCT user_id) AS activeUsersCount, " +
                "	 STRING_AGG(DISTINCT user_id) AS activeUserIds, " +
                "    " + truncatedEventTimestamp + " AS time " +
                "FROM " + Constants.getAnalyticsTable(appOSString) +  " AS T " +
                "    CROSS JOIN " +
                "      T.event_params " +
                "WHERE " +
                "  event_params.key = 'engagement_time_msec' AND event_params.value.int_value > 0 " +
                " 		AND event_timestamp > ("+ fromTime +"000) " +
                "		AND event_timestamp < (" + toTime +"000) "  +
                "GROUP BY time " +
                " " +
                " UNION ALL  " +
                " " +
                "(SELECT " +
                "	'TotalUsers' AS userType, " +
                "  	COUNT(DISTINCT user_id) AS totalUserCount, " +
                "   TIMESTAMP_TRUNC(TIMESTAMP_MILLIS("+fromTime+"), "+intervalBucketUnit+") AS truncatedFromTime," +
                "  	null AS activeUsersCount, " +
                "	null AS activeUserIds, " +
                "  	null AS time " +
                "FROM " + Constants.getAnalyticsTable(appOSString) + ")";

		QueryJobConfiguration activeUsersQueryConfig = QueryJobConfiguration.newBuilder(activeUsersQuery)
														.setUseLegacySql(false).build();
		TableResult activeUsersResult;
		UsageSummary usageSummary = new UsageSummary();
		Long totalActiveUsers = 0L;
		Set<String> totalActiveUserIds = new HashSet<>();

		try {
			activeUsersResult = bigQuery.query(activeUsersQueryConfig);
			affectedUsersResult = bigQuery.query(affectedUsersQueryConfig);
		} catch(InterruptedException inExc) {
			throw new DataProcessingException("Error in fetching data from BigQuery");
		}

        for (FieldValueList value : activeUsersResult.iterateAll()) {
            if (activeUserTrendBucket.isEmpty()) {
                activeUserTrendBucket = BigQueryUtil.createTimeBucket(value.get("truncatedFromTime").getTimestampValue() / 1000L, Long.parseLong(toTime), intervalBucket);
            }
            if (null != value.get("userType").getValue() && value.get("userType").getStringValue().equalsIgnoreCase("TotalUsers")) {
                usageSummary.setTotalUsers(value.get("totalUserCount").getLongValue());
            } else {
                if (activeUserTrendBucket.containsKey(String.valueOf(value.get("time").getTimestampValue() / 1000L))) {
                    activeUserTrendBucket.put(String.valueOf(value.get("time").getTimestampValue() / 1000L), value.get("activeUsersCount").getLongValue());
                    if(null != value.get("activeUserIds").getValue()) {
                    	totalActiveUserIds.addAll(Arrays.asList(value.get("activeUserIds").getStringValue().split(",")));
                    }
                }
            }
        }
        usageSummary.setActiveUsersTrend(activeUserTrendBucket);
        totalActiveUsers = (long)totalActiveUserIds.size();
        
        for (FieldValueList value : affectedUsersResult.iterateAll()) {
            if (null != value.get("userType").getValue() && value.get("userType").getStringValue().equalsIgnoreCase("AffectedUsers")) {
                usageSummary.setAffectedUsers(totalActiveUsers != 0 ? new BigDecimal((double)value.get("count").getLongValue() / totalActiveUsers * 100.00).setScale(2, RoundingMode.HALF_UP) : new BigDecimal(0).setScale(2));
                usageSummary.setUnaffectedUsers(new BigDecimal(100.00).subtract(usageSummary.getAffectedUsers()).setScale(2, RoundingMode.HALF_UP));
                continue;
            } else {
                if (null != value.get("appDisplayVersions").getValue()) {
                    Arrays.asList(value.get("appDisplayVersions").getStringValue().split(",")).forEach(version -> {
                        if (appDisplayVersions.contains(version)) {
                            affectedUserTrendBucket.put(String.valueOf(value.get("time").getTimestampValue() / 1000L), value.get("affectedUserCount").getLongValue());
                        } else {
                            affectedUserTrendBucket.put(String.valueOf(value.get("time").getTimestampValue() / 1000L), 0L);
                        }
                    });
                } else {
                    affectedUserTrendBucket.put(String.valueOf(value.get("time").getTimestampValue() / 1000L), 0L);
                }
            }
            usageSummary.setAffectedUsersTrend(affectedUserTrendBucket);
        }
        return usageSummary;
	}
}
