package com.appnomic.appsone.api.businesslogic;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.mobile.model.CrashFreeUsersTrend;
import com.appnomic.appsone.api.mobile.util.BigQueryService;
import com.appnomic.appsone.api.mobile.util.BigQueryUtil;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.FieldValueList;
import com.google.cloud.bigquery.QueryJobConfiguration;
import com.google.cloud.bigquery.TableResult;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CrashFreeUsersTrendBL implements BusinessLogic<Object, UtilityBean<Object>, CrashFreeUsersTrend> {

	@Override
	public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
		List<String> errorMessages = new ArrayList<>();
		if (!BigQueryUtil.appOSStringValidation(requestObject, errorMessages)
			|| !BigQueryUtil.appDisplayVersionValidation(requestObject, errorMessages)
			|| !BigQueryUtil.fromTimeToTimeValidation(requestObject, errorMessages)) {
				throw new ClientException(String.join(";", errorMessages));
		}
		String appOSString = requestObject.getQueryParams().get("app-os")[0];
		String appDisplayVersions = requestObject.getQueryParams().get("app-build-version")[0];
		String fromTime = requestObject.getQueryParams().get("from-time")[0];
		String toTime = requestObject.getQueryParams().get("to-time")[0];
		
		return UtilityBean.<Object>builder()
				.appOSString(appOSString)
				.appDisplayVersions(appDisplayVersions)
				.fromTimeString(fromTime)
				.toTimeString(toTime)
				.build();
	}

	@Override
	public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
		return utilityBean;
	}

	@Override
	public CrashFreeUsersTrend processData(UtilityBean<Object> configData) throws DataProcessingException {
		String appOSString = configData.getAppOSString();
		BigQuery bigQuery = BigQueryService.getInstance(appOSString.split("\\.")[0]);
		log.debug("bigQuery Obj: {}", bigQuery);
		
		List<String> appDisplayVersions = Arrays.asList(configData.getAppDisplayVersions().split(","));
		String fromTime = configData.getFromTimeString();
		String toTime = configData.getToTimeString();
		String intervalBucket = BigQueryUtil.getIntervalBucket(fromTime, toTime);
		String intervalBucketUnit = intervalBucket.split(" ")[1];
		String joinCondition = BigQueryUtil.getTruncatedEventTimestamp(intervalBucket, intervalBucketUnit, Constants.CRASHLYTICS_ALIAS);
		
		String activeUsersQuery =   "SELECT " + 
									"  COUNT(DISTINCT user_id) AS totalActiveUsers, " + 
									"  STRING_AGG(DISTINCT user_id) AS activeUserIds " + 
									"FROM " + Constants.getAnalyticsTable(appOSString) + " AS T " +
									"    CROSS JOIN " + 
									"      T.event_params " + 
									"WHERE " + 
									"  event_params.key = 'engagement_time_msec' AND event_params.value.int_value > 0 " + 
									"  AND event_timestamp > " + Long.parseLong(fromTime)*1000L + 
									"  AND event_timestamp < " + Long.parseLong(toTime)*1000L;
		
		QueryJobConfiguration activeUsersQueryConfig = QueryJobConfiguration.newBuilder(activeUsersQuery).setUseLegacySql(false).build();
		
		String crashedUsersTrendQuery = "WITH hours AS (  " + 
										"  SELECT *  " + 
										"  FROM UNNEST(GENERATE_TIMESTAMP_ARRAY(TIMESTAMP_TRUNC(TIMESTAMP_MILLIS("+fromTime+"), "+intervalBucketUnit+"), TIMESTAMP_MILLIS("+toTime+"), INTERVAL "+intervalBucket+")" +
										") AS time ) " + 
										"  " + 
										"SELECT   " + 
										"    STRING(hours.time) AS time,  " +
										"    COUNT(DISTINCT(crashlytics.user.id)) AS crashedUserCount,  " + 
										"    STRING_AGG(DISTINCT(crashlytics.user.id)) AS crashedUserIds,  " + 
										"    crashlytics.application.display_version AS appDisplayVersions  " + 
										"FROM " + Constants.getCrashlyticsTable(appOSString) + " AS crashlytics " +
										"	 RIGHT JOIN hours ON "+joinCondition+" = hours.time " +
										"GROUP BY time, appDisplayVersions " + 
										"ORDER BY 1";
		
		QueryJobConfiguration crashedUsersTrendQueryConfig = QueryJobConfiguration.newBuilder(crashedUsersTrendQuery).setUseLegacySql(false).build();
		
		CrashFreeUsersTrend crashFreeUserTrend = new CrashFreeUsersTrend();
		Long activeUsers = 0L;
		
		Map<String, Set<String>> crashedUserTrendMap = new HashMap<>();
		Map<String, BigDecimal> crashFreeUserTrendMapCount = new HashMap<>();
		
		TableResult activeUsersResult;
		TableResult crashedUsersTrendResult;
		try {
			activeUsersResult = bigQuery.query(activeUsersQueryConfig);
			crashedUsersTrendResult = bigQuery.query(crashedUsersTrendQueryConfig);
		} catch(InterruptedException inExc) {
			throw new DataProcessingException("Error in fetching data from BigQuery");
		}
		
		List<String> activeUserIds = new ArrayList<>();
		Set<String> finalCrashedUserIds = new HashSet<>();
		Iterator<FieldValueList> activeUsersResultIterator = activeUsersResult.iterateAll().iterator();
		while(activeUsersResultIterator.hasNext()) {
			FieldValueList activeUsersResultValue = activeUsersResultIterator.next();
			if(null != activeUsersResultValue.get("activeUserIds").getValue() && null != activeUsersResultValue.get("totalActiveUsers").getValue()) {
				activeUserIds.addAll(Arrays.asList(activeUsersResultValue.get("activeUserIds").getStringValue().split(",")));
				activeUsers = activeUsersResultValue.get("totalActiveUsers").getLongValue();
			}
		}
		
		Iterator<FieldValueList> crashedUsersTrendResultIterator = crashedUsersTrendResult.iterateAll().iterator();
		while(crashedUsersTrendResultIterator.hasNext()) {
			FieldValueList crashedUsersTrendResultValue = crashedUsersTrendResultIterator.next();
			Set<String> crashedUserIds = new HashSet<>();
			if (null != crashedUsersTrendResultValue.get("crashedUserIds").getValue()) {
				crashedUserIds.addAll(Arrays.asList(crashedUsersTrendResultValue.get("crashedUserIds").getStringValue().split(",")));
			}
			
			if(!crashedUserTrendMap.containsKey(crashedUsersTrendResultValue.get("time").getStringValue())) {
				if(null != crashedUsersTrendResultValue.get("appDisplayVersions").getValue() && appDisplayVersions.contains(crashedUsersTrendResultValue.get("appDisplayVersions").getStringValue())) {
					crashedUserTrendMap.put(crashedUsersTrendResultValue.get("time").getStringValue(), crashedUserIds);
					finalCrashedUserIds.addAll(crashedUserIds);
				} else {
					crashedUserTrendMap.put(crashedUsersTrendResultValue.get("time").getStringValue(), new HashSet<>());
				}
			} else {
				if(null != crashedUsersTrendResultValue.get("appDisplayVersions").getValue() && appDisplayVersions.contains(crashedUsersTrendResultValue.get("appDisplayVersions").getStringValue())) {
					Set<String> userIds = crashedUserTrendMap.get(crashedUsersTrendResultValue.get("time").getStringValue());
					userIds.addAll(crashedUserIds);
					crashedUserTrendMap.put(crashedUsersTrendResultValue.get("time").getStringValue(), userIds);
					finalCrashedUserIds.addAll(crashedUserIds);
				}
			}
		}
		
		for(Map.Entry<String, Set<String>> entry : crashedUserTrendMap.entrySet()) {
			double crashFreeUserPercentage = (activeUserIds.size() > 0) ? ((double) activeUserIds.size() - entry.getValue().size()) / activeUserIds.size() * 100.00 : 100.00;
			BigDecimal crashFreeUserPercentageBigDecimal = new BigDecimal(crashFreeUserPercentage).setScale(2, RoundingMode.HALF_UP);
			crashFreeUserTrendMapCount.put(entry.getKey(), crashFreeUserPercentageBigDecimal);
		}
		if(activeUsers > 0) {
			crashFreeUserTrend.setCrashFreeUsers(new BigDecimal((double)(activeUsers - finalCrashedUserIds.size()) / activeUsers * 100).setScale(2, RoundingMode.HALF_UP));
		} else {
			crashFreeUserTrend.setCrashFreeUsers(new BigDecimal(100).setScale(2));
		}
		crashFreeUserTrend.setCrashFreeUsersTrend(crashFreeUserTrendMapCount);
		return crashFreeUserTrend;
	}
	
}
