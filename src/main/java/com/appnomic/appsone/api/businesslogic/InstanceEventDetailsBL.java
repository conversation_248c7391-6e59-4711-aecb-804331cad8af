package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.opensearch.AnomalySearchRepo;
import com.appnomic.appsone.api.dao.redis.*;
import com.appnomic.appsone.api.pojo.CompInstClusterDetails;
import com.appnomic.appsone.api.pojo.InstanceEvents;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.StringUtils;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.heal.configuration.entities.BasicInstanceBean;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.CompInstKpiEntity;
import com.heal.configuration.pojos.Service;
import com.heal.configuration.pojos.opensearch.Anomalies;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.appnomic.appsone.api.common.LoggerTags.TAG_INVALID_FROM_TO_TIME;

public class InstanceEventDetailsBL implements BusinessLogic<CompInstClusterDetails, UtilityBean<CompInstClusterDetails>, List<InstanceEvents>> {
    private static final Logger LOGGER = LoggerFactory.getLogger(InstanceEventDetailsBL.class);
    private static final Integer ONE = 1;

    @Override
    public UtilityBean<CompInstClusterDetails> clientValidation(RequestObject requestObject) throws ClientException {
        LOGGER.debug("Inside Client validation");
        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);

        if (StringUtils.isEmpty(identifier)) {
            LOGGER.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION_HEADER);
        if (StringUtils.isEmpty(authKey)) {
            LOGGER.error(Constants.MESSAGE_INVALID_AUTH_TOKEN);
            throw new ClientException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }

        String serviceId = requestObject.getParams().get(Constants.REQUEST_PARAM_SERVICE_ID);
        if (StringUtils.isEmpty(serviceId.trim())) {
            LOGGER.error(UIMessages.INVALID_SERVICE);
            throw new ClientException(UIMessages.INVALID_SERVICE);
        }
        String clusterId = requestObject.getParams().get(Constants.REQUEST_PARAM_CLUSTER_ID);
        if (StringUtils.isEmpty(clusterId.trim())) {
            LOGGER.error(UIMessages.INVALID_CLUSTER_ID);
            throw new ClientException(UIMessages.INVALID_CLUSTER_ID);
        }

        String fromTimeString = requestObject.getQueryParams().get(Constants.REQUEST_PARAM_FROM_TIME)[0];
        String toTimeString = requestObject.getQueryParams().get(Constants.REQUEST_PARAM_TO_TIME)[0];
        validation(serviceId, clusterId, fromTimeString, toTimeString);


        return UtilityBean.<CompInstClusterDetails>builder()
                .accountIdString(identifier)
                .serviceId(Integer.parseInt(serviceId))
                .fromTime(Long.parseLong(fromTimeString))
                .toTime(Long.parseLong(toTimeString))
                .authToken(authKey)
                .clusterId(Integer.parseInt(clusterId))
                .build();
    }

    @Override
    public UtilityBean<CompInstClusterDetails> serverValidation(UtilityBean<CompInstClusterDetails> utilityBean) throws ServerException {
        AccountRepo accountRepo = new AccountRepo();
        ServiceRepo serviceRepo = new ServiceRepo();
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            throw new ServerException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }
        utilityBean.setUserId(userId);

        com.heal.configuration.pojos.Account account = accountRepo.getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
        }
        utilityBean.setAccount(account);

        BasicEntity basicEntity = serviceRepo.getAllServices(account.getIdentifier()).stream().filter(s -> s.getId() == utilityBean.getServiceId()).findAny().orElse(null);
        if (basicEntity == null) {
            LOGGER.error("Invalid service id: {}", utilityBean.getServiceId());
            throw new ServerException(UIMessages.ERROR_INVALID_SERVICE_ID);
        }

        Service service = HealUICache.INSTANCE.getServiceDetails(account.getIdentifier(), basicEntity.getIdentifier());
        if (service == null) {
            LOGGER.error("Invalid service id: {}, identifier:{}", utilityBean.getServiceId(), basicEntity.getIdentifier());
            throw new ServerException(UIMessages.ERROR_INVALID_SERVICE_ID);
        }
        utilityBean.setService(service);

        List<BasicInstanceBean> instances = HealUICache.INSTANCE.getServiceInstanceList(account.getIdentifier(), service.getIdentifier(), true);
        int clusterId = utilityBean.getClusterId();
        BasicInstanceBean clusterInstance = instances.parallelStream()
                .filter(cluster -> cluster.getId() == clusterId)
                .findAny().orElse(null);
        if (clusterId > 0 && clusterInstance == null) {
            LOGGER.error(UIMessages.INVALID_CLUSTER_ID + " {}", clusterId);
            throw new ServerException(UIMessages.INVALID_CLUSTER_ID);
        }
        utilityBean.setClusterInstance(clusterInstance);

        return utilityBean;
    }

    @Override
    public List<InstanceEvents> processData(UtilityBean<CompInstClusterDetails> configData) throws DataProcessingException {
        long start = System.currentTimeMillis();
        Service service = configData.getService();
        AnomalySearchRepo anomalySearchRepo = new AnomalySearchRepo();

        List<InstanceEvents> instanceEvents = HealUICache.INSTANCE.getServiceInstanceList(configData.getAccountIdString(), service.getIdentifier(), true)
                .parallelStream()
                .map(instance -> {
                    AtomicBoolean isConfigChange = new AtomicBoolean(false);
                    AtomicBoolean isEvent = new AtomicBoolean(false);
                    anomalySearchRepo.getAnomalyByInstance(configData.getAccount().getIdentifier(), instance.getIdentifier(), configData.getFromTime(), configData.getToTime())
                            .stream()
                            .map(Anomalies::getKpiId)
                            .distinct()
                            .forEach(kpi -> {
                                if (isConfigChange.get() && isEvent.get()) {
                                    return;
                                }
                                CompInstKpiEntity kpiDetails = new KpiRepo().getKpiDetailByAccInstKpiId(configData.getAccount().getIdentifier(), instance.getIdentifier(), kpi);
                                if (kpiDetails.getType().equals(Constants.MST_TYPE_FILE_WATCH) || kpiDetails.getType().equals(Constants.MST_TYPE_CONFIG_WATCH) || kpiDetails.getIsInfo() == 1) {
                                    isConfigChange.set(true);
                                } else {
                                    isEvent.set(true);
                                }
                            });
                    return InstanceEvents.builder()
                            .instanceName(instance.getName())
                            .instanceId(instance.getId())
                            .configChanges(isConfigChange.get())
                            .eventOccurred(isEvent.get())
                            .build();
                }).collect(Collectors.toList());
        LOGGER.debug("Time taken for processing instance wise event data for event-details is {} ms.", (System.currentTimeMillis() - start));
        return instanceEvents;
    }

    private void validation(String serviceId, String clusterId, String fromTimeString, String toTimeString) throws ClientException {
        try {
            if (Integer.parseInt(serviceId) < ONE) {
                LOGGER.error(UIMessages.INVALID_SERVICE);
                throw new ClientException(UIMessages.INVALID_SERVICE);
            }
            if (Integer.parseInt(clusterId) < ONE) {
                LOGGER.debug("Cluster-id: {} is on service level.", clusterId);
            }
            validationOfFromAndToTime(fromTimeString, toTimeString);
        } catch (NumberFormatException e) {
            LOGGER.error("Error occurred while converting ", e);
            throw new ClientException("Parsing of value not possible.");
        }
    }

    private void validationOfFromAndToTime(String fromTimeString, String toTimeString) throws ClientException {
        Long fromTime;
        Long toTime;
        try {
            fromTime = (fromTimeString == null) ? null : Long.parseLong(fromTimeString);
        } catch (NumberFormatException e) {
            LOGGER.error("Error occurred while converting the fromTime [{}]. Reason: {}", fromTimeString, e.getMessage());
            throw new ClientException(Constants.INVALID_FROM_TIME);
        }

        try {
            toTime = (toTimeString == null) ? null : Long.parseLong(toTimeString);
        } catch (NumberFormatException e) {
            LOGGER.error("Error occurred while converting the toTime [{}]. Reason: {}", toTimeString, e.getMessage());
            throw new ClientException(Constants.INVALID_TO_TIME);
        }

        if ((fromTime == null) || (toTime == null) || (fromTime <= 0) || (toTime <= 0) || (fromTime > toTime)) {
            LOGGER.error(TAG_INVALID_FROM_TO_TIME);
            throw new ClientException(TAG_INVALID_FROM_TO_TIME);
        }
    }
}
