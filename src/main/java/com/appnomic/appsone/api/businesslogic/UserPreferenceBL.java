package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.beans.UtilityBean;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.AppsoneException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.UserRepo;
import com.appnomic.appsone.api.pojo.TagMappingDetails;
import com.appnomic.appsone.api.pojo.UserPreferencePojo;
import com.appnomic.appsone.api.service.mysql.UserPreferenceDataService;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.DateTimeUtil;
import com.appnomic.appsone.api.util.StringUtils;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;

import java.io.IOException;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;

public class UserPreferenceBL {

    private static final Logger logger = LoggerFactory.getLogger(UserPreferenceBL.class);
    private static final ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();

    public UtilityBean clientValidationsForGET(Request request) throws AppsoneException {
        ValidationUtils.commonClientValidations(request);
        String identifier = request.params(Constants.REQUEST_PARAM_IDENTIFIER);
        String authToken = request.headers(Constants.AUTHORIZATION_HEADER);
        return UtilityBean.builder().accountIdentifier(identifier).authToken(authToken).build();
    }

    public TagMappingDetails serverValidationsForGET(UtilityBean utilityBean) throws ServerException {
        try {
            AccountRepo accountRepo = new AccountRepo();
            UserRepo userRepo = new UserRepo();

            String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
            if(userId == null){
                throw new ServerException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
            }

            User user = userRepo.getUser(userId);
            if( user == null ) {
                throw new ServerException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
            }

            Account account = accountRepo.getAccount(utilityBean.getAccountIdentifier());
            if( account == null ) {
                throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
            }
            return getTagMappingForPreferences(account.getId(), user);
        } catch (Exception e) {
            throw new ServerException(e, "Invalid request details.");
        }
    }

    public List<UserPreferencePojo> getUserPreferences(TagMappingDetails tagMappingDetails) {
        List<TagMappingDetails> userPreferencesDb = UserPreferenceDataService.getUserPreferences(tagMappingDetails);
        List<UserPreferencePojo> userPreferences = new ArrayList<>();
        for (TagMappingDetails up : userPreferencesDb) {
            UserPreferencePojo userPreferencePojo = new UserPreferencePojo();
            userPreferencePojo.setId(up.getId());
            userPreferencePojo.setTagKey(up.getTagKey());
            userPreferencePojo.setTagValue(up.getTagValue());
            userPreferences.add(userPreferencePojo);
        }
        return userPreferences;
    }

    public UtilityBean<UserPreferencePojo> clientValidationsForPOST(Request request) throws AppsoneException {
        ValidationUtils.commonClientValidations(request);
        String identifier = request.params(Constants.REQUEST_PARAM_IDENTIFIER);
        String authToken = request.headers(Constants.AUTHORIZATION_HEADER);
        UserPreferencePojo mapperPojo;
        try {
            String sanitizedRequestBody = request.body().replaceAll("\n", "").replaceAll("\r", "");
            mapperPojo = objectMapper.readValue(sanitizedRequestBody, new TypeReference<UserPreferencePojo>() {});
        } catch (IOException e) {
            throw new AppsoneException(Constants.REQUEST_BODY_INVALID_ERROR_MESSAGE);
        }
        if (mapperPojo == null) {
            throw new AppsoneException(Constants.REQUEST_BODY_EMPTY_ERROR_MESSAGE);
        }
        mapperPojo.validate();
        if (!mapperPojo.getError().isEmpty()) {
            throw new AppsoneException(mapperPojo.getError().toString());
        }
        return UtilityBean.<UserPreferencePojo>builder().accountIdentifier(identifier).authToken(authToken).pojoObject(mapperPojo).build();
    }

    public TagMappingDetails serverValidationsForPOST(UtilityBean<UserPreferencePojo> utilityBean) throws ServerException {
        AccountRepo accountRepo = new AccountRepo();
        UserRepo userRepo = new UserRepo();

        Account account = accountRepo.getAccount(utilityBean.getAccountIdentifier());
        if( account == null ) {
            logger.error("Invalid account identifier: {}.", utilityBean.getAccountIdentifier());
            throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
        }
        utilityBean.setAccount(account);

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        User user = userRepo.getUser(userId);
        if(user == null) {
            throw new ServerException(MessageFormat.format(UIMessages.USER_NOT_EXISTS, userId));
        }

        TagMappingDetails tagMappingDetails = new TagMappingDetails();
        tagMappingDetails.setAccountId(account.getId());
        tagMappingDetails.setUserDetailsId(user.getUserDetailsId());
        tagMappingDetails.setObjectId(user.getUserAttributesId());
        tagMappingDetails.setTagId(9); // UserPreference tag id is 9
        tagMappingDetails.setObjectRefTable(Constants.USER_ATTRIBUTES_TABLE_NAME_MYSQL);
        tagMappingDetails.setTagKey(utilityBean.getPojoObject().getTagKey());
        tagMappingDetails.setTagValue(utilityBean.getPojoObject().getTagValue());
        tagMappingDetails.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        tagMappingDetails.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        boolean isAvailable = isUserPreferenceExists(tagMappingDetails);
        if (isAvailable) {
            throw new AppsoneException(MessageFormat.format(UIMessages.MESSAGE_PREFERENCE_EXISTS, tagMappingDetails.getTagKey()));
        }
        return tagMappingDetails;
    }

    public int addUserPreferences(TagMappingDetails tagMappingDetails) throws AppsoneException {
        return UserPreferenceDataService.addUserPreferences(tagMappingDetails);
    }

    public UtilityBean<UserPreferencePojo> clientValidationsForPUT(Request request) throws AppsoneException {
        ValidationUtils.commonClientValidations(request);
        String identifier = request.params(Constants.REQUEST_PARAM_IDENTIFIER);
        String authToken = request.headers(Constants.AUTHORIZATION_HEADER);
        String preferenceId = request.params(Constants.REQUEST_PARAM_PREF_IDENTIFIER);
        if (StringUtils.isEmpty(preferenceId)) {
            throw new AppsoneException(UIMessages.MESSAGE_INVALID_PREFERENCE);
        }
        UserPreferencePojo mapperPojo;
        try {
            mapperPojo = objectMapper.readValue(request.body(), new TypeReference<UserPreferencePojo>() {});
        } catch (IOException e) {
            throw new AppsoneException(Constants.REQUEST_BODY_INVALID_ERROR_MESSAGE);
        }
        if (mapperPojo == null) {
            throw new AppsoneException(Constants.REQUEST_BODY_EMPTY_ERROR_MESSAGE);
        }
        mapperPojo.validate();
        if (!mapperPojo.getError().isEmpty()) {
            logger.error(mapperPojo.getError().toString());
            throw new AppsoneException(mapperPojo.getError().toString());
        }
        mapperPojo.setId(Integer.parseInt(preferenceId));
        int id = getPreferenceId(request);
        mapperPojo.setId(id);
        return UtilityBean.<UserPreferencePojo>builder().accountIdentifier(identifier).authToken(authToken).pojoObject(mapperPojo).build();

    }

    public TagMappingDetails serverValidationsForPUT(UtilityBean<UserPreferencePojo> utilityBean) throws ServerException {
        try {
            AccountRepo accountRepo = new AccountRepo();
            UserRepo userRepo = new UserRepo();

            String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
            if(userId == null){
                throw new ServerException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
            }

            User user = userRepo.getUser(userId);
            if( user == null ) {
                throw new ServerException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
            }

            Account account = accountRepo.getAccount(utilityBean.getAccountIdentifier());
            if( account == null ) {
                throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
            }

            TagMappingDetails tagMappingDetails = getTagMappingForPreferences(account.getId(), user);
            tagMappingDetails.setId(utilityBean.getPojoObject().getId());
            tagMappingDetails.setTagKey(utilityBean.getPojoObject().getTagKey());
            tagMappingDetails.setTagValue(utilityBean.getPojoObject().getTagValue());
            tagMappingDetails.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
            tagMappingDetails.setUserDetailsId(user.getUserDetailsId());
            return tagMappingDetails;
        } catch (Exception e) {
            throw new ServerException(e, "Invalid request details.");
        }
    }

    public void updateUserPreferences(TagMappingDetails tagMappingDetails) {
        UserPreferenceDataService.updateUserPreferences(tagMappingDetails);
    }

    public UtilityBean<UserPreferencePojo> clientValidationsForDELETE(Request request) throws AppsoneException {
        ValidationUtils.commonClientValidations(request);
        String identifier = request.params(Constants.REQUEST_PARAM_IDENTIFIER);
        String authToken = request.headers(Constants.AUTHORIZATION_HEADER);
        int id = getPreferenceId(request);
        UserPreferencePojo userPreferencePojo = new UserPreferencePojo();
        userPreferencePojo.setId(id);
        return UtilityBean.<UserPreferencePojo>builder().accountIdentifier(identifier).authToken(authToken).pojoObject(userPreferencePojo).build();

    }

    public TagMappingDetails serverValidationsForDELETE(UtilityBean<UserPreferencePojo> utilityBean) throws ServerException {

        try {
            AccountRepo accountRepo = new AccountRepo();
            UserRepo userRepo = new UserRepo();

            String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
            if(userId == null){
                throw new ServerException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
            }

            User user = userRepo.getUser(userId);
            if( user == null ) {
                throw new ServerException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
            }

            Account account = accountRepo.getAccount(utilityBean.getAccountIdentifier());
            if( account == null ) {
                throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
            }

            TagMappingDetails tagMappingDetails = getTagMappingForPreferences(account.getId(), user);
            tagMappingDetails.setId(utilityBean.getPojoObject().getId());
            tagMappingDetails.setTagKey(utilityBean.getPojoObject().getTagKey());
            tagMappingDetails.setTagValue(utilityBean.getPojoObject().getTagValue());
            tagMappingDetails.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
            tagMappingDetails.setUserDetailsId(user.getUserDetailsId());
            return tagMappingDetails;
        } catch (Exception e) {
            throw new ServerException(e, "Invalid request details.");
        }
    }

    public void deleteUserPreferences(TagMappingDetails tagMappingDetails) {
        UserPreferenceDataService.deleteUserPreferences(tagMappingDetails);
    }

    private TagMappingDetails getTagMappingForPreferences(int accountId, User user) throws AppsoneException {

        TagMappingDetails tagMappingDetails = new TagMappingDetails();
        tagMappingDetails.setAccountId(accountId);
        tagMappingDetails.setUserDetailsId(user.getUserDetailsId());
        tagMappingDetails.setObjectId(user.getUserAttributesId());
        tagMappingDetails.setTagId(9); // UserPreference tag id is 9
        tagMappingDetails.setObjectRefTable(Constants.USER_ATTRIBUTES_TABLE_NAME_MYSQL);
        return tagMappingDetails;
    }

    private int getPreferenceId(Request request) throws AppsoneException {
        String preferenceId = request.params(Constants.REQUEST_PARAM_PREF_IDENTIFIER);
        if (StringUtils.isEmpty(preferenceId)) {
            throw new AppsoneException(UIMessages.MESSAGE_INVALID_PREFERENCE);
        }
        int id;
        try {
            id = Integer.parseInt(preferenceId);
        } catch (NumberFormatException e) {
            throw new AppsoneException(UIMessages.MESSAGE_INVALID_PREFERENCE);
        }
        return id;
    }

    private boolean isUserPreferenceExists(TagMappingDetails tagMappingDetails) {
        TagMappingDetails userPreference = UserPreferenceDataService.getUserPreference(tagMappingDetails);
        return userPreference != null;
    }
}