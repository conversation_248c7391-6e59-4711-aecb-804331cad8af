package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.beans.tfp.TFPAnomalyTransactionRequestData;
import com.appnomic.appsone.api.beans.tfp.TFPInboundOutboundRequestData;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.ServiceRepo;
import com.appnomic.appsone.api.dao.redis.TimeRangeDetailsDao;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.pojo.tfp.TFPAnomalyTransactionDetails;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.api.util.StringUtils;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.TimeRangeDetails;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class TransactionFlowPathInboundOutboundBL implements BusinessLogic<TFPInboundOutboundRequestData, UtilityBean<TFPInboundOutboundRequestData>, List<TFPAnomalyTransactionDetails>> {

    private static final String RESPONSE_TYPE_DEFAULT = ConfProperties.getString(Constants.TRANSACTION_TYPE,
            Constants.TRANSACTION_TYPE_DEFAULT);

    @Override
    public UtilityBean<TFPInboundOutboundRequestData> clientValidation(RequestObject request) throws ClientException {
        if (request == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authToken)) {
            log.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String accId = request.getParams().get(Constants.REQUEST_PARAM_IDENTIFIER.toLowerCase());
        if (StringUtils.isEmpty(accId)) {
            log.error(Constants.MESSAGE_INVALID_ACCOUNT + ": {}", accId);
            throw new ClientException(Constants.MESSAGE_INVALID_ACCOUNT);
        }

        String srcSvcId = request.getParams().get(Constants.REQUEST_PARAM_SRC_SERVICE_ID.toLowerCase());
        if (StringUtils.isEmpty(srcSvcId)) {
            log.error(Constants.MESSAGE_INVALID_SRC_SERVICE + ": {}", srcSvcId);
            throw new ClientException(Constants.MESSAGE_INVALID_SRC_SERVICE);
        }

        String destSvcId = request.getParams().get(Constants.REQUEST_PARAM_DST_SERVICE_ID.toLowerCase());
        if (StringUtils.isEmpty(destSvcId)) {
            log.error(Constants.MESSAGE_INVALID_DST_SERVICE + ": {}", srcSvcId);
            throw new ClientException(Constants.MESSAGE_INVALID_DST_SERVICE);
        }

        String fromTimeString = request.getQueryParams().get(Constants.REQUEST_PARAM_FROM_TIME)[0];
        if (StringUtils.isEmpty(fromTimeString)) {
            log.error(Constants.MESSAGE_INVALID_TIME_RANGE + ": {}", fromTimeString);
            throw new ClientException(Constants.MESSAGE_INVALID_TIME_RANGE);
        }

        String toTimeString = request.getQueryParams().get(Constants.REQUEST_PARAM_TO_TIME)[0];
        if (StringUtils.isEmpty(toTimeString)) {
            log.error(Constants.MESSAGE_INVALID_TIME_RANGE + ": {}", toTimeString);
            throw new ClientException(Constants.MESSAGE_INVALID_TIME_RANGE);
        }

        long fromTime;
        long toTime;
        try {
            fromTime = Long.parseLong(fromTimeString);
            toTime = Long.parseLong(toTimeString);
        } catch (NumberFormatException e) {
            log.error("Invalid value found where number is required. {}", e.getMessage());
            throw new ClientException(String.format("Invalid value found where number is required. %s", e.getMessage()));
        }

        if (fromTime <= 0 || toTime <= 0 || fromTime >= toTime) {
            log.error("Invalid time range provided");
            throw new ClientException("Invalid time range provided");
        }

        String responseType = request.getParams().getOrDefault(Constants.REQUEST_PARAM_RESPONSE_TYPE, RESPONSE_TYPE_DEFAULT);

        return UtilityBean.<TFPInboundOutboundRequestData>builder()
                .fromTime(fromTime)
                .toTime(toTime)
                .accountIdString(accId)
                .srcServiceIdString(srcSvcId)
                .dstServiceIdString(destSvcId)
                .authToken(authToken)
                .responseType(responseType)
                .build();
    }

    @Override
    public UtilityBean<TFPInboundOutboundRequestData> serverValidation(UtilityBean<TFPInboundOutboundRequestData> utilityBean) throws ServerException {

        TFPInboundOutboundRequestData requestData = new TFPInboundOutboundRequestData();
        ServiceRepo serviceRepo = new ServiceRepo();
        requestData.setFrom(utilityBean.getFromTime());
        requestData.setTo(utilityBean.getToTime());

        Account account = new AccountRepo().getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            log.error("Invalid account identifier: {}.", utilityBean.getAccountIdString());
            throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
        }
        requestData.setAccount(account);

        String accountIdentifier = account.getIdentifier();

        TimeRangeDetailsDao timeRangeDetailsDao = new TimeRangeDetailsDao();
        List<TimeRangeDetails> timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(accountIdentifier);
        if (timeRangeDetailsList == null) {
            log.error("Exception occurred while fetching time range details for account {}", accountIdentifier);
            throw new ServerException("Exception occurred while fetching time range details for account " + accountIdentifier);
        }

        if (timeRangeDetailsList.isEmpty()) {
            log.trace("No time range details found for account {}. Searching for Global account.", accountIdentifier);
            timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }
        if (timeRangeDetailsList == null) {
            log.error("Exception occurred while fetching time range details for account {}", Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
            throw new ServerException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }

        if (timeRangeDetailsList.isEmpty()) {
            log.error("No time range details found");
            throw new ServerException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }
        TimeRangeDetails timeRangeDetails = ValidationUtils.getRollupPojo(utilityBean.getFromTime(), utilityBean.getToTime(), timeRangeDetailsList);
        if (timeRangeDetails == null) {
            log.error("Time range details are invalid.");
            throw new ServerException("Time range details are invalid.");
        }
        utilityBean.setTimeRangeDetails(timeRangeDetails);
        utilityBean.setTimeRangeDetailsList(timeRangeDetailsList);

        List<Integer> dstServiceIds = Arrays.stream(utilityBean.getDstServiceIdString().split(","))
                .map(Integer::parseInt)
                .collect(Collectors.toList());
        List<BasicEntity> destinations = dstServiceIds.stream().map(sId -> serviceRepo.getBasicServiceDetailsWithServiceId(utilityBean.getAccountIdString(), sId)).filter(Objects::nonNull).collect(Collectors.toList());
        if (destinations.isEmpty()) {
            log.error("Invalid destination service id: {}", utilityBean.getDstServiceIdString());
            throw new ServerException(UIMessages.ERROR_INVALID_SERVICE_ID);
        }
        requestData.setDstService(destinations);

        utilityBean.setRequestPayloadObject(requestData);

        return utilityBean;
    }

    /**
     * @param utilityBean contains configData Fields that are required are account, source service, destination service, from and to time
     * @return Top 10 Txns in the following order:
     * 1. Anomalous Txns for Dest Service (Irrespective of source service)
     * 2. Top [10 -  Anomalous Txns] from Source Service to Destination Service by volume
     * @throws DataProcessingException type of exception
     */
    @Override
    public List<TFPAnomalyTransactionDetails> processData(UtilityBean<TFPInboundOutboundRequestData> utilityBean) throws DataProcessingException {
        TFPInboundOutboundRequestData configData = utilityBean.getRequestPayloadObject();
        TFPAnomalyTransactionBL tfpBl = new TFPAnomalyTransactionBL();
        Account account = configData.getAccount();
        List<BasicEntity> dstServices = configData.getDstService();
        long from = configData.getFrom();
        long to = configData.getTo();
        List<TFPAnomalyTransactionDetails> data = new ArrayList<>();

        try {
            for (BasicEntity serviceDetail : dstServices) {
                TFPAnomalyTransactionRequestData reqData = new TFPAnomalyTransactionRequestData();
                reqData.setAccount(account);
                reqData.setFrom(from);
                reqData.setTo(to);
                reqData.setService(serviceDetail);

                UtilityBean<TFPAnomalyTransactionRequestData> temp = UtilityBean.<TFPAnomalyTransactionRequestData>builder()
                        .requestPayloadObject(reqData)
                        .responseType(utilityBean.getResponseType())
                        .timeRangeDetails(utilityBean.getTimeRangeDetails())
                        .timeRangeDetailsList(utilityBean.getTimeRangeDetailsList())
                        .build();
                data.addAll(tfpBl.processData(temp));
                log.debug("Received {} transactions related to service {}", data.size(), serviceDetail.getIdentifier());
            }
        } catch (Exception e) {
            log.error("Exception occurred when fetching transactions. ", e);
        }
        log.debug("Total transactions of size: {} found.", data.size());

        return data;
    }
}
