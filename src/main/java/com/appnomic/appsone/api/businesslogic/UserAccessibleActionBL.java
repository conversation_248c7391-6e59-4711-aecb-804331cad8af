package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.beans.UserAttributesBean;
import com.appnomic.appsone.api.custom.exceptions.AppsoneException;
import com.appnomic.appsone.api.custom.exceptions.RequestException;
import com.appnomic.appsone.api.pojo.UserAccessibleActions;
import com.appnomic.appsone.api.service.mysql.UserAccessDataService;
import com.appnomic.appsone.api.util.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;

import java.util.List;

public class UserAccessibleActionBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserAccessibleActionBL.class);

    public String clientValidation(Request request) throws RequestException, AppsoneException {
        if(null == request) {
            LOGGER.error("Request object is NULL");
            throw new RequestException("Request object is NULL");
        }

        if(null == request.headers("Authorization")) {
            LOGGER.error("Authorization header is NULL");
            throw new RequestException("Authorization header is NULL");
        }

        String userId;
        try {
            userId = CommonUtils.getUserId(request);
        } catch (Exception e) {
            LOGGER.error("Exception encountered while fetching userId. Reason: {}", e.getMessage(), e);
            throw new AppsoneException("");
        }
        return userId;
    }

    public UserAttributesBean serverValidation(String userId) throws AppsoneException {
        UserAccessDataService dataService = new UserAccessDataService();
        UserAttributesBean userAttributesBean = dataService.getUserAttributeDetails(userId);

        if(null == userAttributesBean) {
            LOGGER.error("User details unavailable for userId [{}]", userId);
            throw new AppsoneException("User details unavailable");
        }

        return userAttributesBean;
    }

    public UserAccessibleActions getUserAccessibleActions(UserAttributesBean userAttributesBean) throws AppsoneException {
        UserAccessDataService userAccessDataService = new UserAccessDataService();
        List<String> allowedActions = userAccessDataService.getUserAccessibleActions(userAttributesBean.getAccessProfileId());

        return UserAccessibleActions.builder()
                .profileId(userAttributesBean.getAccessProfileId())
                .profile(userAttributesBean.getAccessProfileName())
                .roleId(userAttributesBean.getRoleId())
                .role(userAttributesBean.getRoleName())
                .isActiveDirectory(0)
                .allowedActions(allowedActions)
                .build();
    }
}
