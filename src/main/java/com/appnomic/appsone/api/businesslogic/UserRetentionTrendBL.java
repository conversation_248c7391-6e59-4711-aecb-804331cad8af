package com.appnomic.appsone.api.businesslogic;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.mobile.util.BigQueryService;
import com.appnomic.appsone.api.mobile.util.BigQueryUtil;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.FieldValueList;
import com.google.cloud.bigquery.QueryJobConfiguration;
import com.google.cloud.bigquery.TableResult;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class UserRetentionTrendBL implements BusinessLogic<Object, UtilityBean<Object>, Map<String, BigDecimal>> {

	@Override
	public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
		List<String> errorMessages = new ArrayList<>();
		if (!BigQueryUtil.appOSStringValidation(requestObject, errorMessages)
			|| !BigQueryUtil.appDisplayVersionValidation(requestObject, errorMessages)
			|| !BigQueryUtil.fromTimeToTimeValidation(requestObject, errorMessages)) {
			throw new ClientException(String.join(";", errorMessages));
		}
		String appOSString = requestObject.getQueryParams().get("app-os")[0];
		String appDisplayVersions = requestObject.getQueryParams().get("app-build-version")[0];
		String fromTime = requestObject.getQueryParams().get("from-time")[0];
		String toTime = requestObject.getQueryParams().get("to-time")[0];
		
		return UtilityBean.<Object>builder()
				.appOSString(appOSString)
				.appDisplayVersions(appDisplayVersions)
                .fromTimeString(fromTime)
                .toTimeString(toTime)
                .build();
	}

	@Override
	public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
		return utilityBean;
	}

	@Override
	public Map<String, BigDecimal> processData(UtilityBean<Object> configData) throws DataProcessingException {
		String appOSString = configData.getAppOSString();
		BigQuery bigQuery = BigQueryService.getInstance(appOSString.split("\\.")[0]);
		log.debug("bigQuery Obj: {}", bigQuery);

		String appDisplayVersionInParam = BigQueryUtil.getInParameter(configData.getAppDisplayVersions());
		String fromTime = configData.getFromTimeString();
		String toTime = configData.getToTimeString();
		
		String fromDateString = getDateString(fromTime);
		String toDateString = getDateString(toTime);
		Long timeDiffMillis = Long.parseLong(toTime) - Long.parseLong(fromTime);
		
		Map<String, BigDecimal> response = new HashMap<>();
		if(timeDiffMillis.equals(Constants.MILLIS_ONE_WEEK)) {
			String query = "WITH " + 
					"    analytics_data AS (" + 
					"        SELECT user_id AS user_id, event_timestamp, event_name, " + 
					"            app_info.version AS app_version, " + 
					"            UNIX_MICROS(TIMESTAMP_MICROS("+fromTime+"000)) AS start_day, " + 
					"            3600*1000*1000*24 AS one_day_micros " + 
					"		 FROM " + Constants.getAnalyticsTable(appOSString) + 
					"        WHERE " + 
					"            _TABLE_SUFFIX BETWEEN '"+fromDateString+"' AND '"+toDateString+"' " + 
					"    )" + 
					"SELECT 		" + 
					"    CASE WHEN day_0_cohort <> 0 THEN day_0_cohort / day_0_cohort * 100 ELSE 0 END AS day0," + 
					"    CASE WHEN day_0_cohort <> 0 THEN day_1_cohort / day_0_cohort * 100 ELSE 0 END AS day1," + 
					"    CASE WHEN day_0_cohort <> 0 THEN day_2_cohort / day_0_cohort * 100 ELSE 0 END AS day2," + 
					"    CASE WHEN day_0_cohort <> 0 THEN day_3_cohort / day_0_cohort * 100 ELSE 0 END AS day3," + 
					"    CASE WHEN day_0_cohort <> 0 THEN day_4_cohort / day_0_cohort * 100 ELSE 0 END AS day4," + 
					"    CASE WHEN day_0_cohort <> 0 THEN day_5_cohort / day_0_cohort * 100 ELSE 0 END AS day5," + 
					"    CASE WHEN day_0_cohort <> 0 THEN day_6_cohort / day_0_cohort * 100 ELSE 0 END AS day6 " + 
					"FROM (   		" + 
					"    WITH day_6_users AS (" + 
					"            SELECT DISTINCT user_id " + 
					"            FROM analytics_data " + 
					"            WHERE event_timestamp BETWEEN start_day+(6*one_day_micros) AND start_day+(7*one_day_micros) " + 
					"        )," + 
					"        day_5_users AS (" + 
					"            SELECT DISTINCT user_id " + 
					"            FROM analytics_data " + 
					"            WHERE event_timestamp BETWEEN start_day+(5*one_day_micros) AND start_day+(6*one_day_micros) " + 
					"        )," + 
					"        day_4_users AS (" + 
					"            SELECT DISTINCT user_id " + 
					"            FROM analytics_data " + 
					"            WHERE event_timestamp BETWEEN start_day+(4*one_day_micros) AND start_day+(5*one_day_micros) " + 
					"        )," + 
					"        day_3_users AS (" + 
					"            SELECT DISTINCT user_id " + 
					"            FROM analytics_data " + 
					"            WHERE event_timestamp BETWEEN start_day+(3*one_day_micros) AND start_day+(4*one_day_micros) " + 
					"        ), " + 
					"        day_2_users AS (" + 
					"            SELECT DISTINCT user_id " + 
					"            FROM analytics_data " + 
					"            WHERE event_timestamp BETWEEN start_day+(2*one_day_micros) AND start_day+(3*one_day_micros) " + 
					"        ), " + 
					"        day_1_users AS ( " + 
					"            SELECT DISTINCT user_id " + 
					"            FROM analytics_data " + 
					"            WHERE event_timestamp BETWEEN start_day+(1*one_day_micros) AND start_day+(2*one_day_micros) " + 
					"        ), " + 
					"        day_0_users AS ( " + 
					"            SELECT DISTINCT user_id " + 
					"            FROM analytics_data " + 
					"            WHERE event_name = 'first_open' " + 
					"            AND app_version IN " + appDisplayVersionInParam + " " + 
					"            AND event_timestamp BETWEEN start_day AND start_day+(1*one_day_micros) " + 
					"        ) " + 
					"        SELECT " + 
					"            (SELECT count(DISTINCT user_id) FROM day_0_users) AS day_0_cohort, " + 
					"            (SELECT count(DISTINCT user_id) FROM day_1_users JOIN day_0_users USING (user_id)) AS day_1_cohort," + 
					"            (SELECT count(DISTINCT user_id) FROM day_2_users JOIN day_0_users USING (user_id)) AS day_2_cohort," + 
					"            (SELECT count(DISTINCT user_id) FROM day_3_users JOIN day_0_users USING (user_id)) AS day_3_cohort," + 
					"            (SELECT count(DISTINCT user_id) FROM day_4_users JOIN day_0_users USING (user_id)) AS day_4_cohort," + 
					"            (SELECT count(DISTINCT user_id) FROM day_5_users JOIN day_0_users USING (user_id)) AS day_5_cohort," + 
					"            (SELECT count(DISTINCT user_id) FROM day_6_users JOIN day_0_users USING (user_id)) AS day_6_cohort" + 
					"            " + 
					"    )";
			QueryJobConfiguration queryConfig = QueryJobConfiguration.newBuilder(query).setUseLegacySql(false).build();
			
			TableResult result;
			try {
				result = bigQuery.query(queryConfig);
			} catch(InterruptedException inExc) {
				throw new DataProcessingException("Error in fetching data from BigQuery");
			}
			
			for(FieldValueList value : result.iterateAll()) {
				response.put("day0", value.get("day0").getNumericValue().setScale(2, RoundingMode.HALF_UP));
				response.put("day1", value.get("day1").getNumericValue().setScale(2, RoundingMode.HALF_UP));
				response.put("day2", value.get("day2").getNumericValue().setScale(2, RoundingMode.HALF_UP));
				response.put("day3", value.get("day3").getNumericValue().setScale(2, RoundingMode.HALF_UP));
				response.put("day4", value.get("day4").getNumericValue().setScale(2, RoundingMode.HALF_UP));
				response.put("day5", value.get("day5").getNumericValue().setScale(2, RoundingMode.HALF_UP));
				response.put("day6", value.get("day6").getNumericValue().setScale(2, RoundingMode.HALF_UP));
			}
		} else if (timeDiffMillis.equals(Constants.MILLIS_ONE_MONTH_28) || timeDiffMillis.equals(Constants.MILLIS_ONE_MONTH_29)
				|| timeDiffMillis.equals(Constants.MILLIS_ONE_MONTH_30) || timeDiffMillis.equals(Constants.MILLIS_ONE_MONTH_31)) {
			String query = "WITH analytics_data AS ( " + 
					"  		SELECT user_id AS user_id, event_timestamp, event_name,  " + 
					"    	app_info.version AS app_version, " + 
					"    	UNIX_MICROS(TIMESTAMP_MICROS("+fromTime+"000)) AS start_day, " + 
					"    	3600*1000*1000*24*7 AS one_week_micros " + 
					"FROM " + Constants.getAnalyticsTable(appOSString) + 
					"WHERE " + 
					"		_TABLE_SUFFIX BETWEEN '"+fromDateString+"' AND '"+toDateString+"' " + 
					") " + 
					" " + 
					"SELECT "+
					"		CASE WHEN week_0_cohort <> 0 THEN week_0_cohort / week_0_cohort * 100 ELSE 0 END AS week0, " + 
					" 		CASE WHEN week_0_cohort <> 0 THEN week_1_cohort / week_0_cohort * 100 ELSE 0 END AS week1, " + 
					" 		CASE WHEN week_0_cohort <> 0 THEN week_2_cohort / week_0_cohort * 100 ELSE 0 END AS week2, " + 
					" 		CASE WHEN week_0_cohort <> 0 THEN week_3_cohort / week_0_cohort * 100 ELSE 0 END AS week3 " + 
					"FROM ( " + 
					"  		WITH week_3_users AS ( " + 
					"    			SELECT DISTINCT user_id " + 
					"    			FROM analytics_data " + 
					"    			WHERE event_timestamp BETWEEN start_day+(3*one_week_micros) AND start_day+(4*one_week_micros) " + 
					"  			), " + 
					"  			week_2_users AS ( " + 
					"    			SELECT DISTINCT user_id " + 
					"    			FROM analytics_data " + 
					"    			WHERE event_timestamp BETWEEN start_day+(2*one_week_micros) AND start_day+(3*one_week_micros) " + 
					"  			), " + 
					"  			week_1_users AS ( " + 
					"    			SELECT DISTINCT user_id " + 
					"    			FROM analytics_data " + 
					"    			WHERE event_timestamp BETWEEN start_day+(1*one_week_micros) AND start_day+(2*one_week_micros) " + 
					"  			),  " + 
					"  			week_0_users AS ( " + 
					"    			SELECT DISTINCT user_id " + 
					"    			FROM analytics_data " + 
					"    			WHERE event_name = 'first_open' " + 
					"      			AND app_version IN " + appDisplayVersionInParam + " " +
					"      			AND event_timestamp BETWEEN start_day AND start_day+(1*one_week_micros) " + 
					"  			) " + 
					"  		SELECT  " + 
					"    		(SELECT count(DISTINCT user_id)  " + 
					"     		FROM week_0_users) AS week_0_cohort, " + 
					"    		(SELECT count(DISTINCT user_id)  " + 
					"     		FROM week_1_users  " + 
					"     		JOIN week_0_users USING (user_id)) AS week_1_cohort, " + 
					"    		(SELECT count(DISTINCT user_id)  " + 
					"     		FROM week_2_users  " + 
					"     		JOIN week_0_users USING (user_id)) AS week_2_cohort, " + 
					"    		(SELECT count(DISTINCT user_id)  " + 
					"     		FROM week_3_users  " + 
					"     		JOIN week_0_users USING (user_id)) AS week_3_cohort " + 
					")";
			
			QueryJobConfiguration queryConfig = QueryJobConfiguration.newBuilder(query).setUseLegacySql(false).build();
			
			TableResult result;
			try {
				result = bigQuery.query(queryConfig);
			} catch(InterruptedException inExc) {
				throw new DataProcessingException("Error in fetching data from BigQuery");
			}
			
			for(FieldValueList value : result.iterateAll()) {
				response.put("week0", value.get("week0").getNumericValue().setScale(2, RoundingMode.HALF_UP));
				response.put("week1", value.get("week1").getNumericValue().setScale(2, RoundingMode.HALF_UP));
				response.put("week2", value.get("week2").getNumericValue().setScale(2, RoundingMode.HALF_UP));
				response.put("week3", value.get("week3").getNumericValue().setScale(2, RoundingMode.HALF_UP));
			}
		} else {
			return null;
		}
		
		return response;
	}
	
	private String getDateString(String timeMillis) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTimeInMillis(Long.parseLong(timeMillis));
		String year = String.valueOf(calendar.get(Calendar.YEAR));
        String month = String.valueOf(calendar.get(Calendar.MONTH) + 1);
        String day = String.valueOf(calendar.get(Calendar.DAY_OF_MONTH));
        
        month = (month.length() == 1) ? (0 + month) : month;
        day = (day.length() == 1) ? (0 + day) : day;
        
		return year+month+day;
	}

}
