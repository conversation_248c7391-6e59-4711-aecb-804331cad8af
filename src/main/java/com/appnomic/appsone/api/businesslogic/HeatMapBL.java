package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.beans.CategoryDetailBean;
import com.appnomic.appsone.api.beans.HeatMapRequestData;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.client.ClientValidator;
import com.appnomic.appsone.api.common.client.impl.HeatMapRequestClientValidator;
import com.appnomic.appsone.api.common.server.ServerValidator;
import com.appnomic.appsone.api.common.server.impl.HeatMapRequestServerValidator;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.opensearch.AnomalySearchRepo;
import com.appnomic.appsone.api.dao.opensearch.CollatedKpiRepo;
import com.appnomic.appsone.api.dao.opensearch.CollatedTransactionsSearchRepo;
import com.appnomic.appsone.api.dao.opensearch.RawTransactionSearchRepo;
import com.appnomic.appsone.api.dao.redis.ComponentRepo;
import com.appnomic.appsone.api.dao.redis.InstanceRepo;
import com.appnomic.appsone.api.dao.redis.KpiRepo;
import com.appnomic.appsone.api.dao.redis.ServiceRepo;
import com.appnomic.appsone.api.pojo.BehaviourType;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.TabularResultsTypePojo;
import com.appnomic.appsone.api.pojo.heatmap.AnomalyDetailedCategory;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.DateTimeUtil;
import com.appnomic.appsone.api.util.NewTimeIntervalGenerationUtility;
import com.appnomic.appsone.opeasearchquery.results.TabularResults;
import com.heal.configuration.entities.BasicInstanceBean;
import com.heal.configuration.pojos.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

public class HeatMapBL implements BusinessLogic<Object, HeatMapRequestData, List<AnomalyDetailedCategory>> {
    private static final Logger LOGGER = LoggerFactory.getLogger(HeatMapBL.class);

    @Override
    public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
        ClientValidator<Object> validator = new HeatMapRequestClientValidator();
        return validator.validate(requestObject);
    }

    @Override
    public HeatMapRequestData serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
        ServerValidator<HeatMapRequestData> validator = new HeatMapRequestServerValidator();
        return validator.validate(utilityBean);
    }

    @Override
    public List<AnomalyDetailedCategory> processData(HeatMapRequestData configData) throws DataProcessingException {
        long st = System.currentTimeMillis();
        try {
            Account account = configData.getAccount();
            Service service = configData.getService();
            long fromTime = configData.getFrom();
            long toTime = configData.getTo();
            List<AnomalyDetailedCategory> anomalyDetails = new ArrayList<>();
            ServiceRepo serviceRepo = new ServiceRepo();
            InstanceRepo instanceRepo = new InstanceRepo();
            AnomalySearchRepo anomalySearchRepo = new AnomalySearchRepo();
            CollatedKpiRepo collatedKpiRepo = new CollatedKpiRepo();

            NewTimeIntervalGenerationUtility t = new NewTimeIntervalGenerationUtility();
            t.setTimezoneOffset(account.getIdentifier());
            t.processTimeRange(fromTime, toTime);
            t.addEndTimeInOSTime();

            List<Long> times = t.getOSTimes();

            long start = System.currentTimeMillis();
            List<BasicInstanceBean> instanceBeans = HealUICache.INSTANCE.getServiceInstanceList(account.getIdentifier(), service.getIdentifier(), true);

            LOGGER.info("Time taken for fetching mapped instances is {} ms.", (System.currentTimeMillis() - start));

            //Get Workload violations
            List<BasicTransactionEntity> transactions = serviceRepo.getTransactionsByServiceIdentifier(account.getIdentifier(), service.getIdentifier())
                    .stream()
                    .filter(c -> c.getStatus() == 1)
                    .collect(Collectors.toList());
            if (!transactions.isEmpty()) {
                LOGGER.debug("Service: {}, has {} mapped transactions.", service.getName(), transactions.size());
                start = System.currentTimeMillis();
                List<CategoryDetailBean> workloadCategoryList = getCategoryForWorkLoad();
                anomalyDetails.addAll(getWorkloadDataByService(account, service, fromTime, toTime, instanceBeans, workloadCategoryList,
                        configData.getTimeRangeDetails(), configData.getTimeRangeDetailsList(), times));
                LOGGER.debug("Time taken to fetch workload violations is {} ms.", (System.currentTimeMillis() - start));
            } else {
                LOGGER.warn("There are no mapped transactions for service id: {}, hence workload is not processed.", service.getName());
            }

            List<String> serviceIdentifierList = instanceBeans.parallelStream()
                    .filter(instance -> instance.getClusterId() != 0)
                    .flatMap(m -> instanceRepo.getServiceDetailsWithInstanceIdentifier(account.getIdentifier(), m.getIdentifier()).stream()
                            .map(BasicEntity::getIdentifier))
                    .distinct()
                    .collect(Collectors.toList());


            List<String> clusterList = instanceRepo.getInstancesByAccount(account.getIdentifier())
                    .parallelStream().filter(CompInstClusterDetails::isCluster)
                    .map(CompInstClusterDetails::getIdentifier)
                    .collect(Collectors.toList());

            Map<String, Map<String, Long>> instCategoryAnomalyMap = anomalySearchRepo.getAnomaliesCountInstanceCategory(account.getIdentifier(),
                    serviceIdentifierList, fromTime, toTime - 1);
            Map<String, Map<String, Long>> instCategoryDataCheckMap = collatedKpiRepo.getDataCheckInstanceCategory(account.getIdentifier(),
                    service.getIdentifier(), fromTime, toTime - 1, configData.getTimeRangeDetails(), configData.getTimeRangeDetailsList(), times);

            start = System.currentTimeMillis();
            anomalyDetails.addAll(instanceBeans.parallelStream()
                    .map(instance -> getBehaviourDataByService(account.getIdentifier(), clusterList.contains(instance.getIdentifier()),
                            instCategoryAnomalyMap.getOrDefault(instance.getIdentifier(), new HashMap<>()),
                            instCategoryDataCheckMap.getOrDefault(instance.getIdentifier(), new HashMap<>()), instance))
                    .flatMap(Collection::parallelStream).collect(Collectors.toList()));
            LOGGER.info("Time taken to fetch kpi violations is {} ms.", (System.currentTimeMillis() - start));

            return anomalyDetails;
        } catch (Exception e) {
            LOGGER.error("Error occurred while processing heat map data.", e);
            throw new DataProcessingException(e.getMessage());
        } finally {
            LOGGER.debug("Total time taken for loading heat map: {} is {} ms.", configData.getService().getName(), System.currentTimeMillis() - st);
        }
    }

    private List<AnomalyDetailedCategory> getBehaviourDataByService(String accountIdentifier, boolean isCluster,
                                                                    Map<String, Long> catAnomalyMap, Map<String, Long> catDataMap, BasicInstanceBean instanceDetails) {
        LOGGER.trace("{} getAnomaliesForService().", Constants.INVOKED_METHOD);

        AtomicBoolean availData = new AtomicBoolean(false);
        AtomicBoolean watcherData = new AtomicBoolean(false);
        AtomicLong availAnomaly = new AtomicLong(0);
        AtomicLong watcherAnomaly = new AtomicLong(0);
        return new KpiRepo().getKpiDetailByAccInst(accountIdentifier, instanceDetails.getIdentifier())
                .stream()
                .map(k -> CategoryDetailBean.builder()
                        .categoryId(k.getCategoryDetails().getId())
                        .identifier(k.getCategoryDetails().getIdentifier())
                        .name(k.getCategoryDetails().getName())
                        .kpiType(k.getType())
                        .build())
                .distinct()
                .map(c -> {
                    if (isCluster &&
                            (c.getKpiType().equalsIgnoreCase("ConfigWatch") || c.getKpiType().equalsIgnoreCase("FileWatch")
                                    || c.getKpiType().equalsIgnoreCase("Availability"))) {
                        return null;
                    }
                    AnomalyDetailedCategory temp = new AnomalyDetailedCategory();
                    temp.setInstanceId(instanceDetails.getId());
                    if (c.getKpiType().equalsIgnoreCase("ConfigWatch") || c.getKpiType().equalsIgnoreCase("FileWatch")) {
                        temp.setCategoryId(-3);
                        temp.setCategoryIdentifier(null);
                        if (!watcherData.get() && catDataMap.containsKey(c.getIdentifier())) {
                            watcherData.set(true);
                        }
                        if (catAnomalyMap.containsKey(c.getIdentifier())) {
                            watcherAnomaly.set(watcherAnomaly.get() + catAnomalyMap.get(c.getIdentifier()));
                        }
                    } else if (c.getKpiType().equalsIgnoreCase("Availability")) {
                        temp.setCategoryId(-1);
                        temp.setCategoryIdentifier(null);
                        if (!availData.get() && catDataMap.containsKey(c.getIdentifier())) {
                            availData.set(true);
                        }
                        if (catAnomalyMap.containsKey(c.getIdentifier())) {
                            availAnomaly.set(availAnomaly.get() + catAnomalyMap.get(c.getIdentifier()));
                        }
                    } else {
                        temp.setCategoryId(c.getCategoryId());
                        temp.setCategoryIdentifier(c.getIdentifier());
                    }
                    temp.setAnomalyCount(catAnomalyMap.getOrDefault(c.getIdentifier(), 0L));
                    temp.setBehaviourType((instanceDetails.getComponentTypeId() == 1) ? BehaviourType.HB.name() : BehaviourType.CB.name());
                    return temp;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(Function.identity()))
                .entrySet().stream().map(entry -> {
                    AnomalyDetailedCategory categoryAnomaly = entry.getKey();
                    categoryAnomaly.setAnomalyCount(entry.getValue().stream().mapToLong(AnomalyDetailedCategory::getAnomalyCount).sum());

                    if (categoryAnomaly.getAnomalyCount() > 0) {
                        categoryAnomaly.setDataExists(1);
                    } else if (categoryAnomaly.getCategoryIdentifier() != null && catDataMap.containsKey(categoryAnomaly.getCategoryIdentifier())) {
                        categoryAnomaly.setDataExists(1);
                    } else if (categoryAnomaly.getCategoryIdentifier() != null && !catDataMap.containsKey(categoryAnomaly.getCategoryIdentifier())) {
                        categoryAnomaly.setDataExists(0);
                    } else if (categoryAnomaly.getCategoryId() == -1) {
                        categoryAnomaly.setDataExists(availData.get() ? 1 : 0);
                        categoryAnomaly.setAnomalyCount(availAnomaly.get());
                    } else if (categoryAnomaly.getCategoryId() == -3) {
                        categoryAnomaly.setDataExists(watcherData.get() ? 1 : 0);
                        categoryAnomaly.setAnomalyCount(watcherAnomaly.get());
                    }
                    return categoryAnomaly;
                }).collect(Collectors.toList());
    }

    private List<AnomalyDetailedCategory> getWorkloadDataByService(Account account, Service serviceDetails,
                                                                   long fromTime, long toTime,
                                                                   List<BasicInstanceBean> mappedInstances,
                                                                   List<CategoryDetailBean> workloadCategories,
                                                                   TimeRangeDetails timeRangeDetails,
                                                                   List<TimeRangeDetails> timeRangeDetailsList, List<Long> times) {

        CollatedTransactionsSearchRepo collatedTransactionsSearchRepo = new CollatedTransactionsSearchRepo();
        InstanceRepo instanceRepo = new InstanceRepo();
        AnomalySearchRepo anomalySearchRepo = new AnomalySearchRepo();

        Map<String, Long> categoryWiseAnomalyMap = anomalySearchRepo.getAnomalyCountByService(account.getIdentifier(), serviceDetails.getIdentifier(), fromTime, toTime - 1);

        Map<String, Long> agentWiseDataMap = new HashMap<>();
        AtomicLong serviceWiseData = new AtomicLong();
        if (DateTimeUtil.inRange(fromTime)) {
            TabularResults tabularResults = new RawTransactionSearchRepo().getWorkloadDataHeatMapPage(account.getIdentifier(), serviceDetails.getIdentifier(), fromTime, toTime - 1);
            if (tabularResults == null || tabularResults.getRowResults() == null || tabularResults.getRowResults().isEmpty()) {
                serviceWiseData.set(0);
            } else {
                serviceWiseData.set(1);
                agentWiseDataMap = tabularResults.getRowResults().stream()
                        .collect(Collectors.toMap(c -> c.getListOfRows().get(0).getColumnValue(), TabularResults.ResultRow::getCountValue));
            }
        } else {
            agentWiseDataMap = collatedTransactionsSearchRepo.getAgentTransactionCollatedDataByService(account.getIdentifier(), serviceDetails.getIdentifier(), fromTime, toTime - 1, timeRangeDetails, timeRangeDetailsList, times);
            List<TabularResultsTypePojo> serviceWiseDataList = collatedTransactionsSearchRepo.getTransactionCountByService(account.getIdentifier(),
                    serviceDetails.getIdentifier(), fromTime, toTime - 1, timeRangeDetails, timeRangeDetailsList, times);
            serviceWiseDataList.forEach(c -> serviceWiseData.addAndGet(c.getCount()));
        }

        Map<Integer, List<String>> instanceWiseAgents = mappedInstances.parallelStream()
                .map(i -> instanceRepo.getInstanceDetailsWithInstIdentifier(account.getIdentifier(), i.getIdentifier()))
                .filter(Objects::nonNull)
                .filter(i -> Objects.nonNull(i.getAgentIds()) && !i.getAgentIds().isEmpty())
                .collect(Collectors.toMap(CompInstClusterDetails::getId, CompInstClusterDetails::getAgentIds));

        Map<String, Long> finalAgentWiseDataMap = agentWiseDataMap;
        List<AnomalyDetailedCategory> result = mappedInstances.parallelStream()
                .map(instance -> {
                    try {
                        return workloadCategories.parallelStream()
                                .map(category -> {
                                    AnomalyDetailedCategory anomalyDetail = new AnomalyDetailedCategory();
                                    anomalyDetail.setInstanceId(instance.getId());
                                    anomalyDetail.setCategoryId(category.getCategoryId());
                                    anomalyDetail.setCategoryIdentifier(category.getIdentifier());
                                    anomalyDetail.setBehaviourType(BehaviourType.WL.toString());

                                    if (instance.getClusterId() == 0) {
                                        LOGGER.debug("Adding workload violation: {}", anomalyDetail);
                                        anomalyDetail.setDataExists(serviceWiseData.get() > 0 ? 1 : 0);
                                        anomalyDetail.setAnomalyCount(categoryWiseAnomalyMap.getOrDefault(category.getIdentifier(), 0L));
                                    } else {
                                        List<String> agents = instanceWiseAgents.getOrDefault(instance.getId(), new ArrayList<>());
                                        anomalyDetail.setDataExists(agents.stream().anyMatch(finalAgentWiseDataMap::containsKey) ? 1 : 0);
                                        // Instance wise transaction anomalies are not generating, so we consider 0 anomalies
                                        anomalyDetail.setAnomalyCount(0);
                                    }
                                    return anomalyDetail;
                                }).collect(Collectors.toList());
                    } catch (Exception e) {
                        HealUICache.INSTANCE.updateHealUIErrors(1);
                        LOGGER.debug("Error occurred while populating category details for instance:{}", instance.getIdentifier(), e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        LOGGER.debug("Collected {} anomalies categories for {} instances.", result.size(), mappedInstances.size());
        return result;
    }

    private List<CategoryDetailBean> getCategoryForWorkLoad() {
        return new ComponentRepo().getComponentKpis(Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT, Constants.TRANSACTION_IDENTIFIER_DEFAULT)
                .stream()
                .filter(c -> c.getStatus() == 1)
                .map(k -> CategoryDetailBean.builder()
                        .identifier(k.getCategoryDetails().getIdentifier())
                        .categoryId(k.getCategoryDetails().getId())
                        .name(k.getCategoryDetails().getName())
                        .build())
                .distinct()
                .collect(Collectors.toList());
    }
}
