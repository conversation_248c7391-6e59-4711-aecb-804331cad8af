package com.appnomic.appsone.api.businesslogic;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.mobile.model.EventVersionTrend;
import com.appnomic.appsone.api.mobile.util.BigQueryService;
import com.appnomic.appsone.api.mobile.util.BigQueryUtil;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.FieldValueList;
import com.google.cloud.bigquery.QueryJobConfiguration;
import com.google.cloud.bigquery.TableResult;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class GetEventVersionTrendBL implements BusinessLogic<Object, UtilityBean<Object>, Map<String, EventVersionTrend>> {

	@Override
	public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
		List<String> errorMessages = new ArrayList<>();
		if (!BigQueryUtil.appOSStringValidation(requestObject, errorMessages)
			|| !BigQueryUtil.appDisplayVersionValidation(requestObject, errorMessages)
			|| !BigQueryUtil.fromTimeToTimeValidation(requestObject, errorMessages)
			|| !BigQueryUtil.issueIdValidation(requestObject, errorMessages)) {
				throw new ClientException(String.join(";", errorMessages));
		}
		String appOSString = requestObject.getQueryParams().get("app-os")[0];
		String appDisplayVersions = requestObject.getQueryParams().get("app-build-version")[0];
		String fromTime = requestObject.getQueryParams().get("from-time")[0];
		String toTime = requestObject.getQueryParams().get("to-time")[0];
		String issueId = requestObject.getParams().get(":id");
		
		return UtilityBean.<Object>builder()
				.appOSString(appOSString)
				.appDisplayVersions(appDisplayVersions)
				.fromTimeString(fromTime)
				.toTimeString(toTime)
				.issueId(issueId)
				.build();
	}

	@Override
	public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
		return utilityBean;
	}

	@Override
	public Map<String, EventVersionTrend> processData(UtilityBean<Object> configData) throws DataProcessingException {
		String appOSString = configData.getAppOSString();
		BigQuery bigQuery = BigQueryService.getInstance(appOSString.split("\\.")[0]);
		log.debug("bigQuery Obj: {}", bigQuery);

		String appDisplayVersionInParam = BigQueryUtil.getInParameter(configData.getAppDisplayVersions());
		String fromTime = configData.getFromTimeString();
		String toTime = configData.getToTimeString();
		String issueId = configData.getIssueId();
		String intervalBucket = BigQueryUtil.getIntervalBucket(fromTime, toTime);
		String intervalBucketUnit = intervalBucket.split(" ")[1];
		String truncatedEventTimestamp = BigQueryUtil.getTruncatedEventTimestamp(intervalBucket, intervalBucketUnit, Constants.CRASHLYTICS_ALIAS);
		
		String query =  "SELECT  " + 
						"   " + truncatedEventTimestamp + " AS time, " +
						"	TIMESTAMP_TRUNC(TIMESTAMP_MILLIS("+fromTime+"), "+intervalBucketUnit+") AS truncatedFromTime, " + 
						"	crashlytics.application.display_version AS appDisplayVersions, " + 
						"	COUNT(crashlytics.event_id) AS crashes " + 
						"FROM "+Constants.getCrashlyticsTable(appOSString)+" AS crashlytics " + 
						"WHERE crashlytics.issue_id = '"+issueId+"' " + 
						"	AND crashlytics.application.display_version IN " + appDisplayVersionInParam + " " +
						"	AND crashlytics.event_timestamp > TIMESTAMP_MILLIS("+fromTime+") " + 
						"   AND crashlytics.event_timestamp < TIMESTAMP_MILLIS("+toTime+") " +
						"GROUP BY time, appDisplayVersions";
		
		QueryJobConfiguration queryConfig = QueryJobConfiguration.newBuilder(query).setUseLegacySql(false).build();
		
		TableResult result;
		try {
			result = bigQuery.query(queryConfig);
		} catch(InterruptedException inExc) {
			throw new DataProcessingException("Error in fetching data from BigQuery");
		}
		Map<String, EventVersionTrend> response = new HashMap<>();
		
		for (FieldValueList value : result.iterateAll()) {
			EventVersionTrend eventVersionTrend = new EventVersionTrend();
			Map<String, Long> eventVersionTrendBucket = new HashMap<>();
			if (!response.containsKey(value.get("appDisplayVersions").getStringValue())) {
				if (eventVersionTrendBucket.isEmpty()) {
					eventVersionTrendBucket = BigQueryUtil.createTimeBucket(value.get("truncatedFromTime").getTimestampValue() / 1000L, Long.parseLong(toTime), intervalBucket);
				}
				if (eventVersionTrendBucket.containsKey(String.valueOf(value.get("time").getTimestampValue() / 1000L))) {
					eventVersionTrendBucket.put(String.valueOf(value.get("time").getTimestampValue() / 1000L), value.get("crashes").getLongValue());
					eventVersionTrend.setCrashCount(value.get("crashes").getLongValue());
				} else {
					eventVersionTrendBucket.put(String.valueOf(value.get("time").getTimestampValue() / 1000L), 0L);
				}
				eventVersionTrend.setTrend(eventVersionTrendBucket);
			} else {
				EventVersionTrend event = response.get(value.get("appDisplayVersions").getStringValue());
				if (event.getTrend().containsKey(String.valueOf(value.get("time").getTimestampValue() / 1000L))) {
					Long existingCount = event.getCrashCount();
					Long newCount = value.get("crashes").getLongValue();
					event.getTrend().put(String.valueOf(value.get("time").getTimestampValue() / 1000L), newCount);
					eventVersionTrend.setCrashCount(existingCount + newCount);
					eventVersionTrend.setTrend(event.getTrend());
				}
			}
			response.put(value.get("appDisplayVersions").getStringValue(), eventVersionTrend);
		}
		
		return response;
	}
	
}
