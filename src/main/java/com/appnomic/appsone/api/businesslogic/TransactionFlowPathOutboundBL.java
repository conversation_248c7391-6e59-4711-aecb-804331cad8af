package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.beans.UserAccountIdentifiersBean;
import com.appnomic.appsone.api.beans.tfp.TFPRequestData;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.cache.UserDetailsCache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.opensearch.CollatedTransactionsSearchRepo;
import com.appnomic.appsone.api.dao.opensearch.RawTransactionSearchRepo;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.ApplicationRepo;
import com.appnomic.appsone.api.dao.redis.ServiceRepo;
import com.appnomic.appsone.api.dao.redis.TimeRangeDetailsDao;
import com.appnomic.appsone.api.pojo.ConnectionDetails;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.TabularResultsTypePojo;
import com.appnomic.appsone.api.pojo.UserAccessDetails;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.pojo.tfp.TFPServiceDetails;
import com.appnomic.appsone.api.service.KeyCloakAuthService;
import com.appnomic.appsone.api.util.*;
import com.appnomic.appsone.model.JWTData;
import com.appnomic.appsone.opeasearchquery.results.TabularResults;
import com.heal.configuration.enums.KPIAttributes;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.Application;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.TimeRangeDetails;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Slf4j
public class TransactionFlowPathOutboundBL implements BusinessLogic<TFPRequestData, UtilityBean<TFPRequestData>, List<TFPServiceDetails>> {

    private static final String RESPONSE_TYPE_DEFAULT = ConfProperties.getString(Constants.TRANSACTION_TYPE,
            Constants.TRANSACTION_TYPE_DEFAULT);

    @Override
    public UtilityBean<TFPRequestData> clientValidation(RequestObject request) throws ClientException {
        if (request == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authToken)) {
            log.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String accId = request.getParams().get(Constants.REQUEST_PARAM_IDENTIFIER.toLowerCase());
        if (StringUtils.isEmpty(accId)) {
            log.error(Constants.MESSAGE_INVALID_ACCOUNT + ": {}", accId);
            throw new ClientException(Constants.MESSAGE_INVALID_ACCOUNT);
        }
        List<String> appIds = new ArrayList<>();
        if (request.getParams().get(Constants.REQUEST_PARAM_APPLICATION_ID).equalsIgnoreCase("all")) {
            appIds = Arrays.stream(request.getQueryParams().get(Constants.REQUEST_QUERY_PARAM_APPLICATION_ID)[0].toLowerCase().split(",")).collect(Collectors.toList());
            for (String appId : appIds) {
                if (StringUtils.isEmpty(appId)) {
                    log.error(Constants.MESSAGE_INVALID_APPLICATION + ": {}", appId);
                    throw new ClientException(Constants.MESSAGE_INVALID_APPLICATION);
                }
            }
        } else {
            appIds.add(request.getParams().get(Constants.REQUEST_PARAM_APPLICATION_SMALLID));
            for (String appId : appIds) {
                if (StringUtils.isEmpty(appId)) {
                    log.error(Constants.MESSAGE_INVALID_APPLICATION + ": {}", appId);
                    throw new ClientException(Constants.MESSAGE_INVALID_APPLICATION);
                }
            }
        }

        String fromTimeString = request.getQueryParams().get(Constants.REQUEST_PARAM_FROM_TIME)[0];
        if (StringUtils.isEmpty(fromTimeString)) {
            log.error(Constants.MESSAGE_INVALID_TIME_RANGE + ": {}", fromTimeString);
            throw new ClientException(Constants.MESSAGE_INVALID_TIME_RANGE);
        }

        String toTimeString = request.getQueryParams().get(Constants.REQUEST_PARAM_TO_TIME)[0];
        if (StringUtils.isEmpty(toTimeString)) {
            log.error(Constants.MESSAGE_INVALID_TIME_RANGE + ": {}", toTimeString);
            throw new ClientException(Constants.MESSAGE_INVALID_TIME_RANGE);
        }
        List<Integer> applicationIds = new ArrayList<>();
        long fromTime;
        long toTime;
        try {
            for (String id : appIds) {
                Integer.parseInt(id);
                applicationIds.add(Integer.valueOf(id));
            }
            fromTime = Long.parseLong(fromTimeString);
            toTime = Long.parseLong(toTimeString);
        } catch (NumberFormatException e) {
            log.error("Invalid value found where number is required. {}", e.getMessage());
            throw new ClientException(String.format("Invalid value found where number is required. %s", e.getMessage()));
        }

        if (fromTime <= 0 || toTime <= 0 || fromTime >= toTime) {
            log.error("Invalid time range provided");
            throw new ClientException("Invalid time range provided");
        }

        String responseType = request.getParams().getOrDefault(Constants.REQUEST_PARAM_RESPONSE_TYPE, RESPONSE_TYPE_DEFAULT);

        return UtilityBean.<TFPRequestData>builder()
                .fromTime(fromTime)
                .toTime(toTime)
                .accountIdString(accId)
                .appIds(applicationIds)
                .authToken(authToken)
                .responseType(responseType)
                .build();

    }

    @Override
    public UtilityBean<TFPRequestData> serverValidation(UtilityBean<TFPRequestData> utilityBean) throws ServerException {

        UserAccessDetails accessDetails = getAccessDetails(utilityBean);
        TFPRequestData tfpRequestData = new TFPRequestData();
        tfpRequestData.setUserAccessDetails(accessDetails);
        tfpRequestData.setFromTime(utilityBean.getFromTime());
        tfpRequestData.setToTime(utilityBean.getToTime());

        Account account = new AccountRepo().getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            log.error("Invalid account identifier: {}.", utilityBean.getAccountIdString());
            throw new ServerException(Constants.MESSAGE_INVALID_ACCOUNT);
        }
        tfpRequestData.setAccount(account);

        String accountIdentifier = account.getIdentifier();

        TimeRangeDetailsDao timeRangeDetailsDao = new TimeRangeDetailsDao();
        List<TimeRangeDetails> timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(accountIdentifier);
        if (timeRangeDetailsList == null) {
            log.error("Exception occurred while fetching time range details for account {}", accountIdentifier);
            throw new ServerException("Exception occurred while fetching time range details for account " + accountIdentifier);
        }

        if (timeRangeDetailsList.isEmpty()) {
            log.trace("No time range details found for account {}. Searching for Global account.", accountIdentifier);
            timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }
        if (timeRangeDetailsList == null) {
            log.error("Exception occurred while fetching time range details for account {}", Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
            throw new ServerException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }

        if (timeRangeDetailsList.isEmpty()) {
            log.error("No time range details found");
            throw new ServerException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }
        TimeRangeDetails timeRangeDetails = ValidationUtils.getRollupPojo(utilityBean.getFromTime(), utilityBean.getToTime(), timeRangeDetailsList);
        if (timeRangeDetails == null) {
            log.error("Time range details are invalid.");
            throw new ServerException("Time range details are invalid.");
        }
        utilityBean.setTimeRangeDetails(timeRangeDetails);
        utilityBean.setTimeRangeDetailsList(timeRangeDetailsList);

        List<Integer> appIds = utilityBean.getAppIds();
        List<Application> applications = new ArrayList<>();
        List<Application> applicationList = new ApplicationRepo().getAllApplicationDetails(account.getIdentifier());
        for (Integer id : appIds) {
            Application application = applicationList.stream().filter(e -> e.getId() == id).findAny().orElse(null);
            if (application == null) {
                log.error("Invalid application id: {}", utilityBean.getApplicationId());
                throw new ServerException("Invalid application id.");
            }
            applications.add(application);
        }

        tfpRequestData.setApplications(applications);
        log.info("All parameter received have been validated from server.");

        utilityBean.setRequestPayloadObject(tfpRequestData);
        return utilityBean;
    }

    private UserAccessDetails getAccessDetails(UtilityBean<TFPRequestData> utilityBean) throws ServerException {

        String userId;
        try {
            JWTData jwtData = KeyCloakAuthService.extractUserDetails(utilityBean.getAuthToken());
            userId = jwtData.getSub();
        } catch (Exception e) {
            log.error("Invalid Authorization Token");
            throw new ServerException("Invalid Authorization Token");
        }

        UserAccessDetails accessDetails;
        try {
            accessDetails = UserDetailsCache.getInstance().userApplications
                    .get(new UserAccountIdentifiersBean(userId, utilityBean.getAccountIdString()));
        } catch (ExecutionException e) {
            log.error("Exception while fetching user access details. Reason: {}", e.getMessage(), e);
            throw new ServerException("Error in fetching user access details");
        }
        return accessDetails;
    }

    @Override
    public List<TFPServiceDetails> processData(UtilityBean<TFPRequestData> utilityBean) throws DataProcessingException {

        TFPRequestData configData = utilityBean.getRequestPayloadObject();
        List<Application> applications = configData.getApplications();
        Account account = configData.getAccount();
        long fromTime = configData.getFromTime();
        long toTime = configData.getToTime();

        ServiceRepo serviceRepo = new ServiceRepo();
        TFPCommonBL tfpCommonBL = new TFPCommonBL();
        long start;
        start = System.currentTimeMillis();
        List<ConnectionDetails> allConnectionList = HealUICache.INSTANCE.getAccountConnectionList(account.getId());
        log.debug("Time taken to fetch configuration data is {} ms.", (System.currentTimeMillis() - start));

        List<BasicEntity> allServiceList = serviceRepo.getAllServices(account.getIdentifier());
        Map<String, BasicEntity> serviceMap = allServiceList.parallelStream().collect(Collectors.toMap(BasicEntity::getIdentifier, c -> c));

        List<TFPServiceDetails> result = new ArrayList<>();
        List<TFPServiceDetails> tempTfpServiceDetailsList;
        if (DateTimeUtil.inRange(fromTime)) {
            TabularResults tabularResults = new RawTransactionSearchRepo().getInboundOutboundTxnDetails(account.getIdentifier(),
                    utilityBean.getResponseType(), fromTime, toTime);
            tempTfpServiceDetailsList = extractRawTabularResults(tabularResults, allServiceList);
        } else {
            NewTimeIntervalGenerationUtility t = new NewTimeIntervalGenerationUtility();
            t.setTimezoneOffset(account.getIdentifier());
            t.processTimeRange(fromTime, toTime);
            t.addEndTimeInOSTime();
            List<Long> times = t.getOSTimes();
            List<TabularResultsTypePojo> tabularResultsTypeList = new CollatedTransactionsSearchRepo().getAllTransactionDataByService(account.getIdentifier(),
                    utilityBean.getResponseType(), fromTime, toTime - Constants.MINUTE,
                    utilityBean.getTimeRangeDetails(), utilityBean.getTimeRangeDetailsList(), times);
            tempTfpServiceDetailsList = extractTabularResults(tabularResultsTypeList, serviceMap);
        }

        List<BasicEntity> allEntryServices = allServiceList
                .parallelStream()
                .map(s -> HealUICache.INSTANCE.getServiceDetails(account.getIdentifier(), s.getIdentifier()))
                .filter(Objects::nonNull)
                .filter(s -> s.getTags() != null)
                .filter(s -> s.getTags().stream().anyMatch(tags -> tags.getType().equalsIgnoreCase(Constants.ENTRY_POINT)))
                .collect(Collectors.toList());

        applications.forEach(app -> {
            List<BasicEntity> applicationServices = HealUICache.INSTANCE.getApplicationServiceList(account.getIdentifier(), app.getIdentifier());
            if (applicationServices.isEmpty()) {
                log.warn("No services mapped to application {}", app.getIdentifier());
                return;
            }

            List<String> applicationServiceIdentifiers = applicationServices.stream().map(BasicEntity::getIdentifier).collect(Collectors.toList());

            List<BasicEntity> otherEntryServices = allEntryServices
                    .stream()
                    .filter(s -> !applicationServiceIdentifiers.contains(s.getIdentifier()))
                    .collect(Collectors.toList());

            List<BasicEntity> outboundServiceList = getAdjacentEntrypointServices(allConnectionList, applicationServices, otherEntryServices);

            if (!outboundServiceList.isEmpty()) {
                List<TFPServiceDetails> tfpServiceDetailsList = tfpCommonBL.getServiceTransactionStats(account, app,
                        configData.getUserAccessDetails(), outboundServiceList, tempTfpServiceDetailsList);

                enrichApplicationName(configData.getUserAccessDetails(), tfpServiceDetailsList, account.getIdentifier());
                log.debug("Outbound services count is '{}' for application {}.", tfpServiceDetailsList.size(), app.getName());
                result.addAll(tfpServiceDetailsList);
            } else {
                log.debug("Outbound services count is '0' for application {}.", app.getName());
            }
        });
        log.debug("Total outbound services count: {}.", result.size());
        return result.parallelStream()
                .sorted(Comparator.comparing(TFPServiceDetails::getVolume).reversed())
                .collect(Collectors.toList());
    }

    public List<TFPServiceDetails> extractRawTabularResults(TabularResults tabularResults, List<BasicEntity> serviceList) {

        List<TFPServiceDetails> tfpServiceDetailsList = new ArrayList<>();

        Map<String, Map<String, Long>> serviceTxnMap = new HashMap<>();

        if (tabularResults != null && tabularResults.getRowResults() != null && !tabularResults.getRowResults().isEmpty()) {
            for (TabularResults.ResultRow resultRow : tabularResults.getRowResults()) {
                Map<String, Long> attrTxnMap = new HashMap<>();

                String serviceIdentifier = "";

                for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {

                    if (resultRowColumn.getColumnName().equalsIgnoreCase("serviceIdentifier")) {
                        serviceIdentifier = resultRowColumn.getColumnValue();
                    } else if (resultRowColumn.getColumnName().equalsIgnoreCase("responseTimes.responseStatusTag")) {
                        attrTxnMap.put(resultRowColumn.getColumnValue(), resultRow.getCountValue());
                    }
                }

                if (serviceTxnMap.containsKey(serviceIdentifier)) {
                    Map<String, Long> tmp = serviceTxnMap.get(serviceIdentifier);
                    attrTxnMap.forEach((key, value) -> tmp.put(key, tmp.getOrDefault(key, 0L) + value));
                    serviceTxnMap.put(serviceIdentifier, tmp);
                } else {
                    serviceTxnMap.put(serviceIdentifier, attrTxnMap);
                }

            }
        }

        serviceList.forEach(s -> {
            if (serviceTxnMap.containsKey(s.getIdentifier())) {
                Map<String, Long> tmp = serviceTxnMap.get(s.getIdentifier());
                long totalVolume = tmp.values().stream().mapToLong(Long::longValue).sum();
                final long[] failVolume = new long[1];
                final long[] slowVolume = new long[1];
                tmp.forEach((k, v) -> {
                    if (KPIAttributes.SLOW_VOLUME.getColumnName().equalsIgnoreCase(k)) {
                        slowVolume[0] = v;
                    } else if (KPIAttributes.FAIL_VOLUME.getColumnName().equalsIgnoreCase(k)) {
                        failVolume[0] = v;
                    }
                });

                tfpServiceDetailsList.add(TFPServiceDetails.builder()
                        .serviceId(s.getId())
                        .serviceName(s.getIdentifier())
                        .volume(totalVolume)
                        .isSlow(slowVolume[0] > 0 ? 1 : 0)
                        .isFailed(failVolume[0] > 0 ? 1 : 0)
                        .build());
            }
        });

        return tfpServiceDetailsList;

    }

    public List<TFPServiceDetails> extractTabularResults(List<TabularResultsTypePojo> tabularResultsTypeList, Map<String, BasicEntity> serviceMap) {

        List<TFPServiceDetails> tfpServiceDetailsList = new ArrayList<>();

        for (TabularResultsTypePojo entry : tabularResultsTypeList) {
            TabularResults tabularResults = entry.getTabularResults();
            for (TabularResults.ResultRow resultRow : tabularResults.getRowResults()) {

                String serviceIdentifier = "";
                long totalVolume = 0;
                long totalSlow = 0;
                long totalFail = 0;

                for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {

                    if (resultRowColumn.getColumnName().equalsIgnoreCase("serviceId")) {
                        serviceIdentifier = resultRowColumn.getColumnValue();
                    } else if (KPIAttributes.TOTAL_VOLUME.name().equalsIgnoreCase(resultRowColumn.getColumnName().split("\\.")[1])) {
                        totalVolume += (long) Double.parseDouble(resultRowColumn.getColumnValue());
                    } else if (KPIAttributes.SLOW_VOLUME.name().equalsIgnoreCase(resultRowColumn.getColumnName().split("\\.")[1])) {
                        totalSlow += (long) Double.parseDouble(resultRowColumn.getColumnValue());
                    } else if (KPIAttributes.FAIL_VOLUME.name().equalsIgnoreCase(resultRowColumn.getColumnName().split("\\.")[1])) {
                        totalFail += (long) Double.parseDouble(resultRowColumn.getColumnValue());
                    }

                }

                tfpServiceDetailsList.add(TFPServiceDetails.builder()
                        .serviceId(serviceMap.get(serviceIdentifier).getId())
                        .serviceName(serviceIdentifier)
                        .volume(totalVolume)
                        .isSlow(totalSlow > 0 ? 1 : 0)
                        .isFailed(totalFail > 0 ? 1 : 0)
                        .build());
            }
        }

        return tfpServiceDetailsList;
    }

    protected void enrichApplicationName(UserAccessDetails userAccessDetails, List<TFPServiceDetails> resultList, String accountIdentifier) {
        long start = System.currentTimeMillis();
        ServiceRepo serviceRepo = new ServiceRepo();
        for (TFPServiceDetails serviceDetails : resultList) {

            List<BasicEntity> svcApplications = serviceRepo.getApplicationsByServiceId(accountIdentifier, serviceDetails.getServiceId());
            if (svcApplications.isEmpty()) {
                log.error("Unable to get application for service id: {}.", serviceDetails.getServiceId());
                continue;
            }

            List<String> apps = userAccessDetails.getApplicationIdentifiers();

            serviceDetails.setUserAccess(apps.contains(svcApplications.get(0).getIdentifier()));

            serviceDetails.setApplicationName(svcApplications.get(0).getName());
            log.debug("Enriched app name: {}.", serviceDetails);
        }
        log.debug("Time taken to enrich app name for outbound services is {} ms.", (System.currentTimeMillis() - start));
    }

    /**
     * This method will get all services that are connected to mapped services and are entrypoint services
     * it is used to get list of outbound services.
     *
     * @param allConnectionDetails  (entire accounts connections list)
     * @param mappedServiceList     (all service mapped to selected application)
     * @param entrypointServiceList (all entrypoint services excluding those mapped to selected application)
     * @return List<Controller>
     */
    private List<BasicEntity> getAdjacentEntrypointServices(List<ConnectionDetails> allConnectionDetails,
                                                            List<BasicEntity> mappedServiceList,
                                                            List<BasicEntity> entrypointServiceList) {
        allConnectionDetails = allConnectionDetails.stream()
                .filter(it -> Constants.CONTROLLER.equalsIgnoreCase(it.getSourceRefObject()) &&
                        Constants.CONTROLLER.equalsIgnoreCase(it.getDestinationRefObject()))
                .collect(Collectors.toList());

        //Get a list of all entrypoint service id where source is mapped svc and destination is an entry point svc
        Set<Integer> filteredServiceId = allConnectionDetails.stream()
                .filter(it -> (entrypointServiceList.stream().anyMatch(epSvc -> epSvc.getId() == it.getDestinationId())))
                .filter(it -> (mappedServiceList.stream().anyMatch(svc -> svc.getId() == it.getSourceId())))
                .map(ConnectionDetails::getDestinationId)
                .collect(Collectors.toSet());

        //return a list of entry point svc list where the source is a mapped service
        return entrypointServiceList.stream()
                .filter(epSvc -> filteredServiceId.contains(epSvc.getId()))
                .collect(Collectors.toList());
    }
}
