package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.opensearch.AnomalySearchRepo;
import com.appnomic.appsone.api.dao.opensearch.CollatedKpiRepo;
import com.appnomic.appsone.api.dao.redis.*;
import com.appnomic.appsone.api.pojo.*;
import com.appnomic.appsone.api.pojo.request.AvailabilityKpiRequest;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.service.KPIAnomalyService;
import com.appnomic.appsone.api.util.NewTimeIntervalGenerationUtility;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.appnomic.appsone.opeasearchquery.results.TabularResults;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.CompInstClusterDetails;
import com.heal.configuration.pojos.*;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 09/05/22
 */
@Slf4j
public class AvailabilityKpiBL implements BusinessLogic<AvailabilityKpiRequest, UtilityBean<AvailabilityKpiRequest>, List<AvailabilityKpi>> {

    @Override
    public UtilityBean<AvailabilityKpiRequest> clientValidation(RequestObject request) throws ClientException {
        log.trace("Inside Client validation");

        if (request == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);

        if (authToken == null || authToken.trim().isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        AvailabilityKpiRequest availabilityKpiRequest = new AvailabilityKpiRequest(request);

        if (!availabilityKpiRequest.isValidParameters(availabilityKpiRequest)) {
            log.error("Client validation failed for GET AVAILABILITY KPI request.");
            throw new ClientException(UIMessages.CLIENT_VALIDATION_FAILED);
        }

        return UtilityBean.<AvailabilityKpiRequest>builder()
                .requestPayloadObject(availabilityKpiRequest)
                .fromTime(Long.valueOf(availabilityKpiRequest.getFromTime()))
                .toTime(Long.valueOf(availabilityKpiRequest.getToTime()))
                .accountIdString(availabilityKpiRequest.getAccountId())
                .serviceIdString(availabilityKpiRequest.getServiceId())
                .componentInstanceIdString(availabilityKpiRequest.getCompInstanceId())
                .authToken(authToken)
                .build();
    }

    @Override
    public UtilityBean<AvailabilityKpiRequest> serverValidation(UtilityBean<AvailabilityKpiRequest> utilityBean) throws ServerException {
        log.trace("Inside serverValidation method.");

        AccountRepo accountRepo = new AccountRepo();
        ServiceRepo serviceRepo = new ServiceRepo();
        InstanceRepo instanceRepo = new InstanceRepo();

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error("Invalid authorization token provided: {}.", utilityBean.getAuthToken());
            throw new ServerException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }
        utilityBean.setUserId(userId);

        Account account = accountRepo.getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            log.error("Invalid account identifier: {}.", utilityBean.getAccountIdString());
            throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
        }
        utilityBean.setAccount(account);

        String accountIdentifier = account.getIdentifier();

        TimeRangeDetailsDao timeRangeDetailsDao = new TimeRangeDetailsDao();
        List<TimeRangeDetails> timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(accountIdentifier);
        if (timeRangeDetailsList == null) {
            log.error("Exception occurred while fetching time range details for account {}", accountIdentifier);
            throw new ServerException("Exception occurred while fetching time range details for account " + accountIdentifier);
        }

        if (timeRangeDetailsList.isEmpty()) {
            log.trace("No time range details found for account {}. Searching for Global account.", accountIdentifier);
            timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }
        if (timeRangeDetailsList == null) {
            log.error("Exception occurred while fetching time range details for account {}", Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
            throw new ServerException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }

        if (timeRangeDetailsList.isEmpty()) {
            log.error("No time range details found");
            throw new ServerException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }
        TimeRangeDetails timeRangeDetails = ValidationUtils.getRollupPojo(utilityBean.getFromTime(), utilityBean.getToTime(), timeRangeDetailsList);
        if (timeRangeDetails == null) {
            log.error("Time range details are invalid.");
            throw new ServerException("Time range details are invalid.");
        }
        utilityBean.setTimeRangeDetails(timeRangeDetails);
        utilityBean.setTimeRangeDetailsList(timeRangeDetailsList);

        BasicEntity basicEntity = serviceRepo.getAllServices(account.getIdentifier()).stream().filter(s -> s.getId() == Integer.parseInt(utilityBean.getServiceIdString())).findAny().orElse(null);
        if (basicEntity == null) {
            log.error("Invalid service id: {}", utilityBean.getServiceId());
            throw new ServerException(UIMessages.ERROR_INVALID_SERVICE_ID);
        }

        Service service = HealUICache.INSTANCE.getServiceDetails(account.getIdentifier(), basicEntity.getIdentifier());
        if (service == null) {
            log.error("Invalid service id: {}, identifier:{}", utilityBean.getServiceId(), basicEntity.getIdentifier());
            throw new ServerException(UIMessages.ERROR_INVALID_SERVICE_ID);
        }
        utilityBean.setService(service);

        CompInstClusterDetails compInstClusterDetails = instanceRepo.getInstanceDetailsWithId(utilityBean.getAccountIdString(), Integer.parseInt(utilityBean.getComponentInstanceIdString()));
        if (compInstClusterDetails == null) {
            log.error("Invalid instance id: {}", utilityBean.getComponentInstanceIdString());
            throw new ServerException(UIMessages.ERROR_INVALID_SERVICE_ID);
        }
        utilityBean.setCompInstance(compInstClusterDetails);

        return utilityBean;
    }

    @Override
    public List<AvailabilityKpi> processData(UtilityBean<AvailabilityKpiRequest> configData) throws DataProcessingException {
        long st = System.currentTimeMillis();

        Account account = configData.getAccount();
        CompInstClusterDetails instanceDetails = configData.getCompInstance();
        Long fromTime = configData.getFromTime();
        Long toTime = configData.getToTime();

        NewTimeIntervalGenerationUtility t = new NewTimeIntervalGenerationUtility();
        t.setTimezoneOffset(account.getIdentifier());
        t.processTimeRange(fromTime, toTime);
        t.addEndTimeInOSTime();

        List<Long> times = t.getOSTimes();

        AnomalySearchRepo anomalySearchRepo = new AnomalySearchRepo();
        CollatedKpiRepo collatedKpiRepo = new CollatedKpiRepo();
        KPIAnomalyService kpiAnomalyService = new KPIAnomalyService();
        AgentRepo agentRepo = new AgentRepo();

        List<CompInstKpiEntity> kpiList = new KpiRepo().getKpiDetailByAccInst(account.getIdentifier(), instanceDetails.getIdentifier());

        List<Integer> groupKpiList = kpiList.parallelStream().filter(BasicKpiEntity::getIsGroup).map(BasicKpiEntity::getId).collect(Collectors.toList());

        //Data exists check Operations----------------
        Map<Integer, AgentValuePojo> nonGroupKpiAgentValueMap = new HashMap<>();
        Map<Integer, Map<String, AgentValuePojo>> groupKpiAgentValueMap = new HashMap<>();

        List<TabularResultsTypePojo> tabularResultsTypePojoList = collatedKpiRepo.getIsDataAvailableInCategory(account.getIdentifier(), instanceDetails.getIdentifier(), fromTime, toTime, configData.getTimeRangeDetails(),
                configData.getTimeRangeDetailsList(), times);

        for (TabularResultsTypePojo entry : tabularResultsTypePojoList) {
            TabularResults tabularResults = entry.getTabularResults();

            if (tabularResults != null && tabularResults.getRowResults() != null && !tabularResults.getRowResults().isEmpty()) {
                for (TabularResults.ResultRow resultRow : tabularResults.getRowResults()) {
                    int kpiId = 0;
                    String groupAttribute = "";
                    List<String> agentIdentifierList = new ArrayList<>();
                    for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {
                        if (resultRowColumn.getColumnName().equalsIgnoreCase("kpiId")) {

                            kpiId = Integer.parseInt(resultRowColumn.getColumnValue());

                        } else if (resultRowColumn.getColumnName().equalsIgnoreCase("groupAttribute")) {

                            groupAttribute = resultRowColumn.getColumnValue();

                        } else if (resultRowColumn.getColumnName().equalsIgnoreCase("UNIQUE" + ": agentIdentifier")) {

                            agentIdentifierList.add(resultRowColumn.getColumnValue());

                        }
                    }

                    //Non Group KPI
                    if (!groupKpiList.contains(kpiId)) {
                        if (nonGroupKpiAgentValueMap.containsKey(kpiId)) {
                            agentIdentifierList.addAll(nonGroupKpiAgentValueMap.get(kpiId).getAgentIdentifierList());
                            nonGroupKpiAgentValueMap.put(kpiId, AgentValuePojo.builder()
                                    .agentIdentifierList(agentIdentifierList.parallelStream().collect(Collectors.toSet()))
                                    .dataExists(nonGroupKpiAgentValueMap.get(kpiId).isDataExists())
                                    .build());
                        } else {
                            nonGroupKpiAgentValueMap.put(kpiId, AgentValuePojo.builder()
                                    .agentIdentifierList(agentIdentifierList.parallelStream().collect(Collectors.toSet()))
                                    .dataExists(resultRow.getCountValue() > 0)
                                    .build());
                        }
                    } else {
                        //Group Kpi

                        Map<String, AgentValuePojo> grpAttributeAgentDetails = new HashMap<>();
                        if (groupKpiAgentValueMap.containsKey(kpiId)) {
                            if (groupKpiAgentValueMap.get(kpiId).containsKey(groupAttribute)) {
                                agentIdentifierList.addAll(groupKpiAgentValueMap.get(kpiId).get(groupAttribute).getAgentIdentifierList());
                                grpAttributeAgentDetails.put(groupAttribute, AgentValuePojo.builder()
                                        .agentIdentifierList(agentIdentifierList.parallelStream().collect(Collectors.toSet()))
                                        .dataExists(resultRow.getCountValue() > 0)
                                        .build());
                            } else {
                                grpAttributeAgentDetails.put(groupAttribute, AgentValuePojo.builder()
                                        .agentIdentifierList(agentIdentifierList.parallelStream().collect(Collectors.toSet()))
                                        .dataExists(resultRow.getCountValue() > 0)
                                        .build());
                            }
                            groupKpiAgentValueMap.get(kpiId).putAll(grpAttributeAgentDetails);
                        } else {
                            grpAttributeAgentDetails.put(groupAttribute, AgentValuePojo.builder()
                                    .agentIdentifierList(agentIdentifierList.parallelStream().collect(Collectors.toSet()))
                                    .dataExists(resultRow.getCountValue() > 0)
                                    .build());
                            groupKpiAgentValueMap.put(kpiId, grpAttributeAgentDetails);
                        }
                    }
                }
            }
        }
        //-------------------------------------------------------------------

        //Anomaly Exists check Operations----------------
        Map<Integer, Long> nonGroupKpiAnomalyCountMap = new HashMap<>();
        Map<Integer, Map<String, Long>> groupKpiAnomalyCountMap = new HashMap<>();

        TabularResults anomaliesTabularResults = anomalySearchRepo.getAnomaliesCountInCategories(account.getIdentifier(),
                instanceDetails.getIdentifier(), Constants.AVAILABILITY_KPI_IDENTIFIER, true, false, fromTime, toTime);

        if (anomaliesTabularResults != null && anomaliesTabularResults.getRowResults() != null && !anomaliesTabularResults.getRowResults().isEmpty()) {
            for (TabularResults.ResultRow resultRow : anomaliesTabularResults.getRowResults()) {
                int kpiId = 0;
                String kpiAttribute = "";
                for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {
                    if (resultRowColumn.getColumnName().equalsIgnoreCase("kpiId")) {

                        kpiId = Integer.parseInt(resultRowColumn.getColumnValue());

                    } else if (resultRowColumn.getColumnName().equalsIgnoreCase("kpiAttribute")) {

                        kpiAttribute = resultRowColumn.getColumnValue();

                    }
                }

                //Non Group KPI
                if (!groupKpiList.contains(kpiId)) {
                    nonGroupKpiAnomalyCountMap.put(kpiId, nonGroupKpiAnomalyCountMap.getOrDefault(kpiId, 0L) + resultRow.getCountValue());
                } else {
                    //Group Kpi
                    if (groupKpiAnomalyCountMap.containsKey(kpiId)) {
                        groupKpiAnomalyCountMap.get(kpiId).put(kpiAttribute, groupKpiAnomalyCountMap.get(kpiId).getOrDefault(kpiAttribute, 0L) + resultRow.getCountValue());
                    } else {
                        Map<String, Long> kpiAttributeAnomalyMap = new HashMap<>();
                        kpiAttributeAnomalyMap.put(kpiAttribute, resultRow.getCountValue());
                        groupKpiAnomalyCountMap.put(kpiId, kpiAttributeAnomalyMap);
                    }
                }

            }
        }
        //----------------------------

        List<KpiNames> filteredKpiListForCategory = kpiList
                .parallelStream()
                .filter(kpi -> (Constants.AVAILABILITY_KPI_IDENTIFIER.equals(kpi.getType())))
                .filter(kpi -> {
                    if (!kpi.getIsGroup() || kpi.getDiscovery() == 1) {
                        return true;
                    }
                    if (kpi.getAttributeValues() == null || kpi.getAttributeValues().isEmpty()) {
                        return false;
                    } else return kpi.getAttributeValues().size() != 1 || !kpi.getAttributeValues().containsKey("ALL");
                })
                .map(kpi -> {

                    AtomicLong totalAnomalyCount = new AtomicLong();
                    boolean dataExists = false;
                    String source = Constants.UNKNOWN_SOURCE;
                    List<KpiAttribute> kpiAttributes = new ArrayList<>();
                    if (kpi.getIsGroup()) {
                        Map<String, Long> attributeAnomalyCount = groupKpiAnomalyCountMap.getOrDefault(kpi.getId(), Collections.emptyMap());
                        Map<String, AgentValuePojo> attributeDataCheck = groupKpiAgentValueMap.getOrDefault(kpi.getId(), Collections.emptyMap());

                        Set<String> sourceSet = attributeDataCheck.values().parallelStream()
                                .map(AgentValuePojo::getAgentIdentifierList)
                                .distinct()
                                .flatMap(Collection::parallelStream).collect(Collectors.toSet());
                        source = kpiAnomalyService.getSourceName(sourceSet, agentRepo);

                        Set<String> attributes = (kpi.getDiscovery() == 1) ? attributeDataCheck.keySet() :
                                (kpi.getAttributeValues() == null) ? Collections.emptySet() : kpi.getAttributeValues().keySet().parallelStream().filter(c -> !c.equalsIgnoreCase("ALL")).collect(Collectors.toSet());

                        kpiAttributes = attributes.stream()
                                .map(s -> {
                                    totalAnomalyCount.addAndGet(attributeAnomalyCount.getOrDefault(s, 0L));
                                    return KpiAttribute.builder()
                                            .name(s)
                                            .aliasName(kpi.getAttributeValues() != null ? kpi.getAttributeValues().getOrDefault(s, s) : s)
                                            .anomalyCount(attributeAnomalyCount.getOrDefault(s, 0L))
                                            .isDataAvailable(attributeDataCheck.containsKey(s) && attributeDataCheck.get(s).isDataExists())
                                            .build();
                                })
                                .collect(Collectors.toList());

                        dataExists = !attributeDataCheck.isEmpty();

                    } else {
                        totalAnomalyCount.set(nonGroupKpiAnomalyCountMap.getOrDefault(kpi.getId(), 0L).intValue());

                        KpiAttribute attribute = KpiAttribute.builder()
                                .anomalyCount(totalAnomalyCount.intValue())
                                .name("ALL")
                                .aliasName("ALL")
//                                .isDataAvailable(true)
                                .build();
                        log.debug("Total anomaly for kpi id {} is {}", kpi.getId(), totalAnomalyCount.get());

                        if (nonGroupKpiAgentValueMap.containsKey(kpi.getId())) {
                            dataExists = nonGroupKpiAgentValueMap.get(kpi.getId()).isDataExists();
                            source = kpiAnomalyService.getSourceName(nonGroupKpiAgentValueMap.get(kpi.getId()).getAgentIdentifierList(), agentRepo);
                            log.debug("source {}, uniqueAgentIdentifiers {}", source, nonGroupKpiAgentValueMap.get(kpi.getId()).getAgentIdentifierList());
                        }

                        attribute.setIsDataAvailable(dataExists);
                        kpiAttributes.add(attribute);
                    }
                    return KpiNames.builder()
                            .id(kpi.getId())
                            .categoryId(kpi.getCategoryDetails().getId())
                            .categoryIdentifier(kpi.getCategoryDetails().getIdentifier())
                            .categoryName(kpi.getCategoryDetails().getName())
                            .status(kpi.getStatus())
                            .isDiscovery(kpi.getDiscovery())
                            .groupId(kpi.getGroupId())
                            .clusterOperation(kpi.getClusterAggType())
                            .name(kpi.getName())
                            .groupName(kpi.getGroupName())
                            .unit(kpi.getUnit())
                            .type(kpi.getType())
                            .anomalyCount(totalAnomalyCount.intValue())
                            .isDataAvailable(kpi.getStatus() == 1 && dataExists) // if KPI will be inactive then we will not display data in UI.
                            .isGroupKpi(kpi.getIsGroup() ? 1 : 0)
                            .attribute(!kpiAttributes.isEmpty() ? kpiAttributes : Collections.emptyList())
                            .description(kpi.getDescription())
                            .source(source)
                            .build();
                })
                .collect(Collectors.toList());

        log.info("Time taken for get filter kpi list:{} ms.", (System.currentTimeMillis() - st));

        st = System.currentTimeMillis();
        log.info("Get filter kpi list:{}", (System.currentTimeMillis() - st));

        //merge group instances
        List<KpiNames> mergedList = mergeGroupKpiNames(filteredKpiListForCategory);

        Map<Integer, List<KpiNames>> map = mergedList.stream()
                .collect(Collectors.groupingBy(KpiNames::getGroupId));

        List<AvailabilityKpi> result = new ArrayList<>();
        map.forEach((groupId, list) -> {
            List<KpiAttribute> attributes = new ArrayList<>();
            Map<String, KpiAttribute> attributeMap = new HashMap<>();
            list.forEach(l -> l.getAttribute().forEach(attribute -> {
                if (attributeMap.containsKey(attribute.getName())) {
                    long count = attributeMap.get(attribute.getName()).getAnomalyCount() + attribute.getAnomalyCount();
                    attributeMap.get(attribute.getName()).setAnomalyCount(count);
                } else attributeMap.put(attribute.getName(), attribute);
            }));
            attributeMap.forEach((key, attribute) -> attributes.add(new KpiAttribute(key, false, attribute.getAnomalyCount(), attribute.getAliasName())));

            boolean isAnomaly = list.stream().anyMatch(kpiNames -> kpiNames.getAnomalyCount() > 0);
            result.add(new AvailabilityKpi(list.get(0).getGroupName(), groupId, isAnomaly,
                    list, attributes));
        });

        return result;
    }

    private List<KpiNames> mergeGroupKpiNames(List<KpiNames> kpiDetails) {
        long st = System.currentTimeMillis();
        List<KpiNames> result = new ArrayList<>();

        Map<Integer, KpiNames> map = new HashMap<>();
        try {
            if (kpiDetails != null && !kpiDetails.isEmpty()) {

                for (KpiNames kpi : kpiDetails) {
                    KpiNames currentKpi = map.get(kpi.getId());

                    if (currentKpi != null) {

                        long sumViolations = currentKpi.getAnomalyCount() + kpi.getAnomalyCount();
                        currentKpi.setAnomalyCount(sumViolations);
                        currentKpi.getAttribute().addAll(kpi.getAttribute());

                    } else {

                        result.add(kpi);
                        map.put(kpi.getId(), kpi);

                    }
                }
            }
        } catch (Exception e) {
            log.error("Error occurred while merging violation counts for group attributes ", e);
        } finally {
            log.debug("Time taken for merge group kpis is {} ms.", System.currentTimeMillis() - st);
        }
        return result;
    }

}
