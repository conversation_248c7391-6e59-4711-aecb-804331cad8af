package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.beans.ComponentInstanceBean;
import com.appnomic.appsone.api.beans.tfp.TFPTopNEvents;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.opensearch.AnomalySearchRepo;
import com.appnomic.appsone.api.dao.redis.*;
import com.appnomic.appsone.api.pojo.KpiAnomalyData;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.service.ForensicServices;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.api.util.StringUtils;
import com.heal.configuration.pojos.*;
import com.heal.configuration.pojos.opensearch.Anomalies;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

import static com.appnomic.appsone.api.common.LoggerTags.TAG_INVALID_FROM_TO_TIME;

@Slf4j
public class TFPTopNEventsBL implements BusinessLogic<Integer, UtilityBean<Integer>, List<TFPTopNEvents>> {
    private static final String EVENT_HIGH_THRESHOLD_IDENTIFIER = ConfProperties.getString(Constants.HIGH_THRESHOLD_IDENTIFIER_IDENTIFIER, Constants.HIGH_THRESHOLD_IDENTIFIER_IDENTIFIER_DEFAULT);
    private static final String EVENT_LOW_THRESHOLD_IDENTIFIER = ConfProperties.getString(Constants.LOW_THRESHOLD_IDENTIFIER_IDENTIFIER, Constants.LOW_THRESHOLD_IDENTIFIER_IDENTIFIER_DEFAULT);
    private static final int SHOW_INFO_TXN = ConfProperties.getInt(Constants.RECENT_TXN_SHOW_INFO_CATEGORIES, Constants.RECENT_TXN_SHOW_INFO_CATEGORIES_DEFAULT);

    @Override
    public UtilityBean<Integer> clientValidation(RequestObject requestObject) throws ClientException {
        log.trace("Inside Client validation");

        if (requestObject == null) {
            log.error("TFP Performance request object is received as null");
            throw new ClientException("TFP Performance request object is received as null");
        }
        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION_HEADER);
        String identifier = requestObject.getParams().get(Constants.REQUEST_PARAM_IDENTIFIER);
        String serviceIdStr = requestObject.getParams().get(Constants.REQUEST_PARAM_SERVICE_ID);
        String fromTimeString = requestObject.getQueryParams().get(Constants.REQUEST_PARAM_FROM_TIME)[0];
        String toTimeString = requestObject.getQueryParams().get(Constants.REQUEST_PARAM_TO_TIME)[0];
        String topNString = requestObject.getQueryParams().get("n")[0];
        int serviceId;
        Long fromTime;
        Long toTime;
        int topN;

        if (StringUtils.isEmpty(authKey)) {
            log.error(Constants.MESSAGE_EMPTY_AUTH_TOKEN);
            throw new ClientException(Constants.MESSAGE_EMPTY_AUTH_TOKEN);
        }

        if (StringUtils.isEmpty(identifier)) {
            log.error(Constants.MESSAGE_EMPTY_ACCOUNT);
            throw new ClientException(Constants.MESSAGE_EMPTY_ACCOUNT);
        }

        if (serviceIdStr == null || serviceIdStr.trim().isEmpty()) {
            log.error("Service id should not be empty or null.");
            throw new ClientException(UIMessages.SERVICE_EMPTY_ERROR);
        } else {
            try {
                serviceId = Integer.parseInt(serviceIdStr);
            } catch (NumberFormatException e) {
                log.error("Error occurred while converting the service id {}. Reason: {}", serviceIdStr, e.getMessage());
                throw new ClientException(UIMessages.INVALID_SERVICE);
            }
        }

        try {
            fromTime = (fromTimeString == null) ? null : Long.parseLong(fromTimeString);
        } catch (NumberFormatException e) {
            log.error("Error occurred while converting the fromTime [{}]. Reason: {}", fromTimeString, e.getMessage());
            throw new ClientException(Constants.INVALID_FROM_TIME);
        }

        try {
            toTime = (toTimeString == null) ? null : Long.parseLong(toTimeString);
        } catch (NumberFormatException e) {
            log.error("Error occurred while converting the toTime [{}]. Reason: {}", toTimeString, e.getMessage());
            throw new ClientException(Constants.INVALID_TO_TIME);
        }

        if ((fromTime == null) || (toTime == null) || (fromTime <= 0) || (toTime <= 0) || (fromTime > toTime)) {
            log.error(TAG_INVALID_FROM_TO_TIME);
            throw new ClientException(TAG_INVALID_FROM_TO_TIME);
        }

        try {
            topN = (topNString == null) ? 0 : Integer.parseInt(topNString);
        } catch (NumberFormatException e) {
            log.error("Error occurred while converting the topNString [{}]. Reason: {}", topNString, e.getMessage());
            throw new ClientException("Invalid param :topN");
        }

        return UtilityBean.<Integer>builder()
                .authToken(authKey)
                .accountIdString(identifier)
                .serviceId(serviceId)
                .fromTime(fromTime)
                .toTime(toTime)
                .requestPayloadObject(topN)
                .build();
    }

    @Override
    public UtilityBean<Integer> serverValidation(UtilityBean<Integer> utilityBean) throws ServerException {
        log.debug("Inside Server validation");
        ServiceRepo serviceRepo = new ServiceRepo();
        AccountRepo accountRepo = new AccountRepo();

        Account account = accountRepo.getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            log.error("Invalid account identifier: {}.", utilityBean.getAccountIdString());
            throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
        }
        utilityBean.setAccount(account);

        BasicEntity basicEntity = serviceRepo.getAllServices(account.getIdentifier()).stream().filter(s -> s.getId() == utilityBean.getServiceId()).findAny().orElse(null);
        if (basicEntity == null) {
            log.error("Invalid service id: {}", utilityBean.getServiceId());
            throw new ServerException(UIMessages.ERROR_INVALID_SERVICE_ID);
        }

        Service service = HealUICache.INSTANCE.getServiceDetails(account.getIdentifier(), basicEntity.getIdentifier());
        if (service == null) {
            log.error("Invalid service id: {}, identifier:{}", utilityBean.getServiceId(), basicEntity.getIdentifier());
            throw new ServerException(UIMessages.ERROR_INVALID_SERVICE_ID);
        }
        utilityBean.setService(service);
        return utilityBean;
    }

    @Override
    public List<TFPTopNEvents> processData(UtilityBean<Integer> configData) throws DataProcessingException {
        log.debug("INSIDE PROCESS DATA");

        List<Anomalies> anomalies = getTopNEvents(configData);
        return anomalies.stream()
                .map(anomaly -> {

                    CompInstKpiEntity kpi = new KpiRepo().getKpiDetailByAccInstKpiId(configData.getAccount().getIdentifier(), anomaly.getInstanceId(), Math.toIntExact(anomaly.getKpiId()));
                    if (kpi == null) {
                        return null;
                    }

                    Map<String, Double> thresholdDetails = anomaly.getThresholds();
                    int instanceId = -1;
                    ComponentInstanceBean componentInstanceBean = null;

                    if (anomaly.getTransactionId() != null) {

                        Transaction transactionDetails = new TransactionRepo().getTransactionDetails(configData.getAccount().getIdentifier(), anomaly.getTransactionId());
                        if (null != transactionDetails) {
                            instanceId = transactionDetails.getId();
                            componentInstanceBean = ComponentInstanceBean.builder()
                                    .id(transactionDetails.getId())
                                    .name(transactionDetails.getName())
                                    .identifier(transactionDetails.getIdentifier())
                                    .build();
                        }
                    } else {
                        CompInstClusterDetails compInstClusterDetails = new InstanceRepo().getInstanceDetailsWithInstIdentifier(configData.getAccount().getIdentifier(), anomaly.getInstanceId());
                        if (null != compInstClusterDetails) {
                            instanceId = compInstClusterDetails.getId();
                            componentInstanceBean = ComponentInstanceBean.builder()
                                    .id(compInstClusterDetails.getId())
                                    .name(compInstClusterDetails.getName())
                                    .identifier(compInstClusterDetails.getIdentifier())
                                    .isCluster(compInstClusterDetails.isCluster() ? 1 : 0)
                                    .build();
                        }
                    }

                    long anomalyTime = anomaly.getAnomalyTime();

                    Category categoryDetails = new CategoryRepo().getCategoryDetails(configData.getAccount().getIdentifier(), anomaly.getCategoryId());

                    TFPTopNEvents topNEvents = TFPTopNEvents.builder()
                            .metricId(kpi.getId())
                            .metric(kpi.getName())
                            .instanceId(instanceId)
                            .instanceName(componentInstanceBean != null ? componentInstanceBean.getName() : "")
                            .time(anomalyTime)
                            .value(anomaly.getValue())
                            .categoryId((categoryDetails != null) ? categoryDetails.getId() : -1)
                            .upperThreshold(thresholdDetails.getOrDefault(EVENT_HIGH_THRESHOLD_IDENTIFIER, null))
                            .lowerThreshold(thresholdDetails.getOrDefault(EVENT_LOW_THRESHOLD_IDENTIFIER, null))
                            .operation(anomaly.getOperationType())
                            .unit(kpi.getUnit())
                            .isForensics(false)
                            .forensicTime(0)
                            .build();

                    if (componentInstanceBean != null && categoryDetails != null) {
                        checkForensicsEnabled(configData.getAccount().getIdentifier(),
                                componentInstanceBean, anomalyTime, (anomalyTime + 60000L), anomaly, topNEvents, categoryDetails.getIdentifier());
                    }

                    return topNEvents;

                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private List<Anomalies> getTopNEvents(UtilityBean<Integer> configData) {

        AnomalySearchRepo anomalySearchRepo = new AnomalySearchRepo();
        if (SHOW_INFO_TXN == 0) {
            return anomalySearchRepo.getAnomalyDetailsSearchByAnomalyTime(configData.getAccount().getIdentifier(),
                    configData.getService().getIdentifier(), configData.getFromTime(),
                    configData.getToTime(), configData.getRequestPayloadObject(), "0");
        } else {
            return anomalySearchRepo.getAnomalyDetailsSearchByAnomalyTime(configData.getAccount().getIdentifier(),
                    configData.getService().getIdentifier(), configData.getFromTime(),
                    configData.getToTime(), configData.getRequestPayloadObject(), "1");
        }

    }

    void checkForensicsEnabled(String accId, ComponentInstanceBean componentInstanceBean, long fromTime,
                               long toTime, Anomalies anomalies, TFPTopNEvents topNEvents, String categoryIdentifier) {
        List<KpiAnomalyData> violationData;

        KpiAnomalyData violationRawData = new KpiAnomalyData().setTime(anomalies.getAnomalyTime());

        if (componentInstanceBean.getIsCluster() == 1) {
            violationData = Collections.singletonList(violationRawData);
        } else {
            violationData = new ForensicServices().getForensicCollectedTimeData(accId, componentInstanceBean.getIdentifier(),
                    categoryIdentifier, null, null,
                    fromTime, toTime, Collections.singletonList(violationRawData));
        }

        violationData.sort(Comparator.comparing(KpiAnomalyData::getTime).reversed());

        if (!violationData.isEmpty()) {
            topNEvents.setForensics(violationData.get(0).isForensicAvailable());
            topNEvents.setForensicTime((null == violationData.get(0).getForensicTime()) ? 0 : violationData.get(0).getForensicTime());
        }
    }
}
