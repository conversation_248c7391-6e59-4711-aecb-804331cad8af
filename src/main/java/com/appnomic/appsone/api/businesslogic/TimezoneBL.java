package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.beans.TimezoneDetail;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.cache.MasterCache;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.TimezonePojo;
import com.appnomic.appsone.api.pojo.request.UtilityBean;

import java.util.ArrayList;
import java.util.List;

public class TimezoneBL implements BusinessLogic<Object, Object, List<TimezonePojo>>{
    @Override
    public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
        return null;
    }

    @Override
    public Object serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
        return null;
    }

    @Override
    public List<TimezonePojo> processData(Object configData) throws DataProcessingException {
        //get all timezone
        List<TimezoneDetail> timezoneDetails = HealUICache.INSTANCE.getTimezones();
        List<TimezonePojo> timezonePojo = new ArrayList<>();
        for (TimezoneDetail td: timezoneDetails) {
            TimezonePojo pojo = TimezonePojo.builder()
                    .id(td.getId())
                    .timeZoneName(td.getTimeZoneName())
                    .offsetName(td.getOffsetName())
                    .timeOffset(td.getTimeOffset())
                    .abbreviation(td.getAbbreviation())
                    .build();
            timezonePojo.add(pojo);
        }
        return timezonePojo;
    }
}
