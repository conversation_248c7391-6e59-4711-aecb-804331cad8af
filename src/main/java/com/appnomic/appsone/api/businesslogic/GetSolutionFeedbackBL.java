package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.beans.SolutionRecommendationFeedbackBean;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.opensearch.SignalSearchRepo;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.SolutionCommentFeedbackPojo;
import com.appnomic.appsone.api.pojo.SolutionIsUsefulFeedbackPojo;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.service.mysql.SolutionRecommendationDataService;
import com.appnomic.appsone.api.util.StringUtils;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.heal.configuration.pojos.opensearch.SignalDetails;
import lombok.extern.slf4j.Slf4j;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class GetSolutionFeedbackBL implements BusinessLogic<String, UtilityBean<String>, SolutionCommentFeedbackPojo> {


    @Override
    public UtilityBean<String> clientValidation(RequestObject requestObject) throws ClientException {

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION_HEADER.toLowerCase());
        String identifier = requestObject.getParams().get(Constants.REQUEST_PARAM_IDENTIFIER);
        String signalIdStr = requestObject.getParams().get(Constants.REQUEST_PARAM_SIGNAL_ID.toLowerCase());

        if (StringUtils.isEmpty(authKey)) {
            log.error(Constants.MESSAGE_EMPTY_AUTH_TOKEN);
            throw new ClientException(Constants.MESSAGE_EMPTY_AUTH_TOKEN);
        }

        if (StringUtils.isEmpty(identifier)) {
            log.error(Constants.MESSAGE_EMPTY_ACCOUNT);
            throw new ClientException(Constants.MESSAGE_EMPTY_ACCOUNT);
        }

        if (signalIdStr == null || signalIdStr.trim().isEmpty()) {
            log.error("Signal id should not be empty or null.");
            throw new ClientException("Invalid signal Id.");
        }

        return UtilityBean.<String>builder().authToken(authKey)
                .accountIdString(identifier)
                .requestPayloadObject(signalIdStr)
                .build();
    }

    @Override
    public UtilityBean<String> serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        AccountRepo accountRepo = new AccountRepo();
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            throw new ServerException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }
        utilityBean.setUserId(userId);

        com.heal.configuration.pojos.Account account = accountRepo.getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
        }
        utilityBean.setAccount(account);

        //Validate signal id
        SignalSearchRepo signalSearchRepo = new SignalSearchRepo();
        SignalDetails signal = signalSearchRepo.getSignalsBySignalId(utilityBean.getRequestPayloadObject(), utilityBean.getAccount().getIdentifier());
        if (signal == null) {
            log.info("Unable to fetch details for signal with id: {}", utilityBean.getRequestPayloadObject());
            throw new ServerException(MessageFormat.format("Unable to fetch details for signal with id: {0}", utilityBean.getRequestPayloadObject()));
        }

        return utilityBean;
    }

    @Override
    public SolutionCommentFeedbackPojo processData(UtilityBean<String> data) throws DataProcessingException {

        SolutionRecommendationDataService recommendationDataService = new SolutionRecommendationDataService();

        SolutionRecommendationFeedbackBean feedback = recommendationDataService.getUserFeedback(data.getRequestPayloadObject(), data.getUserId());
        if (feedback == null) {
            log.info("No feedback found for the signal id: {}", data.getRequestPayloadObject());
            return SolutionCommentFeedbackPojo.builder()
                    .feedbackUserId(null)
                    .signalId(null)
                    .comments(null)
                    .feedback(new ArrayList<>())
                    .build();
        }

        List<SolutionIsUsefulFeedbackPojo> isUseful = recommendationDataService.getFeedbackIsUseful(feedback.getId());

        return SolutionCommentFeedbackPojo.builder()
                .feedbackUserId(feedback.getFeedbackUserId())
                .signalId(feedback.getSignalId())
                .comments(feedback.getComments())
                .feedback(isUseful)
                .build();
    }
}
