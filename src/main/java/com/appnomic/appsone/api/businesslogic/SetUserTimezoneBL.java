package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.beans.*;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.mysql.entity.UserDetailsBean;
import com.appnomic.appsone.api.dao.redis.MasterRepo;
import com.appnomic.appsone.api.manager.MySQLConnectionManager;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.TagMappingDetails;
import com.appnomic.appsone.api.pojo.UserTimezonePojo;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.service.mysql.TimezoneDataService;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.api.util.DateTimeUtil;
import com.appnomic.appsone.api.util.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.TagDetails;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.text.MessageFormat;

public class SetUserTimezoneBL implements BusinessLogic<UserTimezoneRequestData, UserTimezoneRequestData, String> {
    private static final Logger LOGGER = LoggerFactory.getLogger(SetUserTimezoneBL.class);
    private static String timezoneKey = ConfProperties.getString(Constants.TIMEZONE_TAG_DETAILS_IDETIFIER,
            Constants.TIMEZONE_TAG_DETAILS_IDETIFIER_DEFAULT);

    @Override
    public UtilityBean<UserTimezoneRequestData> clientValidation(RequestObject requestObject) throws ClientException {
        LOGGER.debug("Inside Client validation");

        String userIdentifier = requestObject.getParams().get(Constants.REQUEST_PARAM_USER_ID);
        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION_HEADER.toLowerCase());

        if (StringUtils.isEmpty(userIdentifier)) {
            throw new ClientException(MessageFormat.format(UIMessages.ERROR_INVALID_INPUT_PARAM,
                    Constants.REQUEST_PARAM_USER_ID, userIdentifier));
        }

        UserTimezonePojo tzResponse;
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            tzResponse = objectMapper.readValue(requestObject.getBody(), new TypeReference<UserTimezonePojo>() {
            });
        } catch (IOException e) {
            throw new ClientException(e, Constants.REQUEST_BODY_INVALID_ERROR_MESSAGE);
        }

        tzResponse.validate();
        UserTimezoneRequestData requestData = new UserTimezoneRequestData();
        requestData.setUserTimezonePojo(tzResponse);
        requestData.setUserIdentifier(userIdentifier);

        return UtilityBean.<UserTimezoneRequestData>builder()
                .authToken(authKey)
                .requestPayloadObject(requestData).build();
    }

    @Override
    public UserTimezoneRequestData serverValidation(UtilityBean<UserTimezoneRequestData> utilityBean) throws ServerException {
        LOGGER.debug("Inside Server validation");

        UserTimezoneRequestData userTimezoneRequestData = new UserTimezoneRequestData();
        UserAttributeBean userAttributeBean = new TimezoneDataService().getUserAttributesForUserIdentifier(utilityBean.getRequestPayloadObject().getUserIdentifier());
        if (userAttributeBean == null) {
            LOGGER.error(Constants.MESSAGE_INVALID_PARAMETERS, utilityBean.getRequestPayloadObject().getUserIdentifier());
            throw new ServerException(MessageFormat.format(UIMessages.ERROR_INVALID_INPUT_PARAM,
                    Constants.REQUEST_PARAM_USER_ID, utilityBean.getRequestPayloadObject().getUserIdentifier()));
        } else if (userAttributeBean.getStatus() == Constants.STATUS_ACTIVE) {
            userTimezoneRequestData.setUserTimezonePojo(utilityBean.getRequestPayloadObject().getUserTimezonePojo());
            userTimezoneRequestData.setUserAttributeBean(userAttributeBean);
        } else {
            LOGGER.error(UIMessages.USER_INACTIVE, utilityBean.getRequestPayloadObject().getUserIdentifier());
            throw new ServerException(MessageFormat.format(UIMessages.USER_ERROR_INACTIVE,
                    Constants.REQUEST_PARAM_USER_ID, utilityBean.getRequestPayloadObject().getUserIdentifier()));
        }

        UserDetailsBean userDetailsBean = getUserDetails(utilityBean.getAuthToken());

        if (utilityBean.getRequestPayloadObject().getUserTimezonePojo().getTimezoneId() > 0) {
            TimezoneDetail timezoneDetail = new TimezoneDataService().getTimezonesById(
                    String.valueOf(utilityBean.getRequestPayloadObject().getUserTimezonePojo().getTimezoneId()));
            if (null == timezoneDetail) {
                throw new ServerException(MessageFormat.format(UIMessages.INVALID_TIMEZONE_ID, utilityBean.getRequestPayloadObject().getUserTimezonePojo().getTimezoneId()));
            }
            userTimezoneRequestData.setTimezoneDetail(timezoneDetail);
        }

        userTimezoneRequestData.setUserDetailsBean(userDetailsBean);
        return userTimezoneRequestData;
    }

    @Override
    public String processData(UserTimezoneRequestData configData) throws DataProcessingException {
        TagDetails tagDetail = new MasterRepo().getTagDetails().stream()
                .filter(t -> t.getName().equalsIgnoreCase(timezoneKey))
                .findAny()
                .orElse(null);
        if (null == tagDetail) {
            throw new DataProcessingException(MessageFormat.format(UIMessages.TAG_DOES_NOT_EXIST_MESSAGE, timezoneKey));
        }

        int tagMappingId = new TimezoneDataService().getUserTagMappingId(Constants.USER_ATTRIBUTES_TABLE_NAME_MYSQL,
                configData.getUserAttributeBean().getId(), tagDetail.getId());

        DBI dbi = MySQLConnectionManager.getInstance().getHandle();
        return dbi.inTransaction((conn, status) -> {
            try {
                /*`isTimezoneMychoice` & `isNotificationsTimezoneMychoice` is saved inverted in DB as per the UI-English
                 * if checkbox is checked then `isTimezoneMychoice`=0 & `isNotificationsTimezoneMychoice`=0
                 * if checkbox is un-checked then `isTimezoneMychoice`=1 & `isNotificationsTimezoneMychoice`=1
                 * */
                int res = updateUserTimezoneChoice(configData.getUserTimezonePojo().getIsTimezoneMychoice() ^ 1,
                        configData.getUserTimezonePojo().getIsNotificationsTimezoneMychoice() ^ 1,
                        configData.getUserAttributeBean().getUsername(), configData.getUserDetailsBean().getUserIdentifier(), conn);
                if (tagMappingId != 0 && configData.getUserTimezonePojo().getTimezoneId() == 0) {
                    //Delete from tag_mapping
                    res = new TimezoneDataService().deleteUserTagMapping(tagMappingId, conn);
                } else if (tagMappingId != 0 && configData.getUserTimezonePojo().getTimezoneId() != 0) {
                    //Update tag_mapping
                    String updateTime = DateTimeUtil.getTimeInGMT(System.currentTimeMillis());
                    res = new TimezoneDataService().updateUserTagMapping(configData.getTimezoneDetail().getId(), updateTime,
                            tagMappingId, configData.getUserDetailsBean().getCreatedBy(), conn);
                } else if (tagMappingId == 0 && configData.getUserTimezonePojo().getTimezoneId() != 0) {
                    TagMappingDetails tagMappingDetails = populateTagMapping(configData, tagDetail, configData.getTimezoneDetail());
                    //Insert into tag_mapping
                    res = new TimezoneDataService().addUserTagMapping(tagMappingDetails, conn);
                }
                if (res > 0) {
                    return configData.getUserDetailsBean().getUserIdentifier();
                } else {
                    throw new DataProcessingException(MessageFormat.format("Failed to update timezone for user id: {0}", configData.getUserDetailsBean().getId()));
                }
            } catch (DataProcessingException e) {
                throw new DataProcessingException(e, "Failed to update user timezone");
            }
        });
    }

    private int updateUserTimezoneChoice(Integer isTimezoneMychoice, Integer isNotificationsTimezoneMychoice, String username, String updatingUserIdentifier, Handle handle) throws DataProcessingException {
        String timeInGMT = DateTimeUtil.getTimeInGMT(System.currentTimeMillis());
        int isChoiceSet = new TimezoneDataService().updateUserTimezoneChoice(isTimezoneMychoice, isNotificationsTimezoneMychoice, username, updatingUserIdentifier, timeInGMT, handle);
        if (isChoiceSet == 0) {
            throw new DataProcessingException("Failed to update table user_attributes: is_timezone_mychoice");
        }
        return isChoiceSet;
    }

    private TagMappingDetails populateTagMapping(UserTimezoneRequestData configData, TagDetails tagDetailsBean, TimezoneDetail timezoneDetail) {
        TagMappingDetails tagMappingDetails = new TagMappingDetails();
        tagMappingDetails.setAccountId(Constants.DEFAULT_ACCOUNT_ID);
        String timeInGMT = DateTimeUtil.getTimeInGMT(System.currentTimeMillis());
        tagMappingDetails.setCreatedTime(timeInGMT);
        tagMappingDetails.setUpdatedTime(timeInGMT);
        tagMappingDetails.setObjectId(configData.getUserAttributeBean().getId());
        tagMappingDetails.setObjectRefTable(Constants.USER_ATTRIBUTES_TABLE_NAME_MYSQL);
        tagMappingDetails.setTagValue(String.valueOf(timezoneDetail.getTimeOffset()));
        tagMappingDetails.setTagId(tagDetailsBean.getId());
        tagMappingDetails.setTagKey(String.valueOf(configData.getUserTimezonePojo().getTimezoneId()));
        tagMappingDetails.setUserDetailsId(configData.getUserDetailsBean().getCreatedBy());
        return tagMappingDetails;
    }

    protected UserDetailsBean getUserDetails(String authToken) throws ServerException {
        return CommonUtils.getUserDetails(authToken);
    }
}
