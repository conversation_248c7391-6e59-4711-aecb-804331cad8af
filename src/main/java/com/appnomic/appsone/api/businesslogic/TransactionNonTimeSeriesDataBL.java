package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.opensearch.CollatedTransactionsSearchRepo;
import com.appnomic.appsone.api.dao.opensearch.RawTransactionSearchRepo;
import com.appnomic.appsone.api.dao.redis.*;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.TabularResultsTypePojo;
import com.appnomic.appsone.api.pojo.request.TransactionNonTimeSeriesDataRequest;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.*;
import com.appnomic.appsone.opeasearchquery.results.TabularResults;
import com.heal.configuration.enums.KPIAttributes;
import com.heal.configuration.pojos.*;
import lombok.extern.slf4j.Slf4j;

import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class TransactionNonTimeSeriesDataBL implements BusinessLogic<TransactionNonTimeSeriesDataRequest, UtilityBean<TransactionNonTimeSeriesDataRequest>, Map<String, String>> {

    @Override
    public UtilityBean<TransactionNonTimeSeriesDataRequest> clientValidation(RequestObject request) throws ClientException {

        if (request == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);

        if (authToken == null || authToken.trim().isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        TransactionNonTimeSeriesDataRequest nts = new TransactionNonTimeSeriesDataRequest(request);

        if (!nts.validateParameters(nts)) {
            log.error("Client validation failed for GET KPI DATA request.");
            throw new ClientException(UIMessages.CLIENT_VALIDATION_FAILED);
        }

        return UtilityBean.<TransactionNonTimeSeriesDataRequest>builder()
                .requestPayloadObject(nts)
                .fromTime(Long.valueOf(nts.getFromTimeString()))
                .toTime(Long.valueOf(nts.getToTimeString()))
                .accountIdString(nts.getAccountIdString())
                .serviceIdString(nts.getServiceIdString())
                .componentInstanceIdString(nts.getInstanceIdString())
                .kpiNameString(nts.getKpiNameDetails().name())
                .responseType(nts.getTransactionResponseTypeString())
                .authToken(authToken)
                .build();
    }

    @Override
    public UtilityBean<TransactionNonTimeSeriesDataRequest> serverValidation(UtilityBean<TransactionNonTimeSeriesDataRequest> utilityBean) throws ServerException {

        String accountIdentifier = utilityBean.getAccountIdString();
        int serviceId = Integer.parseInt(utilityBean.getServiceIdString());
        int instanceId = Integer.parseInt(utilityBean.getComponentInstanceIdString());
        String kpiName = utilityBean.getKpiNameString();

        AccountRepo accountsRepo = new AccountRepo();
        ServiceRepo serviceRepo = new ServiceRepo();
        InstanceRepo instanceRepo = new InstanceRepo();
        ComponentRepo componentRepo = new ComponentRepo();

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token.");
            throw new ServerException("Error while extracting user details from authorization token.");
        }

        Account account = accountsRepo.getAccount(accountIdentifier);
        if (account == null) {
            log.error("Account identifier is invalid.");
            throw new ServerException("Account identifier is invalid.");
        }
        utilityBean.setAccount(account);

        TimeRangeDetailsDao timeRangeDetailsDao = new TimeRangeDetailsDao();
        List<TimeRangeDetails> timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(accountIdentifier);
        if (timeRangeDetailsList == null) {
            log.error("Exception occurred while fetching time range details for account {}", accountIdentifier);
            throw new ServerException("Exception occurred while fetching time range details for account " + accountIdentifier);
        }

        if (timeRangeDetailsList.isEmpty()) {
            log.trace("No time range details found for account {}. Searching for Global account.", accountIdentifier);
            timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }
        if (timeRangeDetailsList == null) {
            log.error("Exception occurred while fetching time range details for account {}", Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
            throw new ServerException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }

        if (timeRangeDetailsList.isEmpty()) {
            log.error("No time range details found");
            throw new ServerException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }
        TimeRangeDetails timeRangeDetails = ValidationUtils.getRollupPojo(utilityBean.getFromTime(), utilityBean.getToTime(), timeRangeDetailsList);
        if (timeRangeDetails == null) {
            log.error("Time range details are invalid.");
            throw new ServerException("Time range details are invalid.");
        }
        utilityBean.setTimeRangeDetails(timeRangeDetails);
        utilityBean.setTimeRangeDetailsList(timeRangeDetailsList);

        BasicEntity serviceDetails = serviceRepo.getBasicServiceDetailsWithServiceId(accountIdentifier, serviceId);
        if (serviceDetails == null) {
            String error = "Service id is invalid";
            log.error(error);
            throw new ServerException(error);
        }
        utilityBean.setServiceIdString(serviceDetails.getIdentifier());

        CompInstClusterDetails instanceDetails = new CompInstClusterDetails();
        if (utilityBean.getRequestPayloadObject().getRequestTypeString().trim().equalsIgnoreCase(Constants.COM_INSTANCE_TYPE)) {
            instanceDetails = instanceRepo.getInstancesByAccount(accountIdentifier)
                    .parallelStream().filter(c -> c.getId() == instanceId).findAny().orElse(null);
            if (instanceDetails == null) {
                log.error("Instance id is invalid.");
                throw new ServerException("Instance id is invalid.");
            }
            utilityBean.setComponentInstanceIdString(instanceDetails.getIdentifier());
        }

        BasicKpiEntity basicKpiEntity = componentRepo.getComponentKpis(Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT, Constants.TRANSACTION_IDENTIFIER_DEFAULT)
                .parallelStream().filter(c -> c.getStatus() == 1 && c.getIdentifier().equalsIgnoreCase(kpiName)).findAny().orElse(null);
        if (basicKpiEntity == null) {
            log.error("Invalid Kpi Name {}.", kpiName);
            throw new ServerException("Kpi Name id is invalid.");
        }
        utilityBean.setKpiNameString(basicKpiEntity.getIdentifier());
        utilityBean.setOperation(basicKpiEntity.getRollupOperation());

        List<BasicTransactionEntity> transactions = serviceRepo.getTransactionsByServiceIdentifier(accountIdentifier, serviceDetails.getIdentifier());
        if (!utilityBean.getRequestPayloadObject().getTagIdString().equals("0")) {
            transactions.parallelStream()
                    .filter(s -> s.getStatus() == 1)
                    .map(BasicTransactionEntity::getTransactionGroups)
                    .filter(transactionGroups -> (transactionGroups.parallelStream().anyMatch(it -> it.getTransactionGroupId() == Integer.parseInt(utilityBean.getRequestPayloadObject().getTagIdString()))))
                    .flatMap(Collection::stream)
                    .filter(Objects::nonNull)
                    .filter(t -> t.getTransactionGroupName() != null)
                    .findAny().ifPresent(group -> utilityBean.getRequestPayloadObject().setGroupName(group.getTransactionGroupName()));
        }

        //GET Agent Data
        List<String> agentList;
        if (utilityBean.getRequestPayloadObject().getRequestTypeString().trim().equalsIgnoreCase(Constants.SERVICES_CONTROLLER_TYPE)) {
            return utilityBean;
        } else if (utilityBean.getRequestPayloadObject().getRequestTypeString().trim().equalsIgnoreCase(Constants.COM_INSTANCE_TYPE)) {
            agentList = instanceDetails.getAgentIds();
            if (agentList == null || agentList.isEmpty()) {
                log.error("Unable to fetch agents for given service/comp instance: {}", utilityBean.getServiceIdString());
                return utilityBean;
            }
            utilityBean.getRequestPayloadObject().setAgentList(agentList);
        } else {
            log.error("Invalid request type provided: {}", utilityBean.getRequestPayloadObject().getRequestTypeString());
            throw new ServerException(String.format("Invalid request type provided: %s", utilityBean.getRequestPayloadObject().getRequestTypeString()));
        }

        return utilityBean;
    }

    @Override
    public Map<String, String> processData(UtilityBean<TransactionNonTimeSeriesDataRequest> utilityBean) throws DataProcessingException {
        log.trace("{} processData(), with params: {}", Constants.INVOKED_METHOD, utilityBean);

        if (DateTimeUtil.inRange(utilityBean.getFromTime())) {
            return processRawTxnData(utilityBean);
        }

        /*
        tagId and type
        if type = services, call serviceLevelTxnCollation where groupBy on service. put service id in where clause. pass transaction groups if exist in <IN> clause.
        if type = instances, call agentLevelTxnCollation where groupBy on service. put agent uid and service id in where clause. pass transaction group if exist in <IN> clause
         */
        long start = System.currentTimeMillis();

        Map<String, String> output = new LinkedHashMap<>();
        CollatedTransactionsSearchRepo collatedTxnRepo = new CollatedTransactionsSearchRepo();

        String requestType = utilityBean.getRequestPayloadObject().getRequestTypeString();
        long fromTime = utilityBean.getFromTime();
        long toTime = utilityBean.getToTime();
        String kpiIdentifier = utilityBean.getKpiNameString();
        String txnGroupName = utilityBean.getRequestPayloadObject().getGroupName();

        NewTimeIntervalGenerationUtility t = new NewTimeIntervalGenerationUtility();
        t.setTimezoneOffset(utilityBean.getAccountIdString());
        t.processTimeRange(fromTime, toTime);
        t.addEndTimeInOSTime();

        List<Long> times = t.getOSTimes();

        ComponentRepo componentRepo = new ComponentRepo();
        BasicKpiEntity basicKpiEntity = componentRepo.getComponentKpis(Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT, Constants.TRANSACTION_IDENTIFIER_DEFAULT)
                .parallelStream().filter(c -> c.getStatus() == 1 && c.getIdentifier().equalsIgnoreCase(KPIAttributes.TOTAL_VOLUME.name())).findAny().orElse(null);
        if (basicKpiEntity == null) {
            log.error("\"TOTAL_VOLUME\" Kpi details not found.");
            throw new DataProcessingException("\"TOTAL_VOLUME\" Kpi details not found.");
        }

        List<TabularResultsTypePojo> tabularResultsTypePojos;
        Map<String, Double> kpiValueMap = new LinkedHashMap<>();

        if (requestType.trim().equalsIgnoreCase(Constants.SERVICES_CONTROLLER_TYPE)) {
            // Cluster Level
            /* if incoming request kpi is TOTAL_VOLUME..Then make only 1 OS call for that KPI
             * If incoming request kpi is other than TOTAL_VOLUME i.e SLOW_VOLUME, TIMEOUT_VOLUME etc..Then make 2 OS calls..
             * 1 for TOTAL_VOLUME and 1 for the incoming kpi.. TOTAL_VOLUME will be used to calculate the percentage here*/


            //For TOTAL_VOLUME KPI
            tabularResultsTypePojos = collatedTxnRepo.getTransactionCollatedDataByServiceAndTxnGrpNames(utilityBean.getAccountIdString(),
                    utilityBean.getServiceIdString(), KPIAttributes.TOTAL_VOLUME.name(), basicKpiEntity.getRollupOperation(),
                    txnGroupName, utilityBean.getResponseType(), fromTime, toTime - Constants.MINUTE,
                    utilityBean.getTimeRangeDetails(), utilityBean.getTimeRangeDetailsList(), times);

            if (tabularResultsTypePojos == null || tabularResultsTypePojos.isEmpty()) {
                return output;
            }

            for (TabularResultsTypePojo tabularResultsTypePojo : tabularResultsTypePojos) {
                TabularResults results = tabularResultsTypePojo.getTabularResults();
                results.getRowResults().forEach(r -> kpiValueMap.putAll(r.getListOfRows().parallelStream()
                        .filter(c -> !c.getColumnDataType().contains("String"))
                        .collect(Collectors.toMap(c -> KPIAttributes.TOTAL_VOLUME.name(), c -> Double.parseDouble(c.getColumnValue())))));
            }


            if (!kpiIdentifier.equalsIgnoreCase(KPIAttributes.TOTAL_VOLUME.name())) {
                //OTHER KPI Coming in Request
                tabularResultsTypePojos = collatedTxnRepo.getTransactionCollatedDataByServiceAndTxnGrpNames(utilityBean.getAccountIdString(),
                        utilityBean.getServiceIdString(), kpiIdentifier, utilityBean.getOperation(),
                        txnGroupName, utilityBean.getResponseType(), fromTime, toTime - Constants.MINUTE,
                        utilityBean.getTimeRangeDetails(), utilityBean.getTimeRangeDetailsList(), times);

                if (tabularResultsTypePojos == null || tabularResultsTypePojos.isEmpty()) {
                    return output;
                }

                for (TabularResultsTypePojo tabularResultsTypePojo : tabularResultsTypePojos) {
                    TabularResults results = tabularResultsTypePojo.getTabularResults();
                    results.getRowResults().forEach(r -> kpiValueMap.putAll(r.getListOfRows().parallelStream()
                            .filter(c -> !c.getColumnDataType().contains("String"))
                            .collect(Collectors.toMap(c -> kpiIdentifier, c -> Double.parseDouble(c.getColumnValue())))));
                }
            }

        } else {
            //FOR Instance Level i.e get agent wise data
            /* if incoming request kpi is TOTAL_VOLUME..Then make only 1 OS call for that KPI
             * If incoming request kpi is other than TOTAL_VOLUME i.e SLOW_VOLUME, TIMEOUT_VOLUME etc..Then make 2 OS calls..
             * 1 for TOTAL_VOLUME and 1 for the incoming kpi.. TOTAL_VOLUME will be used to calculate the percentage here*/
            Set<String> agentList = utilityBean.getRequestPayloadObject().getAgentList().parallelStream().collect(Collectors.toSet());

            //For TOTAL_VOLUME KPI
            List<TabularResultsTypePojo> agentTabularResults = collatedTxnRepo.getAgentTransactionCollatedDataByServiceAndTxnGrpNames(utilityBean.getAccountIdString(),
                    agentList, utilityBean.getServiceIdString(), KPIAttributes.TOTAL_VOLUME.name(), basicKpiEntity.getRollupOperation(),
                    txnGroupName, utilityBean.getResponseType(), fromTime, toTime - Constants.MINUTE,
                    utilityBean.getTimeRangeDetails(), utilityBean.getTimeRangeDetailsList(), times);

            if (agentTabularResults == null || agentTabularResults.isEmpty()) {
                kpiValueMap.put(kpiIdentifier, 0D);
            } else {
                for (TabularResultsTypePojo tabularResultsTypePojo : agentTabularResults) {
                    TabularResults results = tabularResultsTypePojo.getTabularResults();
                    results.getRowResults().forEach(r -> kpiValueMap.putAll(r.getListOfRows().parallelStream()
                            .filter(c -> !c.getColumnDataType().contains("String"))
                            .collect(Collectors.toMap(c -> KPIAttributes.TOTAL_VOLUME.name(), c -> Double.parseDouble(c.getColumnValue())))));
                }
            }

            if (!kpiIdentifier.equalsIgnoreCase(KPIAttributes.TOTAL_VOLUME.name())) {
                //OTHER KPI Coming in Request
                agentTabularResults = collatedTxnRepo.getAgentTransactionCollatedDataByServiceAndTxnGrpNames(utilityBean.getAccountIdString(),
                        agentList, utilityBean.getServiceIdString(), kpiIdentifier,
                        utilityBean.getOperation(), txnGroupName, utilityBean.getResponseType(), fromTime, toTime - Constants.MINUTE,
                        utilityBean.getTimeRangeDetails(), utilityBean.getTimeRangeDetailsList(), times);

                if (agentTabularResults == null || agentTabularResults.isEmpty()) {
                    kpiValueMap.put(kpiIdentifier, 0D);
                } else {
                    for (TabularResultsTypePojo tabularResultsTypePojo : agentTabularResults) {
                        TabularResults results = tabularResultsTypePojo.getTabularResults();
                        results.getRowResults().forEach(r -> kpiValueMap.putAll(r.getListOfRows().parallelStream()
                                .filter(c -> !c.getColumnDataType().contains("String"))
                                .collect(Collectors.toMap(c -> kpiIdentifier, c -> Double.parseDouble(c.getColumnValue())))));
                    }
                }
            }
        }

        //process all the collected data
        Double txnTotalCount = kpiValueMap.getOrDefault(KPIAttributes.TOTAL_VOLUME.name(), 0D);
        Double txnKpiCount = kpiValueMap.getOrDefault(kpiIdentifier, 0D);
        Double tpm = txnKpiCount / ((toTime - fromTime) / 60000);

        if (txnTotalCount == 0D && txnKpiCount == 0D) {
            return output;
        }

        output.put("total", String.valueOf(txnKpiCount));
        output.put("percentage", new DecimalFormat("#0.00").format(Double.valueOf(((txnKpiCount / txnTotalCount) * 100))));
        output.put("transactionPerMinute", new DecimalFormat("#0.000").format(tpm));

        log.debug("Time taken to process transaction non-time series data is {} ms.", (System.currentTimeMillis() - start));
        return output;
    }

    private Map<String, String> processRawTxnData(UtilityBean<TransactionNonTimeSeriesDataRequest> utilityBean) throws DataProcessingException {

        long start = System.currentTimeMillis();

        Map<String, String> output = new LinkedHashMap<>();
        RawTransactionSearchRepo rawTransactionSearchRepo = new RawTransactionSearchRepo();

        String requestType = utilityBean.getRequestPayloadObject().getRequestTypeString();
        long fromTime = utilityBean.getFromTime();
        long toTime = utilityBean.getToTime();
        String kpiIdentifier = utilityBean.getKpiNameString();
        String txnGroupName = utilityBean.getRequestPayloadObject().getGroupName();

        ComponentRepo componentRepo = new ComponentRepo();
        BasicKpiEntity basicKpiEntity = componentRepo.getComponentKpis(Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT, Constants.TRANSACTION_IDENTIFIER_DEFAULT)
                .parallelStream().filter(c -> c.getStatus() == 1 && c.getIdentifier().equalsIgnoreCase(KPIAttributes.TOTAL_VOLUME.name())).findAny().orElse(null);
        if (basicKpiEntity == null) {
            log.error("\"TOTAL_VOLUME\" Kpi details not found.");
            throw new DataProcessingException("\"TOTAL_VOLUME\" Kpi details not found.");
        }

        TabularResults tabularResults;
        Map<String, Long> kpiCountMap = new LinkedHashMap<>();

        if (requestType.trim().equalsIgnoreCase(Constants.SERVICES_CONTROLLER_TYPE)) {
            // Cluster Level
            tabularResults = rawTransactionSearchRepo.getTransactionNtsDataClusterLevel(utilityBean.getAccount().getIdentifier(),
                    utilityBean.getServiceIdString(), txnGroupName, utilityBean.getResponseType(), fromTime, toTime);

        } else {
            //Instance Level
            Set<String> agentList = utilityBean.getRequestPayloadObject().getAgentList().parallelStream().collect(Collectors.toSet());
            tabularResults = rawTransactionSearchRepo.getTransactionNtsDataInstanceLevel(utilityBean.getAccount().getIdentifier(),
                    utilityBean.getServiceIdString(), txnGroupName, agentList, utilityBean.getResponseType(), fromTime, toTime);

        }
        if (tabularResults == null || tabularResults.getRowResults() == null || tabularResults.getRowResults().isEmpty()) {
            return output;
        }

        for (TabularResults.ResultRow resultRow : tabularResults.getRowResults()) {
            String kpiName = "";
            for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {
                if (resultRowColumn.getColumnName().equalsIgnoreCase("responseTimes.responseStatusTag")) {
                    kpiName = resultRowColumn.getColumnValue();
                }
            }
            kpiCountMap.put(kpiName, resultRow.getCountValue());
        }


        long totalVolume = kpiCountMap.values().parallelStream().mapToLong(Long::longValue).sum();

        String kpiName = kpiCountMap.keySet().parallelStream().filter(c -> KPIAttributes.getOpenSearchName(kpiIdentifier).equalsIgnoreCase(c))
                .findFirst()
                .orElse(null);
        if (StringUtils.isEmpty(kpiName)) {
            if (kpiIdentifier.equalsIgnoreCase(KPIAttributes.TOTAL_VOLUME.name())) {
                kpiName = KPIAttributes.TOTAL_VOLUME.name();
            } else {
                return output;
            }
        }

        //process all the collected data
        long currentKpiCount;
        if (kpiName.equalsIgnoreCase(KPIAttributes.TOTAL_VOLUME.name())) {
            currentKpiCount = totalVolume;
        } else {
            currentKpiCount = kpiCountMap.getOrDefault(kpiName, 0L);
        }

        Double tpm = (double) (currentKpiCount / ((toTime - fromTime) / 60000));

        if (totalVolume == 0D && currentKpiCount == 0D) {
            return output;
        }

        output.put("total", String.valueOf(currentKpiCount));
        output.put("percentage", new DecimalFormat("#0.00").format(((double) (currentKpiCount * 100) / totalVolume)));
        output.put("transactionPerMinute", new DecimalFormat("#0.000").format(tpm));

        log.debug("Time taken to process transaction non-time series data is {} ms.", (System.currentTimeMillis() - start));
        return output;

    }

}
