package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.beans.UserNotificationDetails;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.ApplicationRepo;
import com.appnomic.appsone.api.dao.redis.UserRepo;
import com.appnomic.appsone.api.pojo.ForensicNotificationConfiguration;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.StringUtils;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.stream.Collectors;

public class GetForensicNotificationConfigurations implements BusinessLogic<String, UserNotificationDetails, ForensicNotificationConfiguration> {

    private static final Logger LOGGER = LoggerFactory.getLogger(GetForensicNotificationConfigurations.class);

    @Override
    public UtilityBean<String> clientValidation(RequestObject requestObject) throws ClientException {

        if (requestObject == null) {
            LOGGER.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION_HEADER);
        if (StringUtils.isEmpty(authKey)) {
            LOGGER.error(Constants.MESSAGE_INVALID_AUTH_TOKEN);
            throw new ClientException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(identifier)) {
            LOGGER.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        String applicableUserId = requestObject.getParams().get(Constants.REQUEST_PARAM_USER_ID);
        if (StringUtils.isEmpty(applicableUserId)) {
            LOGGER.error("userId should not be NULL or empty.");
            throw new ClientException("userId should not be NULL or empty.");
        }

        return UtilityBean.<String>builder()
                .accountIdString(identifier)
                .authToken(authKey)
                .requestPayloadObject(applicableUserId)
                .build();
    }

    @Override
    public UserNotificationDetails serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        AccountRepo accountRepo = new AccountRepo();
        UserRepo userRepo = new UserRepo();
        ApplicationRepo applicationRepo = new ApplicationRepo();

        try {
            String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
            if (userId == null) {
                LOGGER.error(Constants.MESSAGE_INVALID_AUTH_TOKEN);
                throw new ServerException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
            }

            Account account = accountRepo.getAccount(utilityBean.getAccountIdString());
            if (account == null) {
                throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID + ":" + utilityBean.getAccountIdString());
            }

            User user = userRepo.getUser(userId);
            if (user == null) {
                throw new ServerException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
            }

            List<BasicEntity> forensicPreferences = applicationRepo.getApplicationForensicsByUserId(account.getIdentifier(), userId);

            return UserNotificationDetails.builder()
                    .accountId(account.getId())
                    .forensicEnabled(user.getForensicEnabled())
                    .forensicNotificationSuppressionInterval(user.getForensicSuppression())
                    .applicationIds(forensicPreferences.stream().map(BasicEntity::getId).collect(Collectors.toList()))
                    .build();
        } catch (Exception e) {
            throw new ServerException(e, "Invalid request details.");
        }
    }

    @Override
    public ForensicNotificationConfiguration processData(UserNotificationDetails userNotificationDetails) throws DataProcessingException {

        return ForensicNotificationConfiguration.builder()
                .applicableUserId(userNotificationDetails.getApplicableUserId())
                .emailNotification(userNotificationDetails.getForensicEnabled())
                .suppressionDuration(userNotificationDetails.getForensicNotificationSuppressionInterval())
                .applicationIds(userNotificationDetails.getApplicationIds())
                .build();
    }
}
