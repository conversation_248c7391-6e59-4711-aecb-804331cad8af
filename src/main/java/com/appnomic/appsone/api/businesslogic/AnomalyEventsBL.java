package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.beans.ServiceDetails;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.SignalType;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.opensearch.AnomalySearchRepo;
import com.appnomic.appsone.api.dao.opensearch.SignalSearchRepo;
import com.appnomic.appsone.api.dao.redis.*;
import com.appnomic.appsone.api.pojo.AnomalyEventDetail;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.AnomalyIncidentRequest;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.api.util.StringUtils;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.google.gson.internal.LinkedHashTreeMap;
import com.heal.configuration.pojos.*;
import com.heal.configuration.pojos.opensearch.Anomalies;
import com.heal.configuration.pojos.opensearch.SignalDetails;
import lombok.extern.slf4j.Slf4j;

import java.time.Clock;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class AnomalyEventsBL implements BusinessLogic<AnomalyIncidentRequest, UtilityBean<AnomalyIncidentRequest>, Map<String, Map<Long, List<AnomalyEventDetail>>>> {
    private static final String COUNT_IDENTIFIER = ConfProperties.getString(Constants.KPI_UNIT_COUNT, Constants.KPI_UNIT_COUNT_DEFAULT);
    private static final String TRANSACTION_KEYWORD_IDENTIFIER = ConfProperties.getString(Constants.TRANSACTION_IDENTIFIER, Constants.TRANSACTION_IDENTIFIER_DEFAULT);
    private static final String CLUSTER_KEYWORD_IDENTIFIER = ConfProperties.getString(Constants.CLUSTER_IDENTIFIER, Constants.CLUSTER_IDENTIFIER_DEFAULT);
    private static final String EVENT_HIGH_THRESHOLD_IDENTIFIER = ConfProperties.getString(Constants.HIGH_THRESHOLD_IDENTIFIER_IDENTIFIER, Constants.HIGH_THRESHOLD_IDENTIFIER_IDENTIFIER_DEFAULT);
    private static final String EVENT_LOW_THRESHOLD_IDENTIFIER = ConfProperties.getString(Constants.LOW_THRESHOLD_IDENTIFIER_IDENTIFIER, Constants.LOW_THRESHOLD_IDENTIFIER_IDENTIFIER_DEFAULT);

    @Override
    public UtilityBean<AnomalyIncidentRequest> clientValidation(RequestObject request) throws ClientException {
        log.trace("Inside Client validation");

        if (request == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (authToken == null || authToken.trim().isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        AnomalyIncidentRequest anomalyIncidentRequest = new AnomalyIncidentRequest(request);
        if (!anomalyIncidentRequest.validateParameters()) {
            log.error("Client validation failed for GET ANOMALY INCIDENTS DATA request.");
            throw new ClientException(UIMessages.CLIENT_VALIDATION_FAILED);
        }

        return UtilityBean.<AnomalyIncidentRequest>builder()
                .authToken(authToken)
                .accountIdString(anomalyIncidentRequest.getAccountIdentifier())
                .serviceId(anomalyIncidentRequest.getServiceId())
                .requestPayloadObject(anomalyIncidentRequest)
                .build();
    }

    @Override
    public UtilityBean<AnomalyIncidentRequest> serverValidation(UtilityBean<AnomalyIncidentRequest> utilityBean) throws ServerException {
        AccountRepo accountRepo = new AccountRepo();
        ServiceRepo serviceRepo = new ServiceRepo();

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error(Constants.MESSAGE_INVALID_AUTH_TOKEN);
            throw new ServerException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }
        utilityBean.setUserId(userId);

        Account account = accountRepo.getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            log.error(UIMessages.ERROR_INVALID_ACCOUNT_ID);
            throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
        }
        utilityBean.setAccount(account);

        if (utilityBean.getServiceId() > 0) {
            BasicEntity serviceDetails = serviceRepo.getBasicServiceDetailsWithServiceId(utilityBean.getAccountIdString(), utilityBean.getServiceId());
            if (serviceDetails == null) {
                String error = "Service id is invalid.";
                log.error(error);
                throw new ServerException(error);
            }
            utilityBean.setServiceIdString(serviceDetails.getIdentifier());
        }

        return utilityBean;
    }

    @Override
    public Map<String, Map<Long, List<AnomalyEventDetail>>> processData(UtilityBean<AnomalyIncidentRequest> configData) throws DataProcessingException {
        return populateUpdatedEventView(getProblemDetails(configData));
    }

    protected Map<String, Map<Long, List<AnomalyEventDetail>>> populateUpdatedEventView(Map<Long, List<AnomalyEventDetail>> data) {
        Map<String, Map<Long, List<AnomalyEventDetail>>> result = new LinkedHashTreeMap<>();
        try {

            long start = System.currentTimeMillis();
            data.forEach((time, problemDetailList) -> problemDetailList.forEach(anomalyEventDetail -> {
                if (result.get(anomalyEventDetail.getName()) == null) {
                    result.put(anomalyEventDetail.getName(), new LinkedHashTreeMap<>());
                    result.get(anomalyEventDetail.getName()).put(time, new ArrayList<>());
                } else {
                    result.get(anomalyEventDetail.getName()).computeIfAbsent(time, k -> new ArrayList<>());
                }
                result.get(anomalyEventDetail.getName()).get(time).add(anomalyEventDetail);
            }));
            log.debug("Time taken to update the problem event view grouping is {} ms.", (System.currentTimeMillis() - start));
        } catch (Exception e) {
            log.error("Error occurred while transforming problem detail to new format", e);
            return null;
        }
        return result;
    }

    protected Map<Long, List<AnomalyEventDetail>> getProblemDetails(UtilityBean<AnomalyIncidentRequest> configData) {
        SignalSearchRepo signalSearchRepo = new SignalSearchRepo();
        AnomalySearchRepo anomalySearchRepo = new AnomalySearchRepo();

        Map<Long, List<AnomalyEventDetail>> problemDetailsMap;

        Clock clock = Clock.systemDefaultZone();
        long start = clock.millis();
        SignalDetails signal = signalSearchRepo.getSignalsBySignalId(configData.getRequestPayloadObject().getSignalIdentifier(), configData.getAccount().getIdentifier());
        if (signal == null) {
            return Collections.emptyMap();
        }
        log.debug("Time taken for fetching signal data from OpenSearch: {} ms.", (clock.millis() - start));
        start = clock.millis();

        List<Anomalies> allAnomalyDetails = anomalySearchRepo.getAnomaliesBySignal(configData.getAccount().getIdentifier(), configData.getRequestPayloadObject().getSignalIdentifier());
        if (allAnomalyDetails.isEmpty()) {
            return Collections.emptyMap();
        }
        log.debug("Time taken for fetching allAnomalyDetails data from OpenSearch: {} ms.", (clock.millis() - start));
        start = clock.millis();

        Set<String> serviceIdSet = allAnomalyDetails.stream()
                .map(Anomalies::getServiceId)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());

        Map<String, ServiceDetails> serviceMap = preProcessMetaData(configData.getAccount().getIdentifier(), serviceIdSet);
        log.debug("Time taken for fetching serviceMap data: {} ms.", (clock.millis() - start));
        start = clock.millis();

        String entryServiceId = (String) signal.getServiceIds().toArray()[0];
        Service entryService = HealUICache.INSTANCE.getServiceDetails(configData.getAccount().getIdentifier(), entryServiceId);
        log.debug("Time taken for fetching entryService data: {} ms.", (clock.millis() - start));
        start = clock.millis();

        problemDetailsMap = allAnomalyDetails.parallelStream()
                .map(anomalyDetail -> getAnomalyDetails(configData.getAccount().getIdentifier(), entryService, anomalyDetail, serviceMap, signal.getSignalType(), signal.getSignalId()))
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(AnomalyEventDetail::getAnomalyTime));
        log.debug("Time taken for getting problemDetailsMap is: {} ms.", (clock.millis() - start));

        return problemDetailsMap;
    }

    /**
     * This method is a helper method which pre computes and fetches all response configurations in advance , which
     * improves performance instead of doing it on per event basis.
     */
    protected Map<String, ServiceDetails> preProcessMetaData(String accountIdentifier, Set<String> affectedServiceIdentifiers) {
        long start = System.currentTimeMillis();

        AgentRepo agentRepo = new AgentRepo();
        ApplicationRepo applicationsRepo = new ApplicationRepo();

        if (affectedServiceIdentifiers == null || affectedServiceIdentifiers.isEmpty()) {
            log.error("Unable to fetch account or service data for account: {}", accountIdentifier);
            return new HashMap<>();
        }

        Map<String, ServiceDetails> serviceDetailsMap = affectedServiceIdentifiers.parallelStream().map(serviceId -> {
            Service service = HealUICache.INSTANCE.getServiceDetails(accountIdentifier, serviceId);
            if (service == null) return null;

            Tags layerTag = service.getTags() == null ? null : service.getTags().parallelStream().filter(t -> t.getType().equalsIgnoreCase("LayerName")).findAny().orElse(null);
            List<Tags> agentTags = service.getTags() == null ? null : service.getTags().parallelStream().filter(t -> t.getType().equalsIgnoreCase("Agent")).collect(Collectors.toList());
            boolean jimFlag = agentTags != null && !agentTags.isEmpty() && agentTags.parallelStream().anyMatch(tags -> {
                Agent agent = agentRepo.getAgentDetails(tags.getValue());
                if (agent == null) {
                    return false;
                }
                return agent.getTypeName().equalsIgnoreCase("JIMAgent");
            });

            boolean isUserAccessible = HealUICache.INSTANCE.getServiceApplicationList(accountIdentifier, service.getIdentifier())
                    .parallelStream()
                    .anyMatch(a -> {
                        List<User> users = applicationsRepo.getAppUsersByAppIdentifier(accountIdentifier, a.getIdentifier());
                        return users != null && !users.isEmpty();
                    });

            return ServiceDetails.builder()
                    .service(service)
                    .isJimEnabled((jimFlag) ? 1 : 0)
                    .iconType(layerTag != null ? layerTag.getValue() : "")
                    .isUserAccessible(isUserAccessible)
                    .build();
        }).filter(Objects::nonNull).collect(Collectors.toMap(c -> c.getService().getIdentifier(), c -> c));
        log.debug("Time taken to prep meta data: {} ms.", (System.currentTimeMillis() - start));

        return serviceDetailsMap;
    }

    public List<AnomalyEventDetail> getAnomalyDetails(String accountIdentifier, Service entryService,
                                                Anomalies anomalyDetail, Map<String, ServiceDetails> serviceMap,
                                                String signalType, String signalId) {

        List<AnomalyEventDetail> anomalyEventsList = new ArrayList<>();
        InstanceRepo instanceRepo = new InstanceRepo();
        KpiRepo kpiRepo = new KpiRepo();
        ComponentRepo componentRepo = new ComponentRepo();
        TransactionRepo txnRepo = new TransactionRepo();

        if (!StringUtils.isEmpty(signalType) && SignalType.BATCH_JOB.equals(SignalType.valueOf(signalType))) {
            AnomalyEventDetail anomalyEventDetail = new AnomalyEventDetail();
            Map<String, String> metaData = anomalyDetail.getMetadata();
            Map<String, Double> thresholds = anomalyDetail.getThresholds();
            anomalyEventDetail.getMetaData().putAll(metaData);
            Map<String, String> newThresholds = new HashMap<>();
            for (Map.Entry<String, Double> entry : thresholds.entrySet()) {
                newThresholds.put(entry.getKey(), Double.toString(entry.getValue()));
            }
            anomalyEventDetail.getMetaData().putAll(newThresholds);
            anomalyEventDetail.setAnomalyTime(anomalyDetail.getAnomalyTime());

            anomalyEventDetail.setSource(!StringUtils.isEmpty(anomalyEventDetail.getMetaData().get("DataSource")) ?
                    anomalyEventDetail.getMetaData().get("DataSource") : Constants.UNKNOWN_SOURCE);

            anomalyEventDetail.setName(metaData.get("applicationid"));
            anomalyEventDetail.setInstanceName(metaData.get("batch_job"));
            anomalyEventDetail.getMetaData().put("actualDuration", metaData.get("actual_duration"));
            anomalyEventDetail.getMetaData().put("expectedDuration", metaData.get("expected_duration"));
            anomalyEventDetail.setUserAccessible(true);

            if (metaData.get("serviceid") == null) {
                ApplicationRepo applicationRepo = new ApplicationRepo();
                Application applicationDetail = applicationRepo.getApplicationDetailWithAppIdentifier(accountIdentifier, metaData.get("applicationid"));
                anomalyEventDetail.setName(applicationDetail.getName());
                anomalyEventDetail.setServiceId(applicationDetail.getId());
                anomalyEventDetail.setServiceIdentifier(applicationDetail.getIdentifier());
            } else {
                Service serviceDetail = HealUICache.INSTANCE.getServiceDetails(accountIdentifier, metaData.get("serviceid"));
                anomalyEventDetail.setName(serviceDetail.getName());
                anomalyEventDetail.setServiceId(serviceDetail.getId());
                anomalyEventDetail.setServiceIdentifier(serviceDetail.getIdentifier());
            }
            BasicKpiEntity basicKpiEntity = componentRepo.getComponentKpis(accountIdentifier, anomalyDetail.getCategoryId()).parallelStream().filter(f -> f.getId() == anomalyDetail.getKpiId()).findAny().orElse(null);
            if (basicKpiEntity == null) {
                log.debug("Couldn't find kpi {} in componentKpis redis key for component {}", anomalyDetail.getKpiId(), anomalyDetail.getCategoryId());
            } else {
                CompInstKpiEntity kpiDetail = CompInstKpiEntity.builder()
                        .name(basicKpiEntity.getName())
                        .unit(basicKpiEntity.getUnit())
                        .type(basicKpiEntity.getType())
                        .groupName(basicKpiEntity.getGroupName())
                        .categoryDetails(basicKpiEntity.getCategoryDetails()).build();
                setKpiFields(anomalyEventDetail, kpiDetail, anomalyDetail);
            }
            anomalyEventsList.add(anomalyEventDetail);
        } else {
            if (anomalyDetail.getServiceId() == null || anomalyDetail.getServiceId().isEmpty()) {
                log.error("Service unavailable in anomaly. Hence ignoring anomaly for signalId:{}, anomalyId:{}", signalId, anomalyDetail.getAnomalyId());
                return anomalyEventsList;
            } else {
                anomalyEventsList = anomalyDetail.getServiceId().stream().map(s -> {
                    ServiceDetails serviceDetails = serviceMap.get(s);
                    if (serviceDetails == null) {
                        log.error("Invalid service id: {} hence ignoring anomaly for signalId:{}, anomalyId:{}", anomalyDetail.getServiceId(), signalId, anomalyDetail.getAnomalyId());
                        return null;
                    }
                    Service service = serviceDetails.getService();

                    AnomalyEventDetail anomalyEventDetail = new AnomalyEventDetail();

                    Map<String, String> metaData = anomalyDetail.getMetadata();
                    Map<String, Double> thresholds = anomalyDetail.getThresholds();
                    anomalyEventDetail.getMetaData().putAll(metaData);
                    Map<String, String> newThresholds = new HashMap<>();
                    for (Map.Entry<String, Double> entry : thresholds.entrySet()) {
                        newThresholds.put(entry.getKey(), Double.toString(entry.getValue()));
                    }
                    anomalyEventDetail.getMetaData().putAll(newThresholds);
                    anomalyEventDetail.setAnomalyTime(anomalyDetail.getAnomalyTime());

                    anomalyEventDetail.setSource(!StringUtils.isEmpty(anomalyEventDetail.getMetaData().get("DataSource")) ?
                            anomalyEventDetail.getMetaData().get("DataSource") : Constants.UNKNOWN_SOURCE);

                    anomalyEventDetail.setEntryServiceNode(entryService != null && entryService.getTags() != null && entryService.getTags().parallelStream().anyMatch(t -> t.getType().equalsIgnoreCase("EntryPoint")));
                    anomalyEventDetail.setName(service.getName());
                    anomalyEventDetail.setServiceId(service.getId());
                    anomalyEventDetail.setServiceIdentifier(service.getIdentifier());
                    anomalyEventDetail.setIsJIMEnabled(serviceDetails.getIsJimEnabled());
                    anomalyEventDetail.setIconType(serviceDetails.getIconType() == null || serviceDetails.getIconType().trim().isEmpty() ? "NA" : serviceDetails.getIconType());
                    anomalyEventDetail.setUserAccessible(serviceDetails.isUserAccessible());

                    return anomalyEventDetail;
                }).collect(Collectors.toList());
            }
        }

        for (AnomalyEventDetail anomalyEventDetail : anomalyEventsList) {
            if (anomalyDetail.getTransactionId() != null) {
                //Transactions
                String transactionId = anomalyDetail.getTransactionId()
                        .replaceAll("&lt;", "<").replaceAll("&gt;", ">")
                        .replaceAll("&apos;", "'").replaceAll("&quot;", "\"")
                        .replaceAll("&amp;", "&");
                com.heal.configuration.pojos.Transaction transactionDetails = txnRepo.getTransactionDetails(accountIdentifier, transactionId);
                if (transactionDetails != null) {

                    anomalyEventDetail.setInstanceName(transactionDetails.getName());
                    anomalyEventDetail.setInstanceId(transactionDetails.getId());
                    anomalyEventDetail.setInstanceIdentifier(transactionDetails.getIdentifier());
                } else {
                    log.error("Could not fetch the transaction details from redis for accountId:{}, txnId:{}, anomalyId:{}", accountIdentifier, transactionId, anomalyDetail.getAnomalyId());
                }

                List<BasicKpiEntity> componentKpisList = componentRepo.getComponentKpis(accountIdentifier, Constants.TRANSACTION_IDENTIFIER_DEFAULT);
                BasicKpiEntity txnKpiDetails = componentKpisList.parallelStream().filter(k -> k.getStatus() == 1 && k.getId() == anomalyDetail.getKpiId()).findAny().orElse(null);
                anomalyEventDetail.setType(TRANSACTION_KEYWORD_IDENTIFIER);
                if (txnKpiDetails != null) {
                    anomalyEventDetail.setKpiName(txnKpiDetails.getName());
                    anomalyEventDetail.setKpiCategoryId(txnKpiDetails.getCategoryDetails().getId());
                    anomalyEventDetail.setKpiCategoryName(txnKpiDetails.getCategoryDetails().getName());
                }
                anomalyEventDetail.setKpiUnit(COUNT_IDENTIFIER);
                anomalyEventDetail.setImpactKPI(true);
            } else {
                //Kpis
                if (!StringUtils.isEmpty(signalType) && !SignalType.BATCH_JOB.equals(SignalType.valueOf(signalType))) {
                    anomalyEventDetail.setType(CLUSTER_KEYWORD_IDENTIFIER);
                    CompInstClusterDetails instanceDetails = instanceRepo.getInstanceDetailsWithInstIdentifier(accountIdentifier, anomalyDetail.getInstanceId());
                    if (instanceDetails != null) {
                        anomalyEventDetail.setInstanceId(instanceDetails.getId());
                        anomalyEventDetail.setInstanceName(instanceDetails.getName());
                        anomalyEventDetail.setInstanceIdentifier(instanceDetails.getIdentifier());
                        anomalyEventDetail.setClusterId(instanceDetails.getId());

                        if (instanceDetails.getClusterIdentifiers() != null && !instanceDetails.getClusterIdentifiers().isEmpty()) {
                            CompInstClusterDetails clusterDetails = instanceRepo.getInstanceDetailsWithInstIdentifier(accountIdentifier, instanceDetails.getClusterIdentifiers().get(0));
                            anomalyEventDetail.setClusterId(clusterDetails == null ? 0 : clusterDetails.getId());
                        }
                    }
                    CompInstKpiEntity kpiDetail = kpiRepo.getKpiDetailByAccInstKpiId(accountIdentifier, anomalyDetail.getInstanceId(), anomalyDetail.getKpiId());
                    if (kpiDetail != null) {
                        setKpiFields(anomalyEventDetail, kpiDetail, anomalyDetail);
                    }
                }
            }
            String[] combinedValue = anomalyDetail.getValue().split(Constants.WATCHER_VALUE_SPLITTER_DEFAULT);
            if (combinedValue.length > 2) {
                anomalyEventDetail.setObservedValue(CommonUtils.changePrecision(combinedValue[1], 2));
                anomalyEventDetail.setOldValue(CommonUtils.changePrecision(combinedValue[2], 2));
                anomalyEventDetail.setOperation(combinedValue[0]);
            } else {
                anomalyEventDetail.setObservedValue(CommonUtils.changePrecision(combinedValue[0], 2));
//            anomalyEventDetail.setOldValue(metaData.getOrDefault("oldValue", null));
//            anomalyEventDetail.setOperation(metaData.get("operation"));
            }
            anomalyEventDetail.setAttribute(anomalyDetail.getKpiAttribute());
            setThresholdForProblemEvent(anomalyEventDetail, anomalyDetail);

        }
        return anomalyEventsList;
    }

    protected void setThresholdForProblemEvent(AnomalyEventDetail anomalyEventDetail, Anomalies prblm) {
        Map<String, Double> thresholdDetails = prblm.getThresholds();

        if (thresholdDetails != null && !thresholdDetails.isEmpty()) {
            Map<String, Object> temp = new LinkedHashMap<>();
            temp.put("upperRange", thresholdDetails.get(EVENT_HIGH_THRESHOLD_IDENTIFIER));
            temp.put("lowerRange", thresholdDetails.get(EVENT_LOW_THRESHOLD_IDENTIFIER));
            temp.put("type", prblm.getThresholdType());
            temp.put("operation", prblm.getOperationType());
            anomalyEventDetail.setOperatingRange(temp);
        }
    }

    protected void setKpiFields(AnomalyEventDetail anomalyEventDetail, CompInstKpiEntity kpiDetail, Anomalies anomalyDetails) {
        if (kpiDetail != null) {
            anomalyEventDetail.setKpiName(kpiDetail.getName());
            anomalyEventDetail.setKpiUnit(kpiDetail.getUnit());
            anomalyEventDetail.setKpiType(kpiDetail.getType());
            anomalyEventDetail.setGroupName(kpiDetail.getGroupName());
            Map<String, String> metaData = anomalyDetails.getMetadata();

            if (kpiDetail.getType().equals(Constants.FILEWATCH_IDENTIFIER)
                    || kpiDetail.getType().equals(Constants.CONFIGWATCH_IDENTIFIER)) {
                anomalyEventDetail.setFileName(metaData.get("fileName"));
            } else {
                anomalyEventDetail.setFileName(kpiDetail.getName());
            }

            //Fetch and update kpi category details from cache
            String kpiType = kpiDetail.getType();
            switch (kpiType) {

                case Constants.AVAILABILITY_KPI_IDENTIFIER:
                    log.trace("Availability category id assigned");
                    anomalyEventDetail.setKpiCategoryId(Constants.AVAILABILITY_LOGICAL_CATEGORY_ID);
                    break;

                case Constants.CONFIGWATCH_IDENTIFIER:
                    //No break statement since config watch and file watch have the same logical id

                case Constants.FILEWATCH_IDENTIFIER:
                    log.trace("Config watch category id assigned");
                    anomalyEventDetail.setKpiCategoryId(Constants.CONFIG_WATCH_LOGICAL_CATEGORY_ID);
                    break;

                case Constants.KPI_CATEGORY_QUERY_DEEP_DIVE:
                    log.trace("Query deep dive category id assigned");
                    anomalyEventDetail.setKpiCategoryId(Constants.CATEGORY_DEEP_DIVE_ID);
                    break;

                default:
                    log.trace("Actual category id assigned");
                    anomalyEventDetail.setKpiCategoryId(kpiDetail.getCategoryDetails().getId());

            }
        }
    }
}
