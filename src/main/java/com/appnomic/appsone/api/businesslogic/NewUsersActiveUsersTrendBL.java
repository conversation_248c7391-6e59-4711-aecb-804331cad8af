package com.appnomic.appsone.api.businesslogic;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.mobile.model.NewUsersActiveUsersTrend;
import com.appnomic.appsone.api.mobile.util.BigQueryService;
import com.appnomic.appsone.api.mobile.util.BigQueryUtil;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.FieldValueList;
import com.google.cloud.bigquery.QueryJobConfiguration;
import com.google.cloud.bigquery.TableResult;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class NewUsersActiveUsersTrendBL implements BusinessLogic<Object, UtilityBean<Object>, NewUsersActiveUsersTrend> {

	@Override
	public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
		List<String> errorMessages = new ArrayList<>();
		if (!BigQueryUtil.appOSStringValidation(requestObject, errorMessages)
			|| !BigQueryUtil.appDisplayVersionValidation(requestObject, errorMessages)
			|| !BigQueryUtil.fromTimeToTimeValidation(requestObject, errorMessages)) {
			throw new ClientException(String.join(";", errorMessages));
		}
		String appOSString = requestObject.getQueryParams().get("app-os")[0];
		String appDisplayVersions = requestObject.getQueryParams().get("app-build-version")[0];
		String fromTime = requestObject.getQueryParams().get("from-time")[0];
		String toTime = requestObject.getQueryParams().get("to-time")[0];
		return UtilityBean.<Object>builder()
				.appOSString(appOSString)
				.appDisplayVersions(appDisplayVersions)
                .fromTimeString(fromTime)
                .toTimeString(toTime)
                .build();
	}

	@Override
	public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
		return utilityBean;
	}

	@Override
	public NewUsersActiveUsersTrend processData(UtilityBean<Object> configData) throws DataProcessingException {
		String appOSString = configData.getAppOSString();
		BigQuery bigQuery = BigQueryService.getInstance(appOSString.split("\\.")[0]);
		log.debug("bigQuery Obj: {}", bigQuery);

		String appDisplayVersionInParam = BigQueryUtil.getInParameter(configData.getAppDisplayVersions());
		String fromTime = configData.getFromTimeString();
		String toTime = configData.getToTimeString();
		String intervalBucket = BigQueryUtil.getIntervalBucket(fromTime, toTime);
		String intervalBucketUnit = intervalBucket.split(" ")[1];
		
		String truncatedEventTimestamp = BigQueryUtil.getTruncatedEventTimestamp(intervalBucket, intervalBucketUnit, Constants.ANALYTICS_ALIAS);
		
		String query = 	"SELECT " + 
							 truncatedEventTimestamp + " AS time, " + 
						"    TIMESTAMP_TRUNC(TIMESTAMP_MILLIS("+fromTime+"), DAY) AS truncatedFromTime, " + 
						"    COUNT(DISTINCT user_id) AS newUsers, " + 
						"    STRING_AGG(DISTINCT user_id) AS newUsersId, " + 
						"    null AS activeUsers, " + 
						"    null AS activeUsersId " + 
						"FROM " + Constants.getAnalyticsTable(appOSString) + " AS T " + 
						"WHERE " + 
						"    event_name = 'first_open' " +
						"    AND event_timestamp > " + fromTime + "000" +
						"    AND event_timestamp < " + toTime + "000" + 
						"    AND app_info.version IN " + appDisplayVersionInParam + " " +
						"GROUP BY " + 
						"    time " + 
						" " + 
						"UNION ALL " + 
						" " + 
						"SELECT " + 
						 	 truncatedEventTimestamp + " AS time, " + 
						"    TIMESTAMP_TRUNC(TIMESTAMP_MILLIS("+fromTime+"), DAY) AS truncatedFromTime, " + 
						"    null AS newUsers, " + 
						"    null AS newUsersId, " + 
						"    COUNT(DISTINCT user_id) AS activeUsers, " + 
						"    STRING_AGG(DISTINCT user_id) AS activeUsersId " + 
						"FROM " + Constants.getAnalyticsTable(appOSString) + " AS T " + 
						"    CROSS JOIN       T.event_params " + 
						"WHERE " + 
						"    event_params.key = 'engagement_time_msec' AND event_params.value.int_value > 0 " + 
						"    AND event_timestamp > " + fromTime + "000" +
						"    AND event_timestamp < " + toTime + "000" + 
						"    AND app_info.version IN " + appDisplayVersionInParam + " " +
						"GROUP BY " + 
						"    time " +
						" " + 
						"UNION ALL " + 
						" " + 
						"SELECT " + 
						"	 null AS time, " + 
						"    TIMESTAMP_TRUNC(TIMESTAMP_MILLIS("+fromTime+"), DAY) AS truncatedFromTime, " + 
						"    null AS newUsers, " + 
						"    null AS newUsersId, " + 
						"    null AS activeUsers, " + 
						"    null AS activeUsersId "; 
		
		QueryJobConfiguration queryConfig = QueryJobConfiguration.newBuilder(query).setUseLegacySql(false).build();
		
		TableResult result;
		try {
			result = bigQuery.query(queryConfig);
		} catch(InterruptedException inExc) {
			throw new DataProcessingException("Error in fetching data from BigQuery");
		}
		
		Set<String> newUsersId = new HashSet<>();
		Set<String> activeUsersId = new HashSet<>();
		
		Map<String, Long> newUsersTrend = new HashMap<>();
		Map<String, Long> activeUsersTrend = new HashMap<>();
		
		NewUsersActiveUsersTrend response = new NewUsersActiveUsersTrend();
		
		for(FieldValueList value : result.iterateAll()) {
			if(newUsersTrend.isEmpty() && activeUsersTrend.isEmpty()) {
				newUsersTrend = BigQueryUtil.createTimeBucket(value.get("truncatedFromTime").getTimestampValue() / 1000L, Long.parseLong(toTime), intervalBucket);
				activeUsersTrend = BigQueryUtil.createTimeBucket(value.get("truncatedFromTime").getTimestampValue() / 1000L, Long.parseLong(toTime), intervalBucket);
			}
			if(null != value.get("time").getValue()) {
				String timeKey = String.valueOf(value.get("time").getTimestampValue() / 1000L);
				if(newUsersTrend.containsKey(timeKey) && null != value.get("newUsers").getValue()) {
					newUsersTrend.put(timeKey, value.get("newUsers").getLongValue());
					if(null != value.get("newUsersId").getValue()) {
						newUsersId.addAll(Arrays.asList(value.get("newUsersId").getStringValue().split(",")));
					}
				}
				if(activeUsersTrend.containsKey(timeKey) && null != value.get("activeUsers").getValue()) {
					activeUsersTrend.put(timeKey, value.get("activeUsers").getLongValue());
					if(null != value.get("activeUsersId").getValue()) {
						activeUsersId.addAll(Arrays.asList(value.get("activeUsersId").getStringValue().split(",")));
					}
				}
			}
		}
		
		response.setNewUsers((long)newUsersId.size());
		response.setNewUsersTrend(newUsersTrend);
		
		response.setActiveUsers((long)activeUsersId.size());
		response.setActiveUsersTrend(activeUsersTrend);
		
		return response;
	}

}
