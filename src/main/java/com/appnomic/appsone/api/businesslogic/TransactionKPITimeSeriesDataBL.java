package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.beans.ThresholdType;
import com.appnomic.appsone.api.beans.Thresholds;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.opensearch.AnomalySearchRepo;
import com.appnomic.appsone.api.dao.opensearch.CollatedTransactionsSearchRepo;
import com.appnomic.appsone.api.dao.opensearch.RawTransactionSearchRepo;
import com.appnomic.appsone.api.dao.opensearch.ThresholdSearchRepo;
import com.appnomic.appsone.api.dao.redis.*;
import com.appnomic.appsone.api.pojo.*;
import com.appnomic.appsone.api.pojo.request.TransactionKPITimeSeriesDataRequest;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.*;
import com.appnomic.appsone.opeasearchquery.results.TabularResults;
import com.heal.configuration.enums.KPIAttributes;
import com.heal.configuration.enums.OperationType;
import com.heal.configuration.enums.TransactionResponseTypes;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.Application;
import com.heal.configuration.pojos.CompInstClusterDetails;
import com.heal.configuration.pojos.Transaction;
import com.heal.configuration.pojos.*;
import com.heal.configuration.pojos.opensearch.Anomalies;
import com.heal.configuration.pojos.opensearch.CollatedTransactionData;
import com.heal.configuration.pojos.opensearch.TransactionKpiThresholds;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.appnomic.appsone.api.common.Constants.AVG_RESPONSE_PERCENTILE_KPI_LITERAL;

/**
 * <AUTHOR> on 20/05/22
 */
@Slf4j
public class TransactionKPITimeSeriesDataBL implements BusinessLogic<TransactionKPITimeSeriesDataRequest, UtilityBean<TransactionKPITimeSeriesDataRequest>, UIData> {

    private static final String UPPER_IDENTIFIER = ConfProperties.getString(Constants.HIGH_THRESHOLD_IDENTIFIER_IDENTIFIER,
            Constants.HIGH_THRESHOLD_IDENTIFIER_IDENTIFIER_DEFAULT);
    private static final String LOWER_IDENTIFIER = ConfProperties.getString(Constants.LOW_THRESHOLD_IDENTIFIER_IDENTIFIER,
            Constants.LOW_THRESHOLD_IDENTIFIER_IDENTIFIER_DEFAULT);

    @Override
    public UtilityBean<TransactionKPITimeSeriesDataRequest> clientValidation(RequestObject request) throws ClientException {

        if (request == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);

        if (authToken == null || authToken.trim().isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        TransactionKPITimeSeriesDataRequest transactionKPITimeSeriesDataRequest = new TransactionKPITimeSeriesDataRequest(request);

        if (!transactionKPITimeSeriesDataRequest.validateParameters(transactionKPITimeSeriesDataRequest)) {
            log.error("Client validation failed for GET TRANSACTION DATA request.");
            throw new ClientException(UIMessages.CLIENT_VALIDATION_FAILED);
        }

        return UtilityBean.<TransactionKPITimeSeriesDataRequest>builder()
                .requestPayloadObject(transactionKPITimeSeriesDataRequest)
                .fromTime(Long.valueOf(transactionKPITimeSeriesDataRequest.getFromTimeString()))
                .toTime(Long.valueOf(transactionKPITimeSeriesDataRequest.getToTimeString()))
                .accountIdString(transactionKPITimeSeriesDataRequest.getAccountIdString())
                .serviceIdString(transactionKPITimeSeriesDataRequest.getServiceIdString())
                .componentInstanceIdString(transactionKPITimeSeriesDataRequest.getInstanceIdString())
                .responseType(transactionKPITimeSeriesDataRequest.getResponseType())
                .requestTypeString(transactionKPITimeSeriesDataRequest.getRequestType())
                .authToken(authToken)
                .build();
    }

    @Override
    public UtilityBean<TransactionKPITimeSeriesDataRequest> serverValidation(UtilityBean<TransactionKPITimeSeriesDataRequest> utilityBean) throws ServerException {

        String accountIdentifier = utilityBean.getAccountIdString();
        int serviceId = Integer.parseInt(utilityBean.getServiceIdString());
        int instanceId = Integer.parseInt(utilityBean.getComponentInstanceIdString());
        int txnId = Integer.parseInt(utilityBean.getRequestPayloadObject().getTxnIdString());
        String kpiName = utilityBean.getRequestPayloadObject().getKpiName();

        AccountRepo accountsRepo = new AccountRepo();
        ServiceRepo serviceRepo = new ServiceRepo();
        InstanceRepo instanceRepo = new InstanceRepo();

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token.");
            throw new ServerException("Error while extracting user details from authorization token.");
        }

        Account account = accountsRepo.getAccount(accountIdentifier);
        if (account == null) {
            log.error("Account identifier is invalid.");
            throw new ServerException("Account identifier is invalid.");
        }
        utilityBean.setAccount(account);

        TimeRangeDetailsDao timeRangeDetailsDao = new TimeRangeDetailsDao();
        List<TimeRangeDetails> timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(accountIdentifier);
        if (timeRangeDetailsList == null) {
            log.error("Exception occurred while fetching time range details for account {}", accountIdentifier);
            throw new ServerException("Exception occurred while fetching time range details for account " + accountIdentifier);
        }

        if (timeRangeDetailsList.isEmpty()) {
            log.trace("No time range details found for account {}. Searching for Global account.", accountIdentifier);
            timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }
        if (timeRangeDetailsList == null) {
            log.error("Exception occurred while fetching time range details for account {}", Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
            throw new ServerException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }

        if (timeRangeDetailsList.isEmpty()) {
            log.error("No time range details found");
            throw new ServerException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }
        TimeRangeDetails timeRangeDetails = ValidationUtils.getRollupPojo(utilityBean.getFromTime(), utilityBean.getToTime(), timeRangeDetailsList);
        if (timeRangeDetails == null) {
            log.error("Time range details are invalid.");
            throw new ServerException("Time range details are invalid.");
        }
        utilityBean.setTimeRangeDetails(timeRangeDetails);
        utilityBean.setTimeRangeDetailsList(timeRangeDetailsList);

        NewTimeIntervalGenerationUtility t = new NewTimeIntervalGenerationUtility();
        t.setTimezoneOffset(accountIdentifier);
        t.processTimeRange(utilityBean.getFromTime(), utilityBean.getToTime());

        int aggregationValue = t.getNewTimeRangeDefinition().getAggregationLevel().getAggregrationValue();

        if (AVG_RESPONSE_PERCENTILE_KPI_LITERAL.equalsIgnoreCase(kpiName) && aggregationValue == 1440) {
            String error = "Can't provide data for more than 24 hour range data for " + AVG_RESPONSE_PERCENTILE_KPI_LITERAL + " kpi.";
            log.error(error);
            throw new ServerException(error);
        }

        BasicEntity serviceDetails = serviceRepo.getBasicServiceDetailsWithServiceId(accountIdentifier, serviceId);
        if (serviceDetails == null) {
            String error = "Service id is invalid";
            log.error(error);
            throw new ServerException(error);
        }
        utilityBean.setServiceIdString(serviceDetails.getIdentifier());

        List<BasicEntity> applicationDetails = HealUICache.INSTANCE.getServiceApplicationList(accountIdentifier, serviceDetails.getIdentifier());
        for (BasicEntity basicEntity : applicationDetails) {
            Application application = new ApplicationRepo().getApplicationDetailWithAppIdentifier(accountIdentifier, basicEntity.getIdentifier());
            if (application != null && !application.getPercentiles().isEmpty()) {
                application.getPercentiles().keySet().forEach(k -> {
                    Set<Double> percentiles = application.getPercentiles().get(k);
                    if (percentiles.size() > 5) {
                        log.warn("Maximum allowed number of percentiles is 5. Taking first 5 percentiles for application {}, kpi {}", application.getIdentifier(), k);
                        application.getPercentiles().get(k).clear();
                        application.getPercentiles().get(k).addAll(percentiles.stream().sorted().collect(Collectors.toList()).subList(0, 5));
                    }
                });
                utilityBean.setApplication(application);
                break;
            }
        }


        CompInstClusterDetails instanceDetails = new CompInstClusterDetails();
        if (utilityBean.getRequestTypeString().trim().equalsIgnoreCase(Constants.COM_INSTANCE_TYPE)) {
            instanceDetails = instanceRepo.getInstancesByAccount(accountIdentifier)
                    .parallelStream().filter(c -> c.getId() == instanceId).findAny().orElse(null);
            if (instanceDetails == null) {
                log.error("Instance id is invalid.");
                throw new ServerException("Instance id is invalid.");
            }
            utilityBean.setComponentInstanceIdString(instanceDetails.getIdentifier());
        }


        TransactionRepo transactionRepo = new TransactionRepo();
        Transaction transaction = transactionRepo.getTransactionDetailsById(accountIdentifier, txnId);
        if (transaction == null) {
            log.error("Transaction id is invalid.");
            throw new ServerException("Transaction id is invalid.");
        } else {
            utilityBean.getRequestPayloadObject().setTaggedTransaction(transaction);
        }


        BasicKpiEntity basicKpiEntity = new ComponentRepo().getComponentKpis(account.getIdentifier(), Constants.TRANSACTION_IDENTIFIER_DEFAULT)
                .parallelStream().filter(c -> c.getStatus() == 1 && c.getIdentifier().equalsIgnoreCase(kpiName)).findAny().orElse(null);
        if (AVG_RESPONSE_PERCENTILE_KPI_LITERAL.equalsIgnoreCase(kpiName)) {
            utilityBean.setKpiNameString(AVG_RESPONSE_PERCENTILE_KPI_LITERAL);
        } else if (basicKpiEntity == null) {
            log.error("Invalid Kpi Name {}.", kpiName);
            throw new ServerException("Kpi Name id is invalid.");
        } else {
            utilityBean.setKpiNameString(basicKpiEntity.getIdentifier());
            utilityBean.setOperation(basicKpiEntity.getRollupOperation());
        }

        //GET Agent Data
        List<String> agentList;
        if (utilityBean.getRequestTypeString().trim().equalsIgnoreCase(Constants.SERVICES_CONTROLLER_TYPE)) {
            return utilityBean;
        } else if (utilityBean.getRequestTypeString().trim().equalsIgnoreCase(Constants.COM_INSTANCE_TYPE)) {
            agentList = instanceDetails.getAgentIds();
            if (agentList == null || agentList.isEmpty()) {
                log.error("Unable to fetch agents for given service/comp instance: {}", utilityBean.getServiceIdString());
                return utilityBean;
            }
            utilityBean.getRequestPayloadObject().setAgentList(agentList);
        } else {
            log.error("Invalid request type provided: {}", utilityBean.getRequestTypeString());
            throw new ServerException(String.format("Invalid request type provided: %s", utilityBean.getRequestTypeString()));
        }

        return utilityBean;
    }

    @Override
    public UIData processData(UtilityBean<TransactionKPITimeSeriesDataRequest> configData) throws DataProcessingException {
        log.debug("{} processData(), with params: {}", Constants.INVOKED_METHOD, configData);

        long start = System.currentTimeMillis();

        CollatedTransactionsSearchRepo collatedTxnRepo = new CollatedTransactionsSearchRepo();
        ThresholdSearchRepo thresholdSearchRepo = new ThresholdSearchRepo();
        AnomalySearchRepo anomalySearchRepo = new AnomalySearchRepo();

        String accountIdentifier = configData.getAccountIdString();
        String serviceIdentifier = configData.getServiceIdString();
        String kpiName = configData.getKpiNameString();
        ClusterOperationEnum clusterOperationEnum = configData.getOperation();
        String responseType = configData.getResponseType();
        String requestType = configData.getRequestTypeString();
        String transaction = configData.getRequestPayloadObject().getTaggedTransaction().getIdentifier();

        long fromTime = configData.getFromTime();
        long toTime = configData.getToTime();

        NewTimeIntervalGenerationUtility t = new NewTimeIntervalGenerationUtility();
        t.setTimezoneOffset(accountIdentifier);
        t.processTimeRange(fromTime, toTime);
        t.addEndTimeInOSTime();

        String timezoneId = t.getTimeZoneId();
        List<Long> times = t.getOSTimes();
        AggregationLevel aggregationLevel = t.getNewTimeRangeDefinition().getAggregationLevel();
        int aggregationValue = aggregationLevel.getAggregrationValue();

        UIData uiData = new UIData();
        uiData.setAggregationLevel(aggregationValue);
        uiData.setDateFormat(aggregationLevel.getDataTimePattern());
        uiData.setTime(t.getDisplayTimes());

        TabularResults rawTabularResults;

        List<TabularResultsTypePojo> tabularResultsTypePojos;
        List<TabularResultsTypePojo> minTabularResults;
        List<TabularResultsTypePojo> maxTabularResults;
        List<AttributeKpiValuePojo> attributeKpiValuePojoList = new ArrayList<>();
        List<AttributeKpiValuePojo> attributeKpiMinValuePojoList = new ArrayList<>();
        List<AttributeKpiValuePojo> attributeKpiMaxValuePojoList = new ArrayList<>();
        RawTransactionSearchRepo rawTransactionSearchRepo = new RawTransactionSearchRepo();

        if (AVG_RESPONSE_PERCENTILE_KPI_LITERAL.equalsIgnoreCase(kpiName)) {

            if (aggregationValue != 1440) {
                attributeKpiValuePojoList = getResponseTimePercentileData(configData, requestType, accountIdentifier, transaction, fromTime,
                        toTime, aggregationValue, timezoneId);
                if (attributeKpiValuePojoList.isEmpty()) {
                    return uiData;
                }
            } else {
                return uiData;
            }
        } else if (Constants.TRANSACTION_RESPONSE_TIME.equalsIgnoreCase(kpiName)) {

            if (DateTimeUtil.inRange(configData.getFromTime())) {
                if (requestType.trim().equalsIgnoreCase(Constants.SERVICES_CONTROLLER_TYPE)) {
                    rawTabularResults = rawTransactionSearchRepo.getTransactionTSDataForRTKPIClusterLevel(accountIdentifier,
                            serviceIdentifier, transaction, responseType, aggregationValue, fromTime, toTime, timezoneId);

                } else {
                    Set<String> agentList = configData.getRequestPayloadObject().getAgentList().parallelStream().collect(Collectors.toSet());

                    rawTabularResults = rawTransactionSearchRepo.getTransactionTSDataForRTKPIInstanceLevel(accountIdentifier,
                            serviceIdentifier, transaction, agentList, responseType, aggregationValue,
                            fromTime, toTime, timezoneId);

                }

                if (rawTabularResults == null || rawTabularResults.getRowResults() == null || rawTabularResults.getRowResults().isEmpty()) {
                    return uiData;
                }

                extractRTDataFromRawTabularResult(rawTabularResults, kpiName, attributeKpiValuePojoList, attributeKpiMaxValuePojoList, attributeKpiMinValuePojoList);
            } else {
                if (requestType.trim().equalsIgnoreCase(Constants.SERVICES_CONTROLLER_TYPE)) {
                    tabularResultsTypePojos = collatedTxnRepo.getTransactionCollatedDataByServiceForSingleKPITimeSeriesTrue(accountIdentifier,
                            serviceIdentifier, transaction,
                            kpiName, clusterOperationEnum,
                            responseType, aggregationValue, fromTime, toTime, configData.getTimeRangeDetails(),
                            configData.getTimeRangeDetailsList(), times, timezoneId);


                    minTabularResults = collatedTxnRepo.getTransactionCollatedDataByServiceForSingleKPITimeSeriesTrue(accountIdentifier,
                            serviceIdentifier, transaction,
                            "MIN_" + kpiName, clusterOperationEnum,
                            responseType, aggregationValue, fromTime, toTime, configData.getTimeRangeDetails(),
                            configData.getTimeRangeDetailsList(), times, timezoneId);


                    maxTabularResults = collatedTxnRepo.getTransactionCollatedDataByServiceForSingleKPITimeSeriesTrue(accountIdentifier,
                            serviceIdentifier, transaction,
                            "MAX_" + kpiName, clusterOperationEnum,
                            responseType, aggregationValue, fromTime, toTime, configData.getTimeRangeDetails(),
                            configData.getTimeRangeDetailsList(), times, timezoneId);


                } else {
                    Set<String> agentList = configData.getRequestPayloadObject().getAgentList().parallelStream().collect(Collectors.toSet());

                    tabularResultsTypePojos = collatedTxnRepo.getAgentTransactionCollatedDataByServiceForSingleKPITimeSeriesTrue(accountIdentifier, agentList,
                            serviceIdentifier, transaction,
                            kpiName, clusterOperationEnum, responseType, aggregationValue,
                            fromTime, toTime, configData.getTimeRangeDetails(),
                            configData.getTimeRangeDetailsList(), times, timezoneId);


                    minTabularResults = collatedTxnRepo.getAgentTransactionCollatedDataByServiceForSingleKPITimeSeriesTrue(accountIdentifier, agentList,
                            serviceIdentifier, transaction,
                            "MIN_" + kpiName, clusterOperationEnum, responseType, aggregationValue,
                            fromTime, toTime, configData.getTimeRangeDetails(),
                            configData.getTimeRangeDetailsList(), times, timezoneId);


                    maxTabularResults = collatedTxnRepo.getAgentTransactionCollatedDataByServiceForSingleKPITimeSeriesTrue(accountIdentifier, agentList,
                            serviceIdentifier, transaction,
                            "MAX_" + kpiName, clusterOperationEnum, responseType, aggregationValue,
                            fromTime, toTime, configData.getTimeRangeDetails(),
                            configData.getTimeRangeDetailsList(), times, timezoneId);

                }

                if (tabularResultsTypePojos == null || tabularResultsTypePojos.isEmpty()) {
                    return uiData;
                }

                if (minTabularResults == null || minTabularResults.isEmpty()) {
                    return uiData;
                }

                if (maxTabularResults == null || maxTabularResults.isEmpty()) {
                    return uiData;
                }

                attributeKpiValuePojoList = extractDataFromTabularResult(tabularResultsTypePojos);
                attributeKpiMinValuePojoList = extractDataFromTabularResult(minTabularResults);
                attributeKpiMaxValuePojoList = extractDataFromTabularResult(maxTabularResults);
            }

        } else if (Constants.TRANSACTION_VOLUME.equalsIgnoreCase(kpiName)) {
            if (DateTimeUtil.inRange(configData.getFromTime())) {
                if (requestType.trim().equalsIgnoreCase(Constants.SERVICES_CONTROLLER_TYPE)) {

                    rawTabularResults = rawTransactionSearchRepo.getTransactionTSDataForKPIClusterLevel(accountIdentifier, serviceIdentifier,
                            transaction, responseType, aggregationValue, fromTime, toTime, timezoneId);

                } else {
                    Set<String> agentList = configData.getRequestPayloadObject().getAgentList().parallelStream().collect(Collectors.toSet());

                    rawTabularResults = rawTransactionSearchRepo.getTransactionTSDataForKPIInstanceLevel(accountIdentifier, serviceIdentifier,
                            transaction, agentList, responseType, aggregationValue, fromTime, toTime, timezoneId);

                }

                if (rawTabularResults == null || rawTabularResults.getRowResults() == null || rawTabularResults.getRowResults().isEmpty()) {
                    return uiData;
                }

                attributeKpiValuePojoList = extractKPIDataFromRawTabularResult(rawTabularResults);
                addRemainingKpis(accountIdentifier, attributeKpiValuePojoList);
                addPercentageKpis(accountIdentifier, attributeKpiValuePojoList);

            } else {
                if (requestType.trim().equalsIgnoreCase(Constants.SERVICES_CONTROLLER_TYPE)) {

                    tabularResultsTypePojos = collatedTxnRepo.getTransactionCollatedDataByServiceForAllKPITimeSeriesTrue(accountIdentifier, serviceIdentifier,
                            transaction, responseType, aggregationValue, fromTime, toTime, configData.getTimeRangeDetails(),
                            configData.getTimeRangeDetailsList(), times, timezoneId);


                } else {
                    Set<String> agentList = configData.getRequestPayloadObject().getAgentList().parallelStream().collect(Collectors.toSet());

                    tabularResultsTypePojos = collatedTxnRepo.getAgentTransactionCollatedDataByServiceForAllKPITimeSeriesTrue(accountIdentifier, agentList, serviceIdentifier,
                            transaction, responseType, aggregationValue, fromTime, toTime, configData.getTimeRangeDetails(),
                            configData.getTimeRangeDetailsList(), times, timezoneId);

                }

                if (tabularResultsTypePojos == null || tabularResultsTypePojos.isEmpty()) {
                    return uiData;
                }

                attributeKpiValuePojoList = extractDataFromTabularResult(tabularResultsTypePojos);

                addPercentageKpis(accountIdentifier, attributeKpiValuePojoList);
            }

        }

        times.remove(times.size() - 1);

        List<String> kpiNamesList = attributeKpiValuePojoList.parallelStream().map(AttributeKpiValuePojo::getAttributeName).distinct().collect(Collectors.toList());
        Map<String, List<Double>> attributeKpiValueMap = getKpiValue(times, attributeKpiValuePojoList, kpiNamesList);

        Map<String, List<Double>> attributeKpiMinValueMap = new HashMap<>();
        Map<String, List<Double>> attributeKpiMaxValueMap = new HashMap<>();
        if (kpiName.equalsIgnoreCase(Constants.TRANSACTION_RESPONSE_TIME)) {
            attributeKpiMinValueMap = getKpiValue(times, attributeKpiMinValuePojoList, Collections.singletonList("MIN_" + Constants.TRANSACTION_RESPONSE_TIME));
            attributeKpiMaxValueMap = getKpiValue(times, attributeKpiMaxValuePojoList, Collections.singletonList("MAX_" + Constants.TRANSACTION_RESPONSE_TIME));
        }

        List<BasicKpiEntity> basicKpiEntityList = new ComponentRepo().getComponentKpis(accountIdentifier, Constants.TRANSACTION_IDENTIFIER_DEFAULT)
                .parallelStream().filter(c -> c.getStatus() == 1).collect(Collectors.toList());
        Map<String, List<BasicKpiEntity>> kpiDetailsMap = basicKpiEntityList.parallelStream().collect(Collectors.groupingBy(BasicKpiEntity::getIdentifier));

        Map<String, Thresholds> staticKpiThresholdMap = new LinkedHashMap<>();
        Map<String, Thresholds> realTimeKpiThresholdMap = new LinkedHashMap<>();
        Map<String, List<Integer>> attributeAnomaliesCountMap = new LinkedHashMap<>();
        //--------------------------------------------------------------------------------------------------------------------------
        //Threshold Data
        kpiNamesList.forEach(kpi -> {

            if (!AVG_RESPONSE_PERCENTILE_KPI_LITERAL.equalsIgnoreCase(kpiName)) {

                String kpiId = String.valueOf(kpiDetailsMap.get(kpi).get(0).getId());
                String kpiIdentifier = String.valueOf(kpiDetailsMap.get(kpi).get(0).getIdentifier());

                if (aggregationValue == 1) {

                    //SOR
                    List<TransactionKpiThresholds> txnKpiThresholdsList = thresholdSearchRepo.getTransactionThresholdDetails(accountIdentifier, transaction, kpiId, ThresholdType.STATIC.name(), fromTime, toTime);
                    staticKpiThresholdMap.putAll(getThresholdMap(times, txnKpiThresholdsList, kpi, ThresholdType.STATIC.name()));

                    //NOR
                    txnKpiThresholdsList = thresholdSearchRepo.getTransactionThresholdDetails(accountIdentifier, transaction, kpiId, ThresholdType.REALTIME.name(), fromTime, toTime);
                    realTimeKpiThresholdMap.putAll(getThresholdMap(times, txnKpiThresholdsList, kpi, ThresholdType.REALTIME.name()));

                }
                //Anomaly
                List<Anomalies> anomaliesList = anomalySearchRepo.getAllAnomaliesByKpi(accountIdentifier, null, transaction, Integer.parseInt(kpiId), fromTime, toTime);
                attributeAnomaliesCountMap.putAll(getKpiAnomaliesCountMap(times, anomaliesList, Long.valueOf(kpiId), kpiIdentifier, aggregationValue));
            }
        });
        //--------------------------------------------------------------------------------------------------------------------------
        Map<String, List<Integer>> attributeAlertMap = attributeAnomaliesCountMap.entrySet().parallelStream()
                .collect(Collectors.toMap(Map.Entry::getKey,
                        d -> d.getValue().parallelStream().map(f -> f > 0 ? 1 : 0).collect(Collectors.toList())));

        Map<String, List<Double>> finalAttributeKpiMinValueMap = attributeKpiMinValueMap;
        Map<String, List<Double>> finalAttributeKpiMaxValueMap = attributeKpiMaxValueMap;
        List<KpiData> kpiDataList = kpiNamesList.parallelStream().map(c -> {
            KpiData kpiData = new KpiData();
            if (AVG_RESPONSE_PERCENTILE_KPI_LITERAL.equalsIgnoreCase(kpiName)) {
                kpiData.setKpiId(0);
                kpiData.setGroupId(0);
                kpiData.setKpi_name(c);
                kpiData.setUnit(kpiDetailsMap.containsKey(AVG_RESPONSE_PERCENTILE_KPI_LITERAL) ? kpiDetailsMap.get(AVG_RESPONSE_PERCENTILE_KPI_LITERAL).get(0).getUnit() : "Milliseconds");
                kpiData.setGroupDisplayValue(kpiData.getKpi_name());
            } else {
                kpiData.setKpiId(kpiDetailsMap.containsKey(c) ? kpiDetailsMap.get(c).get(0).getId() : 0);
                kpiData.setGroupId(kpiDetailsMap.containsKey(c) ? kpiDetailsMap.get(c).get(0).getGroupId() : 0);
                kpiData.setKpi_name(kpiDetailsMap.containsKey(c) ? kpiDetailsMap.get(c).get(0).getName() : null);
                kpiData.setUnit(kpiDetailsMap.containsKey(c) ? kpiDetailsMap.get(c).get(0).getUnit() : null);
                if (!configData.getRequestPayloadObject().getTxnIdString().equalsIgnoreCase("0")) {
                    kpiData.setGroupDisplayValue(kpiData.getKpi_name());
                }
                if (kpiName.equalsIgnoreCase(Constants.TRANSACTION_RESPONSE_TIME)) {
                    kpiData.setMinResponse(finalAttributeKpiMinValueMap.get("MIN_" + c));
                    kpiData.setMaxResponse(finalAttributeKpiMaxValueMap.get("MAX_" + c));
                }

                if (aggregationValue == 1) {
                    kpiData.setStaticRange(staticKpiThresholdMap.get(c));
                    kpiData.setNormalRange(realTimeKpiThresholdMap.get(c));
                }

                kpiData.setAnomalyCounts(attributeAnomaliesCountMap.get(c));
                kpiData.setAlerts(attributeAlertMap.get(c));
                if (attributeAnomaliesCountMap.get(c).parallelStream().anyMatch(d -> d == 1)) {
                    kpiData.setAnomaly(true);
                }

            }
            kpiData.setValue(attributeKpiValueMap.get(c));

            return kpiData;
        }).collect(Collectors.toList());

        uiData.setChart_data(kpiDataList);

        log.debug("Time taken to process transaction performance time-series data is {} ms.", (System.currentTimeMillis() - start));

        return uiData;

    }

    private void addRemainingKpis(String accountIdentifier, List<AttributeKpiValuePojo> attributeKpiValuePojoList) {

        List<AttributeKpiValuePojo> tempAttributeKpiValuePojoList = new ArrayList<>(attributeKpiValuePojoList);
        List<String> kpiIdentifiers = new ComponentRepo().getComponentKpis(accountIdentifier, Constants.TRANSACTION_IDENTIFIER_DEFAULT)
                .parallelStream()
                .filter(c -> c.getStatus() == 1)
                .map(BasicKpiEntity::getIdentifier)
                .collect(Collectors.toList());

        Map<String, List<AttributeKpiValuePojo>> attributeKpiValueMap = attributeKpiValuePojoList.parallelStream()
                .collect(Collectors.groupingBy(AttributeKpiValuePojo::getAttributeName));

        Set<Long> times = attributeKpiValuePojoList.stream().map(c -> c.getValuesMap().keySet()).flatMap(Collection::stream).collect(Collectors.toSet());

        if (!attributeKpiValueMap.containsKey(KPIAttributes.TOTAL_VOLUME.name()) && kpiIdentifiers.contains(KPIAttributes.TOTAL_VOLUME.name())) {
            times.forEach(t -> {
                AtomicReference<Double> val = new AtomicReference<>(0D);
                tempAttributeKpiValuePojoList.forEach(c ->
                        val.set(Double.parseDouble(c.getValuesMap().getOrDefault(t, "0")) + val.get()));
                Map<Long, String> kpiValueMap = new HashMap<>();
                kpiValueMap.put(t, String.valueOf(val));
                attributeKpiValuePojoList.add(AttributeKpiValuePojo.builder()
                        .attributeName(KPIAttributes.TOTAL_VOLUME.name())
                        .valuesMap(kpiValueMap)
                        .build());
            });
        }

        if (!attributeKpiValueMap.containsKey(KPIAttributes.FAIL_VOLUME.name()) && kpiIdentifiers.contains(KPIAttributes.FAIL_VOLUME.name())) {
            getMissingRawKpisData(times, attributeKpiValuePojoList, KPIAttributes.FAIL_VOLUME.name());
        }

        if (!attributeKpiValueMap.containsKey(KPIAttributes.SLOW_VOLUME.name()) && kpiIdentifiers.contains(KPIAttributes.SLOW_VOLUME.name())) {
            getMissingRawKpisData(times, attributeKpiValuePojoList, KPIAttributes.SLOW_VOLUME.name());
        }

        if (!attributeKpiValueMap.containsKey(KPIAttributes.SUCCESS_VOLUME.name()) && kpiIdentifiers.contains(KPIAttributes.SUCCESS_VOLUME.name())) {
            getMissingRawKpisData(times, attributeKpiValuePojoList, KPIAttributes.SUCCESS_VOLUME.name());
        }

        if (!attributeKpiValueMap.containsKey(KPIAttributes.TIMEOUT_VOLUME.name()) && kpiIdentifiers.contains(KPIAttributes.TIMEOUT_VOLUME.name())) {
            getMissingRawKpisData(times, attributeKpiValuePojoList, KPIAttributes.TIMEOUT_VOLUME.name());
        }

        if (!attributeKpiValueMap.containsKey(KPIAttributes.UNKNOWN_VOLUME.name()) && kpiIdentifiers.contains(KPIAttributes.UNKNOWN_VOLUME.name())) {
            getMissingRawKpisData(times, attributeKpiValuePojoList, KPIAttributes.UNKNOWN_VOLUME.name());
        }
    }

    private void getMissingRawKpisData(Set<Long> times, List<AttributeKpiValuePojo> attributeKpiValuePojoList, String kpiIdentifier) {
        times.forEach(t -> {
            Map<Long, String> kpiValueMap = new HashMap<>();
            kpiValueMap.put(t, "0");
            attributeKpiValuePojoList.add(AttributeKpiValuePojo.builder()
                    .attributeName(kpiIdentifier)
                    .valuesMap(kpiValueMap)
                    .build());
        });
    }

    private List<AttributeKpiValuePojo> extractKPIDataFromRawTabularResult(TabularResults tabularResults) {
        List<AttributeKpiValuePojo> attributeKpiValuePojoList = new ArrayList<>();
        for (TabularResults.ResultRow resultRow : tabularResults.getRowResults()) {
            long time = resultRow.getTimestamp().getTime();
            Map<Long, String> timeValueMap = new LinkedHashMap<>();
            AttributeKpiValuePojo attributeKpiValuePojo = new AttributeKpiValuePojo();
            for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {
                if (resultRowColumn.getColumnName().equalsIgnoreCase("responseTimes.responseStatusTag")) {
                    attributeKpiValuePojo.setAttributeName(KPIAttributes.valueOfOpenSearchName(resultRowColumn.getColumnValue().toLowerCase()).name());
                }
            }
            timeValueMap.put(time, String.valueOf(resultRow.getCountValue()));
            attributeKpiValuePojo.setValuesMap(timeValueMap);
            attributeKpiValuePojoList.add(attributeKpiValuePojo);
        }
        return attributeKpiValuePojoList;
    }

    private List<AttributeKpiValuePojo> getResponseTimePercentileData(UtilityBean<TransactionKPITimeSeriesDataRequest> configData, String requestType, String accountIdentifier, String transaction, long fromTime, long toTime, int aggregationValue, String timezoneId) {
        RawTransactionSearchRepo rawTransactionSearchRepo = new RawTransactionSearchRepo();

        if (requestType.trim().equalsIgnoreCase(Constants.SERVICES_CONTROLLER_TYPE)) {
            TabularResults percentilesTabularResults = rawTransactionSearchRepo.getResponsePercentileDataClusterLevel(accountIdentifier, transaction,
                    configData.getApplication().getPercentiles().values().stream().flatMap(Collection::parallelStream).collect(Collectors.toSet()),
                    configData.getResponseType(), fromTime, toTime, aggregationValue, timezoneId);

            if (percentilesTabularResults == null || percentilesTabularResults.getRowResults() == null || percentilesTabularResults.getRowResults().isEmpty()) {
                return Collections.emptyList();
            }

            return extractPercentileDataFromTabularResult(percentilesTabularResults);

        } else {
            Set<String> agentList = configData.getRequestPayloadObject().getAgentList().parallelStream().collect(Collectors.toSet());

            TabularResults percentilesTabularResults = rawTransactionSearchRepo.getResponsePercentileDataInstanceLevel(accountIdentifier, transaction, agentList,
                    configData.getApplication().getPercentiles().values().stream().flatMap(Collection::parallelStream).collect(Collectors.toSet()),
                    configData.getResponseType(), fromTime, toTime, aggregationValue, timezoneId);

            if (percentilesTabularResults == null || percentilesTabularResults.getRowResults() == null || percentilesTabularResults.getRowResults().isEmpty()) {
                return Collections.emptyList();
            }

            return extractPercentileDataFromTabularResult(percentilesTabularResults);

        }
    }

    private void addPercentageKpis(String accountIdentifier, List<AttributeKpiValuePojo> attributeKpiValuePojoList) {

        List<BasicKpiEntity> basicKpiEntity = new ComponentRepo().getComponentKpis(accountIdentifier, Constants.TRANSACTION_IDENTIFIER_DEFAULT)
                .parallelStream()
                .filter(c -> c.getStatus() == 1 && c.getUnit().equalsIgnoreCase("Percentage"))
                .collect(Collectors.toList());
        if (basicKpiEntity.isEmpty()) {
            return;
        }

        Map<String, List<AttributeKpiValuePojo>> attributeKpiValueMap = attributeKpiValuePojoList.parallelStream()
                .collect(Collectors.groupingBy(AttributeKpiValuePojo::getAttributeName));
        Map<Long, String> totalTimeValueMap = new HashMap<>();
        attributeKpiValueMap.get(KPIAttributes.TOTAL_VOLUME.name())
                .forEach(c -> totalTimeValueMap.putAll(c.getValuesMap()));
        if (totalTimeValueMap.isEmpty()) {
            return;
        }

        basicKpiEntity.forEach(c -> {
            if (c.getIdentifier().equalsIgnoreCase(KPIAttributes.SLOW_PERCENTAGE.name())) {
                Map<Long, String> slowTimeValueMap = new HashMap<>();
                attributeKpiValueMap.get(KPIAttributes.SLOW_VOLUME.name())
                        .forEach(d -> slowTimeValueMap.putAll(d.getValuesMap()));
                attributeKpiValuePojoList.add(AttributeKpiValuePojo.builder()
                        .attributeName(KPIAttributes.SLOW_PERCENTAGE.name())
                        .valuesMap(calculatePercentageKpi(totalTimeValueMap, slowTimeValueMap))
                        .build());
            } else if (c.getIdentifier().equalsIgnoreCase(KPIAttributes.FAIL_PERCENTAGE.name())) {
                Map<Long, String> failTimeValueMap = new HashMap<>();
                attributeKpiValueMap.get(KPIAttributes.FAIL_VOLUME.name())
                        .forEach(d -> failTimeValueMap.putAll(d.getValuesMap()));
                attributeKpiValuePojoList.add(AttributeKpiValuePojo.builder()
                        .attributeName(KPIAttributes.FAIL_PERCENTAGE.name())
                        .valuesMap(calculatePercentageKpi(totalTimeValueMap, failTimeValueMap))
                        .build());
            } else if (c.getIdentifier().equalsIgnoreCase(KPIAttributes.TIMEOUT_PERCENTAGE.name())) {
                Map<Long, String> timeOutTimeValueMap = new HashMap<>();
                attributeKpiValueMap.get(KPIAttributes.TIMEOUT_VOLUME.name())
                        .forEach(d -> timeOutTimeValueMap.putAll(d.getValuesMap()));
                attributeKpiValuePojoList.add(AttributeKpiValuePojo.builder()
                        .attributeName(KPIAttributes.TIMEOUT_PERCENTAGE.name())
                        .valuesMap(calculatePercentageKpi(totalTimeValueMap, timeOutTimeValueMap))
                        .build());
            }
        });
    }

    private Map<Long, String> calculatePercentageKpi(Map<Long, String> totalTimeValueMap, Map<Long, String> slowTimeValueMap) {
        Map<Long, String> result = new HashMap<>();
        totalTimeValueMap.forEach((key, denominator) -> {
            String numerator = slowTimeValueMap.getOrDefault(key, null);
            if (numerator == null || denominator == null) {
                result.put(key, "0");
                return;
            }

            double num = Double.parseDouble(numerator);
            double den = Double.parseDouble(denominator);
            if (den == 0D) {
                result.put(key, "0");
                return;
            }

            double res = (num / den) * 100;
            result.put(key, String.valueOf(res));
        });

        return result;
    }

    private Map<String, Thresholds> getThresholdMap(List<Long> times, List<TransactionKpiThresholds> txnKpiThresholdsList, String kpiIdentifier, String thresholdType) {

        Map<String, Thresholds> thresholdsMap = new LinkedHashMap<>();

        if (!txnKpiThresholdsList.isEmpty()) {
            //don't use parallelStream with foreach
            txnKpiThresholdsList.stream().filter(c -> c.getThresholdType().equalsIgnoreCase(thresholdType)).distinct().forEach(c -> {
                Thresholds thresholds = new Thresholds();
                List<Double> minThreshold = new ArrayList<>();
                List<Double> maxThreshold = new ArrayList<>();
                List<String> operationList = new ArrayList<>();

                times.forEach(t -> {
                    if (t >= c.getStartTime() && (t <= c.getEndTime() || c.getEndTime() == 0)) {
                        OperationType opEnum = OperationType.fromString(c.getOperationType());
                        switch (opEnum) {
                            case LESSER_THAN:
                            case BETWEEN:
                            case NOT_BETWEEN:
                                if (thresholdType.equalsIgnoreCase(ThresholdType.REALTIME.name()) && kpiIdentifier.equalsIgnoreCase(Constants.TRANSACTION_RESPONSE_TIME)) {
//                                    minThreshold.add(c.getThresholds().get(LOWER_IDENTIFIER) != null ? c.getThresholds().get(LOWER_IDENTIFIER) / 1000 : null);
//                                    maxThreshold.add(c.getThresholds().get(UPPER_IDENTIFIER) != null ? c.getThresholds().get(UPPER_IDENTIFIER) / 1000 : null);
                                    minThreshold.add(c.getThresholds().get(LOWER_IDENTIFIER) != null ? c.getThresholds().get(LOWER_IDENTIFIER) : null);
                                    maxThreshold.add(c.getThresholds().get(UPPER_IDENTIFIER) != null ? c.getThresholds().get(UPPER_IDENTIFIER) : null);
                                } else {
                                    minThreshold.add(c.getThresholds().getOrDefault(LOWER_IDENTIFIER, null));
                                    maxThreshold.add(c.getThresholds().getOrDefault(UPPER_IDENTIFIER, null));
                                }
                                break;
                            case GREATER_THAN:
                                if (thresholdType.equalsIgnoreCase(ThresholdType.REALTIME.name()) && kpiIdentifier.equalsIgnoreCase(Constants.TRANSACTION_RESPONSE_TIME)) {
//                                    minThreshold.add(c.getThresholds().get(UPPER_IDENTIFIER) != null ? c.getThresholds().get(UPPER_IDENTIFIER) / 1000 : null);
//                                    maxThreshold.add(c.getThresholds().get(LOWER_IDENTIFIER) != null ? c.getThresholds().get(LOWER_IDENTIFIER) / 1000 : null);
                                    minThreshold.add(c.getThresholds().get(UPPER_IDENTIFIER) != null ? c.getThresholds().get(UPPER_IDENTIFIER) : null);
                                    maxThreshold.add(c.getThresholds().get(LOWER_IDENTIFIER) != null ? c.getThresholds().get(LOWER_IDENTIFIER) : null);
                                } else {
                                    minThreshold.add(c.getThresholds().getOrDefault(UPPER_IDENTIFIER, null));
                                    maxThreshold.add(c.getThresholds().getOrDefault(LOWER_IDENTIFIER, null));
                                }
                                break;
                            default:
                                minThreshold.add(null);
                                maxThreshold.add(null);
                        }
                        operationList.add(c.getOperationType());
                    } else {
                        minThreshold.add(null);
                        maxThreshold.add(null);
                        operationList.add(null);
                    }
                });
                thresholds.setMaxThreshold(maxThreshold);
                thresholds.setMinThreshold(minThreshold);
                thresholds.setOperationList(operationList);

                if (thresholdsMap.containsKey(kpiIdentifier)) {
                    thresholdsMap.put(kpiIdentifier, enrichThresholdMap(thresholds, thresholdsMap.get(kpiIdentifier)));
                } else
                    thresholdsMap.put(kpiIdentifier, thresholds);
            });

        } else {

            Thresholds thresholds = new Thresholds();
            List<Double> minThreshold = new ArrayList<>();
            List<Double> maxThreshold = new ArrayList<>();
            List<String> operationList = new ArrayList<>();
            times.forEach(t -> {
                minThreshold.add(null);
                maxThreshold.add(null);
                operationList.add(null);
            });
            thresholds.setMaxThreshold(maxThreshold);
            thresholds.setMinThreshold(minThreshold);
            thresholds.setOperationList(operationList);

            thresholdsMap.put(kpiIdentifier, thresholds);

        }

        return thresholdsMap;
    }

    private Thresholds enrichThresholdMap(Thresholds newThresholds, Thresholds oldThresholds) {

        if (isNull(newThresholds)) {
            return oldThresholds;
        } else if (isNull(oldThresholds)) {
            return newThresholds;
        } else {
            Thresholds thresholds = new Thresholds();
            List<Double> minThreshold = new ArrayList<>();
            List<Double> maxThreshold = new ArrayList<>();
            List<String> operationList = new ArrayList<>();

            List<Double> maxOld = oldThresholds.getMaxThreshold();
            List<Double> maxNew = newThresholds.getMaxThreshold();
            List<Double> minOld = oldThresholds.getMinThreshold();
            List<Double> minNew = newThresholds.getMinThreshold();
            List<String> opOld = oldThresholds.getOperationList();
            List<String> opNew = newThresholds.getOperationList();
            for (int i = 0; i < maxOld.size(); i++) {
                if (maxOld.get(i) == null && minOld.get(i) == null && opOld.get(i) == null) {
                    maxThreshold.add(i, maxNew.get(i));
                    minThreshold.add(i, minNew.get(i));
                    operationList.add(i, opNew.get(i));
                } else {
                    maxThreshold.add(i, maxOld.get(i));
                    minThreshold.add(i, minOld.get(i));
                    operationList.add(i, opOld.get(i));
                }
            }

            thresholds.setMinThreshold(minThreshold);
            thresholds.setMaxThreshold(maxThreshold);
            thresholds.setOperationList(operationList);

            return thresholds;
        }
    }

    private boolean isNull(Thresholds kpiThreshold) {

        Optional<String> operationList = kpiThreshold.getOperationList().parallelStream().filter(Objects::nonNull).findAny();
        Optional<Double> maxThreshold = kpiThreshold.getMaxThreshold().parallelStream()
                .filter(Objects::nonNull).findAny();
        Optional<Double> minThreshold = kpiThreshold.getMinThreshold().parallelStream()
                .filter(Objects::nonNull).findAny();

        return (!maxThreshold.isPresent() && !minThreshold.isPresent()) || !operationList.isPresent();
    }

    private Map<String, List<Integer>> getKpiAnomaliesCountMap(List<Long> times, List<Anomalies> anomaliesList, Long kpiId, String kpiIdentifier, int aggregationValue) {

        Map<String, List<Integer>> kpiAnomaliesMap = new LinkedHashMap<>();

        Map<Long, List<Long>> kpiAnomalyTimeMap = anomaliesList.parallelStream().collect(Collectors.groupingBy(Anomalies::getKpiId,
                Collectors.mapping(Anomalies::getAnomalyTime, Collectors.toList())));

        List<Integer> anomaliesCountList = new ArrayList<>();
        if (kpiAnomalyTimeMap.containsKey(kpiId)) {
            List<Long> anomalyTimes = kpiAnomalyTimeMap.get(kpiId);
            if (aggregationValue == 1) {
                times.forEach(t -> {
                    if (anomalyTimes.contains(t)) {
                        anomaliesCountList.add(1);
                    } else {
                        anomaliesCountList.add(0);
                    }
                });
            } else {
                for (int i = 0; i < times.size() - 1; i++) {
                    int finalI = i;
                    if (anomalyTimes.contains(times.get(i)) || anomalyTimes.contains(times.get(i + 1))) {
                        anomaliesCountList.add(1);
                    } else if (anomalyTimes.parallelStream().anyMatch(a -> times.get(finalI) < a && times.get(finalI + 1) > a)) {
                        long count = anomalyTimes.parallelStream().filter(a -> times.get(finalI) < a && times.get(finalI + 1) > a).count();
                        anomaliesCountList.add((int) count);
                    } else {
                        anomaliesCountList.add(0);
                    }

                }
            }

            kpiAnomaliesMap.put(kpiIdentifier, anomaliesCountList);
        } else {
            times.forEach(t -> anomaliesCountList.add(0));
            kpiAnomaliesMap.put(kpiIdentifier, anomaliesCountList);
        }

        return kpiAnomaliesMap;
    }

    private List<AttributeKpiValuePojo> extractPercentileDataFromCollatedDataList(List<CollatedTransactionData> collatedTransactionDataList, String responseType) {

        return collatedTransactionDataList.parallelStream()
                .map(c -> {

                    Map<String, Double> percentilesMap;
                    if (responseType.equals(TransactionResponseTypes.DC.name())) {
                        percentilesMap = c.getDcPercentiles();
                    } else if (responseType.equals(TransactionResponseTypes.EUM.name())) {
                        percentilesMap = c.getEumPercentiles();
                    } else {
                        percentilesMap = c.getRenderingPercentiles();
                    }

                    List<AttributeKpiValuePojo> attributeKpiValuePojoList = new ArrayList<>();

                    percentilesMap.forEach((key, value) -> {
                        Map<Long, String> timeValueMap = new LinkedHashMap<>();
//                        timeValueMap.put(c.getTimeInGMT(), String.valueOf(value / 1000));
                        timeValueMap.put(c.getTimeInGMT(), String.valueOf(value));
                        attributeKpiValuePojoList.add(AttributeKpiValuePojo.builder()
                                .attributeName(key.split("_")[0])
                                .valuesMap(timeValueMap)
                                .build());
                    });
                    return attributeKpiValuePojoList;
                })
                .flatMap(Collection::parallelStream)
                .collect(Collectors.toList());
    }

    private List<AttributeKpiValuePojo> extractKpiDataFromCollatedDataList(List<CollatedTransactionData> collatedTransactionDataList, String responseType) {

        return collatedTransactionDataList.parallelStream()
                .map(c -> {
                    Map<String, Double> kpiMap;
                    if (responseType.equals(TransactionResponseTypes.DC.name())) {
                        kpiMap = c.getDcKpis();
                    } else if (responseType.equals(TransactionResponseTypes.EUM.name())) {
                        kpiMap = c.getEumKpis();
                    } else {
                        kpiMap = c.getRenderingKpis();
                    }

                    List<AttributeKpiValuePojo> attributeKpiValuePojoList = new ArrayList<>();

                    kpiMap.forEach((key, value) -> {
                        Map<Long, String> timeValueMap = new LinkedHashMap<>();
                        timeValueMap.put(c.getTimeInGMT(), String.valueOf(value));
                        attributeKpiValuePojoList.add(AttributeKpiValuePojo.builder()
                                .attributeName(key)
                                .valuesMap(timeValueMap)
                                .build());
                    });
                    return attributeKpiValuePojoList;
                })
                .flatMap(Collection::parallelStream)
                .collect(Collectors.toList());
    }

    private Map<String, List<Double>> getKpiValue(List<Long> times, List<AttributeKpiValuePojo> attributeKpiValuePojoList, List<String> kpiNamesList) {

        Map<String, List<Double>> attributeKpiValueMap = new LinkedHashMap<>();

        Map<String, List<AttributeKpiValuePojo>> attributeKpiPojoMap = attributeKpiValuePojoList.parallelStream()
                .collect(Collectors.groupingBy(AttributeKpiValuePojo::getAttributeName));

        for (String kpiName : kpiNamesList) {
            List<Double> values = new ArrayList<>();
            if (!attributeKpiPojoMap.containsKey(kpiName)) {
                times.forEach(t -> values.add(null));
            } else {
                Map<Long, String> valuesMapFinal = new LinkedHashMap<>();
                Map<Long, Integer> timeRepeatMap = new HashMap<>();

                attributeKpiPojoMap.get(kpiName).forEach(c -> c.getValuesMap().forEach((key, value) -> {
                    if (valuesMapFinal.containsKey(key)) {
                        timeRepeatMap.replace(key, timeRepeatMap.get(key) + 1);

                        if (kpiName.equalsIgnoreCase(Constants.TRANSACTION_VOLUME)) {
                            valuesMapFinal.replace(key, String.valueOf(Double.parseDouble(valuesMapFinal.get(key)) + Double.parseDouble(value)));
                        } else {
                            String currentAvgValue = String.valueOf(((Double.parseDouble(valuesMapFinal.get(key)) * (timeRepeatMap.get(key) - 1)) + Double.parseDouble(value)) / timeRepeatMap.get(key));
                            valuesMapFinal.replace(key, currentAvgValue);
                        }

                    } else {
                        valuesMapFinal.put(key, value);
                        timeRepeatMap.put(key, 0);
                    }
                }));

                if (kpiName.equalsIgnoreCase(Constants.TRANSACTION_RESPONSE_TIME) ||
                        kpiName.equalsIgnoreCase("MIN_" + Constants.TRANSACTION_RESPONSE_TIME) ||
                        kpiName.equalsIgnoreCase("MAX_" + Constants.TRANSACTION_RESPONSE_TIME)) {
//                    times.forEach(t -> values.add(!valuesMapFinal.containsKey(t) ? null : Double.parseDouble(valuesMapFinal.get(t)) / 1000));
                    times.forEach(t -> values.add(!valuesMapFinal.containsKey(t) ? null : Double.parseDouble(valuesMapFinal.get(t))));
                } else {
                    times.forEach(t -> values.add(!valuesMapFinal.containsKey(t) ? null : Double.parseDouble(valuesMapFinal.get(t))));
                }
            }
            attributeKpiValueMap.put(kpiName, values);
        }

        return attributeKpiValueMap;

    }

    private void extractRTDataFromRawTabularResult(TabularResults tabularResults, String kpiName, List<AttributeKpiValuePojo> attributeKpiValuePojoList,
                                                   List<AttributeKpiValuePojo> attributeKpiMaxValuePojoList, List<AttributeKpiValuePojo> attributeKpiMinValuePojoList) {

        for (TabularResults.ResultRow resultRow : tabularResults.getRowResults()) {
            long time = resultRow.getTimestamp().getTime();
            for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {
                AttributeKpiValuePojo attributeKpiValuePojo = new AttributeKpiValuePojo();
                Map<Long, String> timeValueMap = new LinkedHashMap<>();
                if (resultRowColumn.getColumnName().equalsIgnoreCase("AVERAGE: responseTimes.responseInMicroseconds")) {
                    attributeKpiValuePojo.setAttributeName(kpiName);
                    timeValueMap.put(time, resultRowColumn.getColumnValue());
                    attributeKpiValuePojo.setValuesMap(timeValueMap);
                    attributeKpiValuePojoList.add(attributeKpiValuePojo);
                } else if (resultRowColumn.getColumnName().equalsIgnoreCase("MAX: responseTimes.responseInMicroseconds")) {
                    attributeKpiValuePojo.setAttributeName("MAX_" + kpiName);
                    timeValueMap.put(time, resultRowColumn.getColumnValue());
                    attributeKpiValuePojo.setValuesMap(timeValueMap);
                    attributeKpiMaxValuePojoList.add(attributeKpiValuePojo);
                } else if (resultRowColumn.getColumnName().equalsIgnoreCase("MIN: responseTimes.responseInMicroseconds")) {
                    attributeKpiValuePojo.setAttributeName("MIN_" + kpiName);
                    timeValueMap.put(time, resultRowColumn.getColumnValue());
                    attributeKpiValuePojo.setValuesMap(timeValueMap);
                    attributeKpiMinValuePojoList.add(attributeKpiValuePojo);
                }
            }
        }

    }

    private List<AttributeKpiValuePojo> extractDataFromTabularResult(List<TabularResultsTypePojo> tabularResultsBooleanList) {

        List<AttributeKpiValuePojo> attributeKpiValuePojoList = new ArrayList<>();

        String pattern = ".+\\..*";
        for (TabularResultsTypePojo tabularResultsTypePojo : tabularResultsBooleanList) {
            TabularResults tabularResults = tabularResultsTypePojo.getTabularResults();

            List<AttributeKpiValuePojo> tempAttributeKpiValuePojoList = new ArrayList<>();

            for (TabularResults.ResultRow resultRow : tabularResults.getRowResults()) {
                long time = resultRow.getTimestamp().getTime();
                for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {
                    AttributeKpiValuePojo attributeKpiValuePojo = new AttributeKpiValuePojo();
                    if (resultRowColumn.getColumnName().matches(pattern)) {
                        Map<Long, String> timeValueMap = new LinkedHashMap<>();

                        attributeKpiValuePojo.setAttributeName(resultRowColumn.getColumnName().split("\\.")[1]);
                        timeValueMap.put(time, resultRowColumn.getColumnValue());
                        attributeKpiValuePojo.setValuesMap(timeValueMap);
                        tempAttributeKpiValuePojoList.add(attributeKpiValuePojo);
                    }
                }
            }

            tempAttributeKpiValuePojoList.forEach(t -> {
                Map<String, List<AttributeKpiValuePojo>> attributeKpiPojoMap = attributeKpiValuePojoList.parallelStream()
                        .collect(Collectors.groupingBy(AttributeKpiValuePojo::getAttributeName));

                if (attributeKpiPojoMap.containsKey(t.getAttributeName())) {
                    Map<Long, String> tMap = new HashMap<>();
                    attributeKpiPojoMap.get(t.getAttributeName()).stream().map(AttributeKpiValuePojo::getValuesMap)
                            .flatMap(map -> map.entrySet().stream())
                            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue))
                            .forEach((key, value) -> {
                                if (t.getValuesMap().containsKey(key)) {
                                    String v1 = t.getValuesMap().get(key);
                                    tMap.put(key, String.valueOf(Double.parseDouble(v1) + Double.parseDouble(value)));
                                } else {
                                    tMap.putAll(t.getValuesMap());
                                }

                            });
                    attributeKpiValuePojoList.add(AttributeKpiValuePojo.builder()
                            .attributeName(t.getAttributeName())
                            .valuesMap(tMap).build());
                } else attributeKpiValuePojoList.add(t);
            });
        }

        return attributeKpiValuePojoList;
    }

    private List<AttributeKpiValuePojo> extractPercentileDataFromTabularResult(TabularResults tabularResults) {

        List<AttributeKpiValuePojo> attributeKpiValuePojoList = new ArrayList<>();

        for (TabularResults.ResultRow resultRow : tabularResults.getRowResults()) {
            long time = resultRow.getTimestamp().getTime();
            for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {
                if (resultRowColumn.getColumnDataType().contains("Double")) {
                    AttributeKpiValuePojo attributeKpiValuePojo = new AttributeKpiValuePojo();

                    Map<Long, String> timeValueMap = new LinkedHashMap<>();

                    attributeKpiValuePojo.setAttributeName(resultRowColumn.getColumnName());
//                    timeValueMap.put(time, String.valueOf(Double.parseDouble(resultRowColumn.getColumnValue()) / 1000));
                    timeValueMap.put(time, String.valueOf(Double.parseDouble(resultRowColumn.getColumnValue())));
                    attributeKpiValuePojo.setValuesMap(timeValueMap);
                    attributeKpiValuePojoList.add(attributeKpiValuePojo);

                }

            }
        }
        return attributeKpiValuePojoList;
    }

}
