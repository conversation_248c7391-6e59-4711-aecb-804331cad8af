package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.TimeRangeDetailsDao;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.TimeRangesPojo;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.StringUtils;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.TimeRangeDetails;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class TimeRangesBL implements BusinessLogic<String, UtilityBean<String>, TimeRangesPojo> {
    @Override
    public UtilityBean<String> clientValidation(RequestObject requestObject) throws ClientException {
        log.trace("Inside Client validation");

        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authToken = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authToken == null || authToken.trim().isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String accountIdentifier = requestObject.getParams().get(Constants.REQUEST_PARAM_IDENTIFIER);
        if (StringUtils.isEmpty(accountIdentifier)) {
            log.error(Constants.MESSAGE_EMPTY_ACCOUNT);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        return UtilityBean.<String>builder()
                .authToken(authToken)
                .accountIdString(accountIdentifier)
                .build();
    }

    @Override
    public UtilityBean<String> serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        AccountRepo accountRepo = new AccountRepo();

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error(Constants.MESSAGE_INVALID_AUTH_TOKEN);
            throw new ServerException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }
        utilityBean.setUserId(userId);

        Account account = accountRepo.getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            log.error(UIMessages.ERROR_INVALID_ACCOUNT_ID);
            throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
        }
        utilityBean.setAccount(account);

        return utilityBean;
    }

    @Override
    public TimeRangesPojo processData(UtilityBean<String> utilityBean) throws DataProcessingException {

        String accountIdentifier = utilityBean.getAccount().getIdentifier();
        TimeRangesPojo timeRangesPojo = new TimeRangesPojo();

        TimeRangeDetailsDao timeRangeDetailsDao = new TimeRangeDetailsDao();

        //Fetch for current account...if not found then fetch for global account
        List<TimeRangeDetails> timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(accountIdentifier);
        if (timeRangeDetailsList == null) {
            log.error("Exception occurred while fetching time range details for account {}", accountIdentifier);
            throw new DataProcessingException("Exception occurred while fetching time range details for account " + accountIdentifier);
        }

        if (timeRangeDetailsList.isEmpty()) {
            log.trace("No time range details found for account {}. Searching for Global account.", accountIdentifier);
            timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }
        if (timeRangeDetailsList == null) {
            log.error("Exception occurred while fetching time range details for account {}", Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
            throw new DataProcessingException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }

        if (timeRangeDetailsList.isEmpty()) {
            log.error("No time range details found");
            return null;
        }
        log.debug("Time range details found for account {}", Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);

        List<TimeRangesPojo.NameValuePair> nameValuePairList = timeRangeDetailsList.parallelStream()
                .map(c -> {
                    String weekInNumber = String.valueOf(c.getLookBackTime() / 7);
                    String weekInString = weekInNumber + "weeks";
                    return TimeRangesPojo.NameValuePair.builder().name(weekInString).value(weekInNumber).build();
                })
                .distinct()
                .collect(Collectors.toList());

        Map<String, Integer> timeRangeDetailsMap = timeRangeDetailsList.parallelStream()
                .collect(Collectors.toMap(TimeRangeDetails::getName, c -> c.getLookBackTime() / 7));

        timeRangesPojo.setRanges(nameValuePairList);

        Map<String, Map<String, Boolean>> allowedRangeMap = new HashMap<>();
        timeRangeDetailsList.stream()
                .collect(Collectors.toMap(TimeRangeDetails::getName, TimeRangeDetails::getDisplayName))
                .forEach((key, value) -> {
                    if (!allowedRangeMap.containsKey(value)) {
                        allowedRangeMap.put(value, getAllowedRanges(key, nameValuePairList, timeRangeDetailsMap));
                    }
                });

        timeRangesPojo.setRangeEnabled(allowedRangeMap);
        return timeRangesPojo;
    }

    private Map<String, Boolean> getAllowedRanges(String key, List<TimeRangesPojo.NameValuePair> nameValuePairList, Map<String, Integer> timeRangeDetailsList) {
        Map<String, Boolean> allowedRangesMap = new HashMap<>();
        nameValuePairList.forEach(c -> {
            int limit = timeRangeDetailsList.get(key);
            boolean rangeEnabled = limit >= Integer.parseInt(c.getValue());
            allowedRangesMap.put(c.getName(), rangeEnabled);
        });
        return allowedRangesMap;
    }
}
