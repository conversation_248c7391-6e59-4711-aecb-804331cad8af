package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.opensearch.RawTransactionSearchRepo;
import com.appnomic.appsone.api.dao.redis.*;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.TransactionAuditData;
import com.appnomic.appsone.api.pojo.TransactionQueryParams;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.StringUtils;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.heal.configuration.pojos.*;
import com.heal.configuration.pojos.opensearch.RawTransactionData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

public class TransactionAuditDataBL implements BusinessLogic<Integer, TransactionQueryParams, List<TransactionAuditData>> {

    private static final Logger LOGGER = LoggerFactory.getLogger(TransactionAuditDataBL.class);

    private static final String DATE_TIME_PATTERN_MILLIS = "yyyy-MM-dd HH:mm:ss.SSS";

    private static final String DATE_TIME_PATTERN_MICROS = "yyyy-MM-dd HH:mm:ss.SSSSSS";


    @Override
    public UtilityBean<Integer> clientValidation(RequestObject request) throws ClientException {

        if (StringUtils.isEmpty(request.getHeaders().get(Constants.AUTHORIZATION_HEADER))) {
            LOGGER.error(Constants.MESSAGE_EMPTY_AUTH_TOKEN);
            throw new ClientException(Constants.MESSAGE_EMPTY_AUTH_TOKEN);
        }

        if (StringUtils.isEmpty(request.getParams().get(Constants.REQUEST_PARAM_IDENTIFIER))) {
            LOGGER.error(Constants.MESSAGE_EMPTY_ACCOUNT);
            throw new ClientException(Constants.MESSAGE_EMPTY_ACCOUNT);
        }


        if (StringUtils.isEmpty(request.getParams().get(Constants.REQUEST_PARAM_SERVICE_ID))) {
            LOGGER.error("Service Id is null or empty.");
            throw new ClientException(Constants.MESSAGE_INVALID_SERVICE);
        }
        int serviceId;
        try {
            serviceId = Integer.parseInt(request.getParams().get(Constants.REQUEST_PARAM_SERVICE_ID));
        } catch (Exception e) {
            LOGGER.error("Service Id should be a positive integer.");
            throw new ClientException(UIMessages.INVALID_SERVICE);
        }


        if (StringUtils.isEmpty(request.getParams().get(Constants.REQUEST_PARAM_COM_INSTANCE_ID))) {
            LOGGER.error("Instance Id is null or empty.");
            throw new ClientException(Constants.MESSAGE_INVALID_COMP_INSTANCE);
        }
        int instanceId;
        try {
            instanceId = Integer.parseInt(request.getParams().get(Constants.REQUEST_PARAM_COM_INSTANCE_ID));
        } catch (Exception e) {
            LOGGER.error("Instance Id should be a positive integer.");
            throw new ClientException(UIMessages.INVALID_INSTANCE_MESSAGE);
        }

        if (StringUtils.isEmpty(request.getParams().get(Constants.REQUEST_PARAM_TXN_ID))) {
            LOGGER.error("Transaction Id is null or empty.");
            throw new ClientException(UIMessages.INVALID_TXN_ID);
        }
        int txnId;
        try {
            txnId = Integer.parseInt(request.getParams().get(Constants.REQUEST_PARAM_TXN_ID));
        } catch (Exception e) {
            LOGGER.error("Transaction Id should be a positive integer.");
            throw new ClientException(UIMessages.INVALID_TXN_ID);
        }

        if (StringUtils.isEmpty(request.getQueryParams().get(Constants.REQUEST_PARAM_FROM_TIME)[0]) ||
                StringUtils.isEmpty(request.getQueryParams().get(Constants.REQUEST_PARAM_TO_TIME)[0])) {
            LOGGER.error("fromTime and toTime should not be null or empty.");
            throw new ClientException("Invalid fromTime or toTime.");
        }
        long fromTime;
        long toTime;
        try {
            fromTime = Long.parseLong(request.getQueryParams().get(Constants.REQUEST_PARAM_FROM_TIME)[0]);
            toTime = Long.parseLong(request.getQueryParams().get(Constants.REQUEST_PARAM_TO_TIME)[0]);
            if (fromTime > toTime) {
                LOGGER.error("toTime should be greater than fromTime.");
                throw new ClientException("Invalid fromTime or toTime.");
            }
        } catch (Exception e) {
            LOGGER.error("fromTime or toTime should be positive numbers.");
            throw new ClientException("Invalid fromTime or toTime.");
        }

        if (StringUtils.isEmpty(request.getQueryParams().get(Constants.REQUEST_PARAM_TYPE)[0])) {
            LOGGER.error("Request \"type\" should not be null or empty.");
            throw new ClientException("Invalid \"type\" value provided in request parameters.");
        }
        String type = request.getQueryParams().get(Constants.REQUEST_PARAM_TYPE)[0];

        return UtilityBean.<Integer>builder()
                .accountIdString(request.getParams().get(Constants.REQUEST_PARAM_IDENTIFIER))
                .serviceId(serviceId)
                .componentInstanceIdString(String.valueOf(instanceId))
                .transactionId(txnId)
                .fromTime(fromTime)
                .toTime(toTime)
                .requestTypeString(type)
                .authToken(request.getHeaders().get(Constants.AUTHORIZATION_HEADER))
                .build();
    }

    @Override
    public TransactionQueryParams serverValidation(UtilityBean<Integer> utilityBean) throws ServerException {
        AccountRepo accountRepo = new AccountRepo();
        ServiceRepo serviceRepo = new ServiceRepo();
        InstanceRepo instanceRepo = new InstanceRepo();
        UserRepo userRepo = new UserRepo();
        TransactionRepo txnRepo = new TransactionRepo();

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            LOGGER.error(Constants.MESSAGE_INVALID_AUTH_TOKEN);
            throw new ServerException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }

        User user = userRepo.getUser(userId);
        if (user == null) {
            throw new ServerException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }

        Account account = accountRepo.getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            LOGGER.error("Invalid account identifier: {}.", utilityBean.getAccountIdString());
            throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
        }

        BasicEntity serviceDetails = serviceRepo.getBasicServiceDetailsWithServiceId(account.getIdentifier(), utilityBean.getServiceId());
        if (serviceDetails == null) {
            String error = "Service id is invalid";
            LOGGER.error(error);
            throw new ServerException(error);
        }

        CompInstClusterDetails instanceDetails = new CompInstClusterDetails();
        if (utilityBean.getRequestTypeString().trim().equalsIgnoreCase(Constants.COM_INSTANCE_TYPE)) {
            instanceDetails = instanceRepo.getInstancesByAccount(account.getIdentifier())
                    .parallelStream().filter(c -> c.getId() == Integer.parseInt(utilityBean.getComponentInstanceIdString())).findAny().orElse(null);
            if (instanceDetails == null) {
                LOGGER.error("Instance id is invalid.");
                throw new ServerException("Instance id is invalid.");
            }
        }

        Map<String, String> tagMap = account.getTags().parallelStream().collect(Collectors.toMap(Tags::getType, Tags::getValue));

        Transaction transaction = txnRepo.getTransactionDetailsById(account.getIdentifier(), utilityBean.getTransactionId());
        if (transaction == null) {
            LOGGER.error("Transaction id is invalid.");
            throw new ServerException("Invalid transaction Id provided for the account.");
        }

        TransactionQueryParams transactionQueryParams = TransactionQueryParams.builder()
                .txn(transaction)
                .accountId(account.getIdentifier())
                .serviceId(serviceDetails.getIdentifier())
                .timezoneOffsetFromGMT(tagMap.containsKey("Timezone") ? Long.parseLong(tagMap.get("Timezone")) : Long.parseLong(Constants.OFFSET_FROM_GMT_DEFAULT))
                .transactionId(utilityBean.getTransactionId())
                .txnId(transaction.getIdentifier())
                .responseType(Constants.TRANSACTION_TYPE_DEFAULT)
                .fromTime(utilityBean.getFromTime())
                .toTime(utilityBean.getToTime())
                .requestType(utilityBean.getRequestTypeString())
                .build();

        //GET Agent Data
        List<String> agentList;
        if (utilityBean.getRequestTypeString().trim().equalsIgnoreCase(Constants.SERVICES_CONTROLLER_TYPE)) {
            return transactionQueryParams;
        } else if (utilityBean.getRequestTypeString().trim().equalsIgnoreCase(Constants.COM_INSTANCE_TYPE)) {
            agentList = instanceDetails.getAgentIds();
            if (agentList == null || agentList.isEmpty()) {
                LOGGER.error("Unable to fetch agents for given service/comp instance: {}", utilityBean.getServiceIdString());
                return transactionQueryParams;
            }
            transactionQueryParams.setAgentIdList(agentList.parallelStream().collect(Collectors.toSet()));
        } else {
            LOGGER.error("Invalid request type provided: {}", utilityBean.getRequestTypeString());
            throw new ServerException(String.format("Invalid request type provided: %s", utilityBean.getRequestTypeString()));
        }

        return transactionQueryParams;
    }

    @Override
    public List<TransactionAuditData> processData(TransactionQueryParams request) throws DataProcessingException {
        RawTransactionSearchRepo txnRepo = new RawTransactionSearchRepo();

        List<RawTransactionData> auditTxn;
        if (request.getRequestType().trim().equalsIgnoreCase(Constants.SERVICES_CONTROLLER_TYPE)) {
            auditTxn = txnRepo.getAuditTransactionAtClusterLevel(request.getAccountId(), request.getTxn().getIdentifier(), request.getFromTime(), request.getToTime());
        } else {
            auditTxn = txnRepo.getAuditTransactionAtInstanceLevel(request.getAccountId(), request.getTxn().getIdentifier(), request.getAgentIdList(),
                    request.getFromTime(), request.getToTime());
        }

        TimeZone.setDefault(TimeZone.getTimeZone("GMT"));

        Map<String, String> displayNameMapping = request.getTxn().getAuditConfigurations();
        return auditTxn.parallelStream()
                .map(row -> {
                    List<TransactionAuditData.Details> details = new ArrayList<>();
                    displayNameMapping.forEach((key, val) -> details.add(TransactionAuditData.Details.builder()
                            .displayName(displayNameMapping.get(key))
                            .lookupName(key)
                            .value(row.getContext().getOrDefault(key, "NA"))
                            .build()));

                    String stTime = row.getResponseTimes().get(0).getStartTimeInGMT();
                    String endTime = row.getResponseTimes().get(0).getEndTimeInGMT();

                    long startTimeLong = getDateInLong(stTime, DATE_TIME_PATTERN_MICROS);
                    long endTimeLong = getDateInLong(endTime, DATE_TIME_PATTERN_MICROS);

                    return TransactionAuditData.builder()
                            .startTime(startTimeLong == 0 ? getDateInLong(stTime, DATE_TIME_PATTERN_MILLIS) : startTimeLong)
                            .endTime(endTimeLong == 0 ? getDateInLong(endTime, DATE_TIME_PATTERN_MILLIS) : endTimeLong)
                            .responseTime(row.getResponseTimes().get(0).getResponseInMicroseconds())
                            .status(row.getResponseTimes().get(0).getResponseStatusTag())
                            .operationType(row.getResponseTimes().get(0).getOperationType())
                            .thresholdType(row.getResponseTimes().get(0).getThresholdType())
                            .thresholds(row.getResponseTimes().get(0).getThresholds())
                            .responseCode(row.getHttpTransaction().getResponseCode())
                            .clientIP(row.getClientIp())
                            .clientPort(String.valueOf(row.getClientPort()))
                            .serverIP(row.getServerIp())
                            .serverPort(String.valueOf(row.getServerPort()))
                            .additionalDetails(details)
                            .build();
                }).collect(Collectors.toList());
    }


    Long getDateInLong(String dateStr, String formatter) {
        long result = 0;
        SimpleDateFormat df = new SimpleDateFormat(formatter);

        try {
            result = df.parse(dateStr).getTime();
        } catch (Exception e) {
            LOGGER.error("Error in getting TransactionAuditData" + e);
        }
        return result;
    }
}
