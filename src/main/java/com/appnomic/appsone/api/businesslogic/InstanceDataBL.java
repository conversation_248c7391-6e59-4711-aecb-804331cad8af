package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.client.ClientValidator;
import com.appnomic.appsone.api.common.client.impl.InstanceDataRequestClientValidator;
import com.appnomic.appsone.api.common.server.ServerValidator;
import com.appnomic.appsone.api.common.server.impl.InstanceDataRequestServerValidator;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.redis.InstanceRepo;
import com.appnomic.appsone.api.pojo.InstanceMaintenanceData;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.heal.configuration.entities.BasicInstanceBean;
import com.heal.configuration.pojos.CompInstClusterDetails;
import com.heal.configuration.pojos.MaintenanceDetails;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
public class InstanceDataBL implements BusinessLogic<Object, UtilityBean<Object>, List<InstanceMaintenanceData>> {

    @Override
    public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
        ClientValidator<Object> validator = new InstanceDataRequestClientValidator();
        return validator.validate(requestObject);
    }

    @Override
    public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
        ServerValidator<UtilityBean> validator = new InstanceDataRequestServerValidator();
        return validator.validate(utilityBean);
    }

    @Override
    public List<InstanceMaintenanceData> processData(UtilityBean<Object> configData) throws DataProcessingException {
        List<InstanceMaintenanceData> instanceMaintenanceData = new ArrayList<>();
        Map<Integer, Boolean> hostMaintenanceMap = new HashMap<>();

        InstanceRepo instanceRepo = new InstanceRepo();
        MaintenanceWindowBL maintenanceWindowBL = new MaintenanceWindowBL();
        List<BasicInstanceBean> instances = HealUICache.INSTANCE.getServiceInstanceList(configData.getAccount().getIdentifier(), configData.getService().getIdentifier(), true);

        instances.stream()
                .filter(c -> c.getComponentTypeId() == 1)
                .forEach(instance -> {
                    try {
                        InstanceMaintenanceData instanceDetails = new InstanceMaintenanceData();
                        instanceDetails.setId(instance.getId());
                        instanceDetails.setName(instance.getName());
                        instanceDetails.setHostName(instance.getName());

                        List<MaintenanceDetails> maintenanceDetails = instanceRepo.getMaintenanceDetails(configData.getAccount().getIdentifier(), instance.getIdentifier());
                        if (!maintenanceDetails.isEmpty()) {
                            instanceDetails.setHostMaintenance(maintenanceWindowBL.isUnderMaintenance(maintenanceDetails, System.currentTimeMillis()));
                        } else {
                            instanceDetails.setHostMaintenance(false);
                        }
                        hostMaintenanceMap.put(instance.getId(), instanceDetails.isHostMaintenance());
                        instanceMaintenanceData.add(instanceDetails);
                    } catch (Exception e) {
                        log.debug("Error while fetching the host instance Maintenance Status for instance : {}", instance.getName(), e);
                        throw new RuntimeException("Error while fetching the host instance Maintenance Status for instance. Name:"+instance.getName(), e);
                    }
                });

        instances.stream()
                .filter(c -> c.getComponentTypeId() != 1)
                .forEach(instance -> {
                    try {
                        CompInstClusterDetails instClusterDetails = instanceRepo.getInstanceDetailsWithInstIdentifier(configData.getAccount().getIdentifier(), instance.getIdentifier());

                        InstanceMaintenanceData instanceDetails = new InstanceMaintenanceData();
                        instanceDetails.setId(instance.getId());
                        instanceDetails.setName(instance.getName());
                        instanceDetails.setHostId(instClusterDetails.getHostId());

                        List<MaintenanceDetails> maintenanceDetails = instanceRepo.getMaintenanceDetails(configData.getAccount().getIdentifier(), instance.getIdentifier());
                        if (!maintenanceDetails.isEmpty()) {
                            instanceDetails.setInstanceMaintenance(maintenanceWindowBL.isUnderMaintenance(maintenanceDetails, System.currentTimeMillis()));
                        } else {
                            instanceDetails.setInstanceMaintenance(false);
                        }

                        instanceDetails.setHostMaintenance(hostMaintenanceMap.getOrDefault(instClusterDetails.getHostId(), false));
                        instanceMaintenanceData.add(instanceDetails);
                    } catch (Exception e) {
                        log.debug("Error while fetching the component instance Maintenance Status for instance : {}", instance.getName(), e);
                        throw new RuntimeException("Error while fetching the component instance Maintenance Status for instance :"+ instance.getName(), e);
                    }
                });

        return instanceMaintenanceData;
    }
}
