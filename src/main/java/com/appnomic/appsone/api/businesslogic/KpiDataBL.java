package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.beans.ThresholdType;
import com.appnomic.appsone.api.beans.Thresholds;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.opensearch.AnomalySearchRepo;
import com.appnomic.appsone.api.dao.opensearch.CollatedKpiRepo;
import com.appnomic.appsone.api.dao.opensearch.ThresholdSearchRepo;
import com.appnomic.appsone.api.dao.redis.*;
import com.appnomic.appsone.api.pojo.*;
import com.appnomic.appsone.api.pojo.request.KpiDataRequest;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.*;
import com.appnomic.appsone.opeasearchquery.results.TabularResults;
import com.heal.configuration.enums.OperationType;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.CompInstClusterDetails;
import com.heal.configuration.pojos.*;
import com.heal.configuration.pojos.opensearch.Anomalies;
import com.heal.configuration.pojos.opensearch.InstanceKpiThresholds;
import com.heal.configuration.pojos.opensearch.ServiceKpiThresholds;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class KpiDataBL implements BusinessLogic<KpiDataRequest, UtilityBean<KpiDataRequest>, UIData> {
    private static final String UPPER_IDENTIFIER = ConfProperties.getString(Constants.HIGH_THRESHOLD_IDENTIFIER_IDENTIFIER,
            Constants.HIGH_THRESHOLD_IDENTIFIER_IDENTIFIER_DEFAULT);
    private static final String LOWER_IDENTIFIER = ConfProperties.getString(Constants.LOW_THRESHOLD_IDENTIFIER_IDENTIFIER,
            Constants.LOW_THRESHOLD_IDENTIFIER_IDENTIFIER_DEFAULT);

    public UtilityBean<KpiDataRequest> clientValidation(RequestObject request) throws ClientException {
        if (request == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);

        if (authToken == null || authToken.trim().isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        KpiDataRequest kpiDataRequest = new KpiDataRequest(request);

        if (!kpiDataRequest.validateParameters(kpiDataRequest)) {
            log.error("Client validation failed for GET KPI DATA request.");
            throw new ClientException(UIMessages.CLIENT_VALIDATION_FAILED);
        }

        return UtilityBean.<KpiDataRequest>builder()
                .requestPayloadObject(kpiDataRequest)
                .fromTime(Long.valueOf(kpiDataRequest.getFromTime()))
                .toTime(Long.valueOf(kpiDataRequest.getToTime()))
                .accountIdString(kpiDataRequest.getAccountId())
                .serviceIdString(kpiDataRequest.getServiceId())
                .componentInstanceIdString(kpiDataRequest.getCompInstanceId())
                .authToken(authToken)
                .build();
    }

    @Override
    public UtilityBean<KpiDataRequest> serverValidation(UtilityBean<KpiDataRequest> utilityBean) throws ServerException {

        String accountIdentifier = utilityBean.getAccountIdString();
        int serviceId = Integer.parseInt(utilityBean.getServiceIdString());
        int instanceId = Integer.parseInt(utilityBean.getComponentInstanceIdString());
        int kpiId = Integer.parseInt(utilityBean.getRequestPayloadObject().getKpiId());
        int groupId = Integer.parseInt(utilityBean.getRequestPayloadObject().getGroupId());

        AccountRepo accountsRepo = new AccountRepo();
        ServiceRepo serviceRepo = new ServiceRepo();
        InstanceRepo instanceRepo = new InstanceRepo();
        ComponentRepo componentRepo = new ComponentRepo();
        KpiRepo kpiRepo = new KpiRepo();

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token.");
            throw new ServerException("Error while extracting user details from authorization token.");
        }

        Account account = accountsRepo.getAccount(accountIdentifier);
        if (account == null) {
            log.error("Account identifier is invalid.");
            throw new ServerException("Account identifier is invalid.");
        }

        TimeRangeDetailsDao timeRangeDetailsDao = new TimeRangeDetailsDao();
        List<TimeRangeDetails> timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(accountIdentifier);
        if (timeRangeDetailsList == null) {
            log.error("Exception occurred while fetching time range details for account {}", accountIdentifier);
            throw new ServerException("Exception occurred while fetching time range details for account " + accountIdentifier);
        }

        if (timeRangeDetailsList.isEmpty()) {
            log.trace("No time range details found for account {}. Searching for Global account.", accountIdentifier);
            timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }
        if (timeRangeDetailsList == null) {
            log.error("Exception occurred while fetching time range details for account {}", Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
            throw new ServerException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }

        if (timeRangeDetailsList.isEmpty()) {
            log.error("No time range details found");
            throw new ServerException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }
        TimeRangeDetails timeRangeDetails = ValidationUtils.getRollupPojo(utilityBean.getFromTime(), utilityBean.getToTime(), timeRangeDetailsList);
        if (timeRangeDetails == null) {
            log.error("Time range details are invalid.");
            throw new ServerException("Time range details are invalid.");
        }
        utilityBean.setTimeRangeDetails(timeRangeDetails);
        utilityBean.setTimeRangeDetailsList(timeRangeDetailsList);

        BasicEntity serviceDetails = serviceRepo.getBasicServiceDetailsWithServiceId(accountIdentifier, serviceId);
        if (serviceDetails == null) {
            String error = "Service id is invalid";
            log.error(error);
            throw new ServerException(error);
        }

        CompInstClusterDetails instanceDetails = instanceRepo.getInstancesByAccount(accountIdentifier)
                .parallelStream().filter(c -> c.getId() == instanceId).findAny().orElse(null);
        if (instanceDetails == null) {
            log.error("Instance id is invalid.");
            throw new ServerException("Instance id is invalid.");
        }

        BasicKpiEntity kpiDetails = componentRepo.getComponentKpis(accountIdentifier, instanceDetails.getComponentName())
                .parallelStream().filter(c -> c.getId() == kpiId && c.getStatus() == 1).findAny().orElse(null);
        if (kpiDetails == null) {
            String error = "Kpi id:" + kpiId + " does not exist under the component:" + instanceDetails.getComponentName();
            log.error(error);
            throw new ServerException(error);
        }

        CompInstKpiEntity compInstKpiEntity = kpiRepo.getKpiDetailByAccInstKpiId(accountIdentifier, instanceDetails.getIdentifier(), kpiId);
        if (compInstKpiEntity == null) {
            String error = "Kpi id is invalid.";
            log.error(error);
            throw new ServerException(error);
        }

        if (kpiDetails.getGroupId() != groupId) {
            log.error("Provided Group id {} doesn't match with Configured Kpi group Id {}.", groupId, kpiDetails.getGroupId());
            throw new ServerException("Group id mismatch.");
        }
        utilityBean.setKpiDetails(kpiDetails);

        utilityBean.setServiceIdString(serviceDetails.getIdentifier());
        utilityBean.setComponentInstanceIdString(instanceDetails.getIdentifier());
        utilityBean.setKpiNameString(kpiDetails.getIdentifier());
        return utilityBean;
    }

    @Override
    public UIData processData(UtilityBean<KpiDataRequest> configData) throws DataProcessingException {

        if (configData.getKpiDetails().getType().equalsIgnoreCase(Constants.AVAILABILITY_KPI_IDENTIFIER)) {
            return processAvailabilityKpiData(configData);
        }

        String accountIdentifier = configData.getAccountIdString();
        String serviceIdentifier = configData.getServiceIdString();
        String instanceIdentifier = configData.getComponentInstanceIdString();
        String kpiIdentifier = configData.getKpiNameString();
        int kpiId = Integer.parseInt(configData.getRequestPayloadObject().getKpiId());
        long fromTime = configData.getFromTime();
        long toTime = configData.getToTime();

        InstanceRepo instanceRepo = new InstanceRepo();
        KpiRepo kpiRepo = new KpiRepo();

        CollatedKpiRepo collatedKpiRepo = new CollatedKpiRepo();
        ThresholdSearchRepo thresholdSearchRepo = new ThresholdSearchRepo();
        AnomalySearchRepo anomalySearchRepo = new AnomalySearchRepo();

        NewTimeIntervalGenerationUtility t = new NewTimeIntervalGenerationUtility();
        t.setTimezoneOffset(accountIdentifier);
        t.processTimeRange(fromTime, toTime);
        t.addEndTimeInOSTime();

        String timezoneId = t.getTimeZoneId();
        List<Long> times = t.getOSTimes();

        AggregationLevel aggregationLevel = t.getNewTimeRangeDefinition().getAggregationLevel();
        int aggregationValue = aggregationLevel.getAggregrationValue();

        UIData uiData = new UIData();
        uiData.setAggregationLevel(aggregationLevel.getAggregrationValue());
        uiData.setDateFormat(aggregationLevel.getDataTimePattern());
        uiData.setTime(t.getDisplayTimes());

        CompInstClusterDetails instanceDetail = instanceRepo.getInstanceDetailsWithInstIdentifier(accountIdentifier, instanceIdentifier);
        CompInstKpiEntity kpiDetail = kpiRepo.getKpiDetailByAccInstKpiId(accountIdentifier, instanceIdentifier, kpiId);
        String applicableTo;
        if (instanceDetail.isCluster()) {
            applicableTo = "clusters";
        } else {
            applicableTo = "instances";
        }

        String operation = kpiDetail.getRollupOperation().getOperation();
        boolean validOperation = KpiRollUpOperations.isOpenSearchSupported(operation);
        if (aggregationValue != 1 && !validOperation) {
            log.warn("Roll up Operation {} not allowed for kpi {} for aggregation level {} data.", operation, kpiIdentifier, aggregationValue);
            return uiData;
        } else if (!validOperation) {
            operation = KpiRollUpOperations.SUM.name();
        }


        Set<String> groupAttributesList = new HashSet<>();
        if (kpiDetail.getDiscovery() != 1 && kpiDetail.getAttributeValues() != null) {
            groupAttributesList = kpiDetail.getAttributeValues().keySet();
        }

        Map<String, List<Double>> emptyAttributeKpiValueMap = new HashMap<>();
        Map<String, List<Double>> transformedAttributeKpiValueMap = new HashMap<>();
        Map<String, List<Double>> nonTransformedAttributeKpiValueMap = new HashMap<>();
        //KPI Values
        List<TabularResultsTypePojo> tabularResultsTypeList = collatedKpiRepo.getCollatedKpiDetailsForKPI(times, accountIdentifier, instanceIdentifier,
                String.valueOf(kpiDetail.getId()), aggregationValue, operation, fromTime, toTime, configData.getTimeRangeDetails(),
                configData.getTimeRangeDetailsList(), timezoneId);

        times.remove(times.size() - 1);

        String fieldName = OpenSearchRollupUtility.getValueFieldName(operation);
        for (TabularResultsTypePojo entry : tabularResultsTypeList) {
            TabularResults tabularResults = entry.getTabularResults();
            boolean isTransformed = entry.getIsTransformed();

            if (tabularResults == null || tabularResults.getRowResults() == null || tabularResults.getRowResults().isEmpty()) {

                emptyAttributeKpiValueMap.putAll(getAttributeValue(times, Collections.emptyList(), groupAttributesList));

            } else if (isTransformed) {
                List<AttributeKpiValuePojo> attributeKpiValuePojoList = new ArrayList<>();
                for (TabularResults.ResultRow resultRow : tabularResults.getRowResults()) {
                    Map<Long, String> timeValueMap = new LinkedHashMap<>();
                    AttributeKpiValuePojo attributeKpiValuePojo = new AttributeKpiValuePojo();
                    long time = resultRow.getTimestamp().getTime();
                    String value;

                    for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {
                        if (resultRowColumn.getColumnName().equalsIgnoreCase("groupAttribute")) {

                            attributeKpiValuePojo.setAttributeName(resultRowColumn.getColumnValue());

                        } else if (resultRowColumn.getColumnName().equalsIgnoreCase(operation + ": " + fieldName)) {

                            value = resultRowColumn.getColumnValue();
                            timeValueMap.put(time, value);

                        }
                    }
                    attributeKpiValuePojo.setValuesMap(timeValueMap);
                    attributeKpiValuePojoList.add(attributeKpiValuePojo);

                }
                Set<String> groupAttributesListFromOS = attributeKpiValuePojoList.parallelStream().map(AttributeKpiValuePojo::getAttributeName).collect(Collectors.toSet());
                if (groupAttributesListFromOS.size() > groupAttributesList.size()) {
                    groupAttributesList = groupAttributesListFromOS;
                }
                transformedAttributeKpiValueMap.putAll(getAttributeValue(times, attributeKpiValuePojoList, groupAttributesList));

            } else {

                List<AttributeKpiValuePojo> attributeKpiValuePojoList = new ArrayList<>();
                for (TabularResults.ResultRow resultRow : tabularResults.getRowResults()) {
                    Map<Long, String> timeValueMap = new LinkedHashMap<>();
                    AttributeKpiValuePojo attributeKpiValuePojo = new AttributeKpiValuePojo();
                    long time = resultRow.getTimestamp().getTime();
                    String value;

                    for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {
                        if (resultRowColumn.getColumnName().equalsIgnoreCase("groupAttribute")) {

                            attributeKpiValuePojo.setAttributeName(resultRowColumn.getColumnValue());

                        } else if (resultRowColumn.getColumnName().equalsIgnoreCase(operation + ": value")) {

                            value = resultRowColumn.getColumnValue();
                            timeValueMap.put(time, value);

                        }
                    }
                    attributeKpiValuePojo.setValuesMap(timeValueMap);
                    attributeKpiValuePojoList.add(attributeKpiValuePojo);

                }
                Set<String> groupAttributesListFromOS = attributeKpiValuePojoList.parallelStream().map(AttributeKpiValuePojo::getAttributeName).collect(Collectors.toSet());
                if (groupAttributesListFromOS.size() > groupAttributesList.size()) {
                    groupAttributesList = groupAttributesListFromOS;
                }
                nonTransformedAttributeKpiValueMap.putAll(getAttributeValue(times, attributeKpiValuePojoList, groupAttributesList));
            }
        }
        Map<String, List<Double>> attributeKpiValueMap = new HashMap<>(mergeAttributeKpiValueMap(groupAttributesList, emptyAttributeKpiValueMap, transformedAttributeKpiValueMap, nonTransformedAttributeKpiValueMap));

        Map<String, Thresholds> sorAttributeKpiThresholdMap;
        Map<String, Thresholds> norAttributeKpiThresholdMap;
        if (aggregationValue == 1) {
            //SOR
            List<InstanceKpiThresholds> instanceKpiThresholdsList = thresholdSearchRepo.getInstanceLevelThresholdDetails(accountIdentifier, instanceIdentifier, String.valueOf(kpiDetail.getId()), ThresholdType.STATIC.name(), fromTime, toTime);
            Map<String, Thresholds> instanceSorAttributeKpiThresholdMap = getInstanceThreshold(times, instanceKpiThresholdsList, groupAttributesList);

            List<ServiceKpiThresholds> serviceKpiThresholdsList = thresholdSearchRepo.getServiceLevelThresholdDetails(accountIdentifier, serviceIdentifier, String.valueOf(kpiDetail.getId()), ThresholdType.STATIC.name(), applicableTo, fromTime, toTime);
            Map<String, Thresholds> serviceSorAttributeKpiThresholdMap = getServiceThreshold(times, serviceKpiThresholdsList, groupAttributesList);

            sorAttributeKpiThresholdMap = mergeInstanceServiceThresholdMap(instanceSorAttributeKpiThresholdMap, serviceSorAttributeKpiThresholdMap, groupAttributesList);

            //NOR
            instanceKpiThresholdsList = thresholdSearchRepo.getInstanceLevelThresholdDetails(accountIdentifier, instanceIdentifier, String.valueOf(kpiDetail.getId()), ThresholdType.REALTIME.name(), fromTime, toTime);
            norAttributeKpiThresholdMap = getInstanceThreshold(times, instanceKpiThresholdsList, groupAttributesList);

        } else {
            //Push null in-case aggregation value greater than 1
            sorAttributeKpiThresholdMap = getInstanceThreshold(times, Collections.emptyList(), groupAttributesList);
            norAttributeKpiThresholdMap = getInstanceThreshold(times, Collections.emptyList(), groupAttributesList);
        }

        //Anomaly
        List<Anomalies> anomaliesList = anomalySearchRepo.getAllAnomaliesByKpi(accountIdentifier, instanceIdentifier, null, kpiDetail.getId(), fromTime, toTime);
        Map<String, List<Integer>> attributeAnomaliesCountMap = getAttributeAnomaliesCountMap(times, anomaliesList, groupAttributesList, aggregationValue, toTime);
        Map<String, List<Integer>> attributeAlertMap = attributeAnomaliesCountMap.entrySet().parallelStream()
                .collect(Collectors.toMap(Map.Entry::getKey,
                        d -> d.getValue().parallelStream().map(f -> f > 0 ? 1 : 0).collect(Collectors.toList())));

        List<KpiData> kpiDataList = groupAttributesList.parallelStream().map(c -> {
            KpiData kpiData = new KpiData();
            kpiData.setKpiId(kpiDetail.getId());
            kpiData.setGroupId(kpiDetail.getGroupId());
            kpiData.setKpi_name(kpiDetail.getName());
            kpiData.setUnit(kpiDetail.getUnit());
            if (kpiDetail.getIsGroup()) {
//                kpiData.setGroupDisplayValue(kpiDetail.getDiscovery() == 1 ? c : kpiDetail.getAttributeValues().getOrDefault(c, "ALL"));
                kpiData.setGroupDisplayValue(c);
            }

            if (isNull(sorAttributeKpiThresholdMap.get(c)) && sorAttributeKpiThresholdMap.containsKey("ALL")) {
                kpiData.setStaticRange(sorAttributeKpiThresholdMap.get("ALL"));
            } else
                kpiData.setStaticRange(sorAttributeKpiThresholdMap.get(c));

            if (isNull(norAttributeKpiThresholdMap.get(c)) && norAttributeKpiThresholdMap.containsKey("ALL")) {
                kpiData.setNormalRange(norAttributeKpiThresholdMap.get("ALL"));
            } else
                kpiData.setNormalRange(norAttributeKpiThresholdMap.get(c));

            kpiData.setValue(attributeKpiValueMap.get(c));
            kpiData.setAnomalyCounts(attributeAnomaliesCountMap.get(c));
            kpiData.setAnomaly(attributeAnomaliesCountMap.get(c).contains(1));
            kpiData.setAlerts(attributeAlertMap.get(c));
            return kpiData;
        }).collect(Collectors.toList());

        uiData.setChart_data(kpiDataList);

        return uiData;
    }

    private Map<String, List<Double>> mergeAttributeKpiValueMap(Set<String> groupAttributesList, Map<String, List<Double>> emptyAttributeKpiValueMap,
                                                                Map<String, List<Double>> transformedAttributeKpiValueMap, Map<String, List<Double>> nonTransformedAttributeKpiValueMap) {
        Map<String, List<Double>> result = new HashMap<>();
        groupAttributesList.forEach(attr -> {
            List<Double> empty = emptyAttributeKpiValueMap.getOrDefault(attr, new ArrayList<>());
            List<Double> tf = transformedAttributeKpiValueMap.getOrDefault(attr, new ArrayList<>());
            List<Double> firstStage;
            if (empty.isEmpty()) {
                firstStage = tf;
            } else if (tf.isEmpty()) {
                firstStage = empty;
            } else
                firstStage = mergeAttributeValueList(empty, tf);

            List<Double> ntf = nonTransformedAttributeKpiValueMap.getOrDefault(attr, new ArrayList<>());
            List<Double> secondStage;
            if (firstStage.isEmpty()) {
                secondStage = ntf;
            } else if (ntf.isEmpty()) {
                secondStage = firstStage;
            } else
                secondStage = mergeAttributeValueList(firstStage, ntf);

            result.put(attr, secondStage);
        });
        return result;
    }

    private List<Double> mergeAttributeValueList(List<Double> l1, List<Double> l2) {
        List<Double> result = new ArrayList<>();
        for (int i = 0; i < l1.size(); i++) {
            if (l1.get(i) == null || l1.get(i).isInfinite() || l1.get(i).isNaN()) {
                result.add(i, l2.get(i));
            } else {
                result.add(i, l1.get(i));
            }
        }
        return result;
    }

    private Map<String, Thresholds> mergeInstanceServiceThresholdMap(Map<String, Thresholds> instanceSorAttributeKpiThresholdMap, Map<String, Thresholds> serviceSorAttributeKpiThresholdMap, Set<String> groupAttributesList) {

        Map<String, Thresholds> thresholdsMap = new LinkedHashMap<>();
        groupAttributesList.forEach(c -> {
            Thresholds instThreshold = instanceSorAttributeKpiThresholdMap.getOrDefault(c, instanceSorAttributeKpiThresholdMap.get("ALL"));
            Thresholds servThreshold = serviceSorAttributeKpiThresholdMap.getOrDefault("ALL", serviceSorAttributeKpiThresholdMap.get(c));

            Thresholds thresholds = new Thresholds();
            List<Double> minThreshold = new ArrayList<>();
            List<Double> maxThreshold = new ArrayList<>();
            List<String> operationList = new ArrayList<>();

            List<Double> maxInst = instThreshold.getMaxThreshold();
            List<Double> maxServ = servThreshold.getMaxThreshold();
            List<Double> minInst = instThreshold.getMinThreshold();
            List<Double> minServ = servThreshold.getMinThreshold();
            List<String> opInst = instThreshold.getOperationList();
            List<String> opServ = servThreshold.getOperationList();
            for (int i = 0; i < maxInst.size(); i++) {
                if (maxInst.get(i) == null && minInst.get(i) == null && opInst.get(i) == null) {
                    maxThreshold.add(i, maxServ.get(i));
                    minThreshold.add(i, minServ.get(i));
                    operationList.add(i, opServ.get(i));
                } else {
                    maxThreshold.add(i, maxInst.get(i));
                    minThreshold.add(i, minInst.get(i));
                    operationList.add(i, opInst.get(i));
                }
            }

            thresholds.setMinThreshold(minThreshold);
            thresholds.setMaxThreshold(maxThreshold);
            thresholds.setOperationList(operationList);

            thresholdsMap.put(c, thresholds);
        });

        return thresholdsMap;
    }

    private UIData processAvailabilityKpiData(UtilityBean<KpiDataRequest> configData) {

        String accountIdentifier = configData.getAccountIdString();
        String instanceIdentifier = configData.getComponentInstanceIdString();
        int kpiId = Integer.parseInt(configData.getRequestPayloadObject().getKpiId());
        long fromTime = configData.getFromTime();
        long toTime = configData.getToTime();

        KpiRepo kpiRepo = new KpiRepo();

        CollatedKpiRepo collatedKpiRepo = new CollatedKpiRepo();
        AnomalySearchRepo anomalySearchRepo = new AnomalySearchRepo();

        NewTimeIntervalGenerationUtility t = new NewTimeIntervalGenerationUtility();
        t.setTimezoneOffset(accountIdentifier);
        t.processTimeRange(fromTime, toTime);
        t.addEndTimeInOSTime();

        String timezoneId = t.getTimeZoneId();
        List<Long> times = t.getOSTimes();

        AggregationLevel aggregationLevel = t.getNewTimeRangeDefinition().getAggregationLevel();
        int aggregationValue = aggregationLevel.getAggregrationValue();

        UIData uiData = new UIData();
        uiData.setAggregationLevel(aggregationLevel.getAggregrationValue());
        uiData.setDateFormat(aggregationLevel.getDataTimePattern());
        uiData.setTime(t.getDisplayTimes());

        CompInstKpiEntity kpiDetail = kpiRepo.getKpiDetailByAccInstKpiId(accountIdentifier, instanceIdentifier, kpiId);
        String operation = kpiDetail.getRollupOperation().getOperation();
        if (operation.equalsIgnoreCase("NONE")) {
            operation = "AVERAGE";
        }

        Set<String> groupAttributesList = new HashSet<>();
        if (kpiDetail.getDiscovery() != 1 && kpiDetail.getAttributeValues() != null) {
            groupAttributesList = kpiDetail.getAttributeValues().keySet();
        }

        Map<String, List<Double>> emptyAttributeKpiValueMap = new HashMap<>();
        Map<String, List<Double>> transformedAttributeKpiValueMap = new HashMap<>();
        Map<String, List<Double>> nonTransformedAttributeKpiValueMap = new HashMap<>();

        //KPI Values
        List<TabularResultsTypePojo> tabularResultsMap = collatedKpiRepo.getCollatedKpiDetailsForKPI(times, accountIdentifier, instanceIdentifier,
                String.valueOf(kpiDetail.getId()), aggregationValue, operation, fromTime, toTime,
                configData.getTimeRangeDetails(), configData.getTimeRangeDetailsList(), timezoneId);

        times.remove(times.size() - 1);

        String fieldName = OpenSearchRollupUtility.getValueFieldName(operation);
        for (TabularResultsTypePojo entry : tabularResultsMap) {
            TabularResults tabularResults = entry.getTabularResults();
            boolean isTransformed = entry.getIsTransformed();

            if (tabularResults == null || tabularResults.getRowResults() == null || tabularResults.getRowResults().isEmpty()) {

                emptyAttributeKpiValueMap.putAll(getAttributeValue(times, Collections.emptyList(), groupAttributesList));

            } else if (isTransformed) {
                List<AttributeKpiValuePojo> attributeKpiValuePojoList = new ArrayList<>();
                for (TabularResults.ResultRow resultRow : tabularResults.getRowResults()) {
                    Map<Long, String> timeValueMap = new LinkedHashMap<>();
                    AttributeKpiValuePojo attributeKpiValuePojo = new AttributeKpiValuePojo();
                    long time = resultRow.getTimestamp().getTime();
                    String value;

                    for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {
                        if (resultRowColumn.getColumnName().equalsIgnoreCase("groupAttribute")) {

                            attributeKpiValuePojo.setAttributeName(resultRowColumn.getColumnValue());

                        } else if (resultRowColumn.getColumnName().equalsIgnoreCase(operation + ": " + fieldName)) {

                            value = resultRowColumn.getColumnValue();
                            timeValueMap.put(time, value);
                        }
                    }
                    attributeKpiValuePojo.setValuesMap(timeValueMap);
                    attributeKpiValuePojoList.add(attributeKpiValuePojo);

                }
                Set<String> groupAttributesListFromOS = attributeKpiValuePojoList.parallelStream().map(AttributeKpiValuePojo::getAttributeName).collect(Collectors.toSet());
                if (groupAttributesListFromOS.size() > groupAttributesList.size()) {
                    groupAttributesList = groupAttributesListFromOS;
                }
                transformedAttributeKpiValueMap.putAll(getAttributeValue(times, attributeKpiValuePojoList, groupAttributesList));
            } else {

                List<AttributeKpiValuePojo> attributeKpiValuePojoList = new ArrayList<>();
                for (TabularResults.ResultRow resultRow : tabularResults.getRowResults()) {
                    Map<Long, String> timeValueMap = new LinkedHashMap<>();
                    AttributeKpiValuePojo attributeKpiValuePojo = new AttributeKpiValuePojo();
                    long time = resultRow.getTimestamp().getTime();
                    String value;

                    for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {
                        if (resultRowColumn.getColumnName().equalsIgnoreCase("groupAttribute")) {

                            attributeKpiValuePojo.setAttributeName(resultRowColumn.getColumnValue());

                        } else if (resultRowColumn.getColumnName().equalsIgnoreCase(operation + ": value")) {

                            value = resultRowColumn.getColumnValue();
                            timeValueMap.put(time, value);
                        }
                    }
                    attributeKpiValuePojo.setValuesMap(timeValueMap);
                    attributeKpiValuePojoList.add(attributeKpiValuePojo);

                }
                Set<String> groupAttributesListFromOS = attributeKpiValuePojoList.parallelStream().map(AttributeKpiValuePojo::getAttributeName).collect(Collectors.toSet());
                if (groupAttributesListFromOS.size() > groupAttributesList.size()) {
                    groupAttributesList = groupAttributesListFromOS;
                }
                nonTransformedAttributeKpiValueMap.putAll(getAttributeValue(times, attributeKpiValuePojoList, groupAttributesList));
            }
        }

        Map<String, List<Double>> attributeKpiValueMap = new HashMap<>(mergeAttributeKpiValueMap(groupAttributesList, emptyAttributeKpiValueMap, transformedAttributeKpiValueMap, nonTransformedAttributeKpiValueMap));

        //GET Anomaly Data
        List<Anomalies> anomaliesList = anomalySearchRepo.getAllAnomaliesByKpi(accountIdentifier, instanceIdentifier, null, kpiDetail.getId(), fromTime, toTime);
        Map<String, List<Integer>> attributeAnomaliesCountMap = getAttributeAnomaliesCountMap(times, anomaliesList, groupAttributesList, aggregationValue, toTime);
        Map<String, List<Integer>> attributeAlertMap = attributeAnomaliesCountMap.entrySet().parallelStream()
                .collect(Collectors.toMap(Map.Entry::getKey,
                        d -> d.getValue().parallelStream().map(f -> f > 0 ? 1 : 0).collect(Collectors.toList())));

        //GET Unavailable data points
        Map<String, List<Double>> attributeDataPointCountMap = new LinkedHashMap<>();
        if (aggregationValue != 1) {

            for (int i = 0; i < times.size(); i++) {
                Map<String, Long> attributeUnavailableCountMap;
                if (i == times.size() - 1 && times.get(i) < toTime) {
                    attributeUnavailableCountMap = collatedKpiRepo.getCountOfUnavailableDataPoints(accountIdentifier, instanceIdentifier,
                            String.valueOf(kpiDetail.getId()), times.get(i), toTime - Constants.MINUTE,
                            configData.getTimeRangeDetails(), configData.getTimeRangeDetailsList(), times);
                } else {
                    attributeUnavailableCountMap = collatedKpiRepo.getCountOfUnavailableDataPoints(accountIdentifier, instanceIdentifier,
                            String.valueOf(kpiDetail.getId()), times.get(i), times.get(i + 1) - Constants.MINUTE,
                            configData.getTimeRangeDetails(), configData.getTimeRangeDetailsList(), times);
                }

                groupAttributesList.forEach(c -> {
                    List<Double> temp = attributeDataPointCountMap.getOrDefault(c, new ArrayList<>());
                    temp.add(Double.valueOf(attributeUnavailableCountMap.getOrDefault(c, 0L)));
                    attributeDataPointCountMap.put(c, temp);
                });
            }

        }

        mergeAvailabilityKpiValueAnomalyCount(attributeKpiValueMap, attributeAnomaliesCountMap, groupAttributesList);

        List<KpiData> kpiDataList = groupAttributesList.parallelStream().map(c -> {
            KpiData kpiData = new KpiData();
            kpiData.setKpiId(kpiDetail.getId());
            kpiData.setGroupId(kpiDetail.getGroupId());
            kpiData.setKpi_name(kpiDetail.getName());
            kpiData.setUnit(kpiDetail.getUnit());
            if (kpiDetail.getIsGroup()) {
                kpiData.setGroupDisplayValue(c);
            }

            kpiData.setValue(attributeKpiValueMap.get(c));
            kpiData.setAnomalyCounts(attributeAnomaliesCountMap.get(c));
            kpiData.setAnomaly(attributeAnomaliesCountMap.get(c).contains(1));
            kpiData.setAlerts(attributeAlertMap.get(c));
            if (aggregationValue != 1) {
                kpiData.setCounts(attributeDataPointCountMap.get(c));
            }
            return kpiData;
        }).collect(Collectors.toList());

        uiData.setChart_data(kpiDataList);

        return uiData;
    }

    private void mergeAvailabilityKpiValueAnomalyCount(Map<String, List<Double>> attributeKpiValueMap, Map<String, List<Integer>> attributeAnomaliesCountMap, Set<String> groupAttributesList) {


        groupAttributesList.forEach(c -> {
            List<Double> result = new ArrayList<>();
            List<Double> kpiValues = attributeKpiValueMap.get(c);
            List<Integer> anomalyValues = attributeAnomaliesCountMap.get(c);

            for (int i = 0; i < kpiValues.size(); i++) {

                if (kpiValues.get(i) == null) {
                    result.add(null);
                } else if (anomalyValues.get(i) > 0) {
                    result.add(0D);
                } else {
                    //in 1 hr bucket
                    //if 50 points collected, 50 Available, avg value will be greater than or equals to 1
                    //if 50 points collected, 40 Available, 10 unavailable, avg value will be less than 1
                    //if 50 points collected, 50 Unavailable, avg value will be equals to 0
                    //if all points not collected, value will be set to null

                    result.add(kpiValues.get(i) == 1 ? 1D : 0D);
                }
            }

            attributeKpiValueMap.put(c, result);
        });

    }

    private Map<String, List<Double>> getAttributeValue(List<Long> originalTimes, List<AttributeKpiValuePojo> attributeKpiValuePojoList, Set<String> groupAttributesList) {

        Map<String, List<Double>> attributeKpiValueMap = new LinkedHashMap<>();

        Map<String, List<AttributeKpiValuePojo>> attributeKpiPojoMap = attributeKpiValuePojoList.parallelStream()
                .collect(Collectors.groupingBy(AttributeKpiValuePojo::getAttributeName));

        /*Do not remove this if block
        This is used to get the kpi/threshold value at the start timestamp when difference between all timestamps are not same.
        Example:- Last 12 hours from 5:15 AM or 5:45 AM*/
        List<Long> times = new ArrayList<>(originalTimes);
        if (times.get(1) - times.get(0) != times.get(2) - times.get(1)) {
            long firstTimestamp = times.get(1) - (times.get(2) - times.get(1));
            times.remove(0);
            times.add(0, firstTimestamp);
        }

        for (String grpAttr : groupAttributesList) {
            List<Double> values = new ArrayList<>();
            if (!attributeKpiPojoMap.containsKey(grpAttr)) {
                times.forEach(t -> values.add(null));
            } else {
                Map<Long, String> valuesMapFinal = new LinkedHashMap<>();
                attributeKpiPojoMap.get(grpAttr).forEach(c -> valuesMapFinal.putAll(c.getValuesMap()));
                times.forEach(t -> values.add(!valuesMapFinal.containsKey(t) ? null : Double.valueOf(valuesMapFinal.get(t))));
            }
            attributeKpiValueMap.put(grpAttr, values);
        }

        return attributeKpiValueMap;
    }

    private Map<String, Thresholds> getInstanceThreshold(List<Long> times, List<InstanceKpiThresholds> instanceKpiThresholdsList, Set<String> groupAttributesList) {

        Map<String, Thresholds> thresholdsMap = new LinkedHashMap<>();

        instanceKpiThresholdsList.forEach(c -> {
            Thresholds thresholds = new Thresholds();
            List<Double> minThreshold = new ArrayList<>();
            List<Double> maxThreshold = new ArrayList<>();
            List<String> operationList = new ArrayList<>();
            times.forEach(t -> {
                if (t >= c.getStartTime() && (t <= c.getEndTime() || c.getEndTime() == 0)) {
                    OperationType opEnum = OperationType.fromString(c.getOperationType());
                    switch (opEnum) {
                        case LESSER_THAN:
                        case BETWEEN:
                        case NOT_BETWEEN:
                            minThreshold.add(c.getThresholds().getOrDefault(LOWER_IDENTIFIER, null));
                            maxThreshold.add(c.getThresholds().getOrDefault(UPPER_IDENTIFIER, null));
                            break;
                        case GREATER_THAN:
                            minThreshold.add(c.getThresholds().getOrDefault(UPPER_IDENTIFIER, null));
                            maxThreshold.add(c.getThresholds().getOrDefault(LOWER_IDENTIFIER, null));
                            break;
                        default:
                            minThreshold.add(null);
                            maxThreshold.add(null);
                    }
                    operationList.add(c.getOperationType());
                } else {
                    minThreshold.add(null);
                    maxThreshold.add(null);
                    operationList.add(null);
                }
            });
            thresholds.setMaxThreshold(maxThreshold);
            thresholds.setMinThreshold(minThreshold);
            thresholds.setOperationList(operationList);
            if (thresholdsMap.containsKey(c.getKpiAttribute())) {
                thresholdsMap.put(c.getKpiAttribute(), enrichThresholdMap(thresholds, thresholdsMap.get(c.getKpiAttribute())));
            } else
                thresholdsMap.put(c.getKpiAttribute(), thresholds);
        });

        for (String grpAttr : groupAttributesList) {
            if (!thresholdsMap.containsKey(grpAttr)) {
                Thresholds thresholds = new Thresholds();
                List<Double> minThreshold = new ArrayList<>();
                List<Double> maxThreshold = new ArrayList<>();
                List<String> operationList = new ArrayList<>();
                times.forEach(t -> {
                    minThreshold.add(null);
                    maxThreshold.add(null);
                    operationList.add(null);
                });
                thresholds.setMaxThreshold(maxThreshold);
                thresholds.setMinThreshold(minThreshold);
                thresholds.setOperationList(operationList);
                thresholdsMap.put(grpAttr, thresholds);
            }
        }
        return thresholdsMap;
    }

    private Map<String, Thresholds> getServiceThreshold(List<Long> times, List<ServiceKpiThresholds> serviceKpiThresholdsList, Set<String> groupAttributesList) {

        Map<String, Thresholds> thresholdsMap = new LinkedHashMap<>();

        serviceKpiThresholdsList.forEach(c -> {
            Thresholds thresholds = new Thresholds();
            List<Double> minThreshold = new ArrayList<>();
            List<Double> maxThreshold = new ArrayList<>();
            List<String> operationList = new ArrayList<>();
            times.forEach(t -> {
                if (t >= c.getStartTime() && (t <= c.getEndTime() || c.getEndTime() == 0)) {
                    OperationType opEnum = OperationType.fromString(c.getOperationType());
                    switch (opEnum) {
                        case LESSER_THAN:
                        case BETWEEN:
                        case NOT_BETWEEN:
                            minThreshold.add(c.getThresholds().getOrDefault(LOWER_IDENTIFIER, null));
                            maxThreshold.add(c.getThresholds().getOrDefault(UPPER_IDENTIFIER, null));
                            break;
                        case GREATER_THAN:
                            minThreshold.add(c.getThresholds().getOrDefault(UPPER_IDENTIFIER, null));
                            maxThreshold.add(c.getThresholds().getOrDefault(LOWER_IDENTIFIER, null));
                            break;
                        default:
                            minThreshold.add(null);
                            maxThreshold.add(null);
                    }
                    operationList.add(c.getOperationType());
                } else {
                    minThreshold.add(null);
                    maxThreshold.add(null);
                    operationList.add(null);
                }
            });
            thresholds.setMaxThreshold(maxThreshold);
            thresholds.setMinThreshold(minThreshold);
            thresholds.setOperationList(operationList);
            if (thresholdsMap.containsKey(c.getKpiAttribute())) {
                thresholdsMap.put(c.getKpiAttribute(), enrichThresholdMap(thresholds, thresholdsMap.get(c.getKpiAttribute())));
            } else
                thresholdsMap.put(c.getKpiAttribute(), thresholds);
        });

        for (String grpAttr : groupAttributesList) {
            if (!thresholdsMap.containsKey(grpAttr)) {
                Thresholds thresholds = new Thresholds();
                List<Double> minThreshold = new ArrayList<>();
                List<Double> maxThreshold = new ArrayList<>();
                List<String> operationList = new ArrayList<>();
                times.forEach(t -> {
                    minThreshold.add(null);
                    maxThreshold.add(null);
                    operationList.add(null);
                });
                thresholds.setMaxThreshold(maxThreshold);
                thresholds.setMinThreshold(minThreshold);
                thresholds.setOperationList(operationList);
                thresholdsMap.put(grpAttr, thresholds);
            }
        }
        return thresholdsMap;
    }

    private Thresholds enrichThresholdMap(Thresholds newThresholds, Thresholds oldThresholds) {

        if (isNull(newThresholds)) {
            return oldThresholds;
        } else if (isNull(oldThresholds)) {
            return newThresholds;
        } else {
            Thresholds thresholds = new Thresholds();
            List<Double> minThreshold = new ArrayList<>();
            List<Double> maxThreshold = new ArrayList<>();
            List<String> operationList = new ArrayList<>();

            List<Double> maxOld = oldThresholds.getMaxThreshold();
            List<Double> maxNew = newThresholds.getMaxThreshold();
            List<Double> minOld = oldThresholds.getMinThreshold();
            List<Double> minNew = newThresholds.getMinThreshold();
            List<String> opOld = oldThresholds.getOperationList();
            List<String> opNew = newThresholds.getOperationList();
            for (int i = 0; i < maxOld.size(); i++) {
                if (maxOld.get(i) == null && minOld.get(i) == null && opOld.get(i) == null) {
                    maxThreshold.add(i, maxNew.get(i));
                    minThreshold.add(i, minNew.get(i));
                    operationList.add(i, opNew.get(i));
                } else {
                    maxThreshold.add(i, maxOld.get(i));
                    minThreshold.add(i, minOld.get(i));
                    operationList.add(i, opOld.get(i));
                }
            }

            thresholds.setMinThreshold(minThreshold);
            thresholds.setMaxThreshold(maxThreshold);
            thresholds.setOperationList(operationList);

            return thresholds;
        }
    }

    private Map<String, List<Integer>> getAttributeAnomaliesCountMap(List<Long> resultTimes, List<Anomalies> anomaliesList, Set<String> groupAttributesList, int aggregationValue, long toTime) {

        Map<String, List<Integer>> attributeAnomaliesMap = new LinkedHashMap<>();

        Map<String, List<Long>> attributeAnomalyTimeMap = anomaliesList.parallelStream().collect(Collectors.groupingBy(Anomalies::getKpiAttribute,
                Collectors.mapping(Anomalies::getAnomalyTime, Collectors.toList())));

        List<Long> times = new ArrayList<>(resultTimes);
        if (!times.contains(toTime)) {
            times.add(toTime);
        }

        groupAttributesList.forEach(c -> {
            List<Integer> anomaliesCountList = new ArrayList<>();
            if (attributeAnomalyTimeMap.containsKey(c)) {
                List<Long> anomalyTimes = attributeAnomalyTimeMap.get(c);
                if (aggregationValue == 1) {
                    resultTimes.forEach(t -> {
                        if (anomalyTimes.contains(t)) {
                            anomaliesCountList.add(1);
                        } else {
                            anomaliesCountList.add(0);
                        }
                    });
                } else {
                    for (int i = 0; i < resultTimes.size(); i++) {
                        int finalI = i;
                        if (anomalyTimes.stream().anyMatch(a -> times.get(finalI) <= a && times.get(finalI + 1) > a)) {
                            long count = anomalyTimes.stream().filter(a -> times.get(finalI) <= a && times.get(finalI + 1) > a).count();
                            anomaliesCountList.add((int) count);
                        } else {
                            anomaliesCountList.add(0);
                        }
                    }
                }
            } else {
                resultTimes.forEach(t -> anomaliesCountList.add(0));
            }
            attributeAnomaliesMap.put(c, anomaliesCountList);
        });

        return attributeAnomaliesMap;

    }

    private boolean isNull(Thresholds kpiThreshold) {

        Optional<String> operationList = kpiThreshold.getOperationList().parallelStream().filter(Objects::nonNull).findAny();
        Optional<Double> maxThreshold = kpiThreshold.getMaxThreshold().parallelStream()
                .filter(Objects::nonNull).findAny();
        Optional<Double> minThreshold = kpiThreshold.getMinThreshold().parallelStream()
                .filter(Objects::nonNull).findAny();

        return (!maxThreshold.isPresent() && !minThreshold.isPresent()) || !operationList.isPresent();
    }

}
