package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.UserException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.appnomic.appsone.api.common.LoggerTags.TAG_INVALID_FROM_TO_TIME;

public class ApplicationWiseSignalsBL {
    private static final Logger LOGGER = LoggerFactory.getLogger(ApplicationWiseSignalsBL.class);

    private ApplicationWiseSignalsBL() {
    }

    public static void clientValidation(Request request) throws UserException {
        LOGGER.debug("Inside Client validation");
        if (request == null) {
            LOGGER.error("Request body can not be null.");
            throw new UserException(UIMessages.REQUEST_NULL);
        }

        String accountIdString = request.params(Constants.REQUEST_PARAM_IDENTIFIER);
        StringBuilder errors = new StringBuilder();
        if (accountIdString == null || accountIdString.trim().isEmpty()) {
            LOGGER.error("Account id should not be empty or null, Account id: {}", accountIdString);
            errors.append(UIMessages.ACCOUNT_EMPTY);
        }

        errors.append(validationOfFromAndToTime(request));

        List<String> appIds = new ArrayList<>();
        if (request.params().get(Constants.REQUEST_PARAM_APPLICATION_SMALLID).equalsIgnoreCase("all")) {
            appIds = Arrays.stream(request.queryParams(Constants.REQUEST_QUERY_PARAM_APPLICATION_ID).toLowerCase().split(",")).collect(Collectors.toList());
        } else {
            appIds.add(request.params(Constants.REQUEST_PARAM_APPLICATION_SMALLID));
        }
        for (String applicationId : appIds) {
            if (applicationId == null || applicationId.trim().isEmpty()) {
                LOGGER.error("Application id should not be empty or null");
                errors.append(UIMessages.ERROR_INVALID_APPLICATION_ID);
            } else {
                try {
                    Integer.parseInt(applicationId);
                } catch (NumberFormatException e) {
                    LOGGER.error("Error occurred while converting the application id [{}]. Reason: {}", applicationId, e.getMessage());
                    errors.append(Constants.INVALID_APPLICATION_ID_ERROR_MESSAGE);
                }
            }
        }

        if (errors.length() > 0) {
            throw new UserException(errors.toString());
        }
    }

    private static String validationOfFromAndToTime(Request request) {
        StringBuilder errors = new StringBuilder();
        String fromTimeString = request.queryParams(Constants.REQUEST_PARAM_FROM_TIME);
        String toTimeString = request.queryParams(Constants.REQUEST_PARAM_TO_TIME);
        Long fromTime = 0L;
        Long toTime = 0L;
        try {
            fromTime = (fromTimeString == null) ? null : Long.parseLong(fromTimeString);
        } catch (NumberFormatException e) {
            LOGGER.error("Error occurred while converting the fromTime [{}]. Reason: {}", fromTimeString, e.getMessage());
            errors.append(Constants.INVALID_FROM_TIME);
        }

        try {
            toTime = (toTimeString == null) ? null : Long.parseLong(toTimeString);
        } catch (NumberFormatException e) {
            LOGGER.error("Error occurred while converting the toTime [{}]. Reason: {}", toTimeString, e.getMessage());
            errors.append(Constants.INVALID_TO_TIME);
        }

        if ((fromTime != null && toTime != null) && (fromTime <= 0 || toTime <= 0 || fromTime > toTime)) {
            LOGGER.error(TAG_INVALID_FROM_TO_TIME);
            errors.append(TAG_INVALID_FROM_TO_TIME);
        }
        return errors.toString();
    }
}
