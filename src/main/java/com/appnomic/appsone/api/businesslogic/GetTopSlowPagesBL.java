package com.appnomic.appsone.api.businesslogic;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.mobile.model.TopSlowPage;
import com.appnomic.appsone.api.mobile.util.BigQueryService;
import com.appnomic.appsone.api.mobile.util.BigQueryUtil;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.FieldValueList;
import com.google.cloud.bigquery.QueryJobConfiguration;
import com.google.cloud.bigquery.TableResult;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class GetTopSlowPagesBL implements BusinessLogic<Object, UtilityBean<Object>, List<TopSlowPage>> {

	@Override
	public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
		List<String> errorMessages = new ArrayList<>();
		if (!BigQueryUtil.appOSStringValidation(requestObject, errorMessages)
			|| !BigQueryUtil.appDisplayVersionValidation(requestObject, errorMessages)
			|| !BigQueryUtil.fromTimeToTimeValidation(requestObject, errorMessages)) {
				throw new ClientException(String.join(";", errorMessages));
		}
		String appOSString = requestObject.getQueryParams().get("app-os")[0];
		String appDisplayVersions = requestObject.getQueryParams().get("app-build-version")[0];
		String fromTime = requestObject.getQueryParams().get("from-time")[0];
		String toTime = requestObject.getQueryParams().get("to-time")[0];
		Integer rowLimit = (null != requestObject.getQueryParams().get("row-limit") && !requestObject.getQueryParams().get("row-limit")[0].equalsIgnoreCase("") && 0 < Integer.parseInt(requestObject.getQueryParams().get("row-limit")[0])) ? 
							Integer.parseInt(requestObject.getQueryParams().get("row-limit")[0]) : 5;
		
		return UtilityBean.<Object>builder()
				.appOSString(appOSString)
				.appDisplayVersions(appDisplayVersions)
				.fromTimeString(fromTime)
				.toTimeString(toTime)
				.rowLimit(rowLimit)
				.build();
	}

	@Override
	public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
		return utilityBean;
	}

	@Override
	public List<TopSlowPage> processData(UtilityBean<Object> configData) throws DataProcessingException {
		String appOSString = configData.getAppOSString();

		BigQuery bigQuery = BigQueryService.getInstance(appOSString.split("\\.")[0]);
		log.debug("bigQuery Obj: {}", bigQuery);

		String appDisplayVersionsInParam = BigQueryUtil.getInParameter(configData.getAppDisplayVersions());
		String fromTime = configData.getFromTimeString();
		String toTime = configData.getToTimeString();
        Integer rowLimit = configData.getRowLimit();

        String query = 	"SELECT " + 
        				"	 event_name AS eventName, " +
        				"	 COUNT(trace_info.screen_info.slow_frame_ratio) AS noOfSlowPages " +
		                "FROM "+ Constants.getPerfMonTable(appOSString) +
		                "WHERE " + 
		                "	 event_type LIKE 'SCREEN_TRACE' " +
		                "	 AND trace_info.screen_info.slow_frame_ratio > 0 " +
		                "	 AND app_display_version IN " + appDisplayVersionsInParam + " " +
		                "	 AND event_timestamp > TIMESTAMP_MILLIS("+ fromTime +") " +
		                "	 AND event_timestamp < TIMESTAMP_MILLIS(" + toTime +") "  +
		                "GROUP BY eventName " +
		                "ORDER BY noOfSlowPages DESC";

		QueryJobConfiguration queryConfig = QueryJobConfiguration.newBuilder(query).setUseLegacySql(false).build();

		List<TopSlowPage> topSlowPagesResult = new ArrayList<>();
		List<TopSlowPage> topSlowPageResponse = new ArrayList<>();

		TableResult result;
		try {
			result = bigQuery.query(queryConfig);
		} catch (InterruptedException inExc) {
			throw new DataProcessingException("Error in fetching data from BigQuery");
		}
		
		long totalSlowPageCount = 0L;

		for (FieldValueList resultValue : result.iterateAll()) {
			TopSlowPage topSlowPage = new TopSlowPage();

			topSlowPage.setEventName(resultValue.get("eventName").getStringValue());
			topSlowPage.setNoOfSlowPages(resultValue.get("noOfSlowPages").getLongValue());
			totalSlowPageCount += resultValue.get("noOfSlowPages").getLongValue();

			topSlowPagesResult.add(topSlowPage);
		}

        Iterator<TopSlowPage> topErrorFilesIterator = topSlowPagesResult.iterator();
		while (topErrorFilesIterator.hasNext() && rowLimit > 0) {
			TopSlowPage topSlowPage = topErrorFilesIterator.next();
			TopSlowPage topSlowPageResult = new TopSlowPage();

			topSlowPageResult.setEventName(topSlowPage.getEventName());
			topSlowPageResult.setNoOfSlowPages(topSlowPage.getNoOfSlowPages());

			Long noOfSlowPages = topSlowPage.getNoOfSlowPages();
            String percentageOfTotal = (totalSlowPageCount != 0) ? String.format("%.2f", noOfSlowPages.doubleValue()/ (double) totalSlowPageCount * 100.00) +"%": "0.00%";
			topSlowPageResult.setPercentage(percentageOfTotal);
			topSlowPageResponse.add(topSlowPageResult);
			rowLimit--;
        }
        return topSlowPageResponse;
	}
}
