package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.cache.keys.ServiceTransactionKey;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.ApplicationRepo;
import com.appnomic.appsone.api.dao.redis.ServiceRepo;
import com.appnomic.appsone.api.dao.redis.UserRepo;
import com.appnomic.appsone.api.pojo.Application;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.Tags;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;


public class ServiceApplicationsBL implements BusinessLogic<Integer, ServiceTransactionKey, Set<Application>> {

    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceApplicationsBL.class);

    @Override
    public UtilityBean<Integer> clientValidation(RequestObject request) throws ClientException {

        if (request == null) {
            LOGGER.error("Request is NULL.");
            throw new ClientException("Request is NULL.");
        }

        String identifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            LOGGER.error("Account identifier is NULL or Empty.");
            throw new ClientException("Account identifier is NULL or Empty.");
        }

        String serviceIdString = request.getParams().get(Constants.REQUEST_PARAM_SERVICE_ID);
        if (serviceIdString == null || serviceIdString.trim().isEmpty()) {
            LOGGER.error("':serviceId' is NULL or Empty.");
            throw new ClientException("':serviceId' is NULL or Empty.");
        }
        int serviceId;
        try {
            serviceId = Integer.parseInt(serviceIdString);
        } catch (NumberFormatException e) {
            LOGGER.error("':serviceId' is not a positive integer.");
            throw new ClientException("':serviceId' is not a positive integer.");
        }

        return UtilityBean.<Integer>builder()
                .authToken(request.getHeaders().get(Constants.AUTHORIZATION_HEADER))
                .accountIdString(identifier)
                .serviceId(serviceId)
                .build();
    }

    @Override
    public ServiceTransactionKey serverValidation(UtilityBean<Integer> utilityBean) throws ServerException {
        AccountRepo accountRepo = new AccountRepo();
        UserRepo userRepo = new UserRepo();
        ServiceRepo serviceRepo = new ServiceRepo();

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if(userId == null){
            LOGGER.error(Constants.MESSAGE_INVALID_AUTH_TOKEN);
            throw new ServerException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }

        User user = userRepo.getUser(userId);
        if( user == null ) {
            throw new ServerException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }


        Account account = accountRepo.getAccount(utilityBean.getAccountIdString());
        if( account == null ) {
            LOGGER.error("Invalid account identifier: {}.", utilityBean.getAccountIdString());
            throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
        }


        BasicEntity basicEntity = serviceRepo.getAllServices(account.getIdentifier()).stream().filter(s -> s.getId() == utilityBean.getServiceId()).findAny().orElse(null);
        if( basicEntity == null ) {
            LOGGER.error("Invalid service id: {}",utilityBean.getServiceId());
            throw new ServerException(UIMessages.ERROR_INVALID_SERVICE_ID);
        }

        return ServiceTransactionKey.builder()
                .accountId(account.getId())
                .serviceId(utilityBean.getServiceId())
                .accountIdentifier(account.getIdentifier())
                .serviceIdentifier(basicEntity.getIdentifier())
                .build();
    }

    @Override
    public Set<Application> processData(ServiceTransactionKey key) throws DataProcessingException {
        ApplicationRepo applicationsRepo = new ApplicationRepo();
        List<com.heal.configuration.pojos.BasicEntity> applicationDetails = HealUICache.INSTANCE.getServiceApplicationList(key.getAccountIdentifier(), key.getServiceIdentifier());
        return applicationDetails.stream()
                .map(app -> {
                    com.heal.configuration.pojos.Application application = applicationsRepo.getApplicationDetailWithAppIdentifier(key.getAccountIdentifier(), app.getIdentifier());
                    return Application.builder()
                            .id(application.getId())
                            .name(application.getName())
                            .identifier(application.getIdentifier())
                            .dashboardUId(application.getTags()== null ? null : application.getTags()
                                    .parallelStream()
                                    .filter(t -> t.getType().equalsIgnoreCase(Constants.DASHBOARD_UID_TAG))
                                    .map(com.heal.configuration.pojos.Tags::getValue)
                                    .findAny().orElse(null))
                            .tags(application.getTags()== null ? null : application.getTags()
                                    .parallelStream()
                                    .map(t -> Tags.builder()
                                            .value(t.getValue())
                                            .name(t.getType())
                                            .build()).collect(Collectors.toList()))
                            .build();
                })
                .collect(Collectors.toSet());
    }
}
