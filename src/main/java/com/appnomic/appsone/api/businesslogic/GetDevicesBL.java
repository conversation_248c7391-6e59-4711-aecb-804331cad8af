package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.mobile.model.Devices;
import com.appnomic.appsone.api.mobile.util.BigQueryService;
import com.appnomic.appsone.api.mobile.util.BigQueryUtil;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.FieldValueList;
import com.google.cloud.bigquery.QueryJobConfiguration;
import com.google.cloud.bigquery.TableResult;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

@Slf4j
public class GetDevicesBL implements BusinessLogic<Object, UtilityBean<Object>, List<Devices>>{
    @Override
    public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {

        List<String> errorMessages = new ArrayList<String>();
        if(!BigQueryUtil.appOSStringValidation(requestObject, errorMessages)
                || !BigQueryUtil.issueIdValidation(requestObject, errorMessages)) {
            throw new ClientException(String.join(";", errorMessages));
        }

        String appOSString = requestObject.getQueryParams().get("app-os")[0];
        String issueId = requestObject.getParams().get(":id");

        return UtilityBean.<Object>builder()
                .appOSString(appOSString)
                .issueId(issueId)
                .build();
    }

    @Override
    public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
        return utilityBean;
    }

    @Override
    public List<Devices> processData(UtilityBean<Object> configData) throws DataProcessingException {

        String appOSString = configData.getAppOSString();
        String issueId = configData.getIssueId();
        BigQuery bigQuery = BigQueryService.getInstance(appOSString.split("\\.")[0]);
        log.debug("bigQuery Obj: {}", bigQuery);

        String query = "select device.manufacturer, device.model" +
                "       from " + Constants.getCrashlyticsTable(appOSString) +
                "       where issue_id = '" + issueId + "'";

        QueryJobConfiguration queryConfig = QueryJobConfiguration.newBuilder(query).setUseLegacySql(false).build();

        TableResult result;

        try {
            result = bigQuery.query(queryConfig);
        } catch(InterruptedException inExc) {
            throw new DataProcessingException("Error in fetching data from BigQuery");
        }

        Map<String, Integer> devicesCountMap = new HashMap<>();

        int total = 0;
        List<Devices> devices = new ArrayList<>();

		for (FieldValueList resultValue : result.iterateAll()) {
			if (appOSString.contains("android")) {
				if (!devicesCountMap.containsKey(resultValue.get("manufacturer").getStringValue()))
					devicesCountMap.put(resultValue.get("manufacturer").getStringValue(), 1);
				else
					devicesCountMap.computeIfPresent(resultValue.get("manufacturer").getStringValue(), (k, v) -> v + 1);
			}
			if (appOSString.contains("iOS")) {
				if (!devicesCountMap.containsKey(resultValue.get("model").getStringValue()))
					devicesCountMap.put(resultValue.get("model").getStringValue(), 1);
				else
					devicesCountMap.computeIfPresent(resultValue.get("model").getStringValue(), (k, v) -> v + 1);
			}
			total++;
		}

        System.out.println(devicesCountMap);

        Iterator<Map.Entry<String, Integer>> itr = devicesCountMap.entrySet().iterator();
		while (itr.hasNext()) {
			Devices device = new Devices();
			Map.Entry<String, Integer> entry = itr.next();
			device.setDeviceName(entry.getKey());
			device.setDevicePercentage(new BigDecimal((entry.getValue().doubleValue() / total) * 100).setScale(2, RoundingMode.HALF_UP));
			devices.add(device);
		}
        return devices;
    }
}
