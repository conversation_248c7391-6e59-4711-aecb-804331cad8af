package com.appnomic.appsone.api.businesslogic;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.mobile.util.BigQueryService;
import com.appnomic.appsone.api.mobile.util.BigQueryUtil;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.FieldValueList;
import com.google.cloud.bigquery.QueryJobConfiguration;
import com.google.cloud.bigquery.TableResult;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class GetMobileApplicationVersionsBL implements BusinessLogic<Object, UtilityBean<Object>, List<String>> {

	@Override
	public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
		List<String> errorMessages = new ArrayList<>();
		if (!BigQueryUtil.appOSStringValidation(requestObject, errorMessages)) {
			throw new ClientException(String.join(";", errorMessages));
		}
		String appOSString = requestObject.getQueryParams().get("app-os")[0];
		return  UtilityBean.<Object>builder()
				.appOSString(appOSString)
				.build();
	}

	@Override
	public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
		return utilityBean;
	}

	@Override
	public List<String> processData(UtilityBean<Object> configData) throws DataProcessingException {
		String appOSString = configData.getAppOSString();
		BigQuery bigQuery = BigQueryService.getInstance(appOSString.split("\\.")[0]);
		log.debug("bigQuery Obj: {}", bigQuery.toString());

		String query =	"SELECT  " + 
						"    app_info.id AS appId, " + 
						"    STRING_AGG(DISTINCT app_info.version) AS appVersions, " + 
						"    CONCAT(app_info.id, '-', platform) AS appKey " + 
						"FROM " + Constants.getAnalyticsTable(appOSString) + 
						"GROUP BY  " + 
						"    appId, " + 
						"    platform";

		QueryJobConfiguration queryConfig = QueryJobConfiguration.newBuilder(query).setUseLegacySql(false).build();

		List<String> appVersions = new ArrayList<>();
		TableResult result = null;
		
		try {
			result = bigQuery.query(queryConfig);
		} catch (InterruptedException inExc) {
			throw new DataProcessingException("Error in fetching data from BigQuery");
		}
		for(FieldValueList value : result.iterateAll()) {
			if(null != value.get("appKey").getValue() && value.get("appKey").getStringValue().equalsIgnoreCase(Constants.getAppKey(appOSString))) {
				appVersions.addAll(Arrays.asList(value.get("appVersions").getStringValue().split(",")));
			}
		}
		return appVersions;
	}
}
