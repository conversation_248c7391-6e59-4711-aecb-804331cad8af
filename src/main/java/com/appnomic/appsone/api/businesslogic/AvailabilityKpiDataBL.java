package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.opensearch.AnomalySearchRepo;
import com.appnomic.appsone.api.dao.opensearch.CollatedKpiRepo;
import com.appnomic.appsone.api.dao.redis.*;
import com.appnomic.appsone.api.pojo.KpiDataPoints;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.AvailabilityKpiDataRequest;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.NewTimeIntervalGenerationUtility;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.heal.configuration.pojos.*;
import com.heal.configuration.pojos.opensearch.Anomalies;
import com.heal.configuration.pojos.opensearch.CollatedKpi;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 09/05/22
 */
@Slf4j
public class AvailabilityKpiDataBL implements BusinessLogic<AvailabilityKpiDataRequest, UtilityBean<AvailabilityKpiDataRequest>, KpiDataPoints> {

    @Override
    public UtilityBean<AvailabilityKpiDataRequest> clientValidation(RequestObject request) throws ClientException {
        log.trace("Inside Client validation");

        if (request == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);

        if (authToken == null || authToken.trim().isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        AvailabilityKpiDataRequest availabilityKpiDataRequest = new AvailabilityKpiDataRequest(request);

        if (!availabilityKpiDataRequest.isValidParameters(availabilityKpiDataRequest)) {
            log.error("Client validation failed for GET AVAILABILITY KPI DATA POINTS request.");
            throw new ClientException(UIMessages.CLIENT_VALIDATION_FAILED);
        }

        int aggregationLevel = Integer.parseInt(availabilityKpiDataRequest.getAggLevel());
        long fromTime = Long.parseLong(availabilityKpiDataRequest.getFromTime());
        long toTime = fromTime + ((long) aggregationLevel * 60 * 1000);

        return UtilityBean.<AvailabilityKpiDataRequest>builder()
                .requestPayloadObject(availabilityKpiDataRequest)
                .fromTime(Long.valueOf(availabilityKpiDataRequest.getFromTime()))
                .toTime(toTime)
                .accountIdString(availabilityKpiDataRequest.getAccountId())
                .componentInstanceIdString(availabilityKpiDataRequest.getCompInstanceId())
                .authToken(authToken)
                .build();
    }

    @Override
    public UtilityBean<AvailabilityKpiDataRequest> serverValidation(UtilityBean<AvailabilityKpiDataRequest> utilityBean) throws ServerException {
        log.trace("Inside serverValidation method.");

        String accountIdentifier = utilityBean.getAccountIdString();
        int instanceId = Integer.parseInt(utilityBean.getComponentInstanceIdString());
        int kpiId = Integer.parseInt(utilityBean.getRequestPayloadObject().getKpiId());
        int groupId = Integer.parseInt(utilityBean.getRequestPayloadObject().getGroupId());

        AccountRepo accountRepo = new AccountRepo();
        InstanceRepo instanceRepo = new InstanceRepo();
        ComponentRepo componentRepo = new ComponentRepo();
        KpiRepo kpiRepo = new KpiRepo();

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error("Invalid authorization token provided: {}.", utilityBean.getAuthToken());
            throw new ServerException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }
        utilityBean.setUserId(userId);

        Account account = accountRepo.getAccount(accountIdentifier);
        if (account == null) {
            log.error("Invalid account identifier: {}.", utilityBean.getAccountIdString());
            throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
        }
        utilityBean.setAccount(account);

        TimeRangeDetailsDao timeRangeDetailsDao = new TimeRangeDetailsDao();
        List<TimeRangeDetails> timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(accountIdentifier);
        if (timeRangeDetailsList == null) {
            log.error("Exception occurred while fetching time range details for account {}", accountIdentifier);
            throw new ServerException("Exception occurred while fetching time range details for account " + accountIdentifier);
        }

        if (timeRangeDetailsList.isEmpty()) {
            log.trace("No time range details found for account {}. Searching for Global account.", accountIdentifier);
            timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }
        if (timeRangeDetailsList == null) {
            log.error("Exception occurred while fetching time range details for account {}", Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
            throw new ServerException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }

        if (timeRangeDetailsList.isEmpty()) {
            log.error("No time range details found");
            throw new ServerException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }

//        TimeRangeDetails timeRangeDetails = ValidationUtils.getRollupPojo(utilityBean.getFromTime(), utilityBean.getToTime(), timeRangeDetailsList);
        TimeRangeDetails timeRangeDetails = timeRangeDetailsList.stream().filter(c -> c.getName().equals("60")).findFirst().orElse(null);
        if (timeRangeDetails == null) {
            log.error("Time range details are invalid.");
            throw new ServerException("Time range details are invalid.");
        }
        utilityBean.setTimeRangeDetails(timeRangeDetails);
        utilityBean.setTimeRangeDetailsList(timeRangeDetailsList);

        CompInstClusterDetails instanceDetails = instanceRepo.getInstanceDetailsWithId(accountIdentifier, instanceId);
        if (instanceDetails == null) {
            log.error("Invalid instance id: {}", utilityBean.getComponentInstanceIdString());
            throw new ServerException(UIMessages.ERROR_INVALID_SERVICE_ID);
        }
        utilityBean.setCompInstance(instanceDetails);
        utilityBean.setComponentInstanceIdString(instanceDetails.getIdentifier());

        BasicKpiEntity kpiDetails = componentRepo.getComponentKpis(accountIdentifier, instanceDetails.getComponentName())
                .parallelStream().filter(c -> c.getId() == kpiId && c.getStatus() == 1).findAny().orElse(null);
        if (kpiDetails == null) {
            String error = "Kpi id:" + kpiId + " does not exist under the component:" + instanceDetails.getComponentName();
            log.error(error);
            throw new ServerException(error);
        }

        CompInstKpiEntity compInstKpiEntity = kpiRepo.getKpiDetailByAccInstKpiId(accountIdentifier, instanceDetails.getIdentifier(), kpiId);
        if (compInstKpiEntity == null) {
            String error = "Kpi id is invalid.";
            log.error(error);
            throw new ServerException(error);
        }

        if (kpiDetails.getGroupId() != groupId) {
            log.error("Provided Group id {} doesn't match with Configured Kpi group Id {}.", groupId, kpiDetails.getGroupId());
            throw new ServerException("Group id mismatch.");
        }
        utilityBean.setKpiNameString(kpiDetails.getIdentifier());

        return utilityBean;
    }

    @Override
    public KpiDataPoints processData(UtilityBean<AvailabilityKpiDataRequest> configData) throws DataProcessingException {
        KpiDataPoints dataPoints = new KpiDataPoints();

        String accountIdentifier = configData.getAccount().getIdentifier();
        String instanceIdentifier = configData.getCompInstance().getIdentifier();
        String kpiId = configData.getRequestPayloadObject().getKpiId();
        Long fromTime = configData.getFromTime();
        Long toTime = configData.getToTime() - Constants.MINUTE;
        String attr = configData.getRequestPayloadObject().getAttribute();
        String attribute = attr == null || attr.equalsIgnoreCase("null") ? "ALL" : attr;

        NewTimeIntervalGenerationUtility t = new NewTimeIntervalGenerationUtility();
        t.setTimezoneOffset(accountIdentifier);
        t.processTimeRange(fromTime, toTime);
        t.addEndTimeInOSTime();

        List<Long> times = t.getOSTimes();

        CollatedKpiRepo collatedKpiRepo = new CollatedKpiRepo();
        AnomalySearchRepo anomalySearchRepo = new AnomalySearchRepo();

        List<CollatedKpi> unavailableDataPointsList = collatedKpiRepo.getUnavailableDataPoints(accountIdentifier, instanceIdentifier,
                kpiId, attribute, fromTime, toTime, configData.getTimeRangeDetails(), configData.getTimeRangeDetailsList(), times);
        List<Long> unavailableDataTimes = unavailableDataPointsList.parallelStream().map(CollatedKpi::getTimeInGMT).distinct().sorted().collect(Collectors.toList());


        List<Anomalies> anomaliesList = anomalySearchRepo.getAnomaliesByKpi(accountIdentifier, instanceIdentifier, Integer.parseInt(kpiId), attribute, -1, fromTime, toTime);
        List<Long> anomalyTimes = anomaliesList.parallelStream().map(Anomalies::getAnomalyTime).collect(Collectors.toList());

        dataPoints.setUnavailabilityTimes(unavailableDataTimes);
        dataPoints.setAnomalyTimes(anomalyTimes.parallelStream().sorted().collect(Collectors.toList()));
        return dataPoints;
    }

}
