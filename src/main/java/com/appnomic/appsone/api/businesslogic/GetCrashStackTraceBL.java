package com.appnomic.appsone.api.businesslogic;

import java.util.ArrayList;
import java.util.List;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.mobile.model.StackTrace;
import com.appnomic.appsone.api.mobile.util.BigQueryService;
import com.appnomic.appsone.api.mobile.util.BigQueryUtil;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.FieldValueList;
import com.google.cloud.bigquery.QueryJobConfiguration;
import com.google.cloud.bigquery.TableResult;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class GetCrashStackTraceBL implements BusinessLogic<Object, UtilityBean<Object>, StackTrace>{

    @Override
    public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {

        List<String> errorMessages = new ArrayList<String>();
        if(!BigQueryUtil.appOSStringValidation(requestObject, errorMessages)
                || !BigQueryUtil.issueIdValidation(requestObject, errorMessages)) {
            throw new ClientException(String.join(";", errorMessages));
        }

        String appOSString = requestObject.getQueryParams().get("app-os")[0];
        String issueId = requestObject.getParams().get(":id");

        return UtilityBean.<Object>builder()
                .appOSString(appOSString)
                .issueId(issueId)
                .build();
    }

    @Override
    public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
        return utilityBean;
    }

    @Override
    public StackTrace processData(UtilityBean<Object> configData) throws DataProcessingException {
        String appOSString = configData.getAppOSString();
        String issueId = configData.getIssueId();
        BigQuery bigQuery = BigQueryService.getInstance(appOSString.split("\\.")[0]);
        log.debug("bigQuery Obj: {}", bigQuery);


        String query = "SELECT \n" +
                "STRING_AGG(DISTINCT exc.title) AS exceptionTitle,\n" +
                "CONCAT(IFNULL(excFrame.symbol,\"<symbol not present>\"), \" (\",IFNULL(excFrame.file,\"<file not present>\"), \": \", IFNULL(CAST(excFrame.line AS STRING), \"<line no. not present>\"), \")\") AS exceptionStackTrace,\n" +
                "null AS errorTitle, \n" +
                "null AS errorStackTrace\n" +
                "from " + Constants.getCrashlyticsTable(appOSString) + " AS crash \n" +
                "    CROSS JOIN UNNEST(crash.exceptions) AS exc \n" +
                "    CROSS JOIN UNNEST(exc.frames) AS excFrame\n" +
                "WHERE issue_id = '"+ issueId +"'  \n" +
                "GROUP BY issue_id, exceptionStackTrace\n" +
                " \n" +
                "UNION ALL \n" +
                " \n" +
                "SELECT \n" +
                "null AS exceptionTitle,\n" +
                "null AS exceptionStackTrace,\n" +
                "STRING_AGG(DISTINCT err.title) AS errorTitle, \n" +
                "CONCAT(IFNULL(errFrame.symbol,\"<symbol not present>\"), \" (\",IFNULL(errFrame.file,\"<file not present>\"), \": \", IFNULL(CAST(errFrame.line AS STRING), \"<line no. not present>\"), \")\") AS errorStackTrace\n" +
                "from " + Constants.getCrashlyticsTable(appOSString) + " AS crash \n" +
                "    CROSS JOIN UNNEST(crash.errors) AS err \n" +
                "    CROSS JOIN UNNEST(err.frames) AS errFrame\n" +
                "WHERE issue_id = '"+ issueId +"'  \n" +
                "GROUP BY issue_id, errorStackTrace";

        QueryJobConfiguration queryConfig = QueryJobConfiguration.newBuilder(query).setUseLegacySql(false).build();


        TableResult result;

        try {
            result = bigQuery.query(queryConfig);
        } catch(InterruptedException inExc) {
            throw new DataProcessingException("Error in fetching data from BigQuery");
        }



       String title = "";
       List<String> stackTrace = new ArrayList<>();
       StackTrace response = new StackTrace();
        
        for (FieldValueList resultValue : result.iterateAll()) {
        	if (null != resultValue.get("exceptionTitle").getValue())
                if (title != null)
                    title = resultValue.get("exceptionTitle").getStringValue();
            if (null != resultValue.get("exceptionStackTrace").getValue())
                stackTrace.add(resultValue.get("exceptionStacktrace").getStringValue());
            if (null != resultValue.get("errorTitle").getValue())
                if (title != null)
                    title = resultValue.get("errorTitle").getStringValue();
            if (null != resultValue.get("errorStackTrace").getValue())
                stackTrace.add(resultValue.get("errorStacktrace").getStringValue());
        }

        response.setStackTrace(stackTrace);
        response.setIssueTitle(title);

        return response;
    }
}
