package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.common.Actions;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.mysql.entity.UserForensicNotificationMappingBean;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.ApplicationRepo;
import com.appnomic.appsone.api.dao.redis.UserRepo;
import com.appnomic.appsone.api.manager.MySQLConnectionManager;
import com.appnomic.appsone.api.pojo.ForensicNotificationConfiguration;
import com.appnomic.appsone.api.pojo.IdAction;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.service.mysql.NotificationPreferencesDataService;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.DateTimeUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Throwables;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.ForensicNotificationPreferences;
import com.heal.configuration.pojos.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.skife.jdbi.v2.Handle;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class UpdateForensicNotificationConfiguration implements BusinessLogic<ForensicNotificationConfiguration, ForensicNotificationConfiguration, String> {
    String accountIdentifier;
    private String applicationUserId;

    @Override
    public UtilityBean<ForensicNotificationConfiguration> clientValidation(RequestObject requestObject) throws ClientException {

        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION_HEADER);
        if (StringUtils.isEmpty(authKey)) {
            log.error(Constants.MESSAGE_INVALID_AUTH_TOKEN);
            throw new ClientException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(identifier)) {
            log.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        String applicableUserId = requestObject.getParams().get(Constants.REQUEST_PARAM_USER_ID);
        if (StringUtils.isEmpty(applicableUserId)) {
            log.error("userId should not be NULL or empty.");
            throw new ClientException("userId should not be NULL or empty.");
        }

        String requestBody = requestObject.getBody();
        if (StringUtils.isEmpty(requestBody)) {
            log.error("requestBody should not be null or empty.");
            throw new ClientException("requestBody should not be null or empty.");
        }

        ForensicNotificationConfiguration pojoObject;
        try {
            pojoObject = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(requestBody,
                    new TypeReference<ForensicNotificationConfiguration>() {
                    });
        } catch (IOException e) {
            throw new ClientException(Constants.REQUEST_BODY_INVALID_ERROR_MESSAGE);
        }
        if (pojoObject == null) {
            throw new ClientException(Constants.REQUEST_BODY_EMPTY_ERROR_MESSAGE);
        }
        if (!pojoObject.validate()) {
            throw new ClientException("Request client validation failure. Kindly check the logs.");
        }
        pojoObject.setApplicableUserId(applicableUserId);

        return UtilityBean.<ForensicNotificationConfiguration>builder()
                .accountIdString(identifier)
                .authToken(authKey)
                .requestPayloadObject(pojoObject)
                .build();

    }

    @Override
    public ForensicNotificationConfiguration serverValidation(UtilityBean<ForensicNotificationConfiguration> utilityBean) throws ServerException {

        try {
            AccountRepo accountRepo = new AccountRepo();
            UserRepo userRepo = new UserRepo();
            ApplicationRepo applicationRepo = new ApplicationRepo();
            ForensicNotificationConfiguration forensicNotificationConfiguration = utilityBean.getRequestPayloadObject();

            User user = userRepo.getUser(forensicNotificationConfiguration.getApplicableUserId());
            if (user == null) {
                throw new ServerException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
            }

            applicationUserId = forensicNotificationConfiguration.getApplicableUserId();

            Account account = accountRepo.getAccount(utilityBean.getAccountIdString());
            if (account == null) {
                log.error("Invalid account identifier: {}.", utilityBean.getAccountIdString());
                throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
            }
            accountIdentifier = account.getIdentifier();

            Set<Integer> userAppIds = applicationRepo.getAccessibleApplicationsByUserId(user.getUserDetailsId(), account.getIdentifier())
                    .stream()
                    .map(BasicEntity::getId)
                    .collect(Collectors.toSet());
            if (userAppIds.isEmpty()) {
                throw new ServerException("Exception encountered while fetching applicationIds for the user." + user.getUserName());
            }

            Set<Integer> userForensicAppIds = applicationRepo.getApplicationForensicsByUserId(account.getIdentifier(), user.getUserDetailsId())
                    .stream()
                    .map(BasicEntity::getId)
                    .collect(Collectors.toSet());

            validateRequestApplications(forensicNotificationConfiguration.getApplications(), userAppIds, userForensicAppIds);

            forensicNotificationConfiguration.setUserDetailsId(user.getUserDetailsId());
            forensicNotificationConfiguration.setAccountId(account.getId());
            return forensicNotificationConfiguration;
        } catch (Exception e) {
            throw new ServerException(e, "Invalid request details.");
        }
    }

    private void validateRequestApplications(List<IdAction> applicationsFromUI, Set<Integer> userApplications, Set<Integer> forensicUserApps) throws ServerException {
        for (IdAction application : applicationsFromUI) {
            Actions action = application.getAction();
            if (action == Actions.ADD) {
                if (forensicUserApps.contains(application.getId())) {
                    throw new ServerException("Application is already configured for forensic notification.");
                }
                if (!userApplications.contains(application.getId())) {
                    throw new ServerException("Application is not mapped to the specified user.");
                }
            } else if (action == Actions.DELETE) {
                if (!forensicUserApps.contains(application.getId())) {
                    throw new ServerException("Application is not configured for forensic notification.");
                }
            } else {
                throw new ServerException("Invalid Action.");
            }
        }
    }

    @Override
    public String processData(ForensicNotificationConfiguration configData) throws DataProcessingException {
        try {
            return MySQLConnectionManager.getInstance().getHandle().inTransaction((conn, status) ->
                    updateForensicNotificationConfiguration(configData, conn));
        } catch (Exception e) {
            if (Throwables.getRootCause(e) instanceof DataProcessingException) {
                throw (DataProcessingException) Throwables.getRootCause(e);
            } else {
                throw e;
            }
        }
    }

    private String updateForensicNotificationConfiguration(ForensicNotificationConfiguration configData, Handle handle) {
        Date timestamp = DateTimeUtil.getCurrentTimestampInGMT();
        String userId = configData.getUserDetailsId();
        String applicableUserId = configData.getApplicableUserId();

        NotificationPreferencesDataService.updateForensicNotificationUserDetails(configData.getEmailNotification(),
                configData.getSuppressionDuration(), applicableUserId, userId, timestamp, handle);
        List<UserForensicNotificationMappingBean> addForensicNotificationConfigurations = new ArrayList<>();
        if (configData.getApplications() == null || configData.getApplications().isEmpty()) {

            Set<BasicEntity> applications = new ApplicationRepo().getAccessibleApplicationsByUserId(userId, accountIdentifier);

            if (applications != null && !applications.isEmpty()) {
                applications.forEach(d -> addForensicNotificationConfigurations.add(UserForensicNotificationMappingBean.builder()
                        .accountId(d.getAccountId())
                        .applicationId(d.getId())
                        .applicableUserId(applicableUserId)
                        .forensicNotificationSuppression(configData.getSuppressionDuration())
                        .userDetailsId(userId)
                        .createdTime(timestamp)
                        .status(Constants.STATUS_ACTIVE)
                        .updatedTime(timestamp)
                        .build()));
            }

        } else {
            List<Integer> deleteForensicNotificationConfigurations = new ArrayList<>();
            addForensicNotificationConfigurations.addAll(configData.getApplications()
                    .parallelStream().filter(app -> {
                        boolean check = app.getAction() == Actions.ADD;
                        if (!check) {
                            deleteForensicNotificationConfigurations.add(app.getId());
                        }
                        return check;
                    }).map(app -> UserForensicNotificationMappingBean.builder()
                            .accountId(configData.getAccountId())
                            .applicationId(app.getId())
                            .applicableUserId(applicableUserId)
                            .forensicNotificationSuppression(configData.getSuppressionDuration())
                            .userDetailsId(userId)
                            .createdTime(timestamp)
                            .status(Constants.STATUS_ACTIVE)
                            .updatedTime(timestamp)
                            .build())
                    .collect(Collectors.toList()));

            if (!addForensicNotificationConfigurations.isEmpty()) {
                NotificationPreferencesDataService.addForensicNotificationConfigurations(addForensicNotificationConfigurations, handle);
            }

            for (int appId : deleteForensicNotificationConfigurations) {
                NotificationPreferencesDataService.deleteForensicNotificationConfigurations(appId, applicableUserId, handle);
            }
            updateForensicNotificationInRedis(configData);
        }

        return String.format("Forensic notification configuration updated for user - %s", applicableUserId);
    }

    /*To add and delete Forensic Notification In Redis with all its attributes*/

    private void updateForensicNotificationInRedis(ForensicNotificationConfiguration forensicNotificationConfiguration) {
        Map<Integer, Actions> actionsMap = new HashMap<>();

        ApplicationRepo applicationRepo = new ApplicationRepo();

        List<IdAction> applicationIdAction = forensicNotificationConfiguration.getApplications();
        for (IdAction application : applicationIdAction) {
            Actions action = application.getAction();
            actionsMap.put(application.getId(), action);
        }
        actionsMap.forEach((id, action) -> {
            if (action.name().equalsIgnoreCase("ADD")) {


                String applicationIdentifier = applicationRepo.getApplicationDetailsWithAppId(accountIdentifier, id).getIdentifier();
                List<ForensicNotificationPreferences> addForensicNotification = applicationRepo.getForensicNotificationInRedis(accountIdentifier, applicationIdentifier);
                if (addForensicNotification != null) {
                    ForensicNotificationPreferences forensicNotificationPreferences = ForensicNotificationPreferences.builder()
                            .status(1)
                            .userDetailsId(forensicNotificationConfiguration.getUserDetailsId())
                            .suppression(forensicNotificationConfiguration.getSuppressionDuration())
                            .build();
                    addForensicNotification.add(forensicNotificationPreferences);
                    applicationRepo.updateForensicNotificationInRedis(accountIdentifier, applicationIdentifier, addForensicNotification);
                }
// Deleting App id with all it's detail here

            } else if (action.name().equalsIgnoreCase("DELETE")) {
                String applicationIdentifier = applicationRepo.getApplicationDetailsWithAppId(accountIdentifier, id).getIdentifier();
                List<ForensicNotificationPreferences> deleteForensicNotification = applicationRepo.getForensicNotificationInRedis(accountIdentifier, applicationIdentifier);
                if (deleteForensicNotification != null) {
                    deleteForensicNotification.removeIf(forensicNotificationPreferences -> forensicNotificationPreferences.getUserDetailsId().trim().equalsIgnoreCase(applicationUserId));
                    applicationRepo.updateForensicNotificationInRedis(accountIdentifier, applicationIdentifier, deleteForensicNotification);
                }


            }
        });
    }
}