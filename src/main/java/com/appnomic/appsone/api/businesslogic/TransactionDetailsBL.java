package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.opensearch.CollatedTransactionsSearchRepo;
import com.appnomic.appsone.api.dao.opensearch.RawTransactionSearchRepo;
import com.appnomic.appsone.api.dao.redis.AgentRepo;
import com.appnomic.appsone.api.dao.redis.InstanceRepo;
import com.appnomic.appsone.api.dao.redis.TimeRangeDetailsDao;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.RowDetails;
import com.appnomic.appsone.api.pojo.TabularResultsTypePojo;
import com.appnomic.appsone.api.pojo.TransactionSortedStats;
import com.appnomic.appsone.api.pojo.request.TransactionDetailDataRequest;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.service.KPIAnomalyService;
import com.appnomic.appsone.api.service.TransactionDetailDataService;
import com.appnomic.appsone.api.util.*;
import com.appnomic.appsone.opeasearchquery.results.TabularResults;
import com.heal.configuration.enums.KPIAttributes;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.BasicTransactionEntity;
import com.heal.configuration.pojos.CompInstClusterDetails;
import com.heal.configuration.pojos.TimeRangeDetails;
import lombok.extern.slf4j.Slf4j;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.appnomic.appsone.api.common.LoggerTags.TAG_INVALID_FROM_TO_TIME;

@Slf4j
public class TransactionDetailsBL implements BusinessLogic<TransactionDetailDataRequest, UtilityBean<TransactionDetailDataRequest>, TransactionSortedStats> {
    private static final String TOPN = "topn";
    private static final String DETAILS = "details";

    @Override
    public UtilityBean<TransactionDetailDataRequest> clientValidation(RequestObject request) throws ClientException {

        TransactionDetailDataRequest requestDetails = new TransactionDetailDataRequest();
        requestDetails.setAccountIdString(request.getParams().get(":identifier"));
        requestDetails.setServiceIdString(request.getParams().get(":controllerId"));
        requestDetails.setInstanceIdString(request.getParams().get(":instanceId"));
        requestDetails.setResponseType(request.getParams().get(":response-type") != null ? request.getParams().get(":response-type") : Constants.TRANSACTION_TYPE_DEFAULT);
        requestDetails.setTopNCountString(request.getParams().get(":topN"));
        requestDetails.setFromTimeString((request.getQueryParams().containsKey("fromTime")) ? request.getQueryParams().get("fromTime")[0] : null);
        requestDetails.setToTimeString((request.getQueryParams().containsKey("toTime")) ? request.getQueryParams().get("toTime")[0] : null);
        requestDetails.setAggregationType((request.getQueryParams().containsKey("type")) ? request.getQueryParams().get("type")[0] : Constants.APPLICATION_CONTROLLER_TYPE);
        String modeInput = request.getParams().get("mode");
        requestDetails.setMode((modeInput == null) ? "all" : modeInput);
        requestDetails.setTagIdString((request.getQueryParams().containsKey("tagId")) ? request.getQueryParams().get("tagId")[0] : null);
        requestDetails.setInstanceId(Integer.parseInt(requestDetails.getInstanceIdString()));
        requestDetails.setServiceId(Integer.parseInt(requestDetails.getServiceIdString()));
        requestDetails.setTagId(Integer.parseInt(requestDetails.getTagIdString()));

        if (StringUtils.isEmpty(requestDetails.getAccountIdString())) {
            throw new ClientException(MessageFormat.format(UIMessages.ACCOUNT_EMPTY, Constants.REQUEST_PARAM_IDENTIFIER, requestDetails.getAccountIdString()));
        }
        if (StringUtils.isEmpty(requestDetails.getServiceIdString())) {
            throw new ClientException(MessageFormat.format(UIMessages.SERVICE_EMPTY_ERROR, Constants.REQUEST_PARAM_IDENTIFIER, requestDetails.getAccountIdString()));
        }
        if (StringUtils.isEmpty(requestDetails.getInstanceIdString())) {
            throw new ClientException(MessageFormat.format(UIMessages.INVALID_INSTANCE_MESSAGE, Constants.REQUEST_PARAM_IDENTIFIER, requestDetails.getAccountIdString()));
        }

        Long fromTime;
        Long toTime;
        try {
            fromTime = (requestDetails.getFromTimeString() == null) ? null : Long.parseLong(requestDetails.getFromTimeString());
        } catch (NumberFormatException e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            throw new ClientException(e, MessageFormat.format("Error occurred while converting the fromTime [{0}]. Reason: {1}", requestDetails.getFromTimeString(), e.getMessage()));
        }

        try {
            toTime = (requestDetails.getToTimeString() == null) ? null : Long.parseLong(requestDetails.getToTimeString());
        } catch (NumberFormatException e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            throw new ClientException(e, MessageFormat.format("Error occurred while converting the toTime [{0}]. Reason: {1}", requestDetails.getToTimeString(), e.getMessage()));
        }
        if ((fromTime != null && toTime != null) && (fromTime <= 0 || toTime <= 0 || fromTime > toTime)) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error(TAG_INVALID_FROM_TO_TIME);
            throw new ClientException(TAG_INVALID_FROM_TO_TIME);
        } else if (fromTime == null || toTime == null) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("FromTime or/and ToTime is null.");
            throw new ClientException("From Time or/and To Time is null.");
        }

        requestDetails.setFromTime(fromTime);
        requestDetails.setToTime(toTime);

        return UtilityBean.<TransactionDetailDataRequest>builder()
                .requestPayloadObject(requestDetails)
                .build();
    }

    @Override
    public UtilityBean<TransactionDetailDataRequest> serverValidation(UtilityBean<TransactionDetailDataRequest> utilityBean) throws ServerException {

        if (!utilityBean.getRequestPayloadObject().validateAccount()) {
            throw new ServerException(Constants.MESSAGE_INVALID_ACCOUNT);
        }

        String accountIdentifier = utilityBean.getRequestPayloadObject().getAccount().getIdentifier();

        TimeRangeDetailsDao timeRangeDetailsDao = new TimeRangeDetailsDao();
        List<TimeRangeDetails> timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(accountIdentifier);
        if (timeRangeDetailsList == null) {
            log.error("Exception occurred while fetching time range details for account {}", accountIdentifier);
            throw new ServerException("Exception occurred while fetching time range details for account " + accountIdentifier);
        }

        if (timeRangeDetailsList.isEmpty()) {
            log.trace("No time range details found for account {}. Searching for Global account.", accountIdentifier);
            timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }
        if (timeRangeDetailsList == null) {
            log.error("Exception occurred while fetching time range details for account {}", Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
            throw new ServerException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }

        if (timeRangeDetailsList.isEmpty()) {
            log.error("No time range details found");
            throw new ServerException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }
        TimeRangeDetails timeRangeDetails = ValidationUtils.getRollupPojo(utilityBean.getRequestPayloadObject().getFromTime(), utilityBean.getRequestPayloadObject().getToTime(), timeRangeDetailsList);
        if (timeRangeDetails == null) {
            log.error("Time range details are invalid.");
            throw new ServerException("Time range details are invalid.");
        }
        utilityBean.setTimeRangeDetails(timeRangeDetails);
        utilityBean.setTimeRangeDetailsList(timeRangeDetailsList);

        if (!utilityBean.getRequestPayloadObject().validateServiceId(true)) {
            throw new ServerException(Constants.MESSAGE_INVALID_SERVICE);
        }
        if (!utilityBean.getRequestPayloadObject().validateParamType() || !utilityBean.getRequestPayloadObject().validateAggregationType()) {
            throw new ServerException(Constants.MESSAGE_INVALID_PARAMETERS);
        }
        // Assigning default type as application if nothing is received.
        utilityBean.getRequestPayloadObject().setAggregationType(utilityBean.getRequestPayloadObject().getAggregationType() != null ? utilityBean.getRequestPayloadObject().getAggregationType() :
                Constants.APPLICATION_CONTROLLER_TYPE);

        if (utilityBean.getRequestPayloadObject().getResponseType() == null) {
            utilityBean.getRequestPayloadObject().setResponseType(ConfProperties.getString(Constants.TRANSACTION_TYPE,
                    Constants.TRANSACTION_TYPE_DEFAULT));
        }
        utilityBean.getRequestPayloadObject().populateTaggedTransactionList();

        CompInstClusterDetails instanceDetails = new CompInstClusterDetails();
        if (utilityBean.getRequestPayloadObject().getAggregationType().trim().equalsIgnoreCase(Constants.COM_INSTANCE_TYPE)) {
            instanceDetails = new InstanceRepo().getInstancesByAccount(utilityBean.getRequestPayloadObject().getAccountIdString())
                    .parallelStream().filter(c -> c.getId() == utilityBean.getRequestPayloadObject().getInstanceId()).findAny().orElse(null);
            if (instanceDetails == null) {
                log.error("Instance id is invalid.");
                throw new ServerException("Instance id is invalid.");
            }
            utilityBean.setComponentInstanceIdString(instanceDetails.getIdentifier());
        }

        List<String> agentList;
        if (utilityBean.getRequestPayloadObject().getAggregationType().trim().equalsIgnoreCase(Constants.SERVICES_CONTROLLER_TYPE)) {
            return utilityBean;
        } else if (utilityBean.getRequestPayloadObject().getAggregationType().trim().equalsIgnoreCase(Constants.COM_INSTANCE_TYPE)) {
            agentList = instanceDetails.getAgentIds();
        } else {
            log.error("Invalid request type provided: {}", utilityBean.getRequestPayloadObject().getAggregationType());
            throw new ServerException(String.format("Invalid request type provided: %s", utilityBean.getRequestPayloadObject().getAggregationType()));
        }

        if (agentList == null || agentList.isEmpty()) {
            log.error("Unable to fetch agents for given service/comp instance: {}", utilityBean.getServiceIdString());
            return utilityBean;
        }

        utilityBean.getRequestPayloadObject().setAgentList(agentList);

        return utilityBean;
    }

    @Override
    public TransactionSortedStats processData(UtilityBean<TransactionDetailDataRequest> configData) throws DataProcessingException {
        log.trace("{} getTransactionDetails() . PARAM: {}", Constants.INVOKED_METHOD, configData.getRequestPayloadObject());

        TransactionSortedStats result = new TransactionSortedStats();

        if (configData.getRequestPayloadObject().getTaggedTransactionList().isEmpty()) {
            log.warn("There are no transactions mapped to this tag, hence no data is fetched.");
            return result;
        }

        if (DateTimeUtil.inRange(configData.getRequestPayloadObject().getFromTime())) {
            return processRawTxnData(configData);
        }

        TransactionDetailDataService txnDetailService = new TransactionDetailDataService();
        CollatedTransactionsSearchRepo collatedTxnRepo = new CollatedTransactionsSearchRepo();

        String accountIdentifier = configData.getRequestPayloadObject().getAccountIdString();
        String serviceIdentifier = configData.getRequestPayloadObject().getServiceDetails().getIdentifier();
        String responseType = configData.getRequestPayloadObject().getResponseType();
        String requestType = configData.getRequestPayloadObject().getAggregationType();
        long fromTime = configData.getRequestPayloadObject().getFromTime();
        long toTime = configData.getRequestPayloadObject().getToTime();
        int tagId = configData.getRequestPayloadObject().getTagId();

        NewTimeIntervalGenerationUtility t = new NewTimeIntervalGenerationUtility();
        t.setTimezoneOffset(accountIdentifier);
        t.processTimeRange(fromTime, toTime);
        t.addEndTimeInOSTime();

        List<Long> times = t.getOSTimes();

        String[] groupName = new String[1];
        configData.getRequestPayloadObject().getTaggedTransactionList().parallelStream()
                .map(BasicTransactionEntity::getTransactionGroups)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .filter(c -> c.getTransactionGroupName() != null)
                .findAny().ifPresent(group -> groupName[0] = (group.getTransactionGroupName()));

        long numberOfAggregatedRecords = configData.getRequestPayloadObject().getTaggedTransactionList().size();

        long start = System.currentTimeMillis();
        List<TabularResultsTypePojo> tabularResultsTypePojoList;
        if (requestType.trim().equalsIgnoreCase(Constants.SERVICES_CONTROLLER_TYPE)) {
            tabularResultsTypePojoList = collatedTxnRepo.getTransactionCollatedDataByServiceTimeSeriesFalse(accountIdentifier,
                    serviceIdentifier, groupName[0], responseType, tagId, numberOfAggregatedRecords,
                    fromTime, toTime - Constants.MINUTE, configData.getTimeRangeDetails(), configData.getTimeRangeDetailsList(), times);

        } else {
            log.debug("Agent list - {}", configData.getRequestPayloadObject().getAgentList());
            Set<String> agentList = configData.getRequestPayloadObject().getAgentList().parallelStream().collect(Collectors.toSet());
            tabularResultsTypePojoList = collatedTxnRepo.getAgentTransactionCollatedDataByServiceTimeSeriesFalse(accountIdentifier, agentList,
                    serviceIdentifier, groupName[0], responseType, tagId, numberOfAggregatedRecords,
                    fromTime, toTime - Constants.MINUTE, configData.getTimeRangeDetails(), configData.getTimeRangeDetailsList(), times);

        }
        log.debug("Time taken for querying data from OpenSearch is:{} ms",
                (System.currentTimeMillis() - start));
        start = System.currentTimeMillis();

        if (tabularResultsTypePojoList == null || tabularResultsTypePojoList.isEmpty()) {
            result.setAll(configData.getRequestPayloadObject().getTaggedTransactionList()
                    .stream()
                    .map(c -> sendEmptyResult(c, configData))
                    .collect(Collectors.toList()));
            return result;
        }

        /*Extract data from tabular results*/
        Map<String, Map<String, Double>> txnAttributes = new HashMap<>();
        Map<String, List<String>> listMap = new HashMap<>();
        String pattern = ".+\\..*";
        for (TabularResultsTypePojo tabularResultsTypePojo : tabularResultsTypePojoList) {

            TabularResults tabularResults = tabularResultsTypePojo.getTabularResults();
            Map<String, Map<String, Double>> tempTxnAttributes = new HashMap<>();
            Map<String, List<String>> tempListMap = new HashMap<>();

            for (TabularResults.ResultRow resultRow : tabularResults.getRowResults()) {
                String txnId = "";
                for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows())
                    if (!resultRowColumn.getColumnName().matches(pattern)) {
                        if (tempTxnAttributes.containsKey(resultRowColumn.getColumnValue())) {
                            txnId = resultRowColumn.getColumnValue();
                            break;
                        } else if (!tempTxnAttributes.containsKey(resultRowColumn.getColumnValue())) {
                            tempTxnAttributes.put(resultRowColumn.getColumnValue(), new HashMap<>());
                            txnId = resultRowColumn.getColumnValue();
                            break;
                        }
                    }

                for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {
                    if (resultRowColumn.getColumnName().matches(pattern)) {

                        String attributeName = resultRowColumn.getColumnName().split("\\.")[1];
                        if (tempTxnAttributes.get(txnId).containsKey(attributeName))
                            if (attributeName.equalsIgnoreCase("RESPONSE_TIME") && Double.parseDouble(resultRowColumn.getColumnValue()) > 1)
                                tempTxnAttributes.get(txnId).put(attributeName, (tempTxnAttributes.get(txnId).get(attributeName) + Double.parseDouble(resultRowColumn.getColumnValue())) / 2);
                            else
                                tempTxnAttributes.get(txnId).put(attributeName, tempTxnAttributes.get(txnId).get(attributeName) + Double.parseDouble(resultRowColumn.getColumnValue()));
                        else
                            tempTxnAttributes.get(txnId).put(attributeName, Double.parseDouble(resultRowColumn.getColumnValue()));

                    } else if (resultRowColumn.getColumnName().startsWith("UNIQUE:")) {
                        if (tempListMap.containsKey(txnId)) {
                            tempListMap.get(txnId).addAll(Arrays.asList(resultRowColumn.getColumnValue().split(",")));
                        } else {
                            tempListMap.put(txnId, Arrays.asList(resultRowColumn.getColumnValue().split(",")));
                        }
                    }
                }
            }

            tempTxnAttributes.forEach((key, value) -> {
                if (txnAttributes.containsKey(key)) {
                    Map<String, Double> tMap = new HashMap<>();
                    txnAttributes.get(key).forEach((k, v) -> tMap.put(k, v + value.getOrDefault(k, 0D)));
                    txnAttributes.put(key, tMap);
                } else txnAttributes.put(key, value);
            });

            tempListMap.forEach((key, value) -> {
                if (listMap.containsKey(key)) {
                    Set<String> tempAgentName = listMap.get(key).parallelStream().collect(Collectors.toSet());
                    tempAgentName.addAll(value);
                    listMap.put(key, tempAgentName.parallelStream().collect(Collectors.toList()));
                } else listMap.put(key, value);
            });
        }
        log.debug("Time taken for processing data received from OpenSearch is:{} ms", (System.currentTimeMillis() - start));
        start = System.currentTimeMillis();

        Map<String, BasicTransactionEntity> transactionDetailsMap = configData.getRequestPayloadObject().getTaggedTransactionList()
                .parallelStream().collect(Collectors.toMap(BasicEntity::getIdentifier, c -> c));

        /*get result*/
        List<RowDetails> rowDetails = transactionDetailsMap.entrySet().parallelStream()
                .map(c -> {
                    BasicTransactionEntity txn = c.getValue();

                    RowDetails temp = RowDetails.builder()
                            .transaction_id(txn.getId())
                            .transaction_name(txn.getName())
                            .transactionIdentifier(c.getKey())
                            .service_id(configData.getRequestPayloadObject().getServiceDetails().getId())
                            .application_id(configData.getRequestPayloadObject().getServiceDetails().getId())
                            .average_response_time_unit("ms")
                            .source(Constants.UNKNOWN_SOURCE)
                            .build();

                    if (txnAttributes.containsKey(c.getKey())) {

                        Map<String, Double> value = txnAttributes.get(c.getKey());
                        if (!value.isEmpty()) {
                            temp.setFailures(value.getOrDefault(KPIAttributes.FAIL_VOLUME.name(), 0D).intValue());
                            temp.setSlow(value.getOrDefault(KPIAttributes.SLOW_VOLUME.name(), 0D).intValue());
                            temp.setSuccess(value.getOrDefault(KPIAttributes.SUCCESS_VOLUME.name(), 0D).intValue());
                            temp.setVolume(value.getOrDefault(KPIAttributes.TOTAL_VOLUME.name(), 0D).intValue());
                            float avgResponseTimeValue = value.getOrDefault(KPIAttributes.RESPONSE_TIME.name(), 0D).intValue();
                            float avgResponseTimeFormatted = Float.parseFloat(CommonUtils.changePrecision(Float.toString(avgResponseTimeValue), 2));
                            temp.setAverage_response_time(avgResponseTimeFormatted);
                            temp.setAuditEnabled(txn.getAuditEnabled());

                            if (temp.getVolume() > 0) {
                                temp.setSlowPercentage(((float) temp.getSlow() / temp.getVolume()) * 100);
                                temp.setFailurePercentage(((float) temp.getFailures() / temp.getVolume()) * 100);
                            } else {
                                temp.setSlowPercentage(0.0f);
                                temp.setFailurePercentage(0.0f);
                            }
                            temp.setSource(new KPIAnomalyService().getSourceName(new HashSet<>(listMap.get(c.getKey())), new AgentRepo()));
                        }
                    }
                    return temp;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        log.debug("Time taken for getting txn details for {} no. of transactions from redis is:{} ms",
                numberOfAggregatedRecords, (System.currentTimeMillis() - start));
        start = System.currentTimeMillis();

        //Sort the result
        rowDetails.sort(Comparator.comparing(RowDetails::getFailurePercentage, Comparator.reverseOrder())
                .thenComparing(RowDetails::getSlowPercentage, Comparator.reverseOrder())
                .thenComparing(RowDetails::getVolume, Comparator.reverseOrder())
                .thenComparing(RowDetails::getTransaction_name));

        List<RowDetails> failTransactionList = new ArrayList<>();
        List<RowDetails> slowTransactionList = new ArrayList<>();

        if (!configData.getRequestPayloadObject().getMode().equalsIgnoreCase(DETAILS)) {
            //Needs to be calculated only when all data or top n data is requested, only when txn details are requested
            //this can be avoided
            start = System.currentTimeMillis();
            failTransactionList = txnDetailService.getFailTxns(rowDetails, configData.getRequestPayloadObject());
            slowTransactionList = txnDetailService.getSlowTxns(rowDetails, configData.getRequestPayloadObject());
        }

        switch (configData.getRequestPayloadObject().getMode()) {
            case TOPN:
                result.setFail(failTransactionList);
                result.setSlow(slowTransactionList);
                break;

            case DETAILS:
                result.setAll(rowDetails);
                break;

            default:
                result.setAll(rowDetails);
                result.setFail(failTransactionList);
                result.setSlow(slowTransactionList);
        }

        log.debug("Time taken for sorting and processing top N transaction is {} ms.", (System.currentTimeMillis() - start));
        return result;
    }

    private TransactionSortedStats processRawTxnData(UtilityBean<TransactionDetailDataRequest> configData) {
        TransactionSortedStats result = new TransactionSortedStats();

        TransactionDetailDataService txnDetailService = new TransactionDetailDataService();
        RawTransactionSearchRepo rawTransactionSearchRepo = new RawTransactionSearchRepo();

        String accountIdentifier = configData.getRequestPayloadObject().getAccountIdString();
        String serviceIdentifier = configData.getRequestPayloadObject().getServiceDetails().getIdentifier();
        String responseType = configData.getRequestPayloadObject().getResponseType();
        String requestType = configData.getRequestPayloadObject().getAggregationType();
        long fromTime = configData.getRequestPayloadObject().getFromTime();
        long toTime = configData.getRequestPayloadObject().getToTime();
        int tagId = configData.getRequestPayloadObject().getTagId();

        String[] groupName = new String[1];
        configData.getRequestPayloadObject().getTaggedTransactionList().parallelStream()
                .map(BasicTransactionEntity::getTransactionGroups)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .filter(t -> t.getTransactionGroupName() != null)
                .findAny().ifPresent(group -> groupName[0] = (group.getTransactionGroupName()));

        long numberOfAggregatedRecords = configData.getRequestPayloadObject().getTaggedTransactionList().size();

        long start = System.currentTimeMillis();
        TabularResults tabularResults;
        if (requestType.trim().equalsIgnoreCase(Constants.SERVICES_CONTROLLER_TYPE)) {

            tabularResults = rawTransactionSearchRepo.getTopNTransactionListClusterLevel(accountIdentifier,
                    serviceIdentifier, groupName[0], tagId, responseType,
                    fromTime, toTime);

        } else {
            log.debug("Agent list - {}", configData.getRequestPayloadObject().getAgentList());
            Set<String> agentList = configData.getRequestPayloadObject().getAgentList().parallelStream().collect(Collectors.toSet());

            tabularResults = rawTransactionSearchRepo.getTopNTransactionListInstanceLevel(accountIdentifier,
                    serviceIdentifier, groupName[0], tagId, agentList, responseType,
                    fromTime, toTime);

        }
        log.debug("Time taken for querying data from OpenSearch is:{} ms", (System.currentTimeMillis() - start));
        start = System.currentTimeMillis();

        if (tabularResults == null || tabularResults.getRowResults() == null || tabularResults.getRowResults().isEmpty()) {
            result.setAll(configData.getRequestPayloadObject().getTaggedTransactionList()
                    .stream()
                    .map(t -> sendEmptyResult(t, configData))
                    .collect(Collectors.toList()));
            return result;
        }

        /*Extract data from tabular results*/
        Map<String, Map<String, Double>> txnAttributes = new HashMap<>();
        Map<String, List<String>> txnAgentMap = new HashMap<>();
        for (TabularResults.ResultRow resultRow : tabularResults.getRowResults()) {
            String txnId = "";
            String kpiName = "";
            Set<String> agentIdentifierSet = new HashSet<>();
            long kpiValue;
            for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {
                if (resultRowColumn.getColumnName().equalsIgnoreCase("txnIdentifier")) {
                    txnId = resultRowColumn.getColumnValue();
                } else if (resultRowColumn.getColumnName().equalsIgnoreCase("UNIQUE: agentIdentifier")) {
                    agentIdentifierSet.addAll(Arrays.asList(resultRowColumn.getColumnValue().split(",")));
                } else {
                    kpiName = resultRowColumn.getColumnValue();
                }
            }
            kpiValue = resultRow.getCountValue();
            Map<String, Double> temp = new HashMap<>();
            temp.put(kpiName.toLowerCase(), (double) kpiValue);

            if (txnAttributes.containsKey(txnId)) {
                if (txnAttributes.get(txnId).containsKey(kpiName.toLowerCase())) {
                    double oldVal = txnAttributes.get(txnId).get(kpiName.toLowerCase());
                    double newVal = (double) kpiValue + oldVal;
                    txnAttributes.get(txnId).put(kpiName.toLowerCase(), newVal);
                }

                temp.putAll(txnAttributes.get(txnId));
            }

            txnAttributes.put(txnId, temp);

            if (txnAgentMap.containsKey(txnId)) {
                agentIdentifierSet.addAll(txnAgentMap.get(txnId));
            }
            txnAgentMap.put(txnId, agentIdentifierSet.parallelStream().collect(Collectors.toList()));
        }

        log.debug("Time taken for processing data received from OpenSearch is:{} ms", (System.currentTimeMillis() - start));
        start = System.currentTimeMillis();

        Map<String, BasicTransactionEntity> transactionDetailsMap = configData.getRequestPayloadObject().getTaggedTransactionList()
                .parallelStream().collect(Collectors.toMap(BasicEntity::getIdentifier, c -> c));

        /*get result*/
        List<RowDetails> rowDetails = transactionDetailsMap.entrySet().parallelStream()
                .map(c -> {
                    BasicTransactionEntity txn = c.getValue();

                    RowDetails temp = RowDetails.builder()
                            .transaction_id(txn.getId())
                            .transaction_name(txn.getName())
                            .transactionIdentifier(c.getKey())
                            .service_id(configData.getRequestPayloadObject().getServiceDetails().getId())
                            .application_id(configData.getRequestPayloadObject().getServiceDetails().getId())
                            .average_response_time_unit("ms")
                            .source(Constants.UNKNOWN_SOURCE)
                            .build();

                    if (txnAttributes.containsKey(c.getKey())) {

                        Map<String, Double> value = txnAttributes.get(c.getKey());
                        double totalVolume = value.values().stream().mapToDouble(Double::longValue).sum();
                        if (!value.isEmpty()) {
                            temp.setFailures(value.getOrDefault(KPIAttributes.FAIL_VOLUME.getColumnName().toLowerCase(), 0D).intValue());
                            temp.setSlow(value.getOrDefault(KPIAttributes.SLOW_VOLUME.getColumnName().toLowerCase(), 0D).intValue());
                            temp.setSuccess(value.getOrDefault(KPIAttributes.SUCCESS_VOLUME.getColumnName().toLowerCase(), 0D).intValue());
                            temp.setVolume((int) totalVolume);
                            float avgResponseTimeValue = value.getOrDefault(KPIAttributes.RESPONSE_TIME.getColumnName().toLowerCase(), 0D).intValue();
                            float avgResponseTimeFormatted = Float.parseFloat(CommonUtils.changePrecision(Float.toString(avgResponseTimeValue), 2));
                            temp.setAverage_response_time(avgResponseTimeFormatted);
                            temp.setAuditEnabled(txn.getAuditEnabled());

                            if (temp.getVolume() > 0) {
                                temp.setSlowPercentage(((float) temp.getSlow() / temp.getVolume()) * 100);
                                temp.setFailurePercentage(((float) temp.getFailures() / temp.getVolume()) * 100);
                            } else {
                                temp.setSlowPercentage(0.0f);
                                temp.setFailurePercentage(0.0f);
                            }
                            temp.setSource(new KPIAnomalyService().getSourceName(new HashSet<>(txnAgentMap.get(c.getKey())), new AgentRepo()));
                        }
                    }
                    return temp;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        log.debug("Time taken for getting txn details for {} no. of transactions from redis is:{} ms",
                numberOfAggregatedRecords, (System.currentTimeMillis() - start));
        start = System.currentTimeMillis();

        //Sort the result
        rowDetails.sort(Comparator.comparing(RowDetails::getFailurePercentage, Comparator.reverseOrder())
                .thenComparing(RowDetails::getSlowPercentage, Comparator.reverseOrder())
                .thenComparing(RowDetails::getVolume, Comparator.reverseOrder())
                .thenComparing(RowDetails::getTransaction_name));

        List<RowDetails> failTransactionList = new ArrayList<>();
        List<RowDetails> slowTransactionList = new ArrayList<>();

        if (!configData.getRequestPayloadObject().getMode().equalsIgnoreCase(DETAILS)) {
            //Needs to be calculated only when all data or top n data is requested, only when txn details are requested
            //this can be avoided
            start = System.currentTimeMillis();
            failTransactionList = txnDetailService.getFailTxns(rowDetails, configData.getRequestPayloadObject());
            slowTransactionList = txnDetailService.getSlowTxns(rowDetails, configData.getRequestPayloadObject());
        }


        switch (configData.getRequestPayloadObject().getMode()) {
            case TOPN:
                result.setFail(failTransactionList);
                result.setSlow(slowTransactionList);
                break;

            case DETAILS:
                result.setAll(rowDetails);
                break;

            default:
                result.setAll(rowDetails);
                result.setFail(failTransactionList);
                result.setSlow(slowTransactionList);
        }

        log.debug("Time taken for sorting and processing top N transaction is {} ms.", (System.currentTimeMillis() - start));
        return result;
    }

    private RowDetails sendEmptyResult(BasicTransactionEntity basicTransactionEntity, UtilityBean<TransactionDetailDataRequest> configData) {

        return RowDetails.builder()
                .transaction_id(basicTransactionEntity.getId())
                .transaction_name(basicTransactionEntity.getName())
                .transactionIdentifier(basicTransactionEntity.getIdentifier())
                .application_id(configData.getRequestPayloadObject().getServiceDetails().getId())
                .service_id(configData.getRequestPayloadObject().getServiceDetails().getId())
                .slow(0)
                .success(0)
                .failures(0)
                .average_response_time(0)
                .volume(0)
                .failurePercentage(0)
                .slowPercentage(0)
                .auditEnabled(0)
                .source(Constants.UNKNOWN_SOURCE)
                .build();

    }
}