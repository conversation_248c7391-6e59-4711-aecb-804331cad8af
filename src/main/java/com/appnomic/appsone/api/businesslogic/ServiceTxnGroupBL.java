/* sagalap created on 28/04/22 inside the package - com.appnomic.appsone.api.businesslogic */
package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.ServiceRepo;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.pojo.transaction.TransactionTag;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.BasicTransactionEntity;
import com.heal.configuration.pojos.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.MessageFormat;
import java.util.Collection;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Prasad
 */
public class ServiceTxnGroupBL implements BusinessLogic<Object, UtilityBean<Object>, List<TransactionTag>> {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceTxnGroupBL.class);

    @Override
    public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
        LOGGER.debug("{} clientValidation().", Constants.INVOKED_METHOD);

        if (requestObject == null) {
            LOGGER.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            throw new ClientException(UIMessages.ACCOUNT_EMPTY + ":" + identifier);
        }


        String serviceIdStr = requestObject.getParams().get(Constants.REQUEST_PARAM_SERVICE_ID);
        if (serviceIdStr == null || serviceIdStr.trim().isEmpty()) {
            throw new ClientException(UIMessages.SERVICE_EMPTY_ERROR + ":" + serviceIdStr);
        }

        int serviceId;
        try {
            serviceId = Integer.parseInt(serviceIdStr);
        } catch (NumberFormatException e) {
            throw new ClientException(e, "Error occurred while converting service id:" + requestObject.getParams().get(Constants.REQUEST_PARAM_SERVICE_ID));
        }

        return UtilityBean.builder()
                .accountIdString(identifier)
                .serviceId(serviceId)
                .build();
    }

    @Override
    public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
        LOGGER.debug("{} serverValidation().", Constants.INVOKED_METHOD);
        AccountRepo accountRepo = new AccountRepo();
        ServiceRepo serviceRepo = new ServiceRepo();

        com.heal.configuration.pojos.Account account = accountRepo.getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
        }
        utilityBean.setAccount(account);

        BasicEntity basicEntity = serviceRepo.getAllServices(account.getIdentifier()).stream().filter(s -> s.getId() == utilityBean.getServiceId()).findAny().orElse(null);
        if (basicEntity == null) {
            LOGGER.error("Invalid service id: {}", utilityBean.getServiceId());
            throw new ServerException(UIMessages.ERROR_INVALID_SERVICE_ID);
        }

        Service service = HealUICache.INSTANCE.getServiceDetails(account.getIdentifier(), basicEntity.getIdentifier());
        if (service == null) {
            LOGGER.error("Invalid service id: {}, identifier:{}", utilityBean.getServiceId(), basicEntity.getIdentifier());
            throw new ServerException(UIMessages.ERROR_INVALID_SERVICE_ID);
        }
        utilityBean.setService(service);

        return utilityBean;
    }

    @Override
    public List<TransactionTag> processData(UtilityBean<Object> utilityBean) throws DataProcessingException {
        LOGGER.debug("{} processData().", Constants.INVOKED_METHOD);
        ServiceRepo serviceRepo = new ServiceRepo();
        try {
            List<BasicTransactionEntity> transactions = serviceRepo.getTransactionsByServiceIdentifier(utilityBean.getAccount().getIdentifier(), utilityBean.getService().getIdentifier())
                    .parallelStream()
                    .filter(c -> c.getStatus() == 1)
                    .filter(c -> c.getMonitorEnabled() == 1)
                    .collect(Collectors.toList());

            List<TransactionTag> transactionTags = transactions.stream().map(BasicTransactionEntity::getTransactionGroups)
                    .flatMap(Collection::parallelStream)
                    .map(txnGrp -> TransactionTag.builder().id(txnGrp.getTransactionGroupId()).name(txnGrp.getTransactionGroupName()).build())
                    .collect(Collectors.groupingBy(Function.identity()))
                    .entrySet().stream()
                    .map(entry -> {
                        TransactionTag transactionTag = entry.getKey();
                        transactionTag.setCount(entry.getValue().size());
                        return transactionTag;
                    }).collect(Collectors.toList());
            transactionTags.add(TransactionTag.builder()
                    .id(0)
                    .name(Constants.ALL)
                    .count(transactions.size())
                    .build());
            return transactionTags;
        } catch (Exception e) {
            throw new DataProcessingException(e, MessageFormat.format("Error occurred while fetching transaction tags for account. AccountId: {0}.", utilityBean.getAccount().getId()));
        }
    }
}
