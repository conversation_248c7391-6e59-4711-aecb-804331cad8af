package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.MasterDataDao;
import com.appnomic.appsone.api.dao.opensearch.CollatedKpiRepo;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.InstanceRepo;
import com.appnomic.appsone.api.dao.redis.KpiRepo;
import com.appnomic.appsone.api.dao.redis.TimeRangeDetailsDao;
import com.appnomic.appsone.api.manager.MySQLConnectionManager;
import com.appnomic.appsone.api.pojo.ForensicKpiData;
import com.appnomic.appsone.api.pojo.ForensicKpiDataResponse;
import com.appnomic.appsone.api.pojo.ForensicKpiMetaData;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.ConfProperties;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.CompInstClusterDetails;
import com.heal.configuration.pojos.CompInstKpiEntity;
import com.heal.configuration.pojos.TimeRangeDetails;
import com.heal.configuration.pojos.opensearch.CollatedKpi;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class ForensicKpiTopNSqlBL implements BusinessLogic<Object, UtilityBean<Object>, ForensicKpiDataResponse> {

    @Override
    public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
        log.debug("{} clientValidation().", Constants.INVOKED_METHOD);
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }
        String accountIdString = requestObject.getParams().get(":identifier");
        String compInstanceId = requestObject.getParams().get(":instanceId");
        String categoryIdentifier = (requestObject.getQueryParams().containsKey(Constants.CATEGORY_IDENTIFIER)) ? requestObject.getQueryParams().get(Constants.CATEGORY_IDENTIFIER)[0] : null;
        String timeString = requestObject.getQueryParams().containsKey("time") ? requestObject.getQueryParams().get("time")[0] : null;
        log.debug("{} clientValidation().", Constants.INVOKED_METHOD);

        if (accountIdString == null || accountIdString.trim().isEmpty())
            throw new ClientException(UIMessages.ACCOUNT_EMPTY + ":" + accountIdString);

        if (compInstanceId == null || compInstanceId.trim().isEmpty())
            throw new ClientException(UIMessages.INVALID_INSTANCE_MESSAGE + ":" + compInstanceId);

        if (categoryIdentifier == null || categoryIdentifier.trim().isEmpty())
            throw new ClientException("Category identifier is required");

        if (timeString == null)
            throw new ClientException("Invalid Time");

        return UtilityBean.builder()
                .accountIdString(accountIdString)
                .componentInstanceIdString(compInstanceId)
                .categoryIdentifier(categoryIdentifier)
                .time(Long.parseLong(timeString))
                .build();
    }

    @Override
    public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
        log.debug("{} serverValidation().", Constants.INVOKED_METHOD);
        InstanceRepo instanceRepo = new InstanceRepo();
        AccountRepo accountRepo = new AccountRepo();

        Account account = accountRepo.getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            log.error("Invalid account identifier: {}.", utilityBean.getAccountIdString());
            throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
        }
        utilityBean.setAccount(account);

        String accountIdentifier = account.getIdentifier();

        TimeRangeDetailsDao timeRangeDetailsDao = new TimeRangeDetailsDao();
        List<TimeRangeDetails> timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(accountIdentifier);
        if (timeRangeDetailsList == null) {
            log.error("Exception occurred while fetching time range details for account {}", accountIdentifier);
            throw new ServerException("Exception occurred while fetching time range details for account " + accountIdentifier);
        }

        if (timeRangeDetailsList.isEmpty()) {
            log.trace("No time range details found for account {}. Searching for Global account.", accountIdentifier);
            timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }
        if (timeRangeDetailsList == null) {
            log.error("Exception occurred while fetching time range details for account {}", Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
            throw new ServerException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }

        if (timeRangeDetailsList.isEmpty()) {
            log.error("No time range details found");
            throw new ServerException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }
        TimeRangeDetails timeRangeDetails = timeRangeDetailsList.stream().filter(c -> c.getName().equals("30")).findFirst().orElse(null);
        if (timeRangeDetails == null) {
            log.error("Time range details are invalid.");
            throw new ServerException("Time range details are invalid.");
        }
        timeRangeDetails.setNeedRolledUpIndexes(0);
        utilityBean.setTimeRangeDetails(timeRangeDetails);

        String categoryIdentifier = utilityBean.getCategoryIdentifier();
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        boolean exists = masterDataDao.existsByIdentifier(categoryIdentifier);
        MySQLConnectionManager.getInstance().close(masterDataDao);
        if (!exists) {
            log.error("Category identifier does not exist in DB: {}", categoryIdentifier);
            throw new ServerException("Category identifier does not exist in DB: " + categoryIdentifier);
        }

        Optional<CompInstClusterDetails> instance = instanceRepo.getInstancesByAccount(account.getIdentifier())
                .parallelStream()
                .filter(c -> c.getId() == Integer.parseInt(utilityBean.getComponentInstanceIdString()))
                .filter(c -> (c.getStatus() == Constants.STATUS_ACTIVE)).findAny();
        if (!instance.isPresent()) {
            log.error("Invalid instance id: {}", utilityBean.getComponentInstanceIdString());
            throw new ServerException(UIMessages.ERROR_INVALID_SERVICE_ID);
        }
        utilityBean.setCompInstance(instance.get());
        return utilityBean;
    }

    @Override
    public ForensicKpiDataResponse processData(UtilityBean<Object> utilityBean) throws DataProcessingException {
        String accountIdentifier = utilityBean.getAccount().getIdentifier();
        String instanceIdentifier = utilityBean.getCompInstance().getIdentifier();

        ForensicKpiDataResponse kpiDataResponse = new ForensicKpiDataResponse();

        KpiRepo kpiRepo = new KpiRepo();
        CollatedKpiRepo collatedKpiRepo = new CollatedKpiRepo();

        List<CompInstKpiEntity> kpiList = kpiRepo.getKpiDetailByAccInst(accountIdentifier, instanceIdentifier)
                .parallelStream()
                .filter(i -> i.getIsGroup() && Constants.MST_SUB_TYPE_FORENSIC.equalsIgnoreCase(i.getType()))
                .collect(Collectors.toList());
        if (kpiList.isEmpty()) {
            log.info("Group Kpis not found for account {} and Instance {}", accountIdentifier, instanceIdentifier);
            return kpiDataResponse;
        }

        Map<Integer, Map<String, List<CollatedKpi>>> collatedKpiAttributeMap = collatedKpiRepo.getCollatedKpisByType(accountIdentifier,
                        instanceIdentifier, "Forensic", utilityBean.getTime(), utilityBean.getTime(),
                        utilityBean.getTimeRangeDetails(), new ArrayList<>(), new ArrayList<>(),utilityBean.getCategoryIdentifier())
                .parallelStream().collect(Collectors.groupingBy(CollatedKpi::getKpiId, Collectors.groupingBy(CollatedKpi::getGroupAttribute)));

        ForensicKpiMetaData metaData = getForensicKpiMeta(utilityBean.getCompInstance().getComponentName());
        Map<String, ForensicKpiData> dataMap = new HashMap<>();
        Map<Integer, ForensicKpiData> map = new HashMap<>();
        final int[] actN = {0};

        kpiList.forEach(kpi -> {
            ForensicKpiData kpiData = new ForensicKpiData(kpi.getId(), kpi.getName(), kpi.getUnit());
            dataMap.put(kpi.getIdentifier(), kpiData);
            map.put(kpi.getId(), kpiData);

            Map<String, String> kpiAttributeValuesMap = new HashMap<>();

            Map<String, List<CollatedKpi>> collatedKpiMap = collatedKpiAttributeMap.getOrDefault(kpi.getId(), Collections.emptyMap());
            if (collatedKpiMap.isEmpty()) return;
            collatedKpiMap.forEach((key, value) -> {
                if (value.get(value.size() - 1) != null && value.get(value.size() - 1).getWatcherValue() != null)
                    kpiAttributeValuesMap.put(key, value.get(value.size() - 1).getWatcherValue().getOrDefault("Forensic", "0"));
                else kpiAttributeValuesMap.put(key, "0");
            });
            actN[0] = Math.max(kpiAttributeValuesMap.size(), actN[0]);
            map.get(kpi.getId()).setValues(kpiAttributeValuesMap);
        });

        kpiDataResponse.setDataMap(dataMap);
        metaData.setActualN(actN[0]);
        kpiDataResponse.setMetaData(metaData);

        return kpiDataResponse;
    }

    private ForensicKpiMetaData getForensicKpiMeta(String componentName) {
        ForensicKpiMetaData metaData = new ForensicKpiMetaData();
        String popKpis;
        String sortBy;
        if (componentName.equalsIgnoreCase("Oracle")) {
            popKpis = ConfProperties.getString(componentName.toLowerCase() + Constants.QUERY_DEEP_DRIVE_POPUP_KPIs_PROPERTY, Constants.ORA_QUERY_DEEP_DRIVE_POPUP_KPIs);
            sortBy = ConfProperties.getString(componentName.toLowerCase() + Constants.QUERY_DEEP_DRIVE_SORT_KPI_PROPERTY, Constants.ORA_QUERY_DEEP_DRIVE_SORT_KPI);

            metaData.setSqlIdKpi(ConfProperties.getString(componentName.toLowerCase() + Constants.QUERY_DEEP_DRIVE_SQLID_KPI_PROPERTY, Constants.ORA_QUERY_DEEP_DRIVE_SQLID_KPI));
            metaData.setSqlTextKpi(ConfProperties.getString(componentName.toLowerCase() + Constants.QUERY_DEEP_DRIVE_SQLTEXT_KPI_PROPERTY, Constants.ORA_QUERY_DEEP_DRIVE_SQLTEXT_KPI));
        } else if (componentName.equalsIgnoreCase("MSSQL")) {
            popKpis = ConfProperties.getString(componentName.toLowerCase() + Constants.QUERY_DEEP_DRIVE_POPUP_KPIs_PROPERTY, Constants.MSSQL_QUERY_DEEP_DRIVE_POPUP_KPIs);
            sortBy = ConfProperties.getString(componentName.toLowerCase() + Constants.QUERY_DEEP_DRIVE_SORT_KPI_PROPERTY, Constants.MSSQL_QUERY_DEEP_DRIVE_SORT_KPI);

            metaData.setSqlIdKpi(ConfProperties.getString(componentName.toLowerCase() + Constants.QUERY_DEEP_DRIVE_SQLID_KPI_PROPERTY, Constants.MSSQL_QUERY_DEEP_DRIVE_SQLID_KPI));
            metaData.setSqlTextKpi(ConfProperties.getString(componentName.toLowerCase() + Constants.QUERY_DEEP_DRIVE_SQLTEXT_KPI_PROPERTY, Constants.MSSQL_QUERY_DEEP_DRIVE_SQLTEXT_KPI));
        } else if (componentName.equalsIgnoreCase("PostgreSQL")) {
            popKpis = ConfProperties.getString(componentName.toLowerCase() + Constants.QUERY_DEEP_DRIVE_POPUP_KPIs_PROPERTY, Constants.POSTGRES_QUERY_DEEP_DRIVE_POPUP_KPIs);
            sortBy = ConfProperties.getString(componentName.toLowerCase() + Constants.QUERY_DEEP_DRIVE_SORT_KPI_PROPERTY, Constants.POSTGRES_QUERY_DEEP_DRIVE_SORT_KPI);

            metaData.setSqlIdKpi(ConfProperties.getString(componentName.toLowerCase() + Constants.QUERY_DEEP_DRIVE_SQLID_KPI_PROPERTY, Constants.POSTGRES_QUERY_DEEP_DRIVE_SQLID_KPI));
            metaData.setSqlTextKpi(ConfProperties.getString(componentName.toLowerCase() + Constants.QUERY_DEEP_DRIVE_SQLTEXT_KPI_PROPERTY, Constants.POSTGRES_QUERY_DEEP_DRIVE_SQLTEXT_KPI));
        } else {
            popKpis = ConfProperties.getString(componentName.toLowerCase() + Constants.QUERY_DEEP_DRIVE_POPUP_KPIs_PROPERTY, Constants.QUERY_DEEP_DRIVE_POPUP_KPIs);
            sortBy = ConfProperties.getString(componentName.toLowerCase() + Constants.QUERY_DEEP_DRIVE_SORT_KPI_PROPERTY, Constants.QUERY_DEEP_DRIVE_SORT_KPI);

            metaData.setSqlIdKpi(ConfProperties.
                    getString(componentName.toLowerCase() + Constants.QUERY_DEEP_DRIVE_SQLID_KPI_PROPERTY, Constants.QUERY_DEEP_DRIVE_SQLID_KPI));
            metaData.setSqlTextKpi(ConfProperties.getString(componentName.toLowerCase() + Constants.QUERY_DEEP_DRIVE_SQLTEXT_KPI_PROPERTY, Constants.QUERY_DEEP_DRIVE_SQLTEXT_KPI));
        }
        metaData.setSqlPopupKpis(Stream.of(popKpis.split(",", -1)).collect(Collectors.toList()));
        metaData.setSortBy(Stream.of(sortBy.split(",", -1)).collect(Collectors.toList()));
        return metaData;
    }

}
