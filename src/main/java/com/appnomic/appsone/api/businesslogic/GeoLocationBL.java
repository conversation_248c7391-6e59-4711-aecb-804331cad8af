package com.appnomic.appsone.api.businesslogic;

import java.util.ArrayList;
import java.util.List;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.mobile.model.GeoLocation;
import com.appnomic.appsone.api.mobile.util.BigQueryService;
import com.appnomic.appsone.api.mobile.util.BigQueryUtil;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.FieldValueList;
import com.google.cloud.bigquery.QueryJobConfiguration;
import com.google.cloud.bigquery.TableResult;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class GeoLocationBL implements BusinessLogic<Object, UtilityBean<Object>, List<GeoLocation>> {

	@Override
	public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
		List<String> errorMessages = new ArrayList<>();
		if (!BigQueryUtil.appOSStringValidation(requestObject, errorMessages)
			|| !BigQueryUtil.appDisplayVersionValidation(requestObject, errorMessages)
			|| !BigQueryUtil.fromTimeToTimeValidation(requestObject, errorMessages)) {
			throw new ClientException(String.join(";", errorMessages));
		}
		String appOSString = requestObject.getQueryParams().get("app-os")[0];
		String appDisplayVersions = requestObject.getQueryParams().get("app-build-version")[0];
		String fromTime = requestObject.getQueryParams().get("from-time")[0];
		String toTime = requestObject.getQueryParams().get("to-time")[0];
		return UtilityBean.<Object>builder()
				.appOSString(appOSString)
				.appDisplayVersions(appDisplayVersions)
                .fromTimeString(fromTime)
                .toTimeString(toTime)
                .build();
	}

	@Override
	public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
		return utilityBean;
	}

	@Override
	public List<GeoLocation> processData(UtilityBean<Object> configData) throws DataProcessingException {
		String appOSString = configData.getAppOSString();
		BigQuery bigQuery = BigQueryService.getInstance(appOSString.split("\\.")[0]);
		log.debug("bigQuery Obj: {}", bigQuery);

		String appDisplayVersionInParam = BigQueryUtil.getInParameter(configData.getAppDisplayVersions());
		String fromTime = configData.getFromTimeString();
		String toTime = configData.getToTimeString();
		
		String query = 	"SELECT  " + 
						"    COUNT(DISTINCT user_id) AS users, " + 
						"    STRING_AGG(DISTINCT geo.continent) AS continent, " + 
						"    STRING_AGG(DISTINCT geo.sub_continent) AS subContinent, " + 
						"    STRING_AGG(DISTINCT geo.country) AS country, " + 
						"    STRING_AGG(DISTINCT geo.region) AS region, " + 
						"    STRING_AGG(DISTINCT geo.city) AS city, " + 
						"    STRING_AGG(DISTINCT geo.metro) AS metro " + 
						"FROM " + Constants.getAnalyticsTable(appOSString) + " AS T " + 
						"    CROSS JOIN       T.event_params " + 
						"WHERE  " + 
						"    event_timestamp > " + fromTime + "000" +
						"    AND event_timestamp < " + toTime + "000" +
						"    AND app_info.version IN " + appDisplayVersionInParam + " " +
						"GROUP BY " + 
						"    geo.country";
		
		QueryJobConfiguration queryConfig = QueryJobConfiguration.newBuilder(query).setUseLegacySql(false).build();
		
		TableResult result;
		try {
			result = bigQuery.query(queryConfig);
		} catch(InterruptedException inExc) {
			throw new DataProcessingException("Error in fetching data from BigQuery");
		}
		
		List<GeoLocation> response = new ArrayList<>();
		
		for(FieldValueList value : result.iterateAll()) {
			GeoLocation geoLocation = new GeoLocation();
			geoLocation.setCity(value.get("city").getStringValue());
			geoLocation.setContinent(value.get("continent").getStringValue());
			geoLocation.setCountry(value.get("country").getStringValue());
			geoLocation.setMetro(value.get("metro").getStringValue());
			geoLocation.setRegion(value.get("region").getStringValue());
			geoLocation.setSubContinent(value.get("subContinent").getStringValue());
			geoLocation.setUsers(value.get("users").getLongValue());
			response.add(geoLocation);
		}
		
		return response;
	}
	
}
