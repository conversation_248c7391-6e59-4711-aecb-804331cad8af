package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.beans.SolutionRecommendationFeedbackBean;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.opensearch.SignalSearchRepo;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.SolutionIsUsefulFeedbackPojo;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.service.mysql.SolutionRecommendationDataService;
import com.appnomic.appsone.api.util.DateTimeUtil;
import com.appnomic.appsone.api.util.StringUtils;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.opensearch.SignalDetails;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.text.MessageFormat;

@Slf4j
public class TakeSolutionIsUsefulFeedbackBL implements BusinessLogic<SolutionRecommendationFeedbackBean, UtilityBean<SolutionRecommendationFeedbackBean>, Object> {

    @Override
    public UtilityBean<SolutionRecommendationFeedbackBean> clientValidation(RequestObject requestObject) throws ClientException {

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION_HEADER.toLowerCase());
        String identifier = requestObject.getParams().get(Constants.REQUEST_PARAM_IDENTIFIER);
        String signalIdStr = requestObject.getParams().get(Constants.REQUEST_PARAM_SIGNAL_ID.toLowerCase());

        if (StringUtils.isEmpty(authKey)) {
            log.error(Constants.MESSAGE_EMPTY_AUTH_TOKEN);
            throw new ClientException(Constants.MESSAGE_EMPTY_AUTH_TOKEN);
        }

        if (StringUtils.isEmpty(identifier)) {
            log.error(Constants.MESSAGE_EMPTY_ACCOUNT);
            throw new ClientException(Constants.MESSAGE_EMPTY_ACCOUNT);
        }

        if (signalIdStr == null || signalIdStr.trim().isEmpty()) {
            log.error("Signal id should not be empty or null.");
            throw new ClientException("Invalid signal Id.");
        }

        ObjectMapper objectMapper = new ObjectMapper();
        SolutionRecommendationFeedbackBean feedbackPojo;
        try {
            feedbackPojo = objectMapper.readValue(requestObject.getBody(), new TypeReference<SolutionRecommendationFeedbackBean>() {
            });
        } catch (IOException e) {
            throw new ClientException(e, Constants.REQUEST_BODY_INVALID_ERROR_MESSAGE);
        }

        if (feedbackPojo.getClusterId() == null || feedbackPojo.getClusterId().toString().isEmpty()) {
            log.error("Cluster id should not be empty or null.");
            throw new ClientException("Invalid Cluster Id.");
        }

        if (feedbackPojo.getKpiVector() == null || feedbackPojo.getKpiVector().isEmpty()) {
            log.error("KPI vector should not be empty or null.");
            throw new ClientException("Invalid KPI vector.");
        }

        feedbackPojo.setSignalId(signalIdStr);

        return UtilityBean.<SolutionRecommendationFeedbackBean>builder().authToken(authKey)
                .accountIdString(identifier)
                .requestPayloadObject(feedbackPojo)
                .build();
    }

    @Override
    public UtilityBean<SolutionRecommendationFeedbackBean> serverValidation(UtilityBean<SolutionRecommendationFeedbackBean> utilityBean) throws ServerException {
        AccountRepo accountRepo = new AccountRepo();
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            throw new ServerException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }
        utilityBean.setUserId(userId);
        utilityBean.getRequestPayloadObject().setFeedbackUserId(userId);

        com.heal.configuration.pojos.Account account = accountRepo.getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
        }
        utilityBean.setAccount(account);

        //Validate signal id
        SignalSearchRepo signalSearchRepo = new SignalSearchRepo();
        SignalDetails signal = signalSearchRepo.getSignalsBySignalId(utilityBean.getRequestPayloadObject().getSignalId(), utilityBean.getAccount().getIdentifier());
        if (signal == null) {
            log.info("Unable to fetch details for signal with id: {}", utilityBean.getRequestPayloadObject());
            throw new ServerException(MessageFormat.format("Unable to fetch details for signal with id: {0}", utilityBean.getRequestPayloadObject()));
        }

        return utilityBean;
    }

    @Override
    public Object processData(UtilityBean<SolutionRecommendationFeedbackBean> data) throws DataProcessingException {

        SolutionRecommendationFeedbackBean feedback = data.getRequestPayloadObject();
        SolutionRecommendationDataService recommendationDataService = new SolutionRecommendationDataService();

        SolutionRecommendationFeedbackBean oldFeedback = recommendationDataService.getUserFeedback(feedback.getSignalId(), feedback.getFeedbackUserId());

        if (oldFeedback == null) {
            feedback.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
            feedback.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
            feedback.setVector(feedback.getKpiVector().toString());
            int feedbackId = recommendationDataService.createFeedbackEntry(feedback);
            if (feedbackId == 0) {
                throw new DataProcessingException("Failed to update user feedback.");
            }

            feedback.setId(feedbackId);
            int status = recommendationDataService.createFeedbackMapping(feedback);
            if (status == 0) {
                throw new DataProcessingException("Failed to update user feedback.");
            }

        } else {
            feedback.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
            SolutionIsUsefulFeedbackPojo isUsefulFeedback = recommendationDataService.getFeedbackIsUsefulBySolution(oldFeedback.getId(), feedback.getSolutionId(), feedback.getClusterId());
            if (isUsefulFeedback == null) {
                feedback.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
                feedback.setId(oldFeedback.getId());
                int status = recommendationDataService.createFeedbackMapping(feedback);
                if (status == 0) {
                    throw new DataProcessingException("Failed to update user feedback.");
                }
            } else {
                int status = recommendationDataService.updateSolutionFeedbackIsUseful(feedback.getIsUseful(), oldFeedback.getId(), feedback.getUpdatedTime(), feedback.getSolutionId(), feedback.getClusterId());
                if (status == 0) {
                    throw new DataProcessingException("Failed to update user feedback.");
                }
            }
        }
        return null;
    }
}
