package com.appnomic.appsone.api.businesslogic;

import java.util.ArrayList;
import java.util.List;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.mobile.util.BigQueryService;
import com.appnomic.appsone.api.mobile.util.BigQueryUtil;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.QueryJobConfiguration;
import com.google.cloud.bigquery.TableResult;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class GetLatestCrashedAppVersionsBL implements BusinessLogic<Object, UtilityBean<Object>, List<String>> {

	@Override
	public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
		List<String> errorMessages = new ArrayList<>();
		if(!BigQueryUtil.appOSStringValidation(requestObject, errorMessages)) {
			throw new ClientException(String.join(";", errorMessages));
		}
		String appOSString = requestObject.getQueryParams().get("app-os")[0];
		Integer rowLimit = (null != requestObject.getQueryParams().get("row-limit") && requestObject.getQueryParams().get("row-limit").length != 0 && 0 < Integer.parseInt(requestObject.getQueryParams().get("row-limit")[0])) ? 
				Integer.parseInt(requestObject.getQueryParams().get("row-limit")[0]) : 5;
		
		return  UtilityBean.<Object>builder()
				.appOSString(appOSString)
				.rowLimit(rowLimit)
				.build();
	}

	@Override
	public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
		return utilityBean;
	}

	@Override
	public List<String> processData(UtilityBean<Object> configData) throws DataProcessingException {
		String appOSString = configData.getAppOSString();
		BigQuery bigQuery = BigQueryService.getInstance(appOSString.split("\\.")[0]);
		log.debug("bigQuery Obj: {}", bigQuery);

        Integer rowLimit = configData.getRowLimit();

        String query = 	"SELECT application.display_version AS version" +
                "	  	FROM " + Constants.getCrashlyticsTable(appOSString) +
                "      	GROUP BY version" +
                "      	ORDER BY version DESC LIMIT " + rowLimit;

		QueryJobConfiguration queryConfig = QueryJobConfiguration.newBuilder(query).setUseLegacySql(false).build();

		List<String> lastCrashedAppVersions = new ArrayList<>();
		TableResult result;
		try {
			result = bigQuery.query(queryConfig);
		} catch (InterruptedException inExc) {
			throw new DataProcessingException("Error in fetching data from BigQuery");
		}
		result.iterateAll().forEach(value -> lastCrashedAppVersions.add(value.get("version").getStringValue()));
		return lastCrashedAppVersions;
	}
}
