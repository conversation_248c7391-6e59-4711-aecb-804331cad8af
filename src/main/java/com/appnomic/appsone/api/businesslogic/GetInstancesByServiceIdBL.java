package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.ServiceRepo;
import com.appnomic.appsone.api.pojo.*;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.StringUtils;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.heal.configuration.entities.BasicInstanceBean;
import com.heal.configuration.pojos.BasicEntity;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class GetInstancesByServiceIdBL implements BusinessLogic<String, UtilityBean<String>, List<IdPojo>> {

    @Override
    public UtilityBean<String> clientValidation(RequestObject requestObject) throws ClientException {

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION_HEADER.toLowerCase());
        String identifier = requestObject.getParams().get(Constants.REQUEST_PARAM_IDENTIFIER);
        String serviceId = requestObject.getParams().get(Constants.REQUEST_PARAM_SERVICE_ID.toLowerCase());

        if (StringUtils.isEmpty(authKey)) {
            log.error(Constants.MESSAGE_EMPTY_AUTH_TOKEN);
            throw new ClientException(Constants.MESSAGE_EMPTY_AUTH_TOKEN);
        }

        if (StringUtils.isEmpty(identifier)) {
            log.error(Constants.MESSAGE_EMPTY_ACCOUNT);
            throw new ClientException(Constants.MESSAGE_EMPTY_ACCOUNT);
        }

        if (serviceId == null || serviceId.trim().isEmpty()) {
            log.error("Service id should not be empty or null.");
            throw new ClientException("Invalid service Id.");
        }

        return UtilityBean.<String>builder().authToken(authKey)
                .accountIdString(identifier)
                .requestPayloadObject(serviceId)
                .build();
    }

    @Override
    public UtilityBean<String> serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        AccountRepo accountRepo = new AccountRepo();
        ServiceRepo serviceRepo = new ServiceRepo();

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            throw new ServerException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }
        utilityBean.setUserId(userId);

        com.heal.configuration.pojos.Account account = accountRepo.getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
        }
        utilityBean.setAccount(account);

        BasicEntity basicEntity = serviceRepo.getAllServices(account.getIdentifier()).stream().filter(s -> s.getId() == Integer.parseInt(utilityBean.getRequestPayloadObject())).findAny().orElse(null);
        if (basicEntity == null) {
            log.error("Invalid service id: {}", utilityBean.getServiceId());
            throw new ServerException(UIMessages.ERROR_INVALID_SERVICE_ID);
        }
        utilityBean.setRequestPayloadObject(basicEntity.getIdentifier());
        return utilityBean;
    }

    @Override
    public List<IdPojo> processData(UtilityBean<String> data) throws DataProcessingException {
        List<BasicInstanceBean> details = HealUICache.INSTANCE.getServiceInstanceList(data.getAccount().getIdentifier(), data.getRequestPayloadObject(), true);

        return details.parallelStream()
                .map(list -> IdPojo.builder()
                        .id(list.getId())
                        .identifier(list.getIdentifier())
                        .name(list.getName())
                        .build()
                )
                .collect(Collectors.toList());
    }
}
