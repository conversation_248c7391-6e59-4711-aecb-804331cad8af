package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.AppsoneException;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.ApplicationRepo;
import com.appnomic.appsone.api.pojo.ComponentInstancesResponse;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.StringUtils;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.heal.configuration.pojos.Application;
import com.heal.configuration.pojos.BasicEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

public class ComponentInstanceBL implements BusinessLogic<Integer, UtilityBean<Integer>, Set<ComponentInstancesResponse>> {
    private static final Logger logger = LoggerFactory.getLogger(ComponentInstanceBL.class);

    @Override
    public UtilityBean<Integer> clientValidation(RequestObject requestObject) throws ClientException {
        logger.debug("Inside Client validation");

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION_HEADER);
        String identifier = requestObject.getParams().get(Constants.REQUEST_PARAM_IDENTIFIER);
        String applicationIdStr = requestObject.getParams().get(Constants.REQUEST_PARAM_APPLICATION_ID);
        String componentIdStr = requestObject.getParams().get(Constants.REQUEST_PARAM_COMPONENT_ID);
        int componentId;
        int applicationId;

        if (StringUtils.isEmpty(authKey)) {
            logger.error(Constants.MESSAGE_EMPTY_AUTH_TOKEN);
            throw new ClientException(Constants.MESSAGE_EMPTY_AUTH_TOKEN);
        }

        if (StringUtils.isEmpty(identifier)) {
            logger.error(Constants.MESSAGE_EMPTY_ACCOUNT);
            throw new ClientException(Constants.MESSAGE_EMPTY_ACCOUNT);
        }

        if (applicationIdStr == null || applicationIdStr.trim().isEmpty()) {
            logger.error("Application id should not be empty or null.");
            throw new ClientException(UIMessages.SERVICE_EMPTY_ERROR);
        } else {
            try {
                applicationId = Integer.parseInt(applicationIdStr);
            } catch (NumberFormatException e) {
                logger.error("Error occurred while converting the application id {}. Reason: {}", applicationIdStr, e.getMessage());
                throw new ClientException(UIMessages.INVALID_SERVICE);
            }
        }

        if (componentIdStr == null || componentIdStr.trim().isEmpty()) {
            logger.error("Component id should not be empty or null.");
            throw new ClientException(UIMessages.SERVICE_EMPTY_ERROR);
        } else {
            try {
                componentId = Integer.parseInt(componentIdStr);
            } catch (NumberFormatException e) {
                logger.error("Error occurred while converting the component id {}. Reason: {}", componentIdStr, e.getMessage());
                throw new ClientException(UIMessages.INVALID_SERVICE);
            }
        }

        return UtilityBean.<Integer>builder()
                .authToken(authKey)
                .accountIdString(identifier)
                .applicationId(applicationId)
                .requestPayloadObject(componentId)
                .build();
    }

    @Override
    public UtilityBean<Integer> serverValidation(UtilityBean<Integer> utilityBean) throws ServerException {
        ApplicationRepo applicationRepo = new ApplicationRepo();
        AccountRepo accountRepo = new AccountRepo();
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            throw new AppsoneException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }
        utilityBean.setUserId(userId);

        com.heal.configuration.pojos.Account account = accountRepo.getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            throw new AppsoneException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
        }
        utilityBean.setAccount(account);

        Application application = applicationRepo.getApplicationDetailsWithAppId(account.getIdentifier(), utilityBean.getApplicationId());
        if (application == null) {
            throw new ServerException(UIMessages.ERROR_INVALID_APPLICATION_ID + ":" + utilityBean.getApplicationId());
        }
        utilityBean.setApplication(application);
        return utilityBean;
    }

    @Override
    public Set<ComponentInstancesResponse> processData(UtilityBean<Integer> configData) throws DataProcessingException {
        List<BasicEntity> appServices = HealUICache.INSTANCE.getApplicationServiceList(configData.getAccount().getIdentifier(), configData.getApplication().getIdentifier());

        return appServices.parallelStream()
                .map(s -> HealUICache.INSTANCE.getServiceInstanceList(configData.getAccount().getIdentifier(), s.getIdentifier(), true)
                        .stream().map(b -> ComponentInstancesResponse.builder()
                                .serviceId(s.getId())
                                .serviceName(s.getName())
                                .id(b.getId())
                                .name(b.getName())
                                .build())
                        .collect(Collectors.toList())
                ).flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }
}
