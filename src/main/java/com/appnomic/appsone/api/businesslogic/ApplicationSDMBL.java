package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.SignalStatus;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.opensearch.AnomalySearchRepo;
import com.appnomic.appsone.api.dao.opensearch.MaintenanceWindowSearchRepo;
import com.appnomic.appsone.api.dao.opensearch.SignalSearchRepo;
import com.appnomic.appsone.api.dao.redis.*;
import com.appnomic.appsone.api.pojo.*;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.service.TopologyDetailsService;
import com.appnomic.appsone.api.logging.PrintErrorLogs;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.api.util.StringUtils;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.heal.configuration.entities.BasicInstanceBean;
import com.heal.configuration.pojos.*;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.Application;
import com.heal.configuration.pojos.opensearch.SignalDetails;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class ApplicationSDMBL implements BusinessLogic<ApplicationSDMRequestBean, UtilityBean<ApplicationSDMRequestBean>, List<TopologyDetailsResponse.TopologyDetails>> {

    private static final String SHOW_SDM_TOPOLOGY = ConfProperties.getString(Constants.SHOW_SDM_TOPOLOGY, Constants.SHOW_SDM_TOPOLOGY_DEFAULT_VALUE);

    @Override
    public UtilityBean<ApplicationSDMRequestBean> clientValidation(RequestObject request) throws ClientException {
        if (request == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (authToken == null || authToken.trim().isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String accIdentifier = request.getParams().get(Constants.REQUEST_PARAM_IDENTIFIER);
        if (accIdentifier == null || accIdentifier.trim().isEmpty()) {
            throw new ClientException("Request Exception : Account Identifier is null or empty.");
        }

        List<String> appIds = new ArrayList<>();
        if (request.getParams().get(Constants.REQUEST_PARAM_APPLICATION_SMALLID).equalsIgnoreCase("all")) {
            appIds = Arrays.stream(request.getQueryParams().get(Constants.REQUEST_QUERY_PARAM_APPLICATION_ID)[0].toLowerCase().split(",")).collect(Collectors.toList());
            for (String appId : appIds) {
                if (StringUtils.isEmpty(appId)) {
                    log.error(Constants.MESSAGE_INVALID_APPLICATION + ": {}", appId);
                    throw new ClientException(Constants.MESSAGE_INVALID_APPLICATION);
                } else {
                    try {
                        Integer.parseInt(appId);
                    } catch (NumberFormatException e) {
                        log.error("Error occurred while converting the application id [{}]. Reason: {}", appId, e.getMessage());
                    }
                }
            }

        } else {
            appIds.add(request.getParams().get(Constants.REQUEST_PARAM_APPLICATION_SMALLID));
            for (String appId : appIds) {
                if (StringUtils.isEmpty(appId)) {
                    log.error(Constants.MESSAGE_INVALID_APPLICATION + ": {}", appId);
                    throw new ClientException(Constants.MESSAGE_INVALID_APPLICATION);
                } else {
                    try {
                        Integer.parseInt(appId);
                    } catch (NumberFormatException e) {
                        log.error("Error occurred while converting the application id [{}]. Reason: {}", appId, e.getMessage());
                    }
                }
            }
        }
        List<Integer> applicationIds = appIds.stream().map(Integer::parseInt).collect(Collectors.toList());

        long fromTime;
        long toTime;
        try {
            fromTime = Long.parseLong(request.getQueryParams().get(Constants.REQUEST_PARAM_FROM_TIME)[0]);
            toTime = Long.parseLong(request.getQueryParams().get(Constants.REQUEST_PARAM_TO_TIME)[0]);
        } catch (NumberFormatException e) {
            log.error("Invalid fromTime/toTime found where number is required. {}", e.getMessage());
            throw new ClientException("Invalid fromTime/toTime found where number is required.");
        }
        if (fromTime <= 0 || toTime <= 0 || fromTime > toTime) {
            log.error("Invalid time range provided");
            throw new ClientException("Invalid time range provided");
        }

        return UtilityBean.<ApplicationSDMRequestBean>builder()
                .accountIdString(accIdentifier)
                .fromTime(fromTime)
                .toTime(toTime)
                .appIds(applicationIds)
                .authToken(authToken)
                .requestPayloadObject(ApplicationSDMRequestBean.builder().build())
                .build();
    }

    @Override
    public UtilityBean<ApplicationSDMRequestBean> serverValidation(UtilityBean<ApplicationSDMRequestBean> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token.");
            throw new ServerException("Error while extracting user details from authorization token.");
        }

        Account account = new AccountRepo().getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            log.error("Account identifier is invalid.");
            throw new ServerException("Account identifier is invalid.");
        }

        ApplicationRepo applicationRepo = new ApplicationRepo();

        Set<BasicEntity> allInputAppList = new HashSet<>();

        List<Application> allApplications = applicationRepo.getAllApplicationDetails(account.getIdentifier());
        if (allApplications == null || allApplications.isEmpty()) {
            log.error("No application found for account: {}", account.getIdentifier());
            throw new ServerException("No application found for account " + account.getIdentifier());
        }
        utilityBean.getRequestPayloadObject().setAllApplications(allApplications);

        Set<BasicEntity> allAccessibleAppList = applicationRepo.getAccessibleApplicationsByUserId(userId, account.getIdentifier());
        if (allAccessibleAppList == null || allAccessibleAppList.isEmpty()) {
            log.error("No accessible application found for user {} and account: {}", userId, account.getIdentifier());
            throw new ServerException("No application found for user " + userId + " and account " + account.getIdentifier());
        }
        utilityBean.getRequestPayloadObject().setAllAccessibleApplications(allAccessibleAppList);

        for (int id : utilityBean.getAppIds()) {
            Application application = allApplications.stream().filter(e -> e.getId() == id).findAny().orElse(null);
            if (application == null) {
                log.error("No application with id {} found mapped to account: {}", id, account.getIdentifier());
                throw new ServerException(MessageFormat.format("Invalid application id: {0}", utilityBean.getApplicationId()));
            }
            allInputAppList.add(application);

            BasicEntity app = allAccessibleAppList.stream().filter(c -> c.getId() == id).findAny().orElse(null);
            if (app == null) {
                String errorMsg = "User " + userId + " does not have access to application id: " + id + ".";
                log.error(errorMsg);
                throw new ServerException(errorMsg);
            }
        }

        utilityBean.getRequestPayloadObject().setUserInputApplications(allInputAppList);

        return utilityBean;
    }

    @Override
    public List<TopologyDetailsResponse.TopologyDetails> processData(UtilityBean<ApplicationSDMRequestBean> utilityBean) {
        ApplicationSDMRequestBean applicationSDMRequestBean = utilityBean.getRequestPayloadObject();

        String accountIdentifier = utilityBean.getAccountIdString();
        long fromTime = utilityBean.getFromTime();
        long toTime = utilityBean.getToTime();

        List<BasicEntity> userInputApplications = new ArrayList<>(applicationSDMRequestBean.getUserInputApplications());
        List<Integer> userInputApplicationIds = userInputApplications.parallelStream().map(BasicEntity::getId).collect(Collectors.toList());

        long st = System.currentTimeMillis();
        List<Application> allApplications = applicationSDMRequestBean.getAllApplications();
        Map<String, Application> applicationMap = allApplications.parallelStream().collect(Collectors.toMap(BasicEntity::getIdentifier, c -> c));
        Set<String> allAccessibleApplications = applicationSDMRequestBean.getAllAccessibleApplications().parallelStream()
                .map(BasicEntity::getIdentifier).collect(Collectors.toSet());

        List<BasicAgentEntity> accountAgents = new AgentRepo().getAllAgents(accountIdentifier);
        Map<String, BasicAgentEntity> agentMap = accountAgents
                .parallelStream().collect(Collectors.toMap(BasicEntity::getIdentifier, c -> c, (o, n) -> {
                    PrintErrorLogs.print(String.format("Duplicate agents available for account %s.\nChoosing:\n%s\nRejecting:\n%s\nList:\n%s", accountIdentifier, n, o, accountAgents));
                    log.warn("Duplicate agents available for account {}.\nChoosing:\n{}\nRejecting:\n{}", accountIdentifier, n, o);
                    return n;
                }));
        log.debug("Time taken to fetch agentMap {}ms", System.currentTimeMillis() - st);
        st = System.currentTimeMillis();

        List<BasicEntity> accountServiceList = new ServiceRepo().getAllServices(accountIdentifier);
        Map<Integer, BasicEntity> serviceMap = accountServiceList.parallelStream().collect(Collectors.toMap(BasicEntity::getId, c -> c));
        log.debug("Time taken to fetch service list for account {} is {}ms", accountIdentifier, System.currentTimeMillis() - st);
        st = System.currentTimeMillis();

        List<InstallationAttributes> installationAttributesList = new MasterRepo().getInstallationAttributes();
        Map<String, String> installationAttributeMap;
        boolean isParentApplicationEnabled;
        if (installationAttributesList.isEmpty()) {
            installationAttributeMap = new HashMap<>();
            isParentApplicationEnabled = false;
        } else {
            installationAttributeMap = installationAttributesList.parallelStream()
                    .collect(Collectors.toMap(InstallationAttributes::getName, InstallationAttributes::getValue));

            Optional<String> parentApplicationValue = installationAttributesList.stream()
                    .filter(attrs -> attrs.getName().equalsIgnoreCase(Constants.GET_APPLICATIONHEALTH_BY_PARENT))
                    .map(InstallationAttributes::getValue)
                    .findAny();
            isParentApplicationEnabled = parentApplicationValue.map(Boolean::parseBoolean).orElse(false);
        }
        log.debug("Time taken to fetch installationAttributeBeanList {}ms", System.currentTimeMillis() - st);
        st = System.currentTimeMillis();

        List<ParentApplication> parentApplications = new ArrayList<>();
        if (isParentApplicationEnabled) {
            parentApplications.addAll(new ParentApplicationRepo().getAllParentApplications(accountIdentifier));
            log.debug("Time taken to fetch parents application list for account {} is {}ms", accountIdentifier, System.currentTimeMillis() - st);
            st = System.currentTimeMillis();
        }

        //Don't mode this into a common method -  Reference- TopologyDetailsService
        Set<Service> serviceSet = new HashSet<>();
        userInputApplications.forEach(app -> serviceSet.addAll(HealUICache.INSTANCE.getApplicationServiceList(accountIdentifier, app.getIdentifier())
                .stream()
                .map(c -> HealUICache.INSTANCE.getServiceDetails(accountIdentifier, c.getIdentifier()))
                .filter(Objects::nonNull)
                .collect(Collectors.toSet())));
        log.debug("Time taken to fetch requestBeanApplications list {}ms", System.currentTimeMillis() - st);
        st = System.currentTimeMillis();

        //Don't mode this into a common method -  Reference- TopologyDetailsService
        Map<String, Boolean> servicesMaintenanceMap = new MaintenanceWindowSearchRepo().isServiceUnderMaintenance(accountIdentifier,
                toTime, serviceSet.stream().map(BasicEntity::getIdentifier).collect(Collectors.toSet()));
        log.debug("Time taken to fetch servicesMaintenanceMap list {}ms", System.currentTimeMillis() - st);
        st = System.currentTimeMillis();

        List<TopologyDetailsResponse.Nodes> allNodesList = TopologyDetailsService.getNodeList(accountIdentifier, serviceSet,
                allApplications, agentMap, allAccessibleApplications, servicesMaintenanceMap);
        log.debug("Time taken to fetch nodesList is {}ms", System.currentTimeMillis() - st);
        st = System.currentTimeMillis();

        List<String> allNodeIds = allNodesList.stream().map(TopologyDetailsResponse.Nodes::getId).collect(Collectors.toList());

        List<TopologyDetailsResponse.Edges> edgesList = TopologyDetailsService.getEdgeList(accountIdentifier, serviceSet);
        log.debug("Time taken to fetch edgesList is {}ms", System.currentTimeMillis() - st);
        st = System.currentTimeMillis();

        Map<String, Long> anomalyCountByService = new HashMap<>();
        Set<SignalDetails> signalDetails = new HashSet<>();
        log.debug("Show anomalies for the service on sdm page based on {}, It will query {} index", SHOW_SDM_TOPOLOGY, SHOW_SDM_TOPOLOGY);
        if (installationAttributeMap.getOrDefault(Constants.SHOW_SDM_TOPOLOGY, SHOW_SDM_TOPOLOGY).equalsIgnoreCase("anomalies")) {
            anomalyCountByService.putAll(new AnomalySearchRepo().getAnomalyCountGroupByServiceId(accountIdentifier,
                    fromTime, toTime));
            log.debug("Anomaly Count grouped by service fetched from OS of size: {}", anomalyCountByService.size());
            log.debug("Time taken to fetch anomaly grouped by service {}ms", System.currentTimeMillis() - st);
        } else {
            signalDetails.addAll(new SignalSearchRepo().getAllSignals(accountIdentifier, Collections.emptySet(),
                    new HashSet<String>() {{
                        add(SignalStatus.OPEN.name());
                        add(SignalStatus.UPGRADED.name());
                    }}, fromTime, toTime));
            log.debug("OPEN Signal Ids fetched from OS of size: {}", signalDetails.size());
            log.debug("Time taken to fetch OPEN signal details {}ms", System.currentTimeMillis() - st);
        }
        st = System.currentTimeMillis();

        List<TopologyDetailsResponse.TopologyDetails> result;
        result = userInputApplications.parallelStream()
                .map(app -> {
                    long totalTime = System.currentTimeMillis();
                    long t = System.currentTimeMillis();

                    List<TopologyDetailsResponse.Nodes> nodesList = new ArrayList<>(allNodesList);
                    List<String> nodeIds = new ArrayList<>(allNodeIds);

                    Set<TopologyDetailsResponse.Edges> newEdges = new HashSet<>();

                    for (TopologyDetailsResponse.Edges e : edgesList) {
                        List<TopologyDetailsResponse.Nodes> nodes = new ArrayList<>();
                        if (!nodeIds.contains(e.getSource())) {
                            nodes = getControllerNode(accountIdentifier, allAccessibleApplications, userInputApplications, userInputApplicationIds,
                                    applicationMap, isParentApplicationEnabled, parentApplications,
                                    serviceMap.getOrDefault(Integer.parseInt(e.getSource()), null), agentMap);

                            for (TopologyDetailsResponse.Nodes n : nodes) {
                                TopologyDetailsResponse.Edges secondEdge = TopologyDetailsResponse.Edges.clone(e);
                                secondEdge.setSource(n.getId());
                                newEdges.add(secondEdge);
                            }
                        } else if (!nodeIds.contains(e.getTarget())) {
                            nodes = getControllerNode(accountIdentifier, allAccessibleApplications, userInputApplications, userInputApplicationIds,
                                    applicationMap, isParentApplicationEnabled, parentApplications,
                                    serviceMap.getOrDefault(Integer.parseInt(e.getTarget()), null), agentMap);

                            for (TopologyDetailsResponse.Nodes n : nodes) {
                                if (n != null) {
                                    TopologyDetailsResponse.Edges secondEdge = TopologyDetailsResponse.Edges.clone(e);
                                    secondEdge.setTarget(n.getId());
                                    newEdges.add(secondEdge);
                                }
                            }
                        } else {
                            newEdges.add(e);
                        }

                        if (!nodes.isEmpty()) {
                            nodes.forEach(n -> {
                                if (!nodesList.contains(n)) {
                                    nodesList.add(n);
                                }
                            });

                            nodes.forEach(n -> {
                                if (!nodeIds.contains(n.getId())) {
                                    nodeIds.add(n.getId());
                                }
                            });
                        }
                    }
                    log.debug("Time taken to fetch modified nodes/edges list for application {} is {}ms", app.getIdentifier(), System.currentTimeMillis() - t);
                    t = System.currentTimeMillis();

                    TopologyDetailsResponse.TopologyDetails topologyDetails = new TopologyDetailsResponse.TopologyDetails();

                    updateFlags(accountIdentifier, nodesList, installationAttributeMap, anomalyCountByService, signalDetails);
                    topologyDetails.setNodes(nodesList);
                    log.debug("Time taken to update flags of nodeList for application {} is {}ms", app.getIdentifier(), System.currentTimeMillis() - t);

                    topologyDetails.setEdges(new ArrayList<>(newEdges));

                    log.debug("Time taken to fetch topology details for application {} is {}ms", app.getIdentifier(), System.currentTimeMillis() - totalTime);

                    return topologyDetails;
                })
                .collect(Collectors.toList());
        log.debug("Time taken to fetch topology details is {}ms", System.currentTimeMillis() - st);

        return result;
    }

    private static List<TopologyDetailsResponse.Nodes> getControllerNode(String accountIdentifier, Set<String> allAccessibleApplications,
                                                                         List<BasicEntity> userInputApplications, List<Integer> userInputAppIds,
                                                                         Map<String, Application> applicationMap,
                                                                         boolean isParentApplicationEnabled, List<ParentApplication> parentApplications,
                                                                         BasicEntity service, Map<String, BasicAgentEntity> agentMap) {

        List<TopologyDetailsResponse.Nodes> result = new ArrayList<>();

        if (service != null) {
            Service serviceDetail = HealUICache.INSTANCE.getServiceDetails(accountIdentifier, service.getIdentifier());
            if (serviceDetail != null) {
                List<BasicEntity> serviceApplications = HealUICache.INSTANCE.getServiceApplicationList(accountIdentifier, service.getIdentifier());

                Map<String, Object> metaDataMap = new HashMap<>();

                BasicAgentEntity agent = serviceDetail.getTags().parallelStream()
                        .filter(c -> c.getType().equalsIgnoreCase(Constants.AGENT_TABLE))
                        .map(c -> agentMap.get(c.getValue()))
                        .filter(Objects::nonNull)
                        .filter(a -> a.getType().equalsIgnoreCase(Constants.JIM_AGENT_TYPE))
                        .findFirst().orElse(null);

                if (agent == null) {
                    log.trace("Jim Agent is not mapped to Service : {}", service.getName());
                    metaDataMap.put("jimEnabled", 0);
                } else {
                    log.trace("Jim Agent : {} is mapped to Service : {}", agent.getName(), service.getName());
                    metaDataMap.put("jimEnabled", 1);
                }

                for (BasicEntity application : serviceApplications) {
                    Application appDetail = applicationMap.get(application.getIdentifier());
                    boolean belongsToSameParentApplication = false;
                    if (appDetail != null) {
                        ParentApplication parentApplication = parentApplications.stream()
                                .filter(a -> a.getApplicationIdentifiers().contains(appDetail.getIdentifier()))
                                .findAny()
                                .orElse(null);
                        if (parentApplication != null && !CollectionUtils.isEmpty(userInputApplications)
                                && parentApplication.getApplicationIdentifiers().contains(userInputApplications.get(0).getIdentifier())) {
                            belongsToSameParentApplication = true;
                        }
                    }

                    if (!isParentApplicationEnabled || (!belongsToSameParentApplication &&
                            (userInputAppIds == null || !userInputAppIds.contains(application.getId())))) {
                        TopologyDetailsResponse.Nodes nodes = TopologyDetailsResponse.Nodes.builder()
                                .applications(Collections.singletonList(appDetail))
                                .id(String.valueOf(application.getId()))
                                .name(application.getName())
                                .identifier(application.getIdentifier())
                                .type("application")
                                .build();

                        if (allAccessibleApplications.contains(application.getIdentifier())) {
                            nodes.setUserAccessible(true);
                        }
                        result.add(nodes);

                    } else if (appDetail != null) {
                        if (appDetail.getTags().stream().anyMatch(tag -> tag.getValue().equalsIgnoreCase(Constants.MICROSERVICE))) {
                            appDetail.setIsMicroService(1);
                        }
                        TopologyDetailsResponse.Nodes nodes = TopologyDetailsResponse.Nodes.builder()
                                .applications(Collections.singletonList(appDetail))
                                .id(String.valueOf(serviceDetail.getId()))
                                .name(serviceDetail.getName())
                                .identifier(serviceDetail.getIdentifier())
                                .type(Objects.requireNonNull(serviceDetail.getTags()
                                        .stream().filter(t -> t.getType().equalsIgnoreCase("LayerName"))
                                        .findAny().orElse(null)).getValue())
                                .behaviorEventCount(-1)
                                .workloadEventCount(-1)
                                .metadata(metaDataMap)
                                .build();

                        if (allAccessibleApplications.contains(appDetail.getIdentifier())) {
                            nodes.setUserAccessible(true);
                        }

                        result.add(nodes);
                    }
                }
            }

        }
        return result;
    }

    private void updateFlags(String accountIdentifier, List<TopologyDetailsResponse.Nodes> nodesList,
                             Map<String, String> installationAttributeMap, Map<String, Long> anomalyCountByService, Set<SignalDetails> signalDetails) {

        nodesList.parallelStream()
                .filter(n -> n != null && n.getType() != null && !n.getType().equals("application"))
                .forEach(n -> {
                    List<BasicInstanceBean> basicInstanceBeanList = HealUICache.INSTANCE.getServiceInstanceList(accountIdentifier, n.getIdentifier(), true);
                    Map<String, Object> metaData = new HashMap<>();
                    metaData.put("hostInstances", (int) basicInstanceBeanList.stream().filter(c -> c.getComponentTypeId() == 1 && c.getClusterId() != 0).count());
                    metaData.put("componentInstances", (int) basicInstanceBeanList.stream().filter(c -> c.getComponentTypeId() != 1 && c.getClusterId() != 0).count());
                    n.getMetadata().putAll(metaData);
                    if (!basicInstanceBeanList.isEmpty()) n.setBehaviorEventCount(0);

                    long txnCount = HealUICache.INSTANCE.getServiceTransactionList(accountIdentifier, n.getIdentifier())
                            .stream()
                            .filter(t -> t.getStatus() == 1)
                            .count();
                    if (txnCount > 0) n.setWorkloadEventCount(0);
                });

        getEventStatus(nodesList, installationAttributeMap, anomalyCountByService, signalDetails);
    }

    public List<TopologyDetailsResponse.Nodes> getEventStatus(List<TopologyDetailsResponse.Nodes> nodes, Map<String, String> installationAttributeMap,
                                                              Map<String, Long> anomalyCountByService, Set<SignalDetails> signalDetails) {
        long start = System.currentTimeMillis();

        Map<String, TopologyDetailsResponse.Nodes> anomalyServiceMap = new HashMap<>();
        nodes.forEach(node -> anomalyServiceMap.put(node.getIdentifier(), node));

        if (installationAttributeMap.getOrDefault(Constants.SHOW_SDM_TOPOLOGY, SHOW_SDM_TOPOLOGY).equalsIgnoreCase("anomalies")) {
            anomalyCountByService.keySet().forEach(serviceId -> {
                if (anomalyServiceMap.get(serviceId) != null && anomalyCountByService.get(serviceId) > 0) {
                    log.debug("Setting the behaviour count to 1 based on anomaly index query, service id {}", serviceId);
                    anomalyServiceMap.get(serviceId).setBehaviorEventCount(1);
                }
            });
        } else {
          /*
            Returns the object with the workload and behavior flags set.
            Signal with entryService will have workload and the rest of the affectedServices will be behavior.
           */
            signalDetails.forEach(signalData -> {
                if (signalData.getEntryServiceId() != null && !signalData.getEntryServiceId().isEmpty()) {
                    signalData.getEntryServiceId().forEach(es -> {
                        if(anomalyServiceMap.containsKey(es)) {
                            anomalyServiceMap.get(es).setWorkloadEventCount(1);
                        }
                    });
                }

                if (!signalData.getServiceIds().isEmpty()) {
                    signalData.getServiceIds().forEach(affectedService -> {
                        if (anomalyServiceMap.get(affectedService) != null) {
                            anomalyServiceMap.get(affectedService).setBehaviorEventCount(1);
                        }
                    });
                }
            });
        }
        log.debug("Time taken for the getEventStatus method is {} ms", System.currentTimeMillis() - start);
        return new ArrayList<>(anomalyServiceMap.values());
    }

}
