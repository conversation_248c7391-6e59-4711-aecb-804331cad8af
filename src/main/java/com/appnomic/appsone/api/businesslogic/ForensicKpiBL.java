package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.LoggerTags;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.MasterDataDao;
import com.appnomic.appsone.api.dao.opensearch.CollatedKpiRepo;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.InstanceRepo;
import com.appnomic.appsone.api.dao.redis.TimeRangeDetailsDao;
import com.appnomic.appsone.api.manager.MySQLConnectionManager;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.NewTimeIntervalGenerationUtility;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.CompInstClusterDetails;
import com.heal.configuration.pojos.Tags;
import com.heal.configuration.pojos.TimeRangeDetails;
import com.heal.configuration.pojos.opensearch.CollatedKpi;
import lombok.extern.slf4j.Slf4j;

import java.text.MessageFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
public class ForensicKpiBL implements BusinessLogic<Object, UtilityBean<Object>, LinkedHashMap<String, LinkedHashMap<String, List<Long>>>> {

    @Override
    public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
        log.debug("{} clientValidation().", Constants.INVOKED_METHOD);
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }
        String accountIdString = requestObject.getParams().get(":identifier");
        String compInstanceId = requestObject.getParams().get(":instanceId");
        String categoryIdentifier = (requestObject.getQueryParams().containsKey(Constants.CATEGORY_IDENTIFIER)) ? requestObject.getQueryParams().get(Constants.CATEGORY_IDENTIFIER)[0] : null;
        String fromTimeString = (requestObject.getQueryParams().containsKey(Constants.REQUEST_PARAM_FROM_TIME)) ? requestObject.getQueryParams().get(Constants.REQUEST_PARAM_FROM_TIME)[0] : null;
        String toTimeString = (requestObject.getQueryParams().containsKey(Constants.REQUEST_PARAM_TO_TIME)) ? requestObject.getQueryParams().get(Constants.REQUEST_PARAM_TO_TIME)[0] : null;
        log.debug("{} clientValidation().", Constants.INVOKED_METHOD);

        if (accountIdString == null || accountIdString.trim().isEmpty()) {
            throw new ClientException(UIMessages.ACCOUNT_EMPTY + ":" + accountIdString);
        }
        if (compInstanceId == null || compInstanceId.trim().isEmpty()) {
            throw new ClientException(UIMessages.INVALID_INSTANCE_MESSAGE + ":" + compInstanceId);
        }
        if (categoryIdentifier == null || categoryIdentifier.trim().isEmpty()) {
            throw new ClientException("Category identifier is required");
        }

        Long fromTime;
        Long toTime;
        try {
            fromTime = (fromTimeString == null) ? null : Long.parseLong(fromTimeString);
        } catch (NumberFormatException e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            throw new ClientException(e, MessageFormat.format("Error occurred while converting the fromTime [{0}]. Reason: {1}", fromTimeString, e.getMessage()));
        }
        try {
            toTime = (toTimeString == null) ? null : Long.parseLong(toTimeString);
        } catch (NumberFormatException e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            throw new ClientException(e, MessageFormat.format("Error occurred while converting the toTime [{0}]. Reason: {1}", toTimeString, e.getMessage()));
        }

        if ((fromTime != null && toTime != null) && (fromTime <= 0 || toTime <= 0 || fromTime > toTime)) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error(LoggerTags.TAG_INVALID_FROM_TO_TIME);
            throw new ClientException(LoggerTags.TAG_INVALID_FROM_TO_TIME);
        }
        return UtilityBean.builder()
                .accountIdString(accountIdString)
                .componentInstanceIdString(compInstanceId)
                .fromTime(fromTime)
                .categoryIdentifier(categoryIdentifier)
                .toTime(toTime)
                .build();
    }

    @Override
    public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
        log.debug("{} serverValidation().", Constants.INVOKED_METHOD);
        InstanceRepo instanceRepo = new InstanceRepo();
        AccountRepo accountRepo = new AccountRepo();

        Account account = accountRepo.getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            log.error("Invalid account identifier: {}.", utilityBean.getAccountIdString());
            throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
        }
        Optional<String> timeZoneMilli = account.getTags().parallelStream()
                .filter(tag -> tag.getType().equalsIgnoreCase("Timezone"))
                .findAny().map(Tags::getValue);
        timeZoneMilli.ifPresent(s -> utilityBean.setTimeZoneMilli(Long.parseLong(s)));
        utilityBean.setAccount(account);

        String accountIdentifier =account.getIdentifier();

        TimeRangeDetailsDao timeRangeDetailsDao = new TimeRangeDetailsDao();
        List<TimeRangeDetails> timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(accountIdentifier);
        if (timeRangeDetailsList == null) {
            log.error("Exception occurred while fetching time range details for account {}", accountIdentifier);
            throw new ServerException("Exception occurred while fetching time range details for account " + accountIdentifier);
        }

        if (timeRangeDetailsList.isEmpty()) {
            log.trace("No time range details found for account {}. Searching for Global account.", accountIdentifier);
            timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }
        if (timeRangeDetailsList == null) {
            log.error("Exception occurred while fetching time range details for account {}", Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
            throw new ServerException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }

        if (timeRangeDetailsList.isEmpty()) {
            log.error("No time range details found");
            throw new ServerException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }
        TimeRangeDetails timeRangeDetails = ValidationUtils.getRollupPojo(utilityBean.getFromTime(), utilityBean.getToTime(), timeRangeDetailsList);
        if (timeRangeDetails == null) {
            log.error("Time range details are invalid.");
            throw new ServerException("Time range details are invalid.");
        }
        utilityBean.setTimeRangeDetails(timeRangeDetails);
        utilityBean.setTimeRangeDetailsList(timeRangeDetailsList);

        String categoryIdentifier = utilityBean.getCategoryIdentifier();
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        boolean exists = masterDataDao.existsByIdentifier(categoryIdentifier);
        MySQLConnectionManager.getInstance().close(masterDataDao);
        if (!exists) {
            log.error("Category identifier does not exist in DB: " + categoryIdentifier);
            throw new ServerException("Category identifier does not exist in DB: " + categoryIdentifier);
        }

        Optional<CompInstClusterDetails> instance = instanceRepo.getInstancesByAccount(account.getIdentifier())
                .parallelStream()
                .filter(c -> c.getId() == Integer.parseInt(utilityBean.getComponentInstanceIdString()))
                .filter(c -> (c.getStatus() == Constants.STATUS_ACTIVE)).findAny();
        if (!instance.isPresent()) {
            log.error("Invalid instance id: {}", utilityBean.getComponentInstanceIdString());
            throw new ServerException(UIMessages.ERROR_INVALID_SERVICE_ID);
        }
        utilityBean.setCompInstance(instance.get());

        return utilityBean;
    }

    @Override
    public LinkedHashMap<String, LinkedHashMap<String, List<Long>>> processData(UtilityBean<Object> utilityBean) throws DataProcessingException {
        CollatedKpiRepo collatedKpiRepo = new CollatedKpiRepo();

        String accountIdentifier = utilityBean.getAccount().getIdentifier();
        long fromTime = utilityBean.getFromTime();
        long toTime = utilityBean.getToTime();

        NewTimeIntervalGenerationUtility t = new NewTimeIntervalGenerationUtility();
        t.setTimezoneOffset(accountIdentifier);
        t.processTimeRange(fromTime, toTime);
        t.addEndTimeInOSTime();

        List<Long> times = t.getOSTimes();

        log.debug(" Fetching the Collated KPI for type=FORENSIC , account : {}, compInstance :{}",
               accountIdentifier, utilityBean.getCompInstance().getIdentifier());

        List<Long> timestamps = collatedKpiRepo.getCollatedKpisByType(accountIdentifier,
                        utilityBean.getCompInstance().getIdentifier(), "Forensic", fromTime,
                        toTime, utilityBean.getTimeRangeDetails(), utilityBean.getTimeRangeDetailsList(), times,utilityBean.getCategoryIdentifier())
                .parallelStream()
                .map(CollatedKpi::getTimeInGMT)
                .distinct()
                .sorted(Comparator.reverseOrder())
                .collect(Collectors.toList());

        return timestamps.parallelStream()
                .collect(Collectors.groupingBy(date -> getDateString(date, utilityBean.getTimeZoneMilli()),
                        LinkedHashMap::new, Collectors.groupingBy(d ->
                                getHour(d, utilityBean.getTimeZoneMilli()), LinkedHashMap::new, Collectors.toList())));
    }

    private String getDateString(Long date, long timeZoneMilli) {
        return getTimeString(date, timeZoneMilli, "dd MMM yyyy");
    }

    private String getHour(Long date, long timeZoneMilli) {
        date += 3600000;
        return getTimeString(date, timeZoneMilli, "HH:00");
    }

    private String getTimeString(Long date, long offset, String format) {
        date += offset;
        Instant instant = Instant.ofEpochMilli(date);
        ZoneId zoneId = ZoneId.of("UTC");
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zoneId);
        return localDateTime.format(DateTimeFormatter.ofPattern(format));
    }
}
