package com.appnomic.appsone.api.businesslogic;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.mobile.model.CrashTrend;
import com.appnomic.appsone.api.mobile.util.BigQueryService;
import com.appnomic.appsone.api.mobile.util.BigQueryUtil;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.FieldValueList;
import com.google.cloud.bigquery.QueryJobConfiguration;
import com.google.cloud.bigquery.TableResult;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CrashTrendBL implements BusinessLogic<Object, UtilityBean<Object>, CrashTrend> {

	@Override
	public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
		List<String> errorMessages = new ArrayList<>();
		if (!BigQueryUtil.appOSStringValidation(requestObject, errorMessages)
			|| !BigQueryUtil.appDisplayVersionValidation(requestObject, errorMessages)
			|| !BigQueryUtil.fromTimeToTimeValidation(requestObject, errorMessages)) {
				throw new ClientException(String.join(";", errorMessages));
		}
		String appOSString = requestObject.getQueryParams().get("app-os")[0];
		String appDisplayVersions = requestObject.getQueryParams().get("app-build-version")[0];
		String fromTime = requestObject.getQueryParams().get("from-time")[0];
		String toTime = requestObject.getQueryParams().get("to-time")[0];
		
		return UtilityBean.<Object>builder()
				.appOSString(appOSString)
				.appDisplayVersions(appDisplayVersions)
                .fromTimeString(fromTime)
                .toTimeString(toTime)
                .build();
	}

	@Override
	public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
		return utilityBean;
	}

	@Override
	public CrashTrend processData(UtilityBean<Object> configData) throws DataProcessingException {
		String appOSString = configData.getAppOSString();
		BigQuery bigQuery = BigQueryService.getInstance(appOSString.split("\\.")[0]);
		log.debug("bigQuery Obj: {}", bigQuery);

		List<String> appDisplayVersions = Arrays.asList(configData.getAppDisplayVersions().split(","));
		String appDisplayVersionsInParam = BigQueryUtil.getInParameter(configData.getAppDisplayVersions());
		String fromTime = configData.getFromTimeString();
		String toTime = configData.getToTimeString();
		String intervalBucket = BigQueryUtil.getIntervalBucket(fromTime, toTime);
		String intervalBucketUnit = intervalBucket.split(" ")[1];
		String joinCondition = BigQueryUtil.getTruncatedEventTimestamp(intervalBucket, intervalBucketUnit, Constants.CRASHLYTICS_ALIAS);
		
		String query = 	"(WITH hours AS ( " + 
						"  SELECT * " + 
						"  FROM UNNEST(GENERATE_TIMESTAMP_ARRAY(TIMESTAMP_TRUNC(TIMESTAMP_MILLIS("+fromTime+"), "+intervalBucketUnit+"), TIMESTAMP_MILLIS("+toTime+"), INTERVAL "+intervalBucket+")"+
						") AS time ) " + 
						" " + 
						"SELECT  " + 
						"    STRING(hours.time) AS time, " + 
						"	 STRING_AGG(DISTINCT crashlytics.issue_id) AS uniqueIssueIds, " + 
						"    COUNT(DISTINCT crashlytics.issue_id) AS unique, " + 
						"    COUNTIF(is_fatal=true) AS fatal, " + 
						"    COUNTIF(is_fatal=false) AS nonFatal, " + 
						"    crashlytics.application.display_version AS appDisplayVersions, " + 
						"    '' AS userIds, " + 
						"    false AS isFatal " + 
						"FROM " + Constants.getCrashlyticsTable(appOSString) + " AS crashlytics " +
						"	 RIGHT JOIN hours ON "+joinCondition+" = hours.time " +
						"GROUP BY time, appDisplayVersions " + 
						"ORDER BY 1) " + 
						" " + 
						"UNION ALL " + 
						" " + 
						"SELECT  " + 
						"    null AS time, " + 
						"	 '' AS uniqueIssueIds, " +
						"    null AS unique, " + 
						"    null AS fatal, " + 
						"    null AS nonFatal, " + 
						"    '' AS appDisplayVersions, " + 
						"    STRING_AGG(DISTINCT crashlytics.user.id) AS userIds, " + 
						"    crashlytics.is_fatal AS isFatal " + 
						"FROM " + Constants.getCrashlyticsTable(appOSString) + " AS crashlytics " + 
						"WHERE  " + 
						"    crashlytics.event_timestamp > TIMESTAMP_MILLIS("+fromTime+") " + 
						"    AND crashlytics.event_timestamp < TIMESTAMP_MILLIS("+toTime+") " + 
						"    AND crashlytics.application.display_version IN " + appDisplayVersionsInParam + " " +
						"GROUP BY issue_id, isFatal, appDisplayVersions ";
		QueryJobConfiguration queryConfig = QueryJobConfiguration.newBuilder(query).setUseLegacySql(false).build();
		
		TableResult result;
		try {
			result = bigQuery.query(queryConfig);
		} catch(InterruptedException inExc) {
			throw new DataProcessingException("Error in fetching data from BigQuery");
		}
		
		CrashTrend crashTrend = new CrashTrend();
		Map<String, Long> userDetails = new HashMap<>();
		Set<String> userFatalCrashIds = new HashSet<>();
		Set<String> userNonFatalCrashIds = new HashSet<>();
		Set<String> userUniqueCrashIds = new HashSet<>();
		
		List<String> crashTypeOptions = Arrays.asList("fatal", "nonFatal", "unique");
		Map<String, CrashTrend.TrendDetails> crashDetailsTrend = new HashMap<>();
		CrashTrend.TrendDetails fatal = new CrashTrend.TrendDetails();
		CrashTrend.TrendDetails nonFatal = new CrashTrend.TrendDetails();
		CrashTrend.TrendDetails unique = new CrashTrend.TrendDetails();
		Map<String, Long> fatalTrendMap = new HashMap<>();
		Map<String, Long> nonFatalTrendMap = new HashMap<>();
		Map<String, Long> uniqueTrendMap = new HashMap<>();
		Long fatalCrashCount = 0L;
		Long nonFatalCrashCount = 0L;
		Set<String> uniqueCrashIds = new HashSet<>();
		
		
		Iterator<FieldValueList> resultIterator = result.iterateAll().iterator();
		while(resultIterator.hasNext()) {
			FieldValueList value = resultIterator.next();
			if(null != value.get("userIds").getValue() && !value.get("userIds").getStringValue().equalsIgnoreCase("")) {
				if(value.get("isFatal").getBooleanValue()) {
					userFatalCrashIds.addAll(Arrays.asList(value.get("userIds").getStringValue().split(",")));
				} else {
					userNonFatalCrashIds.addAll(Arrays.asList(value.get("userIds").getStringValue().split(",")));
				}
				userUniqueCrashIds.addAll(Arrays.asList(value.get("userIds").getStringValue().split(",")));
			} else {
				for(String crashType : crashTypeOptions) {
					if (crashType.equalsIgnoreCase("fatal") && null != value.get("time").getValue()) {
						if (!fatalTrendMap.containsKey(value.get("time").getStringValue())) {
							if (null != value.get("appDisplayVersions").getValue() && appDisplayVersions.contains(value.get("appDisplayVersions").getStringValue())) {
								fatalTrendMap.put(value.get("time").getStringValue(), value.get(crashType).getLongValue());
								fatalCrashCount += value.get(crashType).getLongValue();
							} else {
								fatalTrendMap.put(value.get("time").getStringValue(), 0L);
							}
						} else {
							if (null != value.get("appDisplayVersions").getValue() && appDisplayVersions.contains(value.get("appDisplayVersions").getStringValue())) {
								Long existingCount = fatalTrendMap.get(value.get("time").getStringValue());
								Long newCount = existingCount + value.get(crashType).getLongValue();
								fatalTrendMap.put(value.get("time").getStringValue(), newCount);
								fatalCrashCount += value.get(crashType).getLongValue();
							}
						}
					}
					if (crashType.equalsIgnoreCase("nonFatal") && null != value.get("time").getValue()) {
						if (!nonFatalTrendMap.containsKey(value.get("time").getStringValue())) {
							if (null != value.get("appDisplayVersions").getValue() && appDisplayVersions.contains(value.get("appDisplayVersions").getStringValue())) {
								nonFatalTrendMap.put(value.get("time").getStringValue(), value.get(crashType).getLongValue());
								nonFatalCrashCount += value.get(crashType).getLongValue();
							} else {
								nonFatalTrendMap.put(value.get("time").getStringValue(), 0L);
							}
						} else {
							if (null != value.get("appDisplayVersions").getValue() && appDisplayVersions.contains(value.get("appDisplayVersions").getStringValue())) {
								Long existingCount = nonFatalTrendMap.get(value.get("time").getStringValue());
								Long newCount = existingCount + value.get(crashType).getLongValue();
								nonFatalTrendMap.put(value.get("time").getStringValue(), newCount);
								nonFatalCrashCount += value.get(crashType).getLongValue();
							}
						}
					}
					if (crashType.equalsIgnoreCase("unique") && null != value.get("time").getValue()) {
						if (!uniqueTrendMap.containsKey(value.get("time").getStringValue())) {
							if (null != value.get("appDisplayVersions").getValue() && appDisplayVersions.contains(value.get("appDisplayVersions").getStringValue())) {
								uniqueTrendMap.put(value.get("time").getStringValue(), value.get(crashType).getLongValue());
								uniqueCrashIds.addAll(Arrays.asList(value.get("uniqueIssueIds").getStringValue().split(",")));
							} else {
								uniqueTrendMap.put(value.get("time").getStringValue(), 0L);
							}
						} else {
							if (null != value.get("appDisplayVersions").getValue() && appDisplayVersions.contains(value.get("appDisplayVersions").getStringValue())) {
								Long existingCount = uniqueTrendMap.get(value.get("time").getStringValue());
								Long newCount = existingCount + value.get(crashType).getLongValue();
								uniqueTrendMap.put(value.get("time").getStringValue(), newCount);
								uniqueCrashIds.addAll(Arrays.asList(value.get("uniqueIssueIds").getStringValue().split(",")));
							}
						}
					}
				}
			}
		}
		
		userDetails.put("fatalCrashUsers", (long)userFatalCrashIds.size());
		userDetails.put("nonFatalCrashUsers", (long)userNonFatalCrashIds.size());
		userDetails.put("uniqueCrashUsers", (long)userUniqueCrashIds.size());
		
		fatal.setCrashCount(fatalCrashCount);
		fatal.setTrend(fatalTrendMap);
		
		nonFatal.setCrashCount(nonFatalCrashCount);
		nonFatal.setTrend(nonFatalTrendMap);
		
		unique.setCrashCount((long)uniqueCrashIds.size());
		unique.setTrend(uniqueTrendMap);
		
		crashDetailsTrend.put("fatal", fatal);
		crashDetailsTrend.put("nonFatal", nonFatal);
		crashDetailsTrend.put("unique", unique);
		
		crashTrend.setUserDetails(userDetails);
		crashTrend.setCrashDetailsTrend(crashDetailsTrend);
		
		return crashTrend;
	}
}
