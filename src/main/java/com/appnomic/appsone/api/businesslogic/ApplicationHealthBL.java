package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.SignalStatus;
import com.appnomic.appsone.api.common.SignalType;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.AppsoneException;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.opensearch.SignalSearchRepo;
import com.appnomic.appsone.api.dao.redis.*;
import com.appnomic.appsone.api.pojo.ApplicationHealthRequestPojo;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.applicationhealth.ApplicationHealthDetail;
import com.appnomic.appsone.api.pojo.applicationhealth.ApplicationHealthStatus;
import com.appnomic.appsone.api.pojo.applicationhealth.ApplicationSignalHealth;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.service.ApplicationHealthService;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.api.util.StringUtils;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.heal.configuration.pojos.*;
import com.heal.configuration.pojos.opensearch.SignalDetails;
import lombok.extern.slf4j.Slf4j;

import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.appnomic.appsone.api.common.LoggerTags.TAG_INVALID_FROM_TO_TIME;

@Slf4j
public class ApplicationHealthBL implements BusinessLogic<ApplicationHealthRequestPojo, UtilityBean<ApplicationHealthRequestPojo>, List<ApplicationHealthDetail>> {

    private static int SIGNAL_CLOSE_WINDOW_TIME = ConfProperties.getInt(Constants.SIGNAL_CLOSE_WINDOW_TIME, Constants.SIGNAL_CLOSE_WINDOW_DEFAULT_TIME);

    @Override
    public UtilityBean<ApplicationHealthRequestPojo> clientValidation(RequestObject requestObject) throws ClientException {
        log.debug("Inside Client validation");

        String accountIdentifier = requestObject.getParams().get(Constants.REQUEST_PARAM_IDENTIFIER);
        if (StringUtils.isEmpty(accountIdentifier)) {
            throw new ClientException("Request Exception : Account Identifier is null or empty.");
        }

        String fromTimeString = (requestObject.getQueryParams().containsKey(Constants.REQUEST_PARAM_FROM_TIME)) ? requestObject.getQueryParams().get(Constants.REQUEST_PARAM_FROM_TIME)[0] : null;
        String toTimeString = (requestObject.getQueryParams().containsKey(Constants.REQUEST_PARAM_TO_TIME)) ? requestObject.getQueryParams().get(Constants.REQUEST_PARAM_TO_TIME)[0] : null;
        Long fromTime;
        Long toTime;
        try {
            fromTime = (fromTimeString == null) ? null : Long.parseLong(fromTimeString);
        } catch (NumberFormatException e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            throw new ClientException(e, MessageFormat.format("Error occurred while converting the fromTime [{0}]. Reason: {1}", fromTimeString, e.getMessage()));
        }
        try {
            toTime = (toTimeString == null) ? null : Long.parseLong(toTimeString);
        } catch (NumberFormatException e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            throw new ClientException(e, MessageFormat.format("Error occurred while converting the toTime [{0}]. Reason: {1}", toTimeString, e.getMessage()));
        }
        if ((fromTime != null && toTime != null) && (fromTime <= 0 || toTime <= 0 || fromTime > toTime)) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error(TAG_INVALID_FROM_TO_TIME);
            throw new ClientException(TAG_INVALID_FROM_TO_TIME);
        }

        ApplicationHealthRequestPojo pojo = new ApplicationHealthRequestPojo();
        pojo.setActualFromTime(fromTime);

        pojo.setToTime(toTime);

        return UtilityBean.<ApplicationHealthRequestPojo>builder()
                .accountIdString(accountIdentifier)
                .requestPayloadObject(pojo)
                .authToken(requestObject.getHeaders().get(Constants.AUTHORIZATION_HEADER))
                .build();
    }

    @Override
    public UtilityBean<ApplicationHealthRequestPojo> serverValidation(UtilityBean<ApplicationHealthRequestPojo> utilityBean) throws ServerException {
        log.trace("Inside serverValidation method.{}", utilityBean);
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            throw new AppsoneException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }
        utilityBean.setUserId(userId);

        Account account = new AccountRepo().getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            throw new AppsoneException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
        }
        utilityBean.setAccount(account);

        List<InstallationAttributes> installationAttributesList = new MasterRepo().getInstallationAttributes();
        Optional<String> parentApplicationValue = installationAttributesList.stream()
                .filter(attrs -> attrs.getName().equalsIgnoreCase(Constants.GET_APPLICATIONHEALTH_BY_PARENT))
                .map(InstallationAttributes::getValue)
                .findAny();
        utilityBean.getRequestPayloadObject().setGetApplicationHealthByParent(parentApplicationValue.map(Boolean::parseBoolean).orElse(false));

        long fromTime = checkWindowTime(installationAttributesList, utilityBean.getRequestPayloadObject().getActualFromTime(), utilityBean.getRequestPayloadObject().getToTime());
        utilityBean.getRequestPayloadObject().setFromTime(fromTime);

        return utilityBean;
    }

    @Override
    public List<ApplicationHealthDetail> processData(UtilityBean<ApplicationHealthRequestPojo> configData) throws DataProcessingException {
        SignalSearchRepo signalSearchRepo = new SignalSearchRepo();
        try {
            long time = System.currentTimeMillis();

            String userId = configData.getUserId();
            String accountIdentifier = configData.getAccount().getIdentifier();
            long fromTime = configData.getRequestPayloadObject().getFromTime();
            long toTime = configData.getRequestPayloadObject().getToTime();

            ApplicationRepo applicationRepo = new ApplicationRepo();
            Set<BasicEntity> allAccessibleAppList = applicationRepo.getAccessibleApplicationsByUserId(userId, accountIdentifier);
            if (allAccessibleAppList == null || allAccessibleAppList.isEmpty()) {
                log.error("No accessible application found for user {} and account: {}", userId, accountIdentifier);
                throw new DataProcessingException("No application found for user " + userId + " and account " + accountIdentifier);
            }
            log.debug("Time taken to get all accessible applications list: {}", System.currentTimeMillis() - time);

            Set<String> allAccessibleAppIdentifiers = allAccessibleAppList.parallelStream().map(BasicEntity::getIdentifier).collect(Collectors.toSet());

            List<Application> allApplications = applicationRepo.getAllApplicationDetails(accountIdentifier);
            if (allApplications == null || allApplications.isEmpty()) {
                log.error("No application found for account: {}", accountIdentifier);
                throw new DataProcessingException("No application found for account " + accountIdentifier);
            }
            log.debug("Time taken to get all applications list: {}", System.currentTimeMillis() - time);
            time = System.currentTimeMillis();

            Set<Application> applicationsList = allApplications.parallelStream()
                    .filter(c -> allAccessibleAppIdentifiers.contains(c.getIdentifier())).collect(Collectors.toSet());

            // check if problem is open and what application it belongs to.
            Set<SignalDetails> openSignals = signalSearchRepo.getAllSignals(accountIdentifier, new HashSet<String>() {{
                add(SignalType.EARLY_WARNING.name());
                add(SignalType.PROBLEM.name());
                add(SignalType.BATCH_JOB.name());
            }}, Collections.singleton(SignalStatus.OPEN.name()), fromTime, toTime);
            log.debug("Time taken to get OPEN signals from opensearch: {}", System.currentTimeMillis() - time);
            time = System.currentTimeMillis();

            //For each application, problem, batch, warning count of services mapped to it
            Map<String, ApplicationSignalHealth> appSignalHealthMap = getOpenProblems(accountIdentifier, openSignals, applicationsList);
            log.debug("Time taken to get Application-Signal Health map data problems: {}", System.currentTimeMillis() - time);

            List<ApplicationHealthDetail> applicationHealthDetails;
            if (!configData.getRequestPayloadObject().isGetApplicationHealthByParent()) {
                applicationHealthDetails = appSignalHealthMap.values().stream()
                        .map(ApplicationSignalHealth::getApplicationHealthDetail)
                        .collect(Collectors.toList());
            } else {
                time = System.currentTimeMillis();
                applicationHealthDetails = getParentAppWiseApplications(accountIdentifier, appSignalHealthMap);
                log.debug("Time taken to load Parent Applications health detail: {}", System.currentTimeMillis() - time);
            }
            time = System.currentTimeMillis();

            List<ApplicationHealthDetail> sortedApps = new ApplicationHealthService().sortApplicationHealthData(applicationHealthDetails
                    .parallelStream()
                    .filter(p -> p.getServicesMapped() > 0)
                    .collect(Collectors.toList()));
            //Add the apps with no service to the application list at last
            sortedApps.addAll(applicationHealthDetails.parallelStream().filter(p -> p.getServicesMapped() == 0).collect(Collectors.toList()));
            log.debug("Time taken to load Sorted Parent Applications: {}", System.currentTimeMillis() - time);

            return sortedApps;
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            throw new DataProcessingException(e, "Error occurred while processing application health data.");
        }
    }

    private List<ApplicationHealthDetail> getParentAppWiseApplications(String accountIdentifier,
                                                                       Map<String, ApplicationSignalHealth> appSignalHealthData) {
        ParentApplicationRepo parentApplicationRepo = new ParentApplicationRepo();

        return parentApplicationRepo.getAllParentApplications(accountIdentifier)
                .parallelStream()
                .filter(parentApplication -> !parentApplication.getApplicationIdentifiers().isEmpty())
                .map(entry -> {
                    try {
                        Set<String> severeProblemCountSignalIds = new HashSet<>();
                        Set<String> severeWarningCountSignalIds = new HashSet<>();
                        Set<String> severeBatchCountSignalIds = new HashSet<>();
                        Set<String> defaultProblemCountSignalIds = new HashSet<>();
                        Set<String> defaultWarningCountSignalIds = new HashSet<>();
                        Set<String> defaultBatchCountSignalIds = new HashSet<>();

                        AtomicInteger isServiceMapped = new AtomicInteger(0);
                        AtomicBoolean isMaintenanceWindowStatus = new AtomicBoolean(false);

                        entry.getApplicationIdentifiers().stream()
                                .map(a -> appSignalHealthData.getOrDefault(a, null))
                                .filter(Objects::nonNull)
                                .forEach(a -> {
                                    ApplicationHealthDetail applicationHealthDetail = a.getApplicationHealthDetail();
                                    severeProblemCountSignalIds.addAll(a.getSevereProblemCountSignalIds());
                                    severeBatchCountSignalIds.addAll(a.getSevereBatchCountSignalIds());
                                    severeWarningCountSignalIds.addAll(a.getSevereWarningCountSignalIds());
                                    defaultBatchCountSignalIds.addAll(a.getDefaultBatchCountSignalIds());
                                    defaultProblemCountSignalIds.addAll(a.getDefaultProblemCountSignalIds());
                                    defaultWarningCountSignalIds.addAll(a.getDefaultWarningCountSignalIds());
                                    if (applicationHealthDetail.isMaintenanceWindowStatus()) {
                                        isMaintenanceWindowStatus.set(true);
                                    }
                                    if (applicationHealthDetail.getServicesMapped() == 1) {
                                        isServiceMapped.set(1);
                                    }
                                });

                        List<ApplicationHealthStatus> batches = createBatchList(defaultBatchCountSignalIds, severeBatchCountSignalIds);
                        List<ApplicationHealthStatus> problems = createProblemList(defaultProblemCountSignalIds, severeProblemCountSignalIds);
                        List<ApplicationHealthStatus> warnings = createWarningList(defaultWarningCountSignalIds, severeWarningCountSignalIds);

                        ApplicationHealthDetail parentAppHealthDetails = ApplicationHealthDetail.builder()
                                .id(entry.getId())
                                .name(entry.getName())
                                .identifier(entry.getIdentifier())
                                .problem(problems)
                                .batch(batches)
                                .warning(warnings)
                                .dashboardUId(null)
                                .maintenanceWindowStatus(isMaintenanceWindowStatus.get())
                                .servicesMapped(isServiceMapped.get())
                                .severeProblemCount(severeProblemCountSignalIds.size())
                                .severeWarningCount(severeWarningCountSignalIds.size())
                                .severeBatchCount(severeBatchCountSignalIds.size())
                                .severeProblemCountMaintenance(severeProblemCountSignalIds.size())
                                .severeWarningCountMaintenance(severeWarningCountSignalIds.size())
                                .severeBatchCountMaintenance(severeBatchCountSignalIds.size())
                                .defaultProblemCount(defaultProblemCountSignalIds.size())
                                .defaultWarningCount(defaultWarningCountSignalIds.size())
                                .defaultBatchCount(defaultBatchCountSignalIds.size())
                                .defaultProblemCountMaintenance(defaultProblemCountSignalIds.size())
                                .defaultWarningCountMaintenance(defaultWarningCountSignalIds.size())
                                .defaultBatchCountMaintenance(defaultBatchCountSignalIds.size())
                                .build();

                        if (parentAppHealthDetails.getId() == 0) {
                            log.warn("Parent Application {} is not mapped to any Application", entry.getName());
                            return null;
                        }
                        return parentAppHealthDetails;
                    } catch (Exception e) {
                        log.error("Error occurred while populating the details of parent application, parent:{}", entry, e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private List<ApplicationHealthStatus> createBatchList(Set<String> defaultSignalIds, Set<String> severeSignalIds) {
        List<ApplicationHealthStatus> list = new ArrayList<>();
        list.add(ApplicationHealthStatus.builder().name("Default").count(defaultSignalIds.size()).priority(0).build());
        list.add(ApplicationHealthStatus.builder().name("Severe").count(severeSignalIds.size()).priority(1).build());
        return list;
    }

    private List<ApplicationHealthStatus> createProblemList(Set<String> defaultSignalIds, Set<String> severeSignalIds) {
        List<ApplicationHealthStatus> list = new ArrayList<>();
        list.add(ApplicationHealthStatus.builder().name("Default").count(defaultSignalIds.size()).priority(0).build());
        list.add(ApplicationHealthStatus.builder().name("Severe").count(severeSignalIds.size()).priority(1).build());
        return list;
    }

    private List<ApplicationHealthStatus> createWarningList(Set<String> defaultSignalIds, Set<String> severeSignalIds) {
        List<ApplicationHealthStatus> list = new ArrayList<>();
        list.add(ApplicationHealthStatus.builder().name("Default").count(defaultSignalIds.size()).priority(0).build());
        list.add(ApplicationHealthStatus.builder().name("Severe").count(severeSignalIds.size()).priority(1).build());
        return list;
    }


    private Long checkWindowTime(List<InstallationAttributes> installationAttributesList, Long fromTime, Long toTime) {
        try {
            if (!installationAttributesList.isEmpty()) {
                installationAttributesList.parallelStream().filter(attrs -> attrs.getName().equals(Constants.SIGNAL_CLOSE_WINDOW_TIME))
                        .findAny()
                        .ifPresent(installationAttributeBean -> SIGNAL_CLOSE_WINDOW_TIME = Integer.parseInt(installationAttributeBean.getValue()));
            }
        } catch (Exception ex) {
            log.error("Exception encountered while getting Signal Window Closing Time based on attributes. Reason: {}", ex.getMessage(), ex);
        }

        if ((null != fromTime && null != toTime)) {
            long signalWindowTime = toTime - TimeUnit.MINUTES.toMillis(SIGNAL_CLOSE_WINDOW_TIME);
            if (fromTime > signalWindowTime) {
                fromTime = signalWindowTime;
            }
        }

        return fromTime;
    }

    protected Map<String, ApplicationSignalHealth> getOpenProblems(String accountIdentifier, Set<SignalDetails> signals, Set<Application> applications) {
        log.trace("{} getOpenProblem()", Constants.INVOKED_METHOD);

        ApplicationRepo applicationRepo = new ApplicationRepo();

        Map<Integer, String> viewTypesMap = new MasterRepo().getTypes()
                .stream().collect(Collectors.toMap(ViewTypes::getSubTypeId, ViewTypes::getSubTypeName, (a, b) -> b));

        long time = System.currentTimeMillis();
        Map<String, ApplicationHealthDetail> applicationHealthDetailsMap = applications.parallelStream()
                .map(application -> {
                    //For each application, list of services
                    List<BasicEntity> basicEntities = HealUICache.INSTANCE.getApplicationServiceList(accountIdentifier, application.getIdentifier());

                    boolean isUnderMaintenance = false;
                    if (!basicEntities.isEmpty()) {
                        isUnderMaintenance = basicEntities.stream().allMatch(service ->
                                HealUICache.INSTANCE.getServiceMaintenanceDetails(accountIdentifier, service.getIdentifier())
                                        .stream().anyMatch(MaintenanceDetails::isOngoing));
                    }

                    ApplicationHealthDetail healthDetail = new ApplicationHealthDetail(viewTypesMap);
                    healthDetail.setId(application.getId());
                    healthDetail.setName(application.getName());
                    healthDetail.setIdentifier(application.getIdentifier());
                    healthDetail.setMaintenanceWindowStatus(isUnderMaintenance);

                    if (application.getTags() != null) {
                        healthDetail.setDashboardUId(application.getTags().stream()
                                .filter(t -> t.getType().equalsIgnoreCase(Constants.DASHBOARD_UID_TAG))
                                .map(Tags::getValue)
                                .findAny().orElse(null));
                    }
                    if (basicEntities.isEmpty()) {
                        healthDetail.setServicesMapped(0);
                    } else {
                        healthDetail.setServicesMapped(1);
                    }

                    return healthDetail;
                })
                .collect(Collectors.toMap(ApplicationHealthDetail::getIdentifier, Function.identity()));
        log.debug("Time taken to fetch ApplicationHealth mapping : {}ms", System.currentTimeMillis() - time);

        Map<String, ApplicationSignalHealth> applicationSignalHealthMap = applicationHealthDetailsMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> {
                    ApplicationSignalHealth signalHealth = new ApplicationSignalHealth();
                    signalHealth.setApplicationHealthDetail(entry.getValue());
                    return signalHealth;
                }));
        if (signals.isEmpty()) {
            return applicationSignalHealthMap;
        }

        time = System.currentTimeMillis();
        signals.forEach(signal -> {
            Set<String> affectedServices = signal.getServiceIds();
            String signalType = signal.getSignalType();
            Set<String> affectedAppIds;
            // In case of batch job , services are applications
            if (signalType.equalsIgnoreCase(SignalType.BATCH_JOB.name())) {
                affectedAppIds = affectedServices.stream()
                        .map(serviceIdentifier -> applicationRepo.getApplicationDetailWithAppIdentifier(accountIdentifier, serviceIdentifier))
                        .filter(Objects::nonNull)
                        .map(BasicEntity::getIdentifier)
                        .collect(Collectors.toSet());
            } else {
                affectedAppIds = affectedServices.stream()
                        .map(serviceIdentifier -> HealUICache.INSTANCE.getServiceApplicationList(accountIdentifier, serviceIdentifier))
                        .flatMap(Collection::parallelStream)
                        .map(BasicEntity::getIdentifier)
                        .collect(Collectors.toSet());
            }

            String severityTypeString = viewTypesMap.get(signal.getSeverityId());
            if (severityTypeString == null) {
                log.error("No Severity type found in Redis for severity id {} for signal {}. Skipping this signal process.", signal.getSeverityId(), signal.getSignalId());
                return;
            }

            addProblemData(applicationSignalHealthMap, applicationHealthDetailsMap,
                    affectedAppIds, signalType, signal.getSignalId(), severityTypeString);
        });
        log.debug("Time taken to go through signals rows: {}", System.currentTimeMillis() - time);

        return applicationSignalHealthMap;
    }

    protected void addProblemData(Map<String, ApplicationSignalHealth> applicationSignalHealthMap, Map<String, ApplicationHealthDetail> applicationHealthDetailsMap,
                                  Set<String> affectedAppIds, String signalType, String signalId, String severityIdentifier) {
        log.trace("{} addProblemData().", Constants.INVOKED_METHOD);

        Set<String> severeProblemCountSignalIds = new HashSet<>();
        Set<String> severeWarningCountSignalIds = new HashSet<>();
        Set<String> severeBatchCountSignalIds = new HashSet<>();
        Set<String> defaultProblemCountSignalIds = new HashSet<>();
        Set<String> defaultWarningCountSignalIds = new HashSet<>();
        Set<String> defaultBatchCountSignalIds = new HashSet<>();

        affectedAppIds.stream()
                .map(applicationHealthDetailsMap::get)
                .filter(Objects::nonNull)
                .forEach(temp -> {
                    if (Constants.PROBLEM_LITERAL.equalsIgnoreCase(signalType)) {
                        temp.getProblem().stream()
                                .filter(w -> w.getName().equals(severityIdentifier)).forEach(p -> {
                                    if (p.getName().equalsIgnoreCase("Severe")) {
                                        severeProblemCountSignalIds.add(signalId);
                                    } else if (p.getName().equalsIgnoreCase("Default")) {
                                        defaultProblemCountSignalIds.add(signalId);
                                    }
                                    int currCount = p.getCount();
                                    p.setCount(currCount + 1);
                                });
                    } else if (Constants.BATCH_JOB_LITERAL.equalsIgnoreCase(signalType)) {
                        temp.getBatch().stream()
                                .filter(w -> w.getName().equals(severityIdentifier)).forEach(b -> {
                                    if (b.getName().equalsIgnoreCase("Severe")) {
                                        severeBatchCountSignalIds.add(signalId);
                                    } else if (b.getName().equalsIgnoreCase("Default")) {
                                        defaultBatchCountSignalIds.add(signalId);
                                    }
                                    int currCount = b.getCount();
                                    b.setCount(currCount + 1);
                                });
                    } else {
                        temp.getWarning().stream()
                                .filter(w -> w.getName().equals(severityIdentifier))
                                .forEach(w -> {
                                    if (w.getName().equalsIgnoreCase("Severe")) {
                                        severeWarningCountSignalIds.add(signalId);
                                    } else if (w.getName().equalsIgnoreCase("Default")) {
                                        defaultWarningCountSignalIds.add(signalId);
                                    }
                                    int currCount = w.getCount();
                                    w.setCount(currCount + 1);
                                });
                    }

                    ApplicationSignalHealth applicationSignalHealth = applicationSignalHealthMap.get(temp.getIdentifier());
                    applicationSignalHealth.getSevereProblemCountSignalIds().addAll(severeProblemCountSignalIds);
                    applicationSignalHealth.getSevereBatchCountSignalIds().addAll(severeBatchCountSignalIds);
                    applicationSignalHealth.getSevereWarningCountSignalIds().addAll(severeWarningCountSignalIds);

                    applicationSignalHealth.getDefaultProblemCountSignalIds().addAll(defaultProblemCountSignalIds);
                    applicationSignalHealth.getDefaultBatchCountSignalIds().addAll(defaultBatchCountSignalIds);
                    applicationSignalHealth.getDefaultWarningCountSignalIds().addAll(defaultWarningCountSignalIds);
                });
    }
}

