package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.beans.KpiType;
import com.appnomic.appsone.api.beans.UserAccountIdentifiersBean;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.cache.UserDetailsCache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.opensearch.AnomalySearchRepo;
import com.appnomic.appsone.api.dao.opensearch.MaintenanceWindowSearchRepo;
import com.appnomic.appsone.api.dao.opensearch.SignalSearchRepo;
import com.appnomic.appsone.api.dao.redis.*;
import com.appnomic.appsone.api.pojo.*;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.service.TopologyDetailsService;
import com.appnomic.appsone.api.util.DateTimeUtil;
import com.appnomic.appsone.api.util.RCAPathGenerator;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.heal.configuration.pojos.*;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.Application;
import com.heal.configuration.pojos.opensearch.Anomalies;
import com.heal.configuration.pojos.opensearch.SignalDetails;
import lombok.extern.slf4j.Slf4j;

import java.sql.Timestamp;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class RcaPathBL implements BusinessLogic<RCAPathRequestPojo, UtilityBean<RCAPathRequestPojo>, RCAPathResponseObject.ResponseData> {

    //Variable to hold related signal
    private static final String ALL_AFFECTED_KEY = "all";
    private static final String DESTINATION_NODES_KEY = "DESTINATION_NODES_KEY";
    private static final String SOURCE_NODES_KEY = "KPI_AFFECTED";

    AnomalySearchRepo anomalySearchRepo = new AnomalySearchRepo();
    KpiRepo kpiRepo = new KpiRepo();
    ServiceRepo serviceRepo = new ServiceRepo();
    SignalSearchRepo signalSearchRepo = new SignalSearchRepo();

    @Override
    public UtilityBean<RCAPathRequestPojo> clientValidation(RequestObject requestObject) throws ClientException {
        String accountIdentifier = requestObject.getParams().get(Constants.REQUEST_PARAM_IDENTIFIER);
        String problemIdString = requestObject.getParams().get(Constants.REQUEST_PARAM_SIGNAL_ID);
        String[] signalTypes = requestObject.getQueryParams().get(Constants.REQUEST_PARAM_TYPE);

        if (accountIdentifier == null || accountIdentifier.trim().isEmpty()) {
            log.error("Invalid account identifier. Reason: It is either NULL or empty.");
            throw new ClientException("Invalid account identifier");
        }

        if (problemIdString == null || problemIdString.trim().isEmpty()) {
            log.error("Invalid signal identifier. Reason: It is either NULL or empty.");
            throw new ClientException("Invalid signal identifier");
        }

        String signalType;
        switch (signalTypes[0]) {
            case "W":
                signalType = "EARLY_WARNING";
                break;
            case "P":
                signalType = "PROBLEM";
                break;
            case "I":
                signalType = "INFO";
                break;
            case "BP":
                signalType = "BATCH_JOB";
                break;
            default:
                log.error("Invalid signal type [{}]", signalTypes[0]);
                throw new ClientException("Invalid signal type");
        }

        RCAPathRequestPojo pojo = RCAPathRequestPojo.builder()
                .signalType(signalType)
                .signalId(problemIdString)
                .build();

        return UtilityBean.<RCAPathRequestPojo>builder()
                .authToken(requestObject.getHeaders().get(Constants.AUTHORIZATION_HEADER))
                .accountIdString(accountIdentifier)
                .requestPayloadObject(pojo)
                .build();
    }

    @Override
    public UtilityBean<RCAPathRequestPojo> serverValidation(UtilityBean<RCAPathRequestPojo> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            throw new ServerException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }

        Account account = new AccountRepo().getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
        }

        UserAccessDetails userAccessDetails;
        try {
            userAccessDetails = UserDetailsCache.getInstance().userApplications.get(new UserAccountIdentifiersBean(userId, utilityBean.getAccountIdString()));
        } catch (ExecutionException e) {
            log.error("Error while fetching access details for user [{}] and account [{}]", userId, account.getIdentifier());
            throw new ServerException("Error while fetching user access details");
        }

        utilityBean.getRequestPayloadObject().setAccessDetails(userAccessDetails);
        utilityBean.setAccount(account);
        utilityBean.setUserId(userId);
        return utilityBean;
    }

    @Override
    public RCAPathResponseObject.ResponseData processData(UtilityBean<RCAPathRequestPojo> configData) throws DataProcessingException {
        long start = System.currentTimeMillis();
        long toTime;
        String epochEndTimeFieldName;
        String epochStartTimeFieldName = "startedTime";
        RCAPathRequestPojo pojo = configData.getRequestPayloadObject();
        String accountIdentifier = configData.getAccount().getIdentifier();
        Set<String> appIdentifiers = new HashSet<>();

        SignalDetails signal = signalSearchRepo.getSignalsBySignalId(pojo.getSignalId(), accountIdentifier);
        if (signal == null) {
            throw new DataProcessingException(String.format("Error occurred while generating RCA path for signal [%s] for identifier [%s]", pojo.getSignalId(), accountIdentifier));
        }

        for (String serviceIdentifier : signal.getServiceIds()) {
            List<String> appIdsFromRedis = HealUICache.INSTANCE.getServiceApplicationList(accountIdentifier, serviceIdentifier)
                    .parallelStream()
                    .map(BasicEntity::getIdentifier)
                    .distinct()
                    .collect(Collectors.toList());
            appIdentifiers.addAll(appIdsFromRedis);
        }

        if (signal.getMetadata().get("end_time") == null) {
            epochEndTimeFieldName = "updatedTime";
            toTime = System.currentTimeMillis();
        } else {
            epochEndTimeFieldName = "metadata.end_time";
            String end_time = signal.getMetadata().get("end_time");
            toTime = Long.parseLong(end_time);
        }

        Set<String> relatedOpenBatchSignals = signalSearchRepo.getBatchSignalBySignalType(accountIdentifier, appIdentifiers, epochStartTimeFieldName, signal.getStartedTime(), toTime)
                .parallelStream()
                .map(SignalDetails::getSignalId)
                .collect(Collectors.toSet());

        log.info("No batch signals have been opened in the time range {}, {}", signal.getStartedTime(), toTime);
        log.info("Checking if any batch signals have been closed in the time range {}, {}", signal.getStartedTime(), toTime);
        Set<String> relatedCloseBatchSignals = signalSearchRepo.getBatchSignalBySignalType(accountIdentifier, appIdentifiers, epochEndTimeFieldName, signal.getStartedTime(), toTime)
                .parallelStream()
                .map(SignalDetails::getSignalId)
                .collect(Collectors.toSet());


        log.debug("Time taken to fetch signal information: {} ms", System.currentTimeMillis() - start);
        try {
            start = System.currentTimeMillis();
            //ConfigWatch, FileWatch and Core KPI types related signals
            Map<String, Set<RelatedSignals>> relatedInfoSignals = populateRelatedInfoSignals(signal.getServiceIds(), accountIdentifier,
                    signal.getStartedTime(), signal.getUpdatedTime());
            log.debug("Time taken to populate related INFO signals: {} ms", System.currentTimeMillis() - start);

            start = System.currentTimeMillis();

            List<String> accessibleServiceList = pojo.getAccessDetails().getServiceIdentifiers();

            List<BasicEntity> applications = signal.getServiceIds().stream().map(s -> HealUICache.INSTANCE.getServiceApplicationList(accountIdentifier, s)).flatMap(Collection::stream).collect(Collectors.toList());
            applications.addAll(signal.getRootCauseServiceIds().stream().map(s -> HealUICache.INSTANCE.getServiceApplicationList(accountIdentifier, s)).flatMap(Collection::stream).collect(Collectors.toList()));
            List<BasicEntity> entryPointApps = new ArrayList<>();

            if(signal.getEntryServiceId() != null && !signal.getEntryServiceId().isEmpty()) {
                entryPointApps = signal.getEntryServiceId().stream()
                        .map(es -> HealUICache.INSTANCE.getServiceApplicationList(accountIdentifier, es))
                        .flatMap(Collection::stream)
                        .collect(Collectors.toList());
            }

            if (!entryPointApps.isEmpty()) {
                applications.addAll(entryPointApps);
            }
            log.debug("Time taken to populate signal related applications: {} ms", System.currentTimeMillis() - start);

            start = System.currentTimeMillis();
            List<Service> serviceList = applications.parallelStream().map(a -> HealUICache.INSTANCE.getApplicationServiceList(accountIdentifier, a.getIdentifier()))
                    .flatMap(Collection::stream)
                    .distinct()
                    .map(c -> HealUICache.INSTANCE.getServiceDetails(accountIdentifier, c.getIdentifier()))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            log.debug("Time taken to populate signal related services: {} ms", System.currentTimeMillis() - start);

            List<TopologyDetailsResponse.TopologyDetails> enrichedGraphDetails = getRcaPaths(accountIdentifier, signal, accessibleServiceList, serviceList);

            start = System.currentTimeMillis();

            if ("EARLY_WARNING".equalsIgnoreCase(configData.getRequestPayloadObject().getSignalType())) {
                // If the type of the Signal is problem then we need to merge all the RCA paths as a single path
                enrichedGraphDetails = combineRCAPaths(enrichedGraphDetails);
                log.debug("------------Time taken for fetching enrichedGraphDetails/combineRCAPaths: {} ms.", (System.currentTimeMillis() - start));
            }

            RCAPathResponseObject.ResponseData data = new RCAPathResponseObject.ResponseData();
            data.setRcaPaths(enrichedGraphDetails);

            data.setRelatedSignals(signal.getRelatedSignals());
            data.setConfigWatchInfoSignals(relatedInfoSignals.getOrDefault("CONFIG_WATCH_INFO_SIGNALS", new HashSet<>()));
            data.setCoreInfoSignals(relatedInfoSignals.getOrDefault("CORE_INFO_SIGNALS", new HashSet<>()));
            data.setBatchSignals(Stream.concat(relatedOpenBatchSignals.stream(), relatedCloseBatchSignals.stream()).collect(Collectors.toSet()));

            return data;

        } catch (Exception e) {
            log.error("Error occurred while generating RCA path for signal [{}] for identifier [{}]", pojo.getSignalId(), accountIdentifier, e);
            throw new DataProcessingException(String.format("Error occurred while generating RCA path for signal [%s] for identifier [%s]", pojo.getSignalId(), accountIdentifier));
        }
    }

    private List<TopologyDetailsResponse.TopologyDetails> getRcaPaths(String accountIdentifier, SignalDetails signal, List<String> accessibleServiceList, List<Service> serviceList) throws ParseException {
        Timestamp date = new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());

        long start = System.currentTimeMillis();

        Map<String, Boolean> servicesMaintenanceMap = new MaintenanceWindowSearchRepo().isServiceUnderMaintenance(accountIdentifier, date.getTime(),
                serviceList.stream().map(Service::getIdentifier).collect(Collectors.toSet()));
        log.debug("Time taken to populate maintenance window info for services: {} ms", System.currentTimeMillis() - start);

        ApplicationRepo applicationRepo = new ApplicationRepo();
        List<Application> accountApplicationList = applicationRepo.getAllApplicationDetails(accountIdentifier);
        Map<String, BasicAgentEntity> agentMap = new AgentRepo().getAllAgents(accountIdentifier)
                .parallelStream().collect(Collectors.toMap(BasicEntity::getIdentifier, c -> c));

        start = System.currentTimeMillis();
        List<TopologyDetailsResponse.Nodes> nodesList = serviceList.parallelStream()
                .map(service -> {
                    List<BasicEntity> appsMappedToService = HealUICache.INSTANCE.getServiceApplicationList(accountIdentifier, service.getIdentifier());
                    return TopologyDetailsService.getNode(service, accessibleServiceList.contains(service.getIdentifier()),
                            servicesMaintenanceMap, appsMappedToService, accountApplicationList, agentMap);
                })
                .collect(Collectors.toList());
        log.debug("Time taken to enrich {} SDM nodes is {} ms.", serviceList.size(), (System.currentTimeMillis() - start));

        start = System.currentTimeMillis();
        Map<Integer, String> connectedServicesMap = new HashMap<>();
        List<TopologyDetailsResponse.Edges> edgesList = serviceList.parallelStream().map(c ->
                        HealUICache.INSTANCE.getServiceConnectionDetails(accountIdentifier, c.getIdentifier())
                                .parallelStream()
                                .filter(Objects::nonNull)
                                .map(d -> {
                                    connectedServicesMap.put(d.getId(), d.getIdentifier());
                                    return ConnectionDetails.builder()
                                            .sourceId(c.getId())
                                            .destinationId(d.getId())
                                            .build();
                                })
                                .collect(Collectors.toList()))
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .map(c -> {
                    TopologyDetailsResponse.Edges edges = new TopologyDetailsResponse.Edges();
                    Optional<Service> controller = serviceList.stream()
                            .filter(t -> t.getId() == c.getSourceId() ||
                                    t.getId() == c.getDestinationId())
                            .findAny();

                    if (controller.isPresent()) {
                        edges.setSource(String.valueOf(c.getSourceId()));
                        edges.setTarget(String.valueOf(c.getDestinationId()));
                    }
                    return edges;
                }).distinct()
                .collect(Collectors.toList());
        log.debug("------------Time taken for creating connections: {} ms.", (System.currentTimeMillis() - start));

        //All the affected nodes including root-cause and entry-point services
        //Process and get all RCA paths
        start = System.currentTimeMillis();
        Map<String, List<TopologyDetailsResponse.Nodes>> affectedNodesMap = getAffectedNodesDetails(signal, nodesList);
        log.debug("Time taken to fetch affected node details: {} ms.", (System.currentTimeMillis() - start));

        ServiceDependencyGraph serviceDependencyGraph = new ServiceDependencyGraph(nodesList, edgesList, affectedNodesMap.get(ALL_AFFECTED_KEY));

        List<TopologyDetailsResponse.Nodes> affectedNodes = affectedNodesMap.get(ALL_AFFECTED_KEY);

        start = System.currentTimeMillis();
        List<List<TopologyDetailsResponse.Nodes>> rcaPaths = getRCAPathList(serviceDependencyGraph, affectedNodesMap);
        log.debug("------------Time taken for fetching rcaPaths: {} ms.", (System.currentTimeMillis() - start));

        RCAPathGenerator rcaPathGenerator = new RCAPathGenerator();
        List<TopologyDetailsResponse.TopologyDetails> enrichedGraphDetails = new ArrayList<>();

        //For each rca path enrich
        Map<Integer, Service> serviceIdMap = serviceList.parallelStream().collect(Collectors.toMap(Service::getId, Function.identity()));

        start = System.currentTimeMillis();
        for (List<TopologyDetailsResponse.Nodes> rcaPath : rcaPaths) {
            Map<String, Object> rcaGraphDetails = rcaPathGenerator.getDetailedSubGraph(serviceDependencyGraph, rcaPath,
                    affectedNodes, affectedNodesMap.get(DESTINATION_NODES_KEY));

            TopologyDetailsResponse.TopologyDetails topologyDetails = new TopologyDetailsResponse.TopologyDetails();

            List<TopologyDetailsResponse.Edges> rcaEdges = (List<TopologyDetailsResponse.Edges>) rcaGraphDetails.get(RCAPathGenerator.EDGES);
            List<TopologyDetailsResponse.Nodes> rcaNodes = (List<TopologyDetailsResponse.Nodes>) rcaGraphDetails.get(RCAPathGenerator.NODES);

            List<TopologyDetailsResponse.Nodes> nodes = new ArrayList<>();
            rcaNodes.forEach(r -> {
                if (r.getIdentifier() == null) {
                    Service s = serviceIdMap.get(Integer.parseInt(r.getId()));
                    if (s == null) {
                        s = HealUICache.INSTANCE.getServiceDetails(accountIdentifier, connectedServicesMap.get(Integer.parseInt(r.getId())));
                    }
                    List<BasicEntity> appsMappedToService = HealUICache.INSTANCE.getServiceApplicationList(accountIdentifier, s.getIdentifier());
                    nodes.add(TopologyDetailsService.getNode(s, accessibleServiceList.contains(s.getIdentifier()), servicesMaintenanceMap, appsMappedToService, accountApplicationList, agentMap));
                } else {
                    nodes.add(r);
                }
            });

            topologyDetails.setEdges(rcaEdges);
            topologyDetails.setNodes(nodes);
            //Since we use array list the order of Nodes are maintained, therefore the first node is the one on which
            //transaction is affected hence the name is set to affected service.
            topologyDetails.getImpactedServiceName().add(rcaNodes.get(0).getName());
            enrichedGraphDetails.add(topologyDetails);
        }

        log.debug("------------Time taken for constructing the rcaPaths: {} ms.", (System.currentTimeMillis() - start));

        return enrichedGraphDetails;
    }

    private Map<String, Set<RelatedSignals>> populateRelatedInfoSignals(Set<String> affectedServiceSet, String accountIdentifier, long fromTime, long toTime) {
        Map<String, Set<RelatedSignals>> relatedInfoSignals = new HashMap<>();

        Set<SignalDetails> signalList = signalSearchRepo.getSignalBySignalType(accountIdentifier, affectedServiceSet, fromTime, toTime);

        Map<String, BasicEntity> controllerMap = serviceRepo.getAllServices(accountIdentifier).parallelStream().collect(Collectors.toMap(BasicEntity::getIdentifier, Function.identity()));

        for (SignalDetails is : signalList) {
            Set<String> anomalies = is.getAnomalies();
            Anomalies anomalyDetails = null;
            RelatedSignals relatedSignals = new RelatedSignals();
            if (!anomalies.isEmpty()) {
                anomalyDetails = anomalySearchRepo.getAnomaliesById(accountIdentifier, anomalies.iterator().next());
            }

            if (anomalyDetails != null) {
                CompInstKpiEntity masterKPIDetailsBean = kpiRepo.getKpiDetailByAccInstKpiId(accountIdentifier, anomalyDetails.getInstanceId(), anomalyDetails.getKpiId());
                if (masterKPIDetailsBean != null) {
                    if (masterKPIDetailsBean.getType().equalsIgnoreCase(KpiType.CONFIGWATCH.name()) || masterKPIDetailsBean.getType().equalsIgnoreCase(KpiType.FILEWATCH.name())) {
                        relatedSignals.setId(is.getSignalId());

                        for (String svc : is.getServiceIds()) {
                            BasicEntity service = controllerMap.getOrDefault(svc, null);
                            int serviceId = service == null ? 0 : service.getId();
                            relatedSignals.getServices().add(serviceId);
                        }
                        relatedInfoSignals.put("CONFIG_WATCH_INFO_SIGNALS", Collections.singleton(relatedSignals));
                    } else if (masterKPIDetailsBean.getType().equalsIgnoreCase(KpiType.CORE.name())) {
                        relatedSignals.setId(is.getSignalId());

                        for (String svc : is.getServiceIds()) {
                            BasicEntity service = controllerMap.getOrDefault(svc, null);
                            int serviceId = service == null ? 0 : service.getId();
                            relatedSignals.getServices().add(serviceId);
                        }
                        relatedInfoSignals.put("CORE_INFO_SIGNALS", Collections.singleton(relatedSignals));
                    }
                }
            }
        }

        return relatedInfoSignals;
    }

    /*
     * This method returns a map with three list
     * 1: list of source nodes
     * 2: list of destination nodes
     * 3: List of all affected nodes
     * 1 and 2 are used for tracing RCA paths
     * 3 is used for scoring RCA paths so that they can be sorted, where umber of affected nodes on the RCA path increases its score
     */
    private Map<String, List<TopologyDetailsResponse.Nodes>> getAffectedNodesDetails(SignalDetails signal, List<TopologyDetailsResponse.Nodes> nodesList) {
        Map<String, TopologyDetailsResponse.Nodes> nodesMap = nodesList.stream().collect(Collectors.toMap(TopologyDetailsResponse.Nodes::getIdentifier, Function.identity()));
        List<TopologyDetailsResponse.Nodes> entryPointNodes = getEntryPointNode(signal, nodesMap);

        List<TopologyDetailsResponse.Nodes> sourceNodes = signal.getRootCauseServiceIds().stream()
                .map(serviceId -> {
                    TopologyDetailsResponse.Nodes node = nodesMap.getOrDefault(serviceId, null);
                    if (node == null) {
                        log.error("Service [{}] details unavailable in the topology", serviceId);
                    }
                    return node;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<TopologyDetailsResponse.Nodes> affectedNodes = signal.getServiceIds().stream()
                .map(serviceId -> {
                    TopologyDetailsResponse.Nodes node = nodesMap.getOrDefault(serviceId, null);
                    if (node == null) {
                        log.error("Service [{}] details unavailable in the topology", serviceId);
                    }
                    return node;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<TopologyDetailsResponse.Nodes> allAffectedNodeList = new ArrayList<>(entryPointNodes);
        allAffectedNodeList.addAll(sourceNodes);
        allAffectedNodeList.addAll(affectedNodes);

        Map<String, List<TopologyDetailsResponse.Nodes>> allNodes = new HashMap<>();
        allNodes.put(DESTINATION_NODES_KEY, entryPointNodes);
        allNodes.put(SOURCE_NODES_KEY, sourceNodes);
        allNodes.put(ALL_AFFECTED_KEY, allAffectedNodeList.stream().distinct().collect(Collectors.toList()));

        return allNodes;
    }

    private List<TopologyDetailsResponse.Nodes> getEntryPointNode(SignalDetails signal, Map<String, TopologyDetailsResponse.Nodes> nodesMap) {
        List<TopologyDetailsResponse.Nodes> allAffectedNodeList = new ArrayList<>();

        if ("EARLY_WARNING".equalsIgnoreCase(signal.getSignalType())) {
            log.debug("getting affected nodes for type: WARNING.");
            allAffectedNodeList = nodesMap.values().stream().filter(TopologyDetailsResponse.Nodes::isEntryPointNode).collect(Collectors.toList());
        } else {
            log.debug("getting affected nodes for type: PROBLEM.");
            if (signal.getEntryServiceId() == null || signal.getEntryServiceId().isEmpty()) {
                log.error("Signal entry service is unavailable for signal: {}", signal.getSignalId());
            } else {
                allAffectedNodeList = signal.getEntryServiceId().stream().map(es -> {
                    TopologyDetailsResponse.Nodes node = nodesMap.getOrDefault(es, null);
                    if (node == null) {
                        log.error("Service [{}] details unavailable in the topology", es);
                    }
                    return node;
                }).filter(Objects::nonNull).collect(Collectors.toList());
            }
        }
        return allAffectedNodeList;
    }

    protected List<List<TopologyDetailsResponse.Nodes>> getRCAPathList(ServiceDependencyGraph serviceDependencyGraph,
                                                                       Map<String, List<TopologyDetailsResponse.Nodes>> splitServiceSet) {
        List<List<TopologyDetailsResponse.Nodes>> rcaPathCollection = null;
        try {
            rcaPathCollection = new ArrayList<>();
            List<TopologyDetailsResponse.Nodes> affectedNodes = splitServiceSet.get(ALL_AFFECTED_KEY);

            for (TopologyDetailsResponse.Nodes txnAffectedNode : splitServiceSet.get(DESTINATION_NODES_KEY)) {

                for (TopologyDetailsResponse.Nodes kpiAffectedNode : splitServiceSet.get(SOURCE_NODES_KEY)) {

                    RCAPathGenerator rcaPathGenerator = new RCAPathGenerator(serviceDependencyGraph);
                    List<List<TopologyDetailsResponse.Nodes>> RCAPaths = rcaPathGenerator.getAllRCAPath(txnAffectedNode, kpiAffectedNode, affectedNodes);

                    for (List<TopologyDetailsResponse.Nodes> rcaPath : RCAPaths) {
                        rcaPathCollection = rcaPathGenerator.addRCAPath(rcaPath, rcaPathCollection);
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error occurred while generating RCA paths for all affected services.", e);
        }
        //The generated RCA paths are merged on a commonality principle
        return (mergeRCAPathList(serviceDependencyGraph, rcaPathCollection));
    }

    /**
     * This method is useful to merge all the generated RCA path into a single RCA path, this is used in case of
     * warnings where there does not exist multiple RCA paths but a single graph with all generated RCA paths as a
     * single graph
     */
    protected static List<TopologyDetailsResponse.TopologyDetails> combineRCAPaths(List<TopologyDetailsResponse.TopologyDetails> rcaPaths) {
        TopologyDetailsResponse.TopologyDetails result = null;

        for (TopologyDetailsResponse.TopologyDetails rcaPath : rcaPaths) {
            if (result == null) {
                result = rcaPath;
                continue;
            }

            /*
             * while merging the nodes/edges we give priority for nodes/edges that have meta data in them , in this
             * case the meta data is that whether given nodes/edges is path of RCA path
             */

            for (TopologyDetailsResponse.Nodes node : rcaPath.getNodes()) {
                if (result.getNodes().stream().noneMatch(it -> it.equals(node))) {
                    result.getNodes().add(node);
                }

                TopologyDetailsResponse.Nodes curNode = result.getNodes()
                        .stream()
                        .filter(it -> it.equals(node))
                        .findAny()
                        .orElse(null);

                if (curNode != null && !curNode.isRCAPathNode()) {
                    curNode.setStartNode(node.isStartNode());
                    curNode.setRCAPathNode(node.isRCAPathNode());
                }

            }

            for (TopologyDetailsResponse.Edges edge : rcaPath.getEdges()) {
                if (result.getEdges().stream().noneMatch(it -> it.equals(edge))) {
                    result.getEdges().add(edge);
                }

                TopologyDetailsResponse.Edges curEdge = result.getEdges()
                        .stream()
                        .filter(it -> it.equals(edge))
                        .findAny()
                        .orElse(null);
                if (curEdge != null && curEdge.getData().isEmpty()) curEdge.setData(edge.getData());
            }

            if (result.getImpactedServiceName().stream()
                    .noneMatch(it -> it.equalsIgnoreCase(rcaPath.getImpactedServiceName().get(0)))) {

                result.getImpactedServiceName().add(rcaPath.getImpactedServiceName().get(0));
            }
        }
        List<TopologyDetailsResponse.TopologyDetails> temp = new ArrayList<>();
        if (result != null) {
            temp.add(result);
        }
        return temp;
    }

    protected List<List<TopologyDetailsResponse.Nodes>> mergeRCAPathList(ServiceDependencyGraph serviceDependencyGraph,
                                                                         List<List<TopologyDetailsResponse.Nodes>> rcaPaths) {
        List<List<TopologyDetailsResponse.Nodes>> result = null;
        try {
            RCAPathGenerator rcaPathGenerator = new RCAPathGenerator(serviceDependencyGraph);
            result = rcaPathGenerator.mergeRCAPath(rcaPaths);
        } catch (Exception e) {
            log.error("Error occurred while merging RCA paths based on commonality", e);
        }
        return result;
    }
}
