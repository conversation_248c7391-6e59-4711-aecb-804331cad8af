package com.appnomic.appsone.api.businesslogic;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.mobile.util.BigQueryService;
import com.appnomic.appsone.api.mobile.util.BigQueryUtil;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.FieldValueList;
import com.google.cloud.bigquery.QueryJobConfiguration;
import com.google.cloud.bigquery.TableResult;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class GetSuccessRateTrendBL implements BusinessLogic<Object, UtilityBean<Object>, Map<String, BigDecimal>> {

	@Override
	public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
		List<String> errorMessages = new ArrayList<>();
		if (!BigQueryUtil.appOSStringValidation(requestObject, errorMessages)
			|| !BigQueryUtil.appDisplayVersionValidation(requestObject, errorMessages)
			|| !BigQueryUtil.fromTimeToTimeValidation(requestObject, errorMessages)
			|| !BigQueryUtil.requestNameValidation(requestObject, errorMessages)) {
				throw new ClientException(String.join(";", errorMessages));
		}
		String appOSString = requestObject.getQueryParams().get("app-os")[0];
		String appDisplayVersions = requestObject.getQueryParams().get("app-build-version")[0];
		String fromTime = requestObject.getQueryParams().get("from-time")[0];
		String toTime = requestObject.getQueryParams().get("to-time")[0];
		String requestName = requestObject.getParams().get(":request-name");
		
		String country = null != requestObject.getQueryParams().get("country") ? requestObject.getQueryParams().get("country")[0] : new String();
		String deviceName = null != requestObject.getQueryParams().get("device-name") ? requestObject.getQueryParams().get("device-name")[0] : new String();
		String carrier = null != requestObject.getQueryParams().get("carrier") ? requestObject.getQueryParams().get("carrier")[0] : new String();
		String radioType = null != requestObject.getQueryParams().get("radio-type") ? requestObject.getQueryParams().get("radio-type")[0] : new String();
		String mimeType = null != requestObject.getQueryParams().get("mime-type") ? requestObject.getQueryParams().get("mime-type")[0] : new String();
		
		return UtilityBean.<Object>builder()
				.appOSString(appOSString)
				.appDisplayVersions(appDisplayVersions)
				.fromTimeString(fromTime)
				.toTimeString(toTime)
				.requestName(requestName)
				.country(country)
				.deviceName(deviceName)
				.carrier(carrier)
				.radioType(radioType)
				.mimeType(mimeType)
				.build();
	}

	@Override
	public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
		return utilityBean;
	}

	@Override
	public Map<String, BigDecimal> processData(UtilityBean<Object> configData) throws DataProcessingException {
		String appOSString = configData.getAppOSString();
		BigQuery bigQuery = BigQueryService.getInstance(appOSString.split("\\.")[0]);
		log.debug("bigQuery Obj: {}", bigQuery);

		List<String> appDisplayVersions = Arrays.asList(configData.getAppDisplayVersions().split(","));
		String fromTime = configData.getFromTimeString();
		String toTime = configData.getToTimeString();
		String requestName = configData.getRequestName();
		String intervalBucket = BigQueryUtil.getIntervalBucket(fromTime, toTime);
		String intervalBucketUnit = intervalBucket.split(" ")[1];
		
		String truncatedEventTimestamp = BigQueryUtil.getTruncatedEventTimestamp(intervalBucket, intervalBucketUnit, Constants.PERFMON_ALIAS);
		
		String countriesInParam = "";
		String deviceNamesInParam = "";
		String carriersInParam = "";
		String radioTypesInParam = "";
		String mimeTypesInParam = "";

		StringBuilder filterQuery = new StringBuilder();
		if(configData.getCountry().length() > 0) {
			countriesInParam = BigQueryUtil.getInParameter(configData.getCountry());
			filterQuery.append(" AND country IN " + countriesInParam + " ");
		}
		if(configData.getDeviceName().length() > 0) {
			deviceNamesInParam = BigQueryUtil.getInParameter(configData.getDeviceName());
			filterQuery.append(" AND device_name IN " + deviceNamesInParam + " ");
		}
		if(configData.getCarrier().length() > 0) {
			carriersInParam = BigQueryUtil.getInParameter(configData.getCarrier());
			filterQuery.append(" AND carrier IN " + carriersInParam + " ");
		}
		if(configData.getRadioType().length() > 0) {
			radioTypesInParam = BigQueryUtil.getInParameter(configData.getRadioType());
			filterQuery.append(" AND radio_type IN " + radioTypesInParam + " ");
		}
		if(configData.getMimeType().length() > 0) {
			mimeTypesInParam = BigQueryUtil.getInParameter(configData.getMimeType());
			filterQuery.append(" AND network_info.response_mime_type IN " + mimeTypesInParam + " ");
		}
		
		String query = 	"SELECT  " + 
						"    " + truncatedEventTimestamp + " AS time, " +
						"    TIMESTAMP_TRUNC(TIMESTAMP_MILLIS("+fromTime+"), "+intervalBucketUnit+") AS truncatedFromTime," + 
						"    event_name AS requestName, " +
						"    app_display_version AS appDisplayVersions,  " + 
						"    COUNTIF(network_info.response_code >= 200 AND network_info.response_code < 300) AS success, " + 
						"    COUNTIF(network_info IS NOT null) AS total " + 
						"FROM "+Constants.getPerfMonTable(appOSString)+" AS perfmon " + 
						"WHERE " +
						"	 perfmon.event_timestamp > TIMESTAMP_MILLIS("+fromTime+") " +
						"	 AND perfmon.event_timestamp < TIMESTAMP_MILLIS("+toTime+") " +
						"	 AND event_name LIKE '%"+requestName+"%'" +
						"    " + filterQuery +
						"GROUP BY " +
						"	time, " +
						" 	appDisplayVersions, " +
						" 	requestName " +
						"ORDER BY 1";

		QueryJobConfiguration queryConfig = QueryJobConfiguration.newBuilder(query).setUseLegacySql(false).build();
		
		TableResult result;
		try {
			result = bigQuery.query(queryConfig);
		} catch(InterruptedException inExc) {
			throw new DataProcessingException("Error in fetching data from BigQuery");
		}
		
		Map<String, BigDecimal> successRateTrend = new HashMap<>();
		Map<String, Long> successRequestsSumMap = new HashMap<>();
		Map<String, Long> totalRequestsSumMap = new HashMap<>();
		
		for(FieldValueList value : result.iterateAll()) {
			if(successRateTrend.isEmpty()) {
				successRateTrend = BigQueryUtil.createTimeBucketBigDecimal(value.get("truncatedFromTime").getTimestampValue() / 1000L, Long.parseLong(toTime), intervalBucket);
			}
			String timeKey = String.valueOf(value.get("time").getTimestampValue() / 1000L);
			if(successRateTrend.containsKey(timeKey)) {
				if(null != value.get("appDisplayVersions").getValue() && appDisplayVersions.contains(value.get("appDisplayVersions").getStringValue())) {
					Long existingSuccessRequestsSum = null != successRequestsSumMap.get(timeKey) ? successRequestsSumMap.get(timeKey) : 0L;
					Long newSuccessRequests = value.get("success").getLongValue();
					Long successRequestsSum = existingSuccessRequestsSum + newSuccessRequests;
					successRequestsSumMap.put(timeKey, successRequestsSum);
					
					Long existingTotalRequestsSum = null != totalRequestsSumMap.get(timeKey) ? totalRequestsSumMap.get(timeKey) : 0L;
					Long newTotalRequests = value.get("total").getLongValue();
					Long totalRequestsSum = existingTotalRequestsSum + newTotalRequests;
					totalRequestsSumMap.put(timeKey, totalRequestsSum);
					
					BigDecimal newSuccessPercentage = 	(totalRequestsSum > 0) ? 
														new BigDecimal((double)successRequestsSum/totalRequestsSum * 100).setScale(3, RoundingMode.HALF_UP) : 
														new BigDecimal(0).setScale(3);
					successRateTrend.put(timeKey, newSuccessPercentage);
				}
			}
		}
		return successRateTrend;
	}
}
