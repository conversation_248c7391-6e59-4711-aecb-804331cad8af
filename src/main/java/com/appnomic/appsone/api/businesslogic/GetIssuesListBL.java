package com.appnomic.appsone.api.businesslogic;

import java.util.ArrayList;
import java.util.List;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.mobile.model.Issue;
import com.appnomic.appsone.api.mobile.util.BigQueryService;
import com.appnomic.appsone.api.mobile.util.BigQueryUtil;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.QueryJobConfiguration;
import com.google.cloud.bigquery.TableResult;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class GetIssuesListBL implements BusinessLogic<Object, UtilityBean<Object>, List<Issue>> {

	@Override
	public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
		List<String> errorMessages = new ArrayList<String>();
		if (!BigQueryUtil.appOSStringValidation(requestObject, errorMessages)
			|| !BigQueryUtil.appDisplayVersionValidation(requestObject, errorMessages)
			|| !BigQueryUtil.fromTimeToTimeValidation(requestObject, errorMessages)) {
				throw new ClientException(String.join(";", errorMessages));
		}
		
		String appOSString = requestObject.getQueryParams().get("app-os")[0];
		String appDisplayVersions = requestObject.getQueryParams().get("app-build-version")[0];
		String fromTime = requestObject.getQueryParams().get("from-time")[0];
		String toTime = requestObject.getQueryParams().get("to-time")[0];
		return UtilityBean.<Object>builder()
				.appOSString(appOSString)
				.appDisplayVersions(appDisplayVersions)
				.fromTimeString(fromTime)
				.toTimeString(toTime)
				.build();
	}

	@Override
	public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
		return utilityBean;
	}

	@Override
	public List<Issue> processData(UtilityBean<Object> configData) throws DataProcessingException {
		String appOSString = configData.getAppOSString();
		BigQuery bigQuery = BigQueryService.getInstance(appOSString.split("\\.")[0]);
		log.debug("bigQuery Obj: {}", bigQuery);

		String appDisplayVersionInParam = BigQueryUtil.getInParameter(configData.getAppDisplayVersions());
		String fromTime = configData.getFromTimeString();
		String toTime = configData.getToTimeString();
		
		String query =  "SELECT  " + 
						"    issue_id AS issueId, " + 
						"    STRING_AGG(DISTINCT issue_title) AS issueTitle, " + 
						"	 STRING_AGG(DISTINCT issue_subtitle) AS issueSubTitle, "+
						"    is_fatal AS eventType, " + 
						"    STRING_AGG(DISTINCT application.display_version) AS appDisplayVersions, " + 
						"    COUNT(issue_id) AS crashCount, " + 
						"    COUNT(DISTINCT user.id ) AS userCount " + 
						"FROM " + Constants.getCrashlyticsTable(appOSString) +" AS crashlytics  " +
						"WHERE event_timestamp > TIMESTAMP_MILLIS("+fromTime+") " + 
						"    AND event_timestamp < TIMESTAMP_MILLIS("+toTime+") " + 
						"	 AND application.display_version IN " + appDisplayVersionInParam + " " +
						"GROUP BY issueId, eventType";

		QueryJobConfiguration queryConfig = QueryJobConfiguration.newBuilder(query).setUseLegacySql(false).build();
		TableResult result;
		try {
			result = bigQuery.query(queryConfig);
		} catch(InterruptedException inExc) {
			throw new DataProcessingException("Error in fetching data from BigQuery");
		}
		List<Issue> response = new ArrayList<>();
		
		result.iterateAll().forEach(value -> {
			if(null != value.get("appDisplayVersions").getValue()) {
				Issue issue = new Issue();
				issue.setId(value.get("issueId").getStringValue());
				issue.setTitle(value.get("issueTitle").getStringValue());
				issue.setSubTitle(value.get("issueSubTitle").getStringValue());
				issue.setCrashIsFatal(value.get("eventType").getBooleanValue());
				issue.setAppDisplayVersions(value.get("appDisplayVersions").getStringValue());
				issue.setCrashCount(value.get("crashCount").getLongValue());
				issue.setUserCount(value.get("userCount").getLongValue());
				response.add(issue);
			}
		});
		return response;
	}
	
}
