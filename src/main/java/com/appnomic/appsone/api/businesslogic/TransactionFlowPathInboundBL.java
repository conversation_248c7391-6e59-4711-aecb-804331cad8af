package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.beans.UserAccountIdentifiersBean;
import com.appnomic.appsone.api.beans.tfp.TFPRequestData;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.cache.UserDetailsCache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.opensearch.CollatedTransactionsSearchRepo;
import com.appnomic.appsone.api.dao.opensearch.RawTransactionSearchRepo;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.ApplicationRepo;
import com.appnomic.appsone.api.dao.redis.ServiceRepo;
import com.appnomic.appsone.api.dao.redis.TimeRangeDetailsDao;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.TabularResultsTypePojo;
import com.appnomic.appsone.api.pojo.UserAccessDetails;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.pojo.tfp.TFPServiceDetails;
import com.appnomic.appsone.api.service.KeyCloakAuthService;
import com.appnomic.appsone.api.util.*;
import com.appnomic.appsone.model.JWTData;
import com.appnomic.appsone.opeasearchquery.results.TabularResults;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.Application;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.TimeRangeDetails;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Slf4j
public class TransactionFlowPathInboundBL implements BusinessLogic<TFPRequestData, UtilityBean<TFPRequestData>, List<TFPServiceDetails>> {

    private static final String RESPONSE_TYPE_DEFAULT = ConfProperties.getString(Constants.TRANSACTION_TYPE,
            Constants.TRANSACTION_TYPE_DEFAULT);

    @Override
    public UtilityBean<TFPRequestData> clientValidation(RequestObject request) throws ClientException {
        if (request == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authToken)) {
            log.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String accId = request.getParams().get(Constants.REQUEST_PARAM_IDENTIFIER.toLowerCase());
        if (StringUtils.isEmpty(accId)) {
            log.error(Constants.MESSAGE_INVALID_ACCOUNT + ": {}", accId);
            throw new ClientException(Constants.MESSAGE_INVALID_ACCOUNT);
        }
        List<String> appIds = new ArrayList<>();
        if (request.getParams().get(Constants.REQUEST_PARAM_APPLICATION_ID).equalsIgnoreCase("all")) {
            appIds = Arrays.stream(request.getQueryParams().get(Constants.REQUEST_QUERY_PARAM_APPLICATION_ID)[0].toLowerCase().split(",")).collect(Collectors.toList());
            for (String appId : appIds) {
                if (StringUtils.isEmpty(appId)) {
                    log.error(Constants.MESSAGE_INVALID_APPLICATION + ": {}", appId);
                    throw new ClientException(Constants.MESSAGE_INVALID_APPLICATION);
                }
            }
        } else {
            appIds.add(request.getParams().get(Constants.REQUEST_PARAM_APPLICATION_ID));
            for (String appId : appIds) {
                if (StringUtils.isEmpty(appId)) {
                    log.error(Constants.MESSAGE_INVALID_APPLICATION + ": {}", appId);
                    throw new ClientException(Constants.MESSAGE_INVALID_APPLICATION);
                }
            }
        }

        String fromTimeString = request.getQueryParams().get(Constants.REQUEST_PARAM_FROM_TIME)[0];
        if (StringUtils.isEmpty(fromTimeString)) {
            log.error(Constants.MESSAGE_INVALID_TIME_RANGE + ": {}", fromTimeString);
            throw new ClientException(Constants.MESSAGE_INVALID_TIME_RANGE);
        }

        String toTimeString = request.getQueryParams().get(Constants.REQUEST_PARAM_TO_TIME)[0];
        if (StringUtils.isEmpty(toTimeString)) {
            log.error(Constants.MESSAGE_INVALID_TIME_RANGE + ": {}", toTimeString);
            throw new ClientException(Constants.MESSAGE_INVALID_TIME_RANGE);
        }
        List<Integer> applicationIds = new ArrayList<>();
        long fromTime;
        long toTime;
        try {
            for (String id : appIds) {
                Integer.parseInt(id);
                applicationIds.add(Integer.valueOf(id));
            }
            fromTime = Long.parseLong(fromTimeString);
            toTime = Long.parseLong(toTimeString);
        } catch (NumberFormatException e) {
            log.error("Invalid value found where number is required. {}", e.getMessage());
            throw new ClientException(String.format("Invalid value found where number is required. %s", e.getMessage()));
        }

        if (fromTime <= 0 || toTime <= 0 || fromTime >= toTime) {
            log.error("Invalid time range provided");
            throw new ClientException("Invalid time range provided");
        }

        String responseType = request.getParams().getOrDefault(Constants.REQUEST_PARAM_RESPONSE_TYPE, RESPONSE_TYPE_DEFAULT);

        return UtilityBean.<TFPRequestData>builder()
                .fromTime(fromTime)
                .toTime(toTime)
                .accountIdString(accId)
                .appIds(applicationIds)
                .authToken(authToken)
                .responseType(responseType)
                .build();
    }

    @Override
    public UtilityBean<TFPRequestData> serverValidation(UtilityBean<TFPRequestData> utilityBean) throws ServerException {

        UserAccessDetails accessDetails = getAccessDetails(utilityBean);
        TFPRequestData tfpRequestData = new TFPRequestData();
        tfpRequestData.setUserAccessDetails(accessDetails);
        tfpRequestData.setFromTime(utilityBean.getFromTime());
        tfpRequestData.setToTime(utilityBean.getToTime());

        Account account = new AccountRepo().getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            log.error("Invalid account identifier: {}.", utilityBean.getAccountIdString());
            throw new ServerException(Constants.MESSAGE_INVALID_ACCOUNT);
        }
        tfpRequestData.setAccount(account);

        String accountIdentifier = account.getIdentifier();

        TimeRangeDetailsDao timeRangeDetailsDao = new TimeRangeDetailsDao();
        List<TimeRangeDetails> timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(accountIdentifier);
        if (timeRangeDetailsList == null) {
            log.error("Exception occurred while fetching time range details for account {}", accountIdentifier);
            throw new ServerException("Exception occurred while fetching time range details for account " + accountIdentifier);
        }

        if (timeRangeDetailsList.isEmpty()) {
            log.trace("No time range details found for account {}. Searching for Global account.", accountIdentifier);
            timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }
        if (timeRangeDetailsList == null) {
            log.error("Exception occurred while fetching time range details for account {}", Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
            throw new ServerException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }

        if (timeRangeDetailsList.isEmpty()) {
            log.error("No time range details found");
            throw new ServerException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }
        TimeRangeDetails timeRangeDetails = ValidationUtils.getRollupPojo(utilityBean.getFromTime(), utilityBean.getToTime(), timeRangeDetailsList);
        if (timeRangeDetails == null) {
            log.error("Time range details are invalid.");
            throw new ServerException("Time range details are invalid.");
        }
        utilityBean.setTimeRangeDetails(timeRangeDetails);
        utilityBean.setTimeRangeDetailsList(timeRangeDetailsList);

        List<Integer> appIds = utilityBean.getAppIds();
        List<Application> applications = new ArrayList<>();
        List<Application> applicationList = new ApplicationRepo().getAllApplicationDetails(account.getIdentifier());
        for (Integer id : appIds) {
            Application application = applicationList.stream().filter(e -> e.getId() == id).findAny().orElse(null);
            if (application == null) {
                log.error("Invalid application id: {}", utilityBean.getApplicationId());
                throw new ServerException("Invalid application id.");
            }
            applications.add(application);
        }
        tfpRequestData.setApplications(applications);

        log.info("All parameter received have been validated from server.");

        utilityBean.setRequestPayloadObject(tfpRequestData);
        return utilityBean;
    }

    private UserAccessDetails getAccessDetails(UtilityBean<TFPRequestData> utilityBean) throws ServerException {

        String userId;
        try {
            JWTData jwtData = KeyCloakAuthService.extractUserDetails(utilityBean.getAuthToken());
            userId = jwtData.getSub();
        } catch (Exception e) {
            log.error("Invalid Authorization Token");
            throw new ServerException("Invalid Authorization Token");
        }

        UserAccessDetails accessDetails;
        try {
            accessDetails = UserDetailsCache.getInstance().userApplications
                    .get(new UserAccountIdentifiersBean(userId, utilityBean.getAccountIdString()));
        } catch (ExecutionException e) {
            log.error("Exception while fetching user access details. Reason: {}", e.getMessage(), e);
            throw new ServerException("Error in fetching user access details");
        }
        return accessDetails;
    }

    @Override
    public List<TFPServiceDetails> processData(UtilityBean<TFPRequestData> utilityBean) throws DataProcessingException {
        TFPRequestData configData = utilityBean.getRequestPayloadObject();
        List<Application> applications = configData.getApplications();
        Account account = configData.getAccount();
        long fromTime = configData.getFromTime();
        long toTime = configData.getToTime();
        TransactionFlowPathOutboundBL outboundBL = new TransactionFlowPathOutboundBL();

        ServiceRepo serviceRepo = new ServiceRepo();
        TFPCommonBL tfpCommonBL = new TFPCommonBL();
        long start;
        start = System.currentTimeMillis();
        log.debug("Time taken to fetch configuration data is {} ms.", (System.currentTimeMillis() - start));

        List<BasicEntity> allServiceList = serviceRepo.getAllServices(account.getIdentifier());
        Map<String, BasicEntity> serviceMap = allServiceList.parallelStream().collect(Collectors.toMap(BasicEntity::getIdentifier, c -> c));

        List<TFPServiceDetails> result = new ArrayList<>();
        List<TFPServiceDetails> tempTfpServiceDetailsList;
        if (DateTimeUtil.inRange(fromTime)) {
            TabularResults tabularResults = new RawTransactionSearchRepo().getInboundOutboundTxnDetails(account.getIdentifier(),
                    utilityBean.getResponseType(), fromTime, toTime);
            tempTfpServiceDetailsList = outboundBL.extractRawTabularResults(tabularResults, allServiceList);
        } else {
            NewTimeIntervalGenerationUtility t = new NewTimeIntervalGenerationUtility();
            t.setTimezoneOffset(account.getIdentifier());
            t.processTimeRange(fromTime, toTime);
            t.addEndTimeInOSTime();
            List<Long> times = t.getOSTimes();
            List<TabularResultsTypePojo> tabularResultsTypeList = new CollatedTransactionsSearchRepo().getAllTransactionDataByService(account.getIdentifier(),
                    utilityBean.getResponseType(), fromTime, toTime - Constants.MINUTE,
                    utilityBean.getTimeRangeDetails(), utilityBean.getTimeRangeDetailsList(), times);
            tempTfpServiceDetailsList = outboundBL.extractTabularResults(tabularResultsTypeList, serviceMap);
        }

        List<String> allEntryServices = allServiceList
                .parallelStream()
                .map(s -> HealUICache.INSTANCE.getServiceDetails(account.getIdentifier(), s.getIdentifier()))
                .filter(s -> s.getTags() != null)
                .filter(s -> s.getTags().stream().anyMatch(tags -> tags.getType().equalsIgnoreCase(Constants.ENTRY_POINT)))
                .map(BasicEntity::getIdentifier)
                .collect(Collectors.toList());

        applications.forEach(app -> {
            List<BasicEntity> applicationServices = HealUICache.INSTANCE.getApplicationServiceList(account.getIdentifier(), app.getIdentifier());
            List<BasicEntity> applicationEntryServices = applicationServices.stream()
                    .filter(c -> allEntryServices.contains(c.getIdentifier()))
                    .collect(Collectors.toList());

            List<TFPServiceDetails> tfpServiceDetailsList = tfpCommonBL.getServiceTransactionStats(account, app,
                    configData.getUserAccessDetails(), applicationEntryServices, tempTfpServiceDetailsList);

            log.debug("Inbound services count: {} for Application {}.", tfpServiceDetailsList.size(), app.getName());
            result.addAll(tfpServiceDetailsList);
        });

        log.debug("Total inbound services count {}.", result.size());
        return result.parallelStream()
                .sorted(Comparator.comparing(TFPServiceDetails::getVolume).reversed())
                .collect(Collectors.toList());
    }
}