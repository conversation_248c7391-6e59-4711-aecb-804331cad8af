package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.opensearch.CollatedTransactionsSearchRepo;
import com.appnomic.appsone.api.dao.opensearch.RawTransactionSearchRepo;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.ServiceRepo;
import com.appnomic.appsone.api.dao.redis.TimeRangeDetailsDao;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.TabularResultsTypePojo;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.pojo.tfp.TFPPerformanceDetails;
import com.appnomic.appsone.api.util.*;
import com.appnomic.appsone.opeasearchquery.results.TabularResults;
import com.heal.configuration.enums.KPIAttributes;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.Service;
import com.heal.configuration.pojos.TimeRangeDetails;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.appnomic.appsone.api.common.LoggerTags.TAG_INVALID_FROM_TO_TIME;

@Slf4j
public class TFPPerformanceDetailsBL implements BusinessLogic<TFPPerformanceDetails, UtilityBean<TFPPerformanceDetails>, TFPPerformanceDetails> {

    private static final String RESPONSE_TYPE_DEFAULT = ConfProperties.getString(Constants.TRANSACTION_TYPE,
            Constants.TRANSACTION_TYPE_DEFAULT);

    @Override
    public UtilityBean<TFPPerformanceDetails> clientValidation(RequestObject requestObject) throws ClientException {
        log.trace("Inside Client validation");

        if (requestObject == null) {
            log.error("TFP Performance request object is received as null");
            throw new ClientException("TFP Performance request object is received as null");
        }
        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION_HEADER);
        String identifier = requestObject.getParams().get(Constants.REQUEST_PARAM_IDENTIFIER);
        String serviceIdStr = requestObject.getParams().get(Constants.REQUEST_PARAM_SERVICE_ID);
        String fromTimeString = requestObject.getQueryParams().get(Constants.REQUEST_PARAM_FROM_TIME)[0];
        String toTimeString = requestObject.getQueryParams().get(Constants.REQUEST_PARAM_TO_TIME)[0];
        int serviceId;
        Long fromTime;
        Long toTime;

        if (StringUtils.isEmpty(authKey)) {
            log.error(Constants.MESSAGE_EMPTY_AUTH_TOKEN);
            throw new ClientException(Constants.MESSAGE_EMPTY_AUTH_TOKEN);
        }

        if (StringUtils.isEmpty(identifier)) {
            log.error(Constants.MESSAGE_EMPTY_ACCOUNT);
            throw new ClientException(Constants.MESSAGE_EMPTY_ACCOUNT);
        }

        if (serviceIdStr == null || serviceIdStr.trim().isEmpty()) {
            log.error("Service id should not be empty or null.");
            throw new ClientException(UIMessages.SERVICE_EMPTY_ERROR);
        } else {
            try {
                serviceId = Integer.parseInt(serviceIdStr);
            } catch (NumberFormatException e) {
                log.error("Error occurred while converting the service id {}. Reason: {}", serviceIdStr, e.getMessage());
                throw new ClientException(UIMessages.INVALID_SERVICE);
            }
        }

        try {
            fromTime = (fromTimeString == null) ? null : Long.parseLong(fromTimeString);
        } catch (NumberFormatException e) {
            log.error("Error occurred while converting the fromTime [{}]. Reason: {}", fromTimeString, e.getMessage());
            throw new ClientException(Constants.INVALID_FROM_TIME);
        }

        try {
            toTime = (toTimeString == null) ? null : Long.parseLong(toTimeString);
        } catch (NumberFormatException e) {
            log.error("Error occurred while converting the toTime [{}]. Reason: {}", toTimeString, e.getMessage());
            throw new ClientException(Constants.INVALID_TO_TIME);
        }

        if ((fromTime == null) || (toTime == null) || (fromTime <= 0) || (toTime <= 0) || (fromTime > toTime)) {
            log.error(TAG_INVALID_FROM_TO_TIME);
            throw new ClientException(TAG_INVALID_FROM_TO_TIME);
        }

        String responseType = requestObject.getParams().getOrDefault(Constants.REQUEST_PARAM_RESPONSE_TYPE, RESPONSE_TYPE_DEFAULT);

        return UtilityBean.<TFPPerformanceDetails>builder()
                .authToken(authKey)
                .accountIdString(identifier)
                .serviceId(serviceId)
                .fromTime(fromTime)
                .toTime(toTime)
                .responseType(responseType)
                .build();
    }

    @Override
    public UtilityBean<TFPPerformanceDetails> serverValidation(UtilityBean<TFPPerformanceDetails> utilityBean) throws ServerException {
        log.debug("Inside Server validation");
        ServiceRepo serviceRepo = new ServiceRepo();
        AccountRepo accountRepo = new AccountRepo();

        com.heal.configuration.pojos.Account account = accountRepo.getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            log.error("Invalid account identifier: {}.", utilityBean.getAccountIdString());
            throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
        }
        utilityBean.setAccount(account);

        String accountIdentifier = account.getIdentifier();

        TimeRangeDetailsDao timeRangeDetailsDao = new TimeRangeDetailsDao();
        List<TimeRangeDetails> timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(accountIdentifier);
        if (timeRangeDetailsList == null) {
            log.error("Exception occurred while fetching time range details for account {}", accountIdentifier);
            throw new ServerException("Exception occurred while fetching time range details for account " + accountIdentifier);
        }

        if (timeRangeDetailsList.isEmpty()) {
            log.trace("No time range details found for account {}. Searching for Global account.", accountIdentifier);
            timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }
        if (timeRangeDetailsList == null) {
            log.error("Exception occurred while fetching time range details for account {}", Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
            throw new ServerException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }

        if (timeRangeDetailsList.isEmpty()) {
            log.error("No time range details found");
            throw new ServerException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }
        TimeRangeDetails timeRangeDetails = ValidationUtils.getRollupPojo(utilityBean.getFromTime(), utilityBean.getToTime(), timeRangeDetailsList);
        if (timeRangeDetails == null) {
            log.error("Time range details are invalid.");
            throw new ServerException("Time range details are invalid.");
        }
        utilityBean.setTimeRangeDetails(timeRangeDetails);
        utilityBean.setTimeRangeDetailsList(timeRangeDetailsList);

        BasicEntity basicEntity = serviceRepo.getAllServices(account.getIdentifier()).stream().filter(s -> s.getId() == utilityBean.getServiceId()).findAny().orElse(null);
        if (basicEntity == null) {
            log.error("Invalid service id: {}", utilityBean.getServiceId());
            throw new ServerException(UIMessages.ERROR_INVALID_SERVICE_ID);
        }

        Service service = HealUICache.INSTANCE.getServiceDetails(account.getIdentifier(), basicEntity.getIdentifier());
        if (service == null) {
            log.error("Invalid service id: {}, identifier:{}", utilityBean.getServiceId(), basicEntity.getIdentifier());
            throw new ServerException(UIMessages.ERROR_INVALID_SERVICE_ID);
        }
        utilityBean.setService(service);
        return utilityBean;
    }

    @Override
    public TFPPerformanceDetails processData(UtilityBean<TFPPerformanceDetails> configData) throws DataProcessingException {

        if (DateTimeUtil.inRange(configData.getFromTime())) {
            return processRawTxnData(configData);
        }

        long start = System.currentTimeMillis();

        String accountIdentifier = configData.getAccount().getIdentifier();
        long fromTime = configData.getFromTime();
        long toTime = configData.getToTime();

        NewTimeIntervalGenerationUtility t = new NewTimeIntervalGenerationUtility();
        t.setTimezoneOffset(accountIdentifier);
        t.processTimeRange(fromTime, toTime);
        t.addEndTimeInOSTime();

        List<Long> times = t.getOSTimes();

        List<String> columns = new ArrayList<>();
        columns.add("serviceId");

        List<TabularResultsTypePojo> results = new CollatedTransactionsSearchRepo()
                .getTransactionCollatedDataByServiceForAllKpi(accountIdentifier,
                        configData.getService().getIdentifier(), columns, configData.getResponseType(), fromTime,
                        toTime - Constants.MINUTE, configData.getTimeRangeDetails(), configData.getTimeRangeDetailsList(), times);

        if (results == null || results.isEmpty()) {
            log.debug("No transaction data found in OpenSearch.");
            return TFPPerformanceDetails.builder().build();
        }

        Map<String, Double> aggLvlKpisMap = new HashMap<>();
        Pattern p = Pattern.compile(".*\\.");
        for (TabularResultsTypePojo tabularResultsTypePojo : results) {

            TabularResults tabularResults = tabularResultsTypePojo.getTabularResults();
            Map<String, Double> tempAggLvlKpisMap = tabularResults.getRowResults().stream()
                    .map(TabularResults.ResultRow::getListOfRows)
                    .flatMap(Collection::stream)
                    .filter(c -> !c.getColumnDataType().contains("String"))
                    .collect(Collectors.toMap(c -> p.matcher(c.getColumnName()).replaceAll(""), c -> Double.parseDouble(c.getColumnValue())));

            tempAggLvlKpisMap.forEach((key, value) -> {
                if (aggLvlKpisMap.containsKey(key)) {
                    aggLvlKpisMap.put(key, aggLvlKpisMap.get(key) + value);
                } else aggLvlKpisMap.put(key, value);
            });

        }

        log.debug("Time taken to process tfpPerformance txn {} ms.", (System.currentTimeMillis() - start));

        return TFPPerformanceDetails.builder()
                .success(aggLvlKpisMap.get(KPIAttributes.SUCCESS_VOLUME.toString()).longValue())
                .slow(aggLvlKpisMap.get(KPIAttributes.SLOW_VOLUME.toString()).longValue())
                .failed(aggLvlKpisMap.get(KPIAttributes.FAIL_VOLUME.toString()).longValue())
                .timedout(aggLvlKpisMap.get(KPIAttributes.TIMEOUT_VOLUME.toString()).longValue())
                .unknown(aggLvlKpisMap.get(KPIAttributes.UNKNOWN_VOLUME.toString()).longValue())
                .build();

    }

    private TFPPerformanceDetails processRawTxnData(UtilityBean<TFPPerformanceDetails> configData) {
        long start = System.currentTimeMillis();

        TabularResults tabularResults = new RawTransactionSearchRepo()
                .getTransactionNtsDataClusterLevel(configData.getAccount().getIdentifier(),
                        configData.getService().getIdentifier(), null, configData.getResponseType(),
                        configData.getFromTime(), configData.getToTime());

        if (tabularResults == null || tabularResults.getRowResults() == null || tabularResults.getRowResults().isEmpty()) {
            log.debug("No transaction data found in OpenSearch.");
            return TFPPerformanceDetails.builder().build();
        }

        Map<String, Long> kpiCountMap = new LinkedHashMap<>();
        for (TabularResults.ResultRow resultRow : tabularResults.getRowResults()) {
            String kpiName = "";
            for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {
                if (resultRowColumn.getColumnName().equalsIgnoreCase("responseTimes.responseStatusTag")) {
                    kpiName = resultRowColumn.getColumnValue();
                }
            }
            kpiCountMap.put(kpiName, resultRow.getCountValue());
        }

        TFPPerformanceDetails tfpPerformanceDetails = new TFPPerformanceDetails();
        kpiCountMap.forEach((key, value) -> {
            if (KPIAttributes.SUCCESS_VOLUME.getColumnName().equalsIgnoreCase(key)) {
                tfpPerformanceDetails.setSuccess(value);
            } else if (KPIAttributes.SLOW_VOLUME.getColumnName().equalsIgnoreCase(key)) {
                tfpPerformanceDetails.setSlow(value);
            } else if (KPIAttributes.FAIL_VOLUME.getColumnName().equalsIgnoreCase(key)) {
                tfpPerformanceDetails.setFailed(value);
            } else if (KPIAttributes.TIMEOUT_VOLUME.getColumnName().equalsIgnoreCase(key)) {
                tfpPerformanceDetails.setTimedout(value);
            } else if (KPIAttributes.UNKNOWN_VOLUME.getColumnName().equalsIgnoreCase(key)) {
                tfpPerformanceDetails.setUnknown(value);
            }
        });
        log.debug("Time taken to process tfpPerformance txn {} ms.", (System.currentTimeMillis() - start));
        return tfpPerformanceDetails;
    }
}
