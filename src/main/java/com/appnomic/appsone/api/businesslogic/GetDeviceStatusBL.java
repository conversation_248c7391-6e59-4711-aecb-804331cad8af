package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.mobile.model.DeviceStatus;
import com.appnomic.appsone.api.mobile.util.BigQueryService;
import com.appnomic.appsone.api.mobile.util.BigQueryUtil;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;

import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.FieldValueList;
import com.google.cloud.bigquery.QueryJobConfiguration;
import com.google.cloud.bigquery.TableResult;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class GetDeviceStatusBL implements BusinessLogic<Object, UtilityBean<Object>, List<DeviceStatus>> {

    @Override
    public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {

        List<String> errorMessages = new ArrayList<String>();
        if(!BigQueryUtil.appOSStringValidation(requestObject, errorMessages)
        || !BigQueryUtil.issueIdValidation(requestObject, errorMessages)) {
            throw new ClientException(String.join(";", errorMessages));
        }

        String appOSString = requestObject.getQueryParams().get("app-os")[0];
        String issue_id = requestObject.getParams().get(":id");

        return UtilityBean.<Object>builder()
                .appOSString(appOSString)
                .issueId(issue_id)
                .build();
    }

    @Override
    public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
        return utilityBean;
    }

    @Override
    public List<DeviceStatus> processData(UtilityBean<Object> configData) throws DataProcessingException {
        String appOSString = configData.getAppOSString();
        String issue_id = configData.getIssueId();
        BigQuery bigQuery = BigQueryService.getInstance(appOSString.split("\\.")[0]);
        log.debug("bigQuery Obj: {}", bigQuery);

        String query = "select operating_system.modification_state, process_state\n" +
                "       from " + Constants.getCrashlyticsTable(appOSString) +
                "       where issue_id = '" + issue_id + "'";

        QueryJobConfiguration queryConfig = QueryJobConfiguration.newBuilder(query).setUseLegacySql(false).build();

        TableResult result;

        try {
            result = bigQuery.query(queryConfig);
        } catch(InterruptedException inExc) {
            throw new DataProcessingException("Error in fetching data from BigQuery");
        }

        List<DeviceStatus> response = new ArrayList<>();
        
        int modified =0, unmodified =0, background =0, foreground=0;
        BigDecimal backgroundStatus = new BigDecimal(0);
        BigDecimal modifiedStatus = new BigDecimal(0);
        String temp = "", temp1 = "" ;

        for (FieldValueList resultValue : result.iterateAll()) {
            temp1 = resultValue.get("process_state").getStringValue();
            temp = resultValue.get("modification_state").getStringValue();

            if(null != temp && temp.equals("UNMODIFIED"))
                unmodified++;
            if(null != temp && temp.equals("MODIFIED"))
                modified++;
            if(null != temp1 && temp1.equals("BACKGROUND"))
                background++;
            if(null != temp1 && temp1.equals("FOREGROUND"))
                foreground++;
        }

        if (background != 0 && foreground !=0) {
        	backgroundStatus = new BigDecimal((double) background /  (background + foreground) * 100).setScale(2, RoundingMode.HALF_UP);
        }
        if (modified !=0 && unmodified !=0) {
        	modifiedStatus = new BigDecimal((double) modified / (modified + unmodified) * 100).setScale(2, RoundingMode.HALF_UP);
        }

        DeviceStatus deviceStatus = new DeviceStatus();
        deviceStatus.setName("Rooted Percentage");
        deviceStatus.setValue(modifiedStatus);
        response.add(deviceStatus);
        
        deviceStatus = new DeviceStatus();
        deviceStatus.setName("Background Percentage");
        deviceStatus.setValue(backgroundStatus);
        response.add(deviceStatus);

        return response;

    }
}
