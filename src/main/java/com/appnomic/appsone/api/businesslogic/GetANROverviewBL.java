package com.appnomic.appsone.api.businesslogic;

import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.mobile.model.ANROverview;
import com.appnomic.appsone.api.mobile.util.BigQueryService;
import com.appnomic.appsone.api.mobile.util.BigQueryUtil;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.QueryJobConfiguration;
import com.google.cloud.bigquery.TableResult;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class GetANROverviewBL implements BusinessLogic<Object, UtilityBean<Object>, ANROverview> {

	@Override
	public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
		List<String> errorMessages = new ArrayList<>();
		if (!BigQueryUtil.appOSStringValidation(requestObject, errorMessages)
			|| !BigQueryUtil.appDisplayVersionValidation(requestObject, errorMessages)
			|| !BigQueryUtil.fromTimeToTimeValidation(requestObject, errorMessages)) {
				throw new ClientException(String.join(";", errorMessages));
		}
		String appOSString = requestObject.getQueryParams().get("app-os")[0];
		String appDisplayVersions = requestObject.getQueryParams().get("app-build-version")[0];
		String fromTime = requestObject.getQueryParams().get("from-time")[0];
		String toTime = requestObject.getQueryParams().get("to-time")[0];
		
		return UtilityBean.<Object>builder()
				.appOSString(appOSString)
				.appDisplayVersions(appDisplayVersions)
				.fromTimeString(fromTime)
				.toTimeString(toTime)
				.build();
	}

	@Override
	public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
		return utilityBean;
	}

	@Override
	public ANROverview processData(UtilityBean<Object> configData) throws DataProcessingException {
		String appOSString = configData.getAppOSString();
		BigQuery bigQuery = BigQueryService.getInstance(appOSString.split("\\.")[0]);
		log.debug("bigQuery Obj: {}", bigQuery);

		String appDisplayVersionInParam = BigQueryUtil.getInParameter(configData.getAppDisplayVersions());
		String fromTime = configData.getFromTimeString();
		String toTime = configData.getToTimeString();
		
		String query = 	"SELECT  " + 
						"    COUNT(*) AS events, " + 
						"    AVG(trace_info.duration_us/1000000) AS duration " + 
						"FROM " + Constants.getPerfMonTable(appOSString) + " AS perfmon " + 
						"WHERE " + 
						"    trace_info.screen_info.frozen_frame_ratio > 0.0 " + 
						"    AND event_type = 'SCREEN_TRACE' " + 
						"    AND event_timestamp > TIMESTAMP_MILLIS("+ fromTime +") " + 
						"    AND event_timestamp < TIMESTAMP_MILLIS(" + toTime + ") " +
						"    AND app_display_version IN " + appDisplayVersionInParam;
		
		QueryJobConfiguration queryConfig = QueryJobConfiguration.newBuilder(query)
														.setUseLegacySql(false).build();
		TableResult result;
		ANROverview response = new ANROverview();

		try {
			result = bigQuery.query(queryConfig);
		} catch(InterruptedException inExc) {
			throw new DataProcessingException("Error in fetching data from BigQuery");
		}
		
		result.iterateAll().forEach(value -> {
			if(null != value.get("events").getValue()) {
				response.setAffectedScreens(value.get("events").getLongValue());
			}
			if(null != value.get("duration").getValue()) {
				response.setAverageDuration(value.get("duration").getNumericValue().setScale(3, RoundingMode.HALF_UP));
			}
			
		});
		return response;
	}

}
