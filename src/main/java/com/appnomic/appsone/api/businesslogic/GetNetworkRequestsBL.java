package com.appnomic.appsone.api.businesslogic;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.mobile.model.NetworkRequest;
import com.appnomic.appsone.api.mobile.util.BigQueryService;
import com.appnomic.appsone.api.mobile.util.BigQueryUtil;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.FieldValueList;
import com.google.cloud.bigquery.QueryJobConfiguration;
import com.google.cloud.bigquery.TableResult;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class GetNetworkRequestsBL implements BusinessLogic<Object, UtilityBean<Object>, List<NetworkRequest>> {

	@Override
	public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
		List<String> errorMessages = new ArrayList<>();
		if (!BigQueryUtil.appOSStringValidation(requestObject, errorMessages)
			|| !BigQueryUtil.fromTimeToTimeValidation(requestObject, errorMessages)) {
				throw new ClientException(String.join(";", errorMessages));
		}
		String appOSString = requestObject.getQueryParams().get("app-os")[0];
		String fromTime = requestObject.getQueryParams().get("from-time")[0];
		String toTime = requestObject.getQueryParams().get("to-time")[0];
		Integer rowLimit = (null != requestObject.getQueryParams().get("row-limit") 
							&& !requestObject.getQueryParams().get("row-limit")[0].equalsIgnoreCase("")
							&& 0 < Integer.parseInt(requestObject.getQueryParams().get("row-limit")[0])) 
							? Integer.parseInt(requestObject.getQueryParams().get("row-limit")[0]) : 100000;

		return UtilityBean.<Object>builder()
				.appOSString(appOSString)
				.fromTimeString(fromTime)
				.toTimeString(toTime)
				.rowLimit(rowLimit)
				.build();
	}

	@Override
	public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
		return utilityBean;
	}

	@Override
	public List<NetworkRequest> processData(UtilityBean<Object> configData) throws DataProcessingException {
		String appOSString = configData.getAppOSString();
		BigQuery bigQuery = BigQueryService.getInstance(appOSString.split("\\.")[0]);
		log.debug("bigQuery Obj: {}", bigQuery);

		String fromTime = configData.getFromTimeString();
		String toTime = configData.getToTimeString();
		Integer rowLimit = configData.getRowLimit();
		
		String query = 	"SELECT " + 
				"    event_name AS requestName, " + 
				"    AVG(network_info.response_completed_time_us/1000000) AS responseTime, " + 
				"    COUNTIF(network_info.response_code >= 200 AND network_info.response_code < 300) AS successfulResponses, " + 
				"    COUNTIF(network_info IS NOT null) AS totalNetworkTraces, " + 
				"    network_info.request_http_method AS httpMethod, "  + 
 				"    network_info.response_mime_type AS responseMimeType " + 
				"FROM " + Constants.getPerfMonTable(appOSString) + 
				"WHERE  " + 
				"    event_type = 'NETWORK_REQUEST' " + 
				"    AND network_info.response_mime_type <> '[MISSING]' " + 
				"    AND event_timestamp > TIMESTAMP_MILLIS("+fromTime+") " + 
				"    AND event_timestamp < TIMESTAMP_MILLIS("+toTime+") " + 
				"GROUP BY " + 
				"    requestName, " + 
				"    httpMethod, " + 
				"    responseMimeType " +
				"ORDER BY " + 
				"  	 totalNetworkTraces DESC " +
				"LIMIT " + rowLimit;
		
		QueryJobConfiguration queryConfig = QueryJobConfiguration.newBuilder(query).setUseLegacySql(false).build();
		
		TableResult result;
		try {
			result = bigQuery.query(queryConfig);
		} catch(InterruptedException inExc) {
			throw new DataProcessingException("Error in fetching data from BigQuery");
		}
		
		List<NetworkRequest> response = new ArrayList<>();
		for(FieldValueList value : result.iterateAll()) {
			NetworkRequest networkRequest = new NetworkRequest();
			String requestName = value.get("requestName").getStringValue();
			networkRequest.setRequestName(requestName.substring(0, requestName.length()-3));
			networkRequest.setResponseTime(value.get("responseTime").getNumericValue().setScale(3, RoundingMode.HALF_UP));
			BigDecimal successRate = value.get("successfulResponses").getNumericValue().divide(value.get("totalNetworkTraces").getNumericValue(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
			networkRequest.setSuccessRate(successRate);
			BigDecimal failureRate = value.get("totalNetworkTraces").getNumericValue().subtract(value.get("successfulResponses").getNumericValue()).divide(value.get("totalNetworkTraces").getNumericValue(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
			networkRequest.setFailureRate(failureRate);
			networkRequest.setHttpMethod(value.get("httpMethod").getStringValue());
			networkRequest.setResponseMimeType(value.get("responseMimeType").getStringValue());
			response.add(networkRequest);
		}
		return response;
	}

}
