package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.beans.WatcherKpiRawBean;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.opensearch.ConfigWatcherRepo;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.InstanceRepo;
import com.appnomic.appsone.api.dao.redis.KpiRepo;
import com.appnomic.appsone.api.dao.redis.MasterRepo;
import com.appnomic.appsone.api.pojo.ConfigData;
import com.appnomic.appsone.api.pojo.ConfigWatchCompareRequest;
import com.appnomic.appsone.api.pojo.InstanceWiseKpis;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.TimeRequest;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.api.util.StringUtils;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.CompInstClusterDetails;
import com.heal.configuration.pojos.CompInstKpiEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

public class ConfigWatchCompareBL implements BusinessLogic<ConfigWatchCompareRequest, UtilityBean<ConfigWatchCompareRequest>, List<InstanceWiseKpis>> {
    private static final Logger logger = LoggerFactory.getLogger(ConfigWatchCompareBL.class);
    private static final List<String> systemPropertiesKpis = Arrays.asList(ConfProperties.getString(Constants.SYS_PROPERTIES_KPI_LIST,
            Constants.SYS_PROPERTIES_KPI_LIST_DEFAULT).split(","));

    MasterRepo masterViewTypesRepo = new MasterRepo();
    private final List<com.heal.configuration.pojos.ViewTypes> typeDetailsList = masterViewTypesRepo.getTypes();

    KpiRepo kpiRepo = new KpiRepo();
    InstanceRepo instanceRepo = new InstanceRepo();

    private static final String FILE_CONTENT = "File Content";

    private static final long TWENTY_FOUR_HRS_MILLS = 86400000;

    private static final int MAX_CONFIG_COMPARE_DATES_ALLOWED = 4;
    private static final int MAX_CONFIG_COMPARE_INSTANCES_ALLOWED = 4;

    public ConfigWatchCompareBL() {
    }

    private final ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();

    @Override
    public UtilityBean<ConfigWatchCompareRequest> clientValidation(RequestObject requestObject) throws ClientException {
        logger.debug("{} clientValidation().", Constants.INVOKED_METHOD);

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION_HEADER);
        String identifier = requestObject.getParams().get(Constants.REQUEST_PARAM_IDENTIFIER);
        String instanceIdsStr = requestObject.getQueryParams().get(Constants.REQUEST_PARAM_INSTANCE_IDS_LIST)[0];
        String dateListStr = requestObject.getQueryParams().get(Constants.REQUEST_PARAM_DATE_LIST)[0];

        List<Integer> instanceIds = new ArrayList<>();
        List<Long> dates;

        if (StringUtils.isEmpty(identifier)) {
            logger.error(Constants.MESSAGE_EMPTY_ACCOUNT);
            throw new ClientException(Constants.MESSAGE_EMPTY_ACCOUNT);
        }

        try {
            instanceIds = objectMapper.readValue(instanceIdsStr, new TypeReference<List<Integer>>() {
            });
        } catch (IOException e) {
            throw new ClientException(e, MessageFormat.format("Error occurred while converting instance id: {0}", instanceIds));
        }

        try {
            dates = objectMapper.readValue(dateListStr, new TypeReference<List<Long>>() {
            });
        } catch (IOException e) {
            throw new ClientException(e, MessageFormat.format("Error occurred while parsing date {0}", dateListStr));
        }

        if (instanceIds.isEmpty() || instanceIds.size() > MAX_CONFIG_COMPARE_INSTANCES_ALLOWED) {
            throw new ClientException(MessageFormat.format(UIMessages.ERROR_INVALID_INPUT_PARAM, "instanceIds", instanceIds.size()));
        }

        if (dates.isEmpty() || dates.size() > MAX_CONFIG_COMPARE_DATES_ALLOWED) {
            throw new ClientException(MessageFormat.format(UIMessages.ERROR_INVALID_INPUT_PARAM, "dates", dates.size()));
        }

        List<TimeRequest> timeRequestList = new ArrayList<>();

        for (Long d : dates) {
            timeRequestList.add(TimeRequest.builder()
                    .fromTime(d)
                    .toTime((d + TWENTY_FOUR_HRS_MILLS))
                    .build());
        }

        return UtilityBean.<ConfigWatchCompareRequest>builder()
                .authToken(authKey)
                .accountIdString(identifier)
                .requestPayloadObject(ConfigWatchCompareRequest.builder()
                        .instanceIds(instanceIds)
                        .dates(timeRequestList)
                        .build())
                .build();
    }

    @Override
    public UtilityBean<ConfigWatchCompareRequest> serverValidation(UtilityBean<ConfigWatchCompareRequest> utilityBean) throws ServerException {
        logger.debug("{} serverValidation().", Constants.INVOKED_METHOD);
        AccountRepo accountRepo = new AccountRepo();
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            throw new ServerException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }
        utilityBean.setUserId(userId);

        com.heal.configuration.pojos.Account account = accountRepo.getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
        }
        utilityBean.setAccount(account);
        return utilityBean;
    }

    @Override
    public List<InstanceWiseKpis> processData(UtilityBean<ConfigWatchCompareRequest> utilityBean) throws DataProcessingException {
        logger.debug("{} processData().", Constants.INVOKED_METHOD);
        List<Integer> instanceIds = utilityBean.getRequestPayloadObject().getInstanceIds();

        List<InstanceWiseKpis> instanceKpisList = new ArrayList<>();
        List<CompInstClusterDetails> componentInstanceList = instanceIds.stream()
                .map(instanceId -> instanceRepo.getInstanceDetailsWithId(utilityBean.getAccountIdString(), instanceId))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        logger.debug("componentInstanceList size:{}", componentInstanceList.size());

        for (CompInstClusterDetails instance : componentInstanceList) {
            //get all kpi which are fileWatch or configWatch

            List<CompInstKpiEntity> instKpiEntities = kpiRepo.getKpiDetailByAccInst(utilityBean.getAccountIdString(), instance.getIdentifier())
                    .stream()
                    .filter(instKpi -> Constants.MST_TYPE_CONFIG_WATCH.equalsIgnoreCase(instKpi.getType()) || Constants.MST_TYPE_FILE_WATCH.equalsIgnoreCase(instKpi.getType()))
                    .collect(Collectors.toList());

            logger.debug("getAllKpiDetails for instance: {}, allKpisSize {}.", instance, instKpiEntities.size());
            Map<String, List<ConfigData>> configChanges = new HashMap<>();

            for (CompInstKpiEntity kpi : instKpiEntities) {
                populateData(kpi, utilityBean, instance, configChanges);
            }

            instanceKpisList.add(
                    InstanceWiseKpis.builder()
                            .id(instance.getId())
                            .name(instance.getName())
                            .configChanges(configChanges)
                            .build());
        }
        return instanceKpisList;
    }

    private void populateData(CompInstKpiEntity kpi, UtilityBean<ConfigWatchCompareRequest> utilityBean, CompInstClusterDetails instance, Map<String, List<ConfigData>> configChanges) {
        logger.debug("{} populateData().", Constants.INVOKED_METHOD);
        List<TimeRequest> dates = utilityBean.getRequestPayloadObject().getDates();

        logger.debug("getMasterKPIDetailsBean() -> CompInstKpi :{}, dates: {}", kpi, dates);
        ConfigWatcherRepo configWatcherRepo = new ConfigWatcherRepo();

        for (TimeRequest d : dates) {
            logger.debug("getWatcherKpisRaw() -> instanceId: {}, kpiId: {}, kpiAttributeName: {}, fromTime: {}, toTime: {}",
                    instance.getIdentifier(), kpi.getId(), kpi.getAttributeValues(), d.getFromTime(), d.getToTime());
            if (systemPropertiesKpis.contains(kpi.getIdentifier())) {
                return;
            }
            List<String> attributeValue = new ArrayList<>();
            if (kpi.getAttributeValues() != null) {
                attributeValue.addAll(kpi.getAttributeValues().keySet());
            }
            for (String val : attributeValue) {
                WatcherKpiRawBean rawBean = configWatcherRepo.getWatcherKpisRaw(utilityBean.getAccount().getIdentifier(),
                        instance.getIdentifier(), kpi.getId(), val, d.getFromTime(), d.getToTime());
                if (rawBean == null) {
                    continue;
                }
                if (!configChanges.containsKey(kpi.getIdentifier())) {
                    configChanges.put(kpi.getIdentifier(), new ArrayList<>());
                }
                populateDataConfig(rawBean, d.getFromTime(), configChanges.get(kpi.getIdentifier()), kpi.getGroupName());
            }
        }
    }

    private void populateDataConfig(WatcherKpiRawBean rawBean, Long fromTime, List<ConfigData> configDataList, String kpiName) {
        logger.debug("{} populateDataConfig(), rawBean: {}.", Constants.INVOKED_METHOD, rawBean);
        Map<Long, Map<String, String>> configMap = new HashMap<>();
        ConfigData configData = new ConfigData();
        Map<String, String> values = new HashMap<>(rawBean.getKpiValue());
        Optional<ConfigData> u = configDataList.stream().filter(x -> x.getName().equals(rawBean.getKpiAttributeName())).findFirst();
        if (u.isPresent()) {
            if (!u.get().getValues().containsKey(fromTime)) {
                u.get().setKeys(rawBean.getKpiValue().keySet());
                u.get().getValues().put(fromTime, values);
            }
        } else {
            configData.setName(rawBean.getKpiAttributeName());
            configData.setKpiName(kpiName);
            configData.setLastUpdatedTime(rawBean.getLastUpdatedTime());
            configData.setKeys(rawBean.getKpiValue().keySet());
            configMap.put(fromTime, values);
            configData.setValues(configMap);
            configDataList.add(configData);
        }
    }

}
