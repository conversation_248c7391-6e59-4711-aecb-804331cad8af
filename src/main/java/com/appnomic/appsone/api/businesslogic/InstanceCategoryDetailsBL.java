package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.beans.CategoryDetailBean;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.LoggerTags;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.opensearch.AnomalySearchRepo;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.CategoryRepo;
import com.appnomic.appsone.api.dao.redis.KpiRepo;
import com.appnomic.appsone.api.dao.redis.ServiceRepo;
import com.appnomic.appsone.api.pojo.BehaviourType;
import com.appnomic.appsone.api.pojo.CategoryEvents;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.heatmap.AnomalyDetailedCategory;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.appnomic.appsone.opeasearchquery.results.TabularResults;
import com.heal.configuration.entities.BasicInstanceBean;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.Category;
import com.heal.configuration.pojos.Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class InstanceCategoryDetailsBL implements BusinessLogic<Map<String, Integer>, UtilityBean<Map<String, Integer>>, List<CategoryEvents>> {
    private static final Integer ONE = 1;

    /**
     * This method validates the request parameters.
     */
    @Override
    public UtilityBean<Map<String, Integer>> clientValidation(RequestObject requestObject) throws ClientException {
        log.trace("Inside Client validation");
        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);

        if (StringUtils.isEmpty(identifier)) {
            log.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION_HEADER);
        if (StringUtils.isEmpty(authKey)) {
            log.error(Constants.MESSAGE_INVALID_AUTH_TOKEN);
            throw new ClientException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }

        String serviceId = requestObject.getParams().get(Constants.REQUEST_PARAM_SERVICE_ID);
        if (StringUtils.isEmpty(serviceId.trim())) {
            log.error(UIMessages.INVALID_SERVICE);
            throw new ClientException(UIMessages.INVALID_SERVICE);
        }
        String clusterId = requestObject.getParams().get(Constants.REQUEST_PARAM_CLUSTER_ID);
        if (StringUtils.isEmpty(clusterId.trim())) {
            log.error(UIMessages.INVALID_CLUSTER_ID);
            throw new ClientException(UIMessages.INVALID_CLUSTER_ID);
        }
        String instanceId = requestObject.getParams().get(Constants.REQUEST_PARAM_COM_INSTANCE_ID);
        if (StringUtils.isEmpty(instanceId.trim())) {
            log.error(UIMessages.ERROR_INVALID_COMP_INSTANCE_ID);
            throw new ClientException(UIMessages.ERROR_INVALID_COMP_INSTANCE_ID);
        }
        String fromTimeString = requestObject.getQueryParams().get(Constants.REQUEST_PARAM_FROM_TIME)[0];
        String toTimeString = requestObject.getQueryParams().get(Constants.REQUEST_PARAM_TO_TIME)[0];
        validation(serviceId, clusterId, instanceId, fromTimeString, toTimeString);
        Map<String, Integer> map = new HashMap<>();
        map.put(Constants.REQUEST_PARAM_CLUSTER_ID, Integer.parseInt(clusterId));
        map.put(Constants.REQUEST_PARAM_COM_INSTANCE_ID, Integer.parseInt(instanceId));

        return UtilityBean.<Map<String, Integer>>builder()
                .accountIdString(identifier)
                .serviceId(Integer.parseInt(serviceId))
                .fromTime(Long.parseLong(fromTimeString))
                .toTime(Long.parseLong(toTimeString))
                .authToken(authKey)
                .requestPayloadObject(map)
                .build();
    }

    private void validation(String serviceId, String clusterId, String instanceId, String fromTimeString, String toTimeString) throws ClientException {
        try {
            if (Integer.parseInt(serviceId) < ONE) {
                log.error(UIMessages.INVALID_SERVICE);
                throw new ClientException(UIMessages.INVALID_SERVICE);
            }
            if (Integer.parseInt(clusterId) < ONE) {
                log.error(UIMessages.INVALID_CLUSTER_ID);
                throw new ClientException(UIMessages.INVALID_CLUSTER_ID);
            }
            if (Integer.parseInt(instanceId) < ONE) {
                log.error(UIMessages.ERROR_INVALID_COMP_INSTANCE_ID);
                throw new ClientException(UIMessages.ERROR_INVALID_COMP_INSTANCE_ID);
            }
            validationOfFromAndToTime(fromTimeString, toTimeString);
        } catch (NumberFormatException e) {
            log.error("Error occurred while converting ", e);
            throw new ClientException("Parsing of value not possible.");
        }
    }

    private void validationOfFromAndToTime(String fromTimeString, String toTimeString) throws ClientException {
        Long fromTime;
        Long toTime;
        try {
            fromTime = (fromTimeString == null) ? null : Long.parseLong(fromTimeString);
        } catch (NumberFormatException e) {
            log.error("Error occurred while converting the fromTime [{}]. Reason: {}", fromTimeString, e.getMessage());
            throw new ClientException(Constants.INVALID_FROM_TIME);
        }

        try {
            toTime = (toTimeString == null) ? null : Long.parseLong(toTimeString);
        } catch (NumberFormatException e) {
            log.error("Error occurred while converting the toTime [{}]. Reason: {}", toTimeString, e.getMessage());
            throw new ClientException(Constants.INVALID_TO_TIME);
        }

        if ((fromTime == null) || (toTime == null) || (fromTime <= 0) || (toTime <= 0) || (fromTime > toTime)) {
            log.error(LoggerTags.TAG_INVALID_FROM_TO_TIME);
            throw new ClientException(LoggerTags.TAG_INVALID_FROM_TO_TIME);
        }
    }

    /**
     * This method validates the account, service, cluster and instance.
     */
    @Override
    public UtilityBean<Map<String, Integer>> serverValidation(UtilityBean<Map<String, Integer>> utilityBean) throws ServerException {
        AccountRepo accountRepo = new AccountRepo();
        ServiceRepo serviceRepo = new ServiceRepo();
        Account account = accountRepo.getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            log.error("Invalid account identifier: {}.", utilityBean.getAccountIdString());
            throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
        }
        BasicEntity basicEntity = serviceRepo.getAllServices(account.getIdentifier()).parallelStream().filter(s -> s.getId() == utilityBean.getServiceId()).findAny().orElse(null);
        if (basicEntity == null) {
            throw new ServerException(UIMessages.ERROR_INVALID_SERVICE_ID + ": " + utilityBean.getServiceId());
        }

        Service service = HealUICache.INSTANCE.getServiceDetails(account.getIdentifier(), basicEntity.getIdentifier());
        if (service == null) {
            throw new ServerException(UIMessages.ERROR_INVALID_SERVICE_ID + ": " + utilityBean.getServiceId() + ", identifier:" + basicEntity.getIdentifier());
        }

        List<BasicInstanceBean> instances = HealUICache.INSTANCE.getServiceInstanceList(account.getIdentifier(), service.getIdentifier(), true);
        BasicInstanceBean clusterInstance = instances.parallelStream()
                .filter(inst -> inst.getId() == utilityBean.getRequestPayloadObject().get(Constants.REQUEST_PARAM_CLUSTER_ID))
                .findAny().orElse(null);
        if (clusterInstance == null) {
            throw new ServerException(UIMessages.INVALID_CLUSTER_ID + " :" + utilityBean.getRequestPayloadObject().get(Constants.REQUEST_PARAM_CLUSTER_ID));
        }

        BasicInstanceBean instance = instances.parallelStream()
                .filter(i -> i.getId() == utilityBean.getRequestPayloadObject().get(Constants.REQUEST_PARAM_COM_INSTANCE_ID))
                .findAny()
                .orElse(null);
        if (instance == null) {
            throw new ServerException(UIMessages.ERROR_INVALID_COMP_INSTANCE_ID + " :" + utilityBean.getRequestPayloadObject().get(Constants.REQUEST_PARAM_COM_INSTANCE_ID));
        }
        utilityBean.setInstance(instance);
        utilityBean.setClusterInstance(clusterInstance);
        utilityBean.setAccount(account);
        utilityBean.setUserId(ValidationUtils.getUserId(utilityBean.getAuthToken()));
        utilityBean.setService(service);
        return utilityBean;
    }

    /**
     * This method fetches the anomalies for the given instance and then fetches the category details for each anomaly.
     */
    @Override
    public List<CategoryEvents> processData(UtilityBean<Map<String, Integer>> configData) throws DataProcessingException {
        BasicInstanceBean instanceBean = configData.getInstance();
        String accountIdentifier = configData.getAccount().getIdentifier();

        List<AnomalyDetailedCategory> anomaliesForService = getAnomaliesForService(accountIdentifier, configData.getFromTime(), configData.getToTime(), instanceBean);

        CategoryRepo categoryRepo = new CategoryRepo();
        Map<Boolean, List<CategoryEvents>> partitionedCategories = anomaliesForService
                .parallelStream()
                .map(anomaly -> {
                    String categoryName = "";
                    int isInformative = 1;
                    if (anomaly.getCategoryId() > 0) {
                        Category categoryDetailBean = categoryRepo.getCategoryDetails(accountIdentifier, anomaly.getCategoryIdentifier());
                        if (categoryDetailBean != null) {
                            categoryName = categoryDetailBean.getName();
                            isInformative = categoryDetailBean.getInformative();
                        }
                    } else if (anomaly.getCategoryId() == Constants.CONFIG_WATCH_LOGICAL_CATEGORY_ID) {
                        categoryName = Constants.CONFIGWATCH_IDENTIFIER;
                    } else if (anomaly.getCategoryId() == Constants.AVAILABILITY_LOGICAL_CATEGORY_ID) {
                        categoryName = Constants.AVAILABILITY_KPI_IDENTIFIER;
                        //availability KPI can't be marked as informative, hence setting the flag as 0.
                        isInformative = 0;
                    }
                    boolean forensicEnabled = CommonUtils.forensicExistsForCategory(accountIdentifier, anomaly.getCategoryIdentifier());
                    
                    // Check if this category is Forensic type for deep dive
                    KpiRepo kpiRepo = new KpiRepo();
                    boolean isDeepDive = kpiRepo.getKpiDetailByAccInst(accountIdentifier, instanceBean.getIdentifier())
                            .stream()
                            .anyMatch(k -> k.getCategoryDetails().getIdentifier().equals(anomaly.getCategoryIdentifier()) &&
                                    k.getType().equalsIgnoreCase(Constants.MST_SUB_TYPE_FORENSIC));

                    return CategoryEvents.builder()
                            .categoryId(anomaly.getCategoryId())
                            .eventCount(anomaly.getAnomalyCount())
                            .categoryName(categoryName)
                            .forensicEnabled(forensicEnabled)
                            .isInfo(isInformative == 1)
                            .isDeepDive(isDeepDive)
                            .build();
                })
                .collect(Collectors.partitioningBy(CategoryEvents::isDeepDive));

        List<CategoryEvents> result = new ArrayList<>(partitionedCategories.get(true));
        result.addAll(partitionedCategories.get(false));
        return result;
    }

    private List<AnomalyDetailedCategory> getAnomaliesForService(String accountIdentifier, long from, long to, BasicInstanceBean instanceBean) {
        log.trace("{} getAnomaliesForService().", Constants.INVOKED_METHOD);

        Map<String, Long> categoryAnomalyCountMap = new HashMap<>();

        TabularResults tabularResults = new AnomalySearchRepo().getAnomaliesByInstanceCategory(accountIdentifier, instanceBean.getIdentifier(), from, to);

        if (tabularResults != null && tabularResults.getRowResults() != null && !tabularResults.getRowResults().isEmpty()) {
            for (TabularResults.ResultRow resultRow : tabularResults.getRowResults()) {
                String categoryIdentifier = "";
                for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {
                    if (resultRowColumn.getColumnName().equalsIgnoreCase("categoryId")) {
                        categoryIdentifier = resultRowColumn.getColumnValue();
                    }

                    categoryAnomalyCountMap.put(categoryIdentifier, categoryAnomalyCountMap.getOrDefault(categoryIdentifier, 0L) + resultRow.getCountValue());
                }
            }
        }

        AnomalyDetailedCategory configWatch = new AnomalyDetailedCategory();
        AnomalyDetailedCategory availability = new AnomalyDetailedCategory();

        return new KpiRepo().getKpiDetailByAccInst(accountIdentifier, instanceBean.getIdentifier())
                .parallelStream()
                .map(k -> CategoryDetailBean.builder()
                        .categoryId(k.getCategoryDetails().getId())
                        .identifier(k.getCategoryDetails().getIdentifier())
                        .name(k.getCategoryDetails().getName())
                        .kpiType(k.getType())
                        .build())
                .distinct()
                .map(c -> {
                    if (c.getKpiType().equalsIgnoreCase("ConfigWatch") || c.getKpiType().equalsIgnoreCase("FileWatch")) {
                        configWatch.setInstanceId(instanceBean.getId());
                        configWatch.setCategoryId(-3);
                        configWatch.setCategoryIdentifier(null);
                        configWatch.setAnomalyCount(configWatch.getAnomalyCount() + categoryAnomalyCountMap.getOrDefault(c.getIdentifier(), 0L));
                        configWatch.setBehaviourType(instanceBean.getComponentTypeId() == 1 ? BehaviourType.HB.toString() : BehaviourType.CB.toString());
                        return configWatch;
                    } else if (c.getKpiType().equalsIgnoreCase("Availability")) {
                        availability.setInstanceId(instanceBean.getId());
                        availability.setCategoryId(-1);
                        availability.setCategoryIdentifier(null);
                        availability.setAnomalyCount(availability.getAnomalyCount() + categoryAnomalyCountMap.getOrDefault(c.getIdentifier(), 0L));
                        availability.setBehaviourType(instanceBean.getComponentTypeId() == 1 ? BehaviourType.HB.toString() : BehaviourType.CB.toString());
                        return availability;
                    } else if (c.getKpiType().equalsIgnoreCase(Constants.MST_SUB_TYPE_FORENSIC)) {
                        AnomalyDetailedCategory forensic = new AnomalyDetailedCategory();
                        forensic.setInstanceId(instanceBean.getId());
                        forensic.setCategoryId(c.getCategoryId());
                        forensic.setCategoryIdentifier(c.getIdentifier());
                        forensic.setAnomalyCount(categoryAnomalyCountMap.getOrDefault(c.getIdentifier(), 0L));
                        forensic.setBehaviourType(instanceBean.getComponentTypeId() == 1 ? BehaviourType.HB.toString() : BehaviourType.CB.toString());
                        return forensic;
                    } else {
                        AnomalyDetailedCategory temp = new AnomalyDetailedCategory();
                        temp.setInstanceId(instanceBean.getId());
                        temp.setCategoryId(c.getCategoryId());
                        temp.setCategoryIdentifier(c.getIdentifier());
                        temp.setAnomalyCount(categoryAnomalyCountMap.getOrDefault(c.getIdentifier(), 0L));
                        temp.setBehaviourType(instanceBean.getComponentTypeId() == 1 ? BehaviourType.HB.toString() : BehaviourType.CB.toString());
                        return temp;
                    }
                }).distinct().collect(Collectors.toList());
    }
}
