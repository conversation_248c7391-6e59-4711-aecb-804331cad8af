package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;

public interface BusinessLogic<T,V,R> {
    UtilityBean<T> clientValidation(RequestObject requestObject) throws ClientException;

    V serverValidation(UtilityBean<T> utilityBean) throws ServerException;

    R processData(V configData) throws DataProcessingException;
}
