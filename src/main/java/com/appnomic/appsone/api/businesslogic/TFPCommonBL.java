package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.dao.redis.ServiceRepo;
import com.appnomic.appsone.api.pojo.UserAccessDetails;
import com.appnomic.appsone.api.pojo.tfp.TFPServiceDetails;
import com.heal.configuration.entities.BasicInstanceBean;
import com.heal.configuration.pojos.*;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class TFPCommonBL {

    public List<TFPServiceDetails> getServiceTransactionStats(Account account, BasicEntity application,
                                                              UserAccessDetails userAccessDetails, List<BasicEntity> entryServices,
                                                              List<TFPServiceDetails> tempTfpServiceDetailsList) {
        log.trace("{} getServiceTransactionStatsFromCollatedIndices().", Constants.INVOKED_METHOD);

        if (tempTfpServiceDetailsList == null || tempTfpServiceDetailsList.isEmpty()) {
            return new ArrayList<>();
        }

        ServiceRepo serviceRepo = new ServiceRepo();

        long start = System.currentTimeMillis();
        Map<String, BasicEntity> entryServiceMap = entryServices.parallelStream().collect(Collectors.toMap(BasicEntity::getIdentifier, c -> c));

        List<TFPServiceDetails> tfpServiceDetailsList = populateApplicableServices(entryServices, tempTfpServiceDetailsList);

        List<TFPServiceDetails> result = tfpServiceDetailsList.parallelStream().peek(c -> {
            try {
                String serviceIdentifier = c.getServiceName();
                c.setApplicationName(application.getName());
                c.setServiceName(entryServiceMap.get(serviceIdentifier).getName());
                c.setTransactionCount((int) HealUICache.INSTANCE.getServiceTransactionList(account.getIdentifier(), serviceIdentifier)
                        .parallelStream().filter(txn -> txn.getStatus() == 1).filter(txn -> txn.getMonitorEnabled() == 1).count());

                List<BasicAgentEntity> serviceAgents = serviceRepo.getAgents(account.getIdentifier(), serviceIdentifier);
                c.setIsJIMEnabled(serviceAgents.parallelStream().anyMatch(e -> e.getType().equalsIgnoreCase("JIMAgent")) ? 1 : 0);

                BasicInstanceBean cluster = HealUICache.INSTANCE.getServiceInstanceList(account.getIdentifier(), serviceIdentifier, false)
                        .parallelStream()
                        .filter(i -> i.getStatus() == 1)
                        .filter(i -> i.getComponentTypeId() != 1)
                        .findAny()
                        .orElse(null);
                c.setClusterId(cluster != null ? cluster.getId() : 0);

                List<String> apps = userAccessDetails.getApplicationIdentifiers();
                if (apps.contains(application.getIdentifier()))
                    c.setUserAccess(true);

                log.trace("Service details: {}", c);
            } catch (Exception e) {
                log.error("Exception while processing data for service {}. ", c, e);
            }
        }).collect(Collectors.toList());

        log.debug("Time taken to get service transaction stats is {} ms.", (System.currentTimeMillis() - start));

        return result;
    }

    private List<TFPServiceDetails> populateApplicableServices(List<BasicEntity> serviceList, List<TFPServiceDetails> tempTfpServiceDetailsList) {

        List<TFPServiceDetails> tfpServiceDetailsList = new ArrayList<>();
        Map<Integer, TFPServiceDetails> svcTfpSvcDetailsMap = tempTfpServiceDetailsList.stream().collect(Collectors.toMap(TFPServiceDetails::getServiceId, c -> c));

        serviceList.forEach(svc -> {
            if (svcTfpSvcDetailsMap.containsKey(svc.getId())) {
                tfpServiceDetailsList.add(svcTfpSvcDetailsMap.get(svc.getId()));
            }
        });

        return tfpServiceDetailsList;
    }
}
