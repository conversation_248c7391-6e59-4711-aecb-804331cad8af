package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.ServiceRepo;
import com.appnomic.appsone.api.dao.redis.TransactionRepo;
import com.appnomic.appsone.api.pojo.JimTraceRequest;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.api.util.StringUtils;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.google.gson.JsonElement;
import com.google.gson.JsonParser;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.Transaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.client.methods.RequestBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

@Slf4j
public class JimTraceBL implements BusinessLogic<JimTraceRequest, JimTraceRequest, JsonElement> {

    @Override
    public UtilityBean<JimTraceRequest> clientValidation(RequestObject requestObject) throws ClientException {
        log.debug("{} clientValidation().", Constants.INVOKED_METHOD);
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }
        String authToken = requestObject.getHeaders().get(Constants.AUTHORIZATION);

        if (authToken == null || authToken.trim().isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String identifier = requestObject.getParams().get(Constants.REQUEST_PARAM_IDENTIFIER);

        if (StringUtils.isEmpty(identifier)) {
            log.error(Constants.MESSAGE_EMPTY_ACCOUNT);
            throw new ClientException(Constants.MESSAGE_EMPTY_ACCOUNT);
        }

        String serviceIdString = requestObject.getQueryParams().get(Constants.SERVICE_ID)[0];
        if (serviceIdString == null || serviceIdString.trim().isEmpty()) {
            log.error("Service is null/empty");
            throw new ClientException(UIMessages.CLIENT_VALIDATION_FAILED);
        }
        int serviceId;
        try {
            serviceId = Integer.parseInt(serviceIdString);
        } catch (Exception e) {
            log.error("Exception while parsing the service Id.");
            throw new ClientException("Exception while parsing service Id.");
        }

        if (serviceId <= 0) {
            log.error("Invalid request. Service ID is not a valid integer");
            throw new ClientException("Service ID is invalid");
        }

        String transactionIdStr = requestObject.getQueryParams().get(Constants.TRANSACTION_ID)[0];
        if (transactionIdStr == null) {
            log.error("Transaction is null/empty");
            throw new ClientException(UIMessages.CLIENT_VALIDATION_FAILED);
        }

        String startTimeString = requestObject.getQueryParams().get("start")[0];
        if (StringUtils.isEmpty(startTimeString)) {
            log.error("startTime can't be null/empty.");
            throw new ClientException("startTime can't be null/empty.");
        }

        String endTimeString = requestObject.getQueryParams().get("end")[0];
        if (StringUtils.isEmpty(endTimeString)) {
            log.error("endTime can't be null/empty.");
            throw new ClientException("ToTime can't be null/empty.");
        }

        long startTime;
        long endTime;
        try {
            startTime = Long.parseLong(startTimeString);
            endTime = Long.parseLong(endTimeString);
        } catch (Exception e) {
            log.error("Exception while parsing time.");
            throw new ClientException("Exception while parsing time.");
        }

        if (startTime > endTime) {
            log.error("start Time can't be greater than end Time.");
            throw new ClientException("start Time can't be greater than end Time.");
        }

        int transactionId;
        try {
            transactionId = Integer.parseInt(requestObject.getQueryParams().get(Constants.TRANSACTION_ID)[0]);
        } catch (Exception e) {
            log.error("Exception while parsing the transaction Id.");
            throw new ClientException("Exception while parsing transaction Id.");
        }

        if (transactionId <= 0) {
            log.error("Invalid request. Transaction ID is not a valid integer");
            throw new ClientException("Transaction ID is invalid");
        }

        JimTraceRequest jimTraceRequest = new JimTraceRequest(requestObject);

        return UtilityBean.<JimTraceRequest>builder()
                .requestPayloadObject(jimTraceRequest)
                .authToken(authToken)
                .serviceIdString(serviceIdString)
                .transactionId(transactionId)
                .accountIdString(identifier)
                .fromTime(startTime)
                .toTime(endTime)
                .build();
    }

    @Override
    public JimTraceRequest serverValidation(UtilityBean<JimTraceRequest> utilityBean) throws ServerException {
        JimTraceRequest jimTraceRequest = utilityBean.getRequestPayloadObject();
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token.");
            throw new ServerException("Error while extracting user details from authorization token.");
        }

        AccountRepo accountRepo = new AccountRepo();

        Account account = accountRepo.getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            log.error("Account identifier: {} is invalid.", utilityBean.getAccountIdString());
            throw new ServerException("Account identifier is Invalid.");
        }

        ServiceRepo serviceRepo = new ServiceRepo();

        int serviceId = Integer.parseInt(utilityBean.getServiceIdString());
        BasicEntity serviceDetail = serviceRepo.getAllServices(account.getIdentifier()).parallelStream()
                .filter(service -> service.getId() == serviceId)
                .findFirst().orElse(null);
        if (serviceDetail == null) {
            log.error("serviceId: {} is Invalid.", utilityBean.getServiceId());
            throw new ServerException("Service Id is Invalid.");
        }

        jimTraceRequest.setServiceIdentifier(serviceDetail.getIdentifier());

        TransactionRepo transactionRepo = new TransactionRepo();

        Transaction transaction = transactionRepo.getTransactionDetailsById(account.getIdentifier(), utilityBean.getTransactionId());
        if (transaction == null) {
            log.error("Transaction [{}] is unavailable for account [{}]", utilityBean.getTransactionId(), account.getIdentifier());
            throw new ServerException(("Invalid transaction"));
        }

        if (transaction.getServiceId() != serviceDetail.getId()) {
            log.error("transaction Id: {} is unavailable for service Id: {}", utilityBean.getTransactionId(), utilityBean.getServiceId());
            throw new ServerException("Invalid transaction");
        }

        if (transaction.getAttributes() == null) {
            log.error("Transaction attributes unavailable for transaction [{}]", transaction.getIdentifier());
            throw new ServerException("Invalid transaction");
        }

        String urlPattern = transaction.getAttributes().get("url");
        if (urlPattern == null) {
            log.error("URL pattern is unavailable for transaction [{}]", transaction.getIdentifier());
            throw new ServerException("Invalid transaction");
        }

        jimTraceRequest.setTransactionUrlPattern(urlPattern);
        jimTraceRequest.setStart(utilityBean.getFromTime());
        jimTraceRequest.setEnd(utilityBean.getToTime());

        return jimTraceRequest;
    }

    @Override
    public JsonElement processData(JimTraceRequest configData) throws DataProcessingException {
        try (CloseableHttpClient httpClient = HttpClients.createMinimal()) {
            HttpUriRequest request = RequestBuilder.get()
                    .setUri("http://" + ConfProperties.getString(Constants.JAEGER_IP) + ":" + ConfProperties.getString(Constants.JAEGER_PORT) + "/api/traces")
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Accept", "*/*")
                    .addParameter("service", configData.getServiceIdentifier())
                    .addParameter("minDuration", configData.getMinDuration() != null ? configData.getMinDuration() : "")
                    .addParameter("maxDuration", configData.getMaxDuration() != null ? configData.getMaxDuration() : "")
                    .addParameter("end", String.valueOf(configData.getEnd() * 1000))
                    .addParameter("start", String.valueOf(configData.getStart() * 1000))
                    .addParameter("limit", String.valueOf(configData.getLimit()))
                    .addParameter("lookback", configData.getLookBack())
                    .addParameter("operation", configData.getTransactionUrlPattern())
                    .build();

            log.debug("Jaeger traces request: {}", request);

            HttpResponse execute = httpClient.execute(request);

            InputStream content = execute.getEntity().getContent();

            InputStreamReader inputStreamReader = new InputStreamReader(content);
            BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
            StringBuilder sb = new StringBuilder();

            sb.append("{\"message\":\"SUCCESS\",\"responseStatus\":\"SUCCESS\",");

            String str;
            while ((str = StringUtils.readLine(bufferedReader)) != null) {
                sb.append(str.substring(1));
            }
            return JsonParser.parseString(new String(sb.toString().getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8).replaceAll(" +", " "));
        } catch (Exception e) {
            log.error("Error occurred while fetching the jaeger trace transactions", e);
            throw new DataProcessingException("Error occurred while fetching the jaeger trace transactions");
        }
    }
}
