package com.appnomic.appsone.api.businesslogic;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.mobile.model.FrozenSlowFramesTrend;
import com.appnomic.appsone.api.mobile.util.BigQueryService;
import com.appnomic.appsone.api.mobile.util.BigQueryUtil;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.FieldValueList;
import com.google.cloud.bigquery.QueryJobConfiguration;
import com.google.cloud.bigquery.TableResult;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class GetFrozenSlowFramesTrendBL implements BusinessLogic<Object, UtilityBean<Object>, FrozenSlowFramesTrend> {

	@Override
	public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
		List<String> errorMessages = new ArrayList<>();
		if (!BigQueryUtil.appOSStringValidation(requestObject, errorMessages)
			|| !BigQueryUtil.appDisplayVersionValidation(requestObject, errorMessages)
			|| !BigQueryUtil.fromTimeToTimeValidation(requestObject, errorMessages)) {
				throw new ClientException(String.join(";", errorMessages));
		}
		String appOSString = requestObject.getQueryParams().get("app-os")[0];
		String appDisplayVersions = requestObject.getQueryParams().get("app-build-version")[0];
		String fromTime = requestObject.getQueryParams().get("from-time")[0];
		String toTime = requestObject.getQueryParams().get("to-time")[0];
		
		return UtilityBean.<Object>builder()
				.appOSString(appOSString)
				.appDisplayVersions(appDisplayVersions)
				.fromTimeString(fromTime)
				.toTimeString(toTime)
				.build();
	}

	@Override
	public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
		return utilityBean;
	}

	@Override
	public FrozenSlowFramesTrend processData(UtilityBean<Object> configData) throws DataProcessingException {
		String appOSString = configData.getAppOSString();
		BigQuery bigQuery = BigQueryService.getInstance(appOSString.split("\\.")[0]);
		log.debug("bigQuery Obj: {}", bigQuery);

		List<String> appDisplayVersions = Arrays.asList(configData.getAppDisplayVersions().split(","));
		String fromTime = configData.getFromTimeString();
		String toTime = configData.getToTimeString();
		String intervalBucket = BigQueryUtil.getIntervalBucket(fromTime, toTime);
		String intervalBucketUnit = intervalBucket.split(" ")[1];
		String truncatedEventTimestamp = BigQueryUtil.getTruncatedEventTimestamp(intervalBucket, intervalBucketUnit, Constants.PERFMON_ALIAS);
		
		String query =	"SELECT " + 
							 truncatedEventTimestamp +" AS time, " +
						"    TIMESTAMP_TRUNC(TIMESTAMP_MILLIS("+fromTime+"), "+intervalBucketUnit+") AS truncatedFromTime," +
					    "	 AVG(trace_info.screen_info.slow_frame_ratio * 100) AS slowFrameRatio, " +
					    "	 AVG(trace_info.screen_info.frozen_frame_ratio * 100) AS frozenFrameRatio, " +
					    "	 app_display_version AS appDisplayVersions " +
						"FROM " + Constants.getPerfMonTable(appOSString) + " AS perfmon  " + 
						"WHERE " +
					    "	 event_timestamp > TIMESTAMP_MILLIS("+fromTime+") " +
					    "	 AND event_timestamp < TIMESTAMP_MILLIS("+toTime+") " +
						"GROUP BY " + 
						"    time, " + 
						"	 appDisplayVersions " + 
						"ORDER BY 1";
		
		QueryJobConfiguration queryConfig = QueryJobConfiguration.newBuilder(query).setUseLegacySql(false).build();
		
		TableResult result;
		try {
			result = bigQuery.query(queryConfig);
		} catch(InterruptedException inExc) {
			throw new DataProcessingException("Error in fetching data from BigQuery");
		}
		
		FrozenSlowFramesTrend response = new FrozenSlowFramesTrend();
		Map<String, BigDecimal> slowFrameTrend = new HashMap<>();
		Map<String, Integer> slowFrameCounterMap = new HashMap<>();
		Map<String, BigDecimal> frozenFrameTrend = new HashMap<>();
		Map<String, Integer> frozenFrameCounterMap = new HashMap<>();
		
		for(FieldValueList value : result.iterateAll()) {
			int slowFrameCounter = 0;
			int frozenFrameCounter = 0;
			if(slowFrameTrend.isEmpty() || frozenFrameTrend.isEmpty()) {
				slowFrameTrend = BigQueryUtil.createTimeBucketBigDecimal(value.get("truncatedFromTime").getTimestampValue() / 1000L, Long.parseLong(toTime), intervalBucket);
				frozenFrameTrend = BigQueryUtil.createTimeBucketBigDecimal(value.get("truncatedFromTime").getTimestampValue() / 1000L, Long.parseLong(toTime), intervalBucket);
			}
			String timeKey = String.valueOf(value.get("time").getTimestampValue() / 1000L);
			if(null != value.get("appDisplayVersions").getValue() && appDisplayVersions.contains(value.get("appDisplayVersions").getStringValue())) {
				if(null != value.get("slowFrameRatio").getValue()) {
					if(slowFrameTrend.get(timeKey) == BigDecimal.ZERO) {
						slowFrameTrend.put(timeKey, value.get("slowFrameRatio").getNumericValue().setScale(3, RoundingMode.HALF_UP));
						slowFrameCounterMap.put(timeKey, slowFrameCounter + 1);
					} else {
						BigDecimal existingSlowFrame = slowFrameTrend.get(timeKey);
						BigDecimal slowFrameSum = existingSlowFrame.multiply(new BigDecimal(slowFrameCounterMap.get(timeKey))).setScale(3, RoundingMode.HALF_UP);
						BigDecimal newSlowFrameSum = slowFrameSum.add(value.get("slowFrameRatio").getNumericValue().setScale(3, RoundingMode.HALF_UP));
						slowFrameCounterMap.put(timeKey, slowFrameCounter + 1);
						BigDecimal newSlowFrame = newSlowFrameSum.divide(new BigDecimal(slowFrameCounterMap.get(timeKey)), 3, RoundingMode.HALF_UP);
						slowFrameTrend.put(timeKey, newSlowFrame);
					}
				}
				if(null != value.get("frozenFrameRatio").getValue()) {
					if(frozenFrameTrend.get(timeKey) == BigDecimal.ZERO) {
						frozenFrameTrend.put(timeKey, value.get("frozenFrameRatio").getNumericValue().setScale(3, RoundingMode.HALF_UP));
						frozenFrameCounterMap.put(timeKey, frozenFrameCounter + 1);
					} else {
						BigDecimal existingFrozenFrame = frozenFrameTrend.get(timeKey);
						BigDecimal frozenFrameSum = existingFrozenFrame.multiply(new BigDecimal(frozenFrameCounterMap.get(timeKey))).setScale(3, RoundingMode.HALF_UP);
						BigDecimal newFrozenFrameSum = frozenFrameSum.add(value.get("frozenFrameRatio").getNumericValue().setScale(3, RoundingMode.HALF_UP));
						frozenFrameCounterMap.put(timeKey, frozenFrameCounter + 1);
						BigDecimal newFrozenFrame = newFrozenFrameSum.divide(new BigDecimal(frozenFrameCounterMap.get(timeKey)), 3, RoundingMode.HALF_UP);
						frozenFrameTrend.put(timeKey, newFrozenFrame);
					}
				}
			}
		}
		response.setFrozenFrameTrend(frozenFrameTrend);
		response.setSlowFrameTrend(slowFrameTrend);
		return response;
	}
	
}
