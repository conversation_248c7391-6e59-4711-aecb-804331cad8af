package com.appnomic.appsone.api.businesslogic;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.mobile.model.UserAction;
import com.appnomic.appsone.api.mobile.util.BigQueryService;
import com.appnomic.appsone.api.mobile.util.BigQueryUtil;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.FieldValue;
import com.google.cloud.bigquery.FieldValueList;
import com.google.cloud.bigquery.QueryJobConfiguration;
import com.google.cloud.bigquery.TableResult;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class UserActionsBL implements BusinessLogic<Object, UtilityBean<Object>, List<UserAction>> {

	@Override
	public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
		List<String> errorMessages = new ArrayList<>();
		if (!BigQueryUtil.appOSStringValidation(requestObject, errorMessages)
			|| !BigQueryUtil.appDisplayVersionValidation(requestObject, errorMessages)
			|| !BigQueryUtil.fromTimeToTimeValidation(requestObject, errorMessages)) {
			throw new ClientException(String.join(";", errorMessages));
		}
		String appOSString = requestObject.getQueryParams().get("app-os")[0];
		String appDisplayVersions = requestObject.getQueryParams().get("app-build-version")[0];
		String fromTime = requestObject.getQueryParams().get("from-time")[0];
		String toTime = requestObject.getQueryParams().get("to-time")[0];
		Integer rowLimit = (null != requestObject.getQueryParams().get("row-limit") 
				&& !requestObject.getQueryParams().get("row-limit")[0].equalsIgnoreCase("")
				&& 0 < Integer.parseInt(requestObject.getQueryParams().get("row-limit")[0])) 
				? Integer.parseInt(requestObject.getQueryParams().get("row-limit")[0]) : 5;
		
		return UtilityBean.<Object>builder()
				.appOSString(appOSString)
				.appDisplayVersions(appDisplayVersions)
                .fromTimeString(fromTime)
                .toTimeString(toTime)
                .rowLimit(rowLimit)
                .build();
	}

	@Override
	public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
		return utilityBean;
	}

	@Override
	public List<UserAction> processData(UtilityBean<Object> configData) throws DataProcessingException {
		String appOSString = configData.getAppOSString();
		BigQuery bigQuery = BigQueryService.getInstance(appOSString.split("\\.")[0]);
		log.debug("bigQuery Obj: {}", bigQuery);

		String appDisplayVersionInParam = BigQueryUtil.getInParameter(configData.getAppDisplayVersions());
		String fromTime = configData.getFromTimeString();
		String toTime = configData.getToTimeString();
		Integer rowLimit = configData.getRowLimit();

		String query = 	"SELECT " + 
						"    event_params AS events " + 
						"FROM " + Constants.getAnalyticsTable(appOSString) +
						"WHERE  " + 
						"    event_name = 'user_engagement' " + 
						"    AND event_timestamp > " + fromTime + "000" +
						"    AND event_timestamp < " + toTime + "000" + 
						"    AND app_info.version IN " + appDisplayVersionInParam + " " ;
		
		QueryJobConfiguration queryConfig = QueryJobConfiguration.newBuilder(query).setUseLegacySql(false).build();
		
		TableResult result;
		try {
			result = bigQuery.query(queryConfig);
		} catch(InterruptedException inExc) {
			throw new DataProcessingException("Error in fetching data from BigQuery");
		}
		
		List<UserAction> userActionsResponse = new ArrayList<>();
		Map<String, Long> individualActionsTimeSum = new HashMap<>();
		Map<String, Long> individualActionsFreq = new HashMap<>();
		Long totalEngagementTime = 0L;
		
		for(FieldValueList value : result.iterateAll()) {
			List<FieldValue> events = value.get("events").getRepeatedValue();
			String activityName = "";
			Long engagementTime = 0L;
			Long freqCount = 0L;
			for(FieldValue event : events) {
				String eventsKey = event.getRecordValue().get(0).getStringValue();
				String eventsValueString = null != event.getRecordValue().get(1).getRecordValue().get(0).getValue() ? event.getRecordValue().get(1).getRecordValue().get(0).getStringValue() : "";
				Long eventsValueInt = null != event.getRecordValue().get(1).getRecordValue().get(1).getValue() ? event.getRecordValue().get(1).getRecordValue().get(1).getLongValue() : 0L;
				
				if(eventsKey.equalsIgnoreCase("firebase_screen_class")) {
					activityName = eventsValueString;
				}
				if(eventsKey.equalsIgnoreCase("engagement_time_msec")) {
					engagementTime = eventsValueInt;
				}
				if(!activityName.isEmpty() && engagementTime > 0) {
					if(!individualActionsTimeSum.containsKey(activityName)) {
						individualActionsTimeSum.put(activityName, engagementTime);
						freqCount++;
						individualActionsFreq.put(activityName, freqCount);
					} else {
						Long oldSum = individualActionsTimeSum.get(activityName);
						Long newSum = oldSum + engagementTime;
						individualActionsTimeSum.put(activityName, newSum);
						Long oldFreq = individualActionsFreq.get(activityName);
						Long newFreq = oldFreq + 1;
						individualActionsFreq.put(activityName, newFreq);
					}
					totalEngagementTime += engagementTime;
					activityName = "";
					engagementTime = 0L;
				}
			}
		}
		
		for(Map.Entry<String, Long> entry : individualActionsTimeSum.entrySet()) {
			UserAction userAction = new UserAction();
			userAction.setPages(entry.getKey());
			userAction.setTotalTime(new BigDecimal((double) entry.getValue() / totalEngagementTime * 100).setScale(2, RoundingMode.HALF_UP));
			userAction.setAverageTime(new BigDecimal((double) entry.getValue() / individualActionsFreq.get(entry.getKey()) / 1000).setScale(3, RoundingMode.HALF_UP));
			if(userActionsResponse.size() < rowLimit) {
				userActionsResponse.add(userAction);
			}
		}
		
		Collections.sort(userActionsResponse);
		return userActionsResponse;
	}

}
