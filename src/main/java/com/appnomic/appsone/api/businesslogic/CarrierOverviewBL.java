package com.appnomic.appsone.api.businesslogic;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.mobile.model.CarrierOverview;
import com.appnomic.appsone.api.mobile.util.BigQueryService;
import com.appnomic.appsone.api.mobile.util.BigQueryUtil;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.FieldValueList;
import com.google.cloud.bigquery.QueryJobConfiguration;
import com.google.cloud.bigquery.TableResult;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CarrierOverviewBL implements BusinessLogic<Object, UtilityBean<Object>, List<CarrierOverview>> {

	@Override
	public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
		List<String> errorMessages = new ArrayList<>();
		if (!BigQueryUtil.appOSStringValidation(requestObject, errorMessages)
			|| !BigQueryUtil.appDisplayVersionValidation(requestObject, errorMessages)
			|| !BigQueryUtil.fromTimeToTimeValidation(requestObject, errorMessages)) {
			throw new ClientException(String.join(";", errorMessages));
		}
		String appOSString = requestObject.getQueryParams().get("app-os")[0];
		String appDisplayVersions = requestObject.getQueryParams().get("app-build-version")[0];
		String fromTime = requestObject.getQueryParams().get("from-time")[0];
		String toTime = requestObject.getQueryParams().get("to-time")[0];
		
		return UtilityBean.<Object>builder()
				.appOSString(appOSString)
				.appDisplayVersions(appDisplayVersions)
                .fromTimeString(fromTime)
                .toTimeString(toTime)
                .build();
	}

	@Override
	public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
		return utilityBean;
	}

	@Override
	public List<CarrierOverview> processData(UtilityBean<Object> configData) throws DataProcessingException {
		String appOSString = configData.getAppOSString();
		BigQuery bigQuery = BigQueryService.getInstance(appOSString.split("\\.")[0]);
		log.debug("bigQuery Obj: {}", bigQuery);

		String appDisplayVersionInParam = BigQueryUtil.getInParameter(configData.getAppDisplayVersions());
		String fromTime = configData.getFromTimeString();
		String toTime = configData.getToTimeString();
		
		String query = 	"SELECT " + 
						"    country, " + 
						"    COUNT(DISTINCT custom_attributes.value) AS users, " + 
						"    STRING_AGG(DISTINCT custom_attributes.value) AS userIds, " + 
						"    carrier, " + 
						"    COUNTIF(radio_type = 'WIFI') AS wifiRadioCount, " + 
						"    COUNTIF(radio_type <> 'WIFI') AS mobileRadioCount, " + 
						"	 COUNT(*) AS allRadioCount " +
						"FROM " + Constants.getPerfMonTable(appOSString) + " AS perfmon " + 
						"    CROSS JOIN perfmon.custom_attributes " + 
						"WHERE " + 
						"    event_name = 'userIdTrace' " + 
						"    AND event_timestamp > TIMESTAMP_MILLIS("+ fromTime +") " + 
						"    AND event_timestamp < TIMESTAMP_MILLIS("+ toTime +") " + 
						"    AND app_display_version IN " + appDisplayVersionInParam + " " +
						"GROUP BY " + 
						"    country, " + 
						"    carrier";
		
		QueryJobConfiguration queryConfig = QueryJobConfiguration.newBuilder(query).setUseLegacySql(false).build();
		
		TableResult result;
		try {
			result = bigQuery.query(queryConfig);
		} catch(InterruptedException inExc) {
			throw new DataProcessingException("Error in fetching data from BigQuery");
		}
		
		List<CarrierOverview> response = new ArrayList<>();
		
		Map<String, Long> usersByCountry = new HashMap<>();
		Long totalUsersAcrossCountries = 0L;
		
		Map<String, Long> wifiRadioByCountry = new HashMap<>();
		Map<String, Long> allRadioByCountry = new HashMap<>();
		
		Map<String, List<Map<String, Long>>> mobileCarriersByCountry = new HashMap<>();
		
		Map<String, Long> mobileCarrierCountMap = new HashMap<>();
		
		for(FieldValueList value : result.iterateAll()) {
			String countryKey = value.get("country").getStringValue();
			
			Set<String> userIdsSetByCountry = new HashSet<>();
			if(!usersByCountry.containsKey(countryKey)) {
				userIdsSetByCountry.addAll(Arrays.asList(value.get("userIds").getStringValue().split(",")));
				usersByCountry.put(countryKey, (long) userIdsSetByCountry.size());
				totalUsersAcrossCountries += (long) userIdsSetByCountry.size();
			} else {
				Long existingUsers = usersByCountry.get(countryKey);
				userIdsSetByCountry.addAll(Arrays.asList(value.get("userIds").getStringValue().split(",")));
				Long newUsers = (long) userIdsSetByCountry.size();
				Long totalUsers = existingUsers + newUsers;
				usersByCountry.put(countryKey, totalUsers);
				totalUsersAcrossCountries = totalUsers;
			}
			
			Long wifiRadioCount = 0L;
			if(!wifiRadioByCountry.containsKey(countryKey)) {
				wifiRadioCount = value.get("wifiRadioCount").getLongValue();
				wifiRadioByCountry.put(countryKey, wifiRadioCount);
			} else {
				Long existingRadioCount = wifiRadioByCountry.get(countryKey);
				Long newRadioCount = value.get("wifiRadioCount").getLongValue();
				Long totalWifiRadioCount = existingRadioCount + newRadioCount;
				wifiRadioByCountry.put(countryKey, totalWifiRadioCount);
			}
			
			Long allRadioCount = 0L;
			if(!allRadioByCountry.containsKey(countryKey)) {
				allRadioCount = value.get("allRadioCount").getLongValue();
				allRadioByCountry.put(countryKey, allRadioCount);
			} else {
				Long existingAllRadioCount = allRadioByCountry.get(countryKey);
				Long newAllRadioCount = value.get("allRadioCount").getLongValue();
				Long totalAllRadioCount = existingAllRadioCount + newAllRadioCount;
				allRadioByCountry.put(countryKey, totalAllRadioCount);
			}
			
			Map<String, Long> mobileCarrierMap = new HashMap<>();
			Long mobileCarrierCount = 0L;
			List<Map<String, Long>> mobileCarrierMapList = new ArrayList<>();
			if(value.get("mobileRadioCount").getLongValue() > 0) {
				if(!mobileCarriersByCountry.containsKey(countryKey)) {
					mobileCarrierMap.put(value.get("carrier").getStringValue(), value.get("mobileRadioCount").getLongValue());
					mobileCarrierMapList.add(mobileCarrierMap);
					mobileCarriersByCountry.put(countryKey, mobileCarrierMapList);
					mobileCarrierCount += value.get("mobileRadioCount").getLongValue();
					mobileCarrierCountMap.put(countryKey, mobileCarrierCount);
				} else {
					List<Map<String, Long>> existingMobileCarrierMapList = mobileCarriersByCountry.get(countryKey);
					mobileCarrierMap.put(value.get("carrier").getStringValue(), value.get("mobileRadioCount").getLongValue());
					mobileCarrierMapList.add(mobileCarrierMap);
					mobileCarrierMapList.addAll(existingMobileCarrierMapList);
					mobileCarriersByCountry.put(countryKey, mobileCarrierMapList);
					Long existingMobileCarrierCount = mobileCarrierCountMap.get(countryKey);
					Long newMobileCarrierCount = value.get("mobileRadioCount").getLongValue();
					Long totalMobileCarrierCount = existingMobileCarrierCount + newMobileCarrierCount;
					mobileCarrierCount = totalMobileCarrierCount;
					mobileCarrierCountMap.put(countryKey, mobileCarrierCount);
				}
			}
			
		}
		
		for(Map.Entry<String, Long> entry : usersByCountry.entrySet()) {
			CarrierOverview carrierOverview = new CarrierOverview();
			
			String country = entry.getKey();
			carrierOverview.setCountry(country);
			
			Long users = entry.getValue();
			carrierOverview.setUsers(users);
			
			BigDecimal userShare = new BigDecimal((double) users / totalUsersAcrossCountries * 100.00).setScale(2, RoundingMode.HALF_UP);
			carrierOverview.setUserShare(userShare);
			
			Long wifiRadioUsageCount = wifiRadioByCountry.get(entry.getKey());
			Long allRadioUsageCount = allRadioByCountry.get(entry.getKey());
			BigDecimal wifiUsage = new BigDecimal((double) wifiRadioUsageCount / allRadioUsageCount * 100.00).setScale(2, RoundingMode.HALF_UP);
			carrierOverview.setWifiUsage(wifiUsage);
			
			List<Map<String, Long>> mobileCarriersMapList = mobileCarriersByCountry.get(entry.getKey());
			List<Map<String, BigDecimal>> mobileCarriersMapListFinal = new ArrayList<>();
			
			if(null != mobileCarriersMapList && !mobileCarriersMapList.isEmpty()) {
				for(Map<String, Long> mobileCarriersMap : mobileCarriersMapList) {
					for(Map.Entry<String, Long> mobileCarriersEntry : mobileCarriersMap.entrySet()) {
						Map<String, BigDecimal> mobileCarriersMapFinal = new HashMap<>();
						Long carrierCount = mobileCarriersEntry.getValue();
						BigDecimal carrierPercentage = new BigDecimal((double) carrierCount / mobileCarrierCountMap.get(entry.getKey()) * 100.00).setScale(2, RoundingMode.HALF_UP);
						mobileCarriersMapFinal.put(mobileCarriersEntry.getKey(), carrierPercentage);
						mobileCarriersMapListFinal.add(mobileCarriersMapFinal);
					}
				}
			}
			
			carrierOverview.setCarrier(mobileCarriersMapListFinal);
			response.add(carrierOverview);
		}
		
		return response;
	}
	
}
