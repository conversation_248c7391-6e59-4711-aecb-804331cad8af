package com.appnomic.appsone.api.businesslogic;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.mobile.util.BigQueryService;
import com.appnomic.appsone.api.mobile.util.BigQueryUtil;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.QueryJobConfiguration;
import com.google.cloud.bigquery.TableResult;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class GetErrorFilesTrendBL implements BusinessLogic<Object, UtilityBean<Object>, Map<String, Long>> {

	@Override
	public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
		List<String> errorMessages = new ArrayList<>();
		if (!BigQueryUtil.appOSStringValidation(requestObject, errorMessages)
			|| !BigQueryUtil.appDisplayVersionValidation(requestObject, errorMessages)
			|| !BigQueryUtil.fromTimeToTimeValidation(requestObject, errorMessages)) {
				throw new ClientException(String.join(";", errorMessages));
		}
		String appOSString = requestObject.getQueryParams().get("app-os")[0];
		String appDisplayVersions = requestObject.getQueryParams().get("app-build-version")[0];
		String fromTime = requestObject.getQueryParams().get("from-time")[0];
		String toTime = requestObject.getQueryParams().get("to-time")[0];
		
		return UtilityBean.<Object>builder()
				.appOSString(appOSString)
				.appDisplayVersions(appDisplayVersions)
				.fromTimeString(fromTime)
				.toTimeString(toTime)
				.build();
	}

	@Override
	public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
		return utilityBean;
	}

	@Override
	public Map<String, Long> processData(UtilityBean<Object> configData) throws DataProcessingException {
		String appOSString = configData.getAppOSString();
		BigQuery bigQuery = BigQueryService.getInstance(appOSString.split("\\.")[0]);
		log.debug("bigQuery Obj: {}", bigQuery);

		List<String> appDisplayVersions = Arrays.asList(configData.getAppDisplayVersions().split(","));
		String fromTime = configData.getFromTimeString();
		String toTime = configData.getToTimeString();
		String intervalBucket = BigQueryUtil.getIntervalBucket(fromTime, toTime);
		String intervalBucketUnit = intervalBucket.split(" ")[1];
		String joinCondition = BigQueryUtil.getTruncatedEventTimestamp(intervalBucket, intervalBucketUnit, Constants.CRASHLYTICS_ALIAS);

        String query = 	" WITH hours as (" +
                "	SELECT *" +
                "	FROM UNNEST(GENERATE_TIMESTAMP_ARRAY(TIMESTAMP_TRUNC(TIMESTAMP_MILLIS("+fromTime+"), "+intervalBucketUnit+"), TIMESTAMP_MILLIS("+toTime+"), INTERVAL "+intervalBucket+")" +
                "	) AS time )" +
                "	SELECT" +
                "		STRING(hours.time) AS time," +
                "		COUNT(crashlytics.issue_id) AS errorFiles," +
                "		crashlytics.application.display_version AS appDisplayVersions" +
                "	FROM " + Constants.getCrashlyticsTable(appOSString) +" AS crashlytics  " +
                "	RIGHT JOIN hours ON "+ joinCondition + "= hours.time " +
                "	GROUP BY time, appDisplayVersions" +
                "	ORDER BY 1" ;

		QueryJobConfiguration queryConfig = QueryJobConfiguration.newBuilder(query).setUseLegacySql(false).build();

		Map<String, Long> errorFileCountMap = new HashMap<>();
		
		TableResult result;
		try {
			result = bigQuery.query(queryConfig);
		} catch(InterruptedException inExc) {
			throw new DataProcessingException("Error in fetching data from BigQuery");
		}
		
		result.iterateAll().forEach(value -> {
            if(!errorFileCountMap.containsKey(value.get("time").getStringValue())) {
                if(null != value.get("appDisplayVersions").getValue() && appDisplayVersions.contains(value.get("appDisplayVersions").getStringValue())) {
                    errorFileCountMap.put(value.get("time").getStringValue(), value.get("errorFiles").getLongValue());
                } else {
                    errorFileCountMap.put(value.get("time").getStringValue(), 0L);
                }
            } else {
                if(null != value.get("appDisplayVersions").getValue() && appDisplayVersions.contains(value.get("appDisplayVersions").getStringValue())) {
                    Long existingCount = errorFileCountMap.get(value.get("time").getStringValue());
                    Long newCount = existingCount + value.get("errorFiles").getLongValue();
                    errorFileCountMap.put(value.get("time").getStringValue(), newCount);
                }

            }
        });
        return errorFileCountMap;
	}
}
