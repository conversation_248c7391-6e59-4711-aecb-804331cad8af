package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.beans.MasterComponentBean;
import com.appnomic.appsone.api.beans.MasterComponentTypeBean;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.CategoryRepo;
import com.appnomic.appsone.api.pojo.GetCategoriesResponse;
import com.appnomic.appsone.api.pojo.NotificationPreferences;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.service.mysql.ComponentDataService;
import com.appnomic.appsone.api.util.ValidationUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.MessageFormat;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

public class UserNotificationPreferenceBL implements BusinessLogic<Object, UtilityBean<Object>, NotificationPreferences>{
    private static final Logger LOGGER = LoggerFactory.getLogger(UserNotificationPreferenceBL.class);

    @Override
    public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
        LOGGER.debug("{} clientValidation().", Constants.INVOKED_METHOD);

        if (requestObject == null) {
            LOGGER.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION_HEADER);
        if (StringUtils.isEmpty(authKey)) {
            LOGGER.error(Constants.MESSAGE_INVALID_AUTH_TOKEN);
            throw new ClientException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(identifier)) {
            LOGGER.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        return UtilityBean.<Object>builder()
                .accountIdString(identifier)
                .authToken(authKey)
                .build();
    }

    @Override
    public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
        LOGGER.debug("{} serverValidation().", Constants.INVOKED_METHOD);
        AccountRepo accountRepo = new AccountRepo();
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if(userId == null){
            throw new ServerException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }
        utilityBean.setUserId(userId);

        com.heal.configuration.pojos.Account account = accountRepo.getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
        }
        utilityBean.setAccount(account);
        return utilityBean;
    }

    @Override
    public NotificationPreferences processData(UtilityBean<Object> utilityBean) throws DataProcessingException {
        LOGGER.debug("{} processData().", Constants.INVOKED_METHOD);
        CategoryRepo categoryRepo = new CategoryRepo();
        try {
            List<MasterComponentBean> allComponents =
                    new ComponentDataService().getAllComponents(utilityBean.getAccount().getId());
            LOGGER.trace("Component for account: {}, [{}]", utilityBean.getAccount().getIdentifier(),
                    allComponents);

            List<MasterComponentTypeBean> allComponentTypes = new ComponentDataService().getAllComponentTypes(utilityBean.getAccount().getId());
            LOGGER.trace("Component types for account: {}, [{}]", utilityBean.getAccount().getIdentifier(),
                    allComponentTypes);

            List<GetCategoriesResponse> categories = categoryRepo.getCategoryDetails(utilityBean.getAccount().getIdentifier())
                    .parallelStream()
                    .map(c -> GetCategoriesResponse.builder().id(c.getId()).name(c.getName()).identifier(c.getIdentifier()).build())
                    .sorted(Comparator.comparing(GetCategoriesResponse::getName))
                    .collect(Collectors.toList());
            LOGGER.debug("Categories for account: {}, [{}]", utilityBean.getAccount().getIdentifier(), categories);

            return NotificationPreferences.builder()
                    .componentnames(allComponents)
                    .componenttypes(allComponentTypes)
                    .category(categories)
                    .build();

        } catch (Exception e) {
            throw new DataProcessingException(e, MessageFormat.format("Error occurred while fetching notification preferences for account. AccountId: {0}.", utilityBean.getAccount().getId()));
        }
    }
}
