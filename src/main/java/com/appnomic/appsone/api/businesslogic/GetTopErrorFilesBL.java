package com.appnomic.appsone.api.businesslogic;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.mobile.model.TopErrorFile;
import com.appnomic.appsone.api.mobile.util.BigQueryService;
import com.appnomic.appsone.api.mobile.util.BigQueryUtil;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.FieldValueList;
import com.google.cloud.bigquery.QueryJobConfiguration;
import com.google.cloud.bigquery.TableResult;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class GetTopErrorFilesBL implements BusinessLogic<Object, UtilityBean<Object>, List<TopErrorFile>> { 

	@Override
	public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
		List<String> errorMessages = new ArrayList<>();
		if (!BigQueryUtil.appOSStringValidation(requestObject, errorMessages)
			|| !BigQueryUtil.appDisplayVersionValidation(requestObject, errorMessages)
			|| !BigQueryUtil.fromTimeToTimeValidation(requestObject, errorMessages)) {
				throw new ClientException(String.join(";", errorMessages));
		}
		String appOSString = requestObject.getQueryParams().get("app-os")[0];
		String appDisplayVersions = requestObject.getQueryParams().get("app-build-version")[0];
		String fromTime = requestObject.getQueryParams().get("from-time")[0];
		String toTime = requestObject.getQueryParams().get("to-time")[0];
		Integer rowLimit = (null != requestObject.getQueryParams().get("row-limit") && !requestObject.getQueryParams().get("row-limit")[0].equalsIgnoreCase("") && 0 < Integer.parseInt(requestObject.getQueryParams().get("row-limit")[0])) ? 
							Integer.parseInt(requestObject.getQueryParams().get("row-limit")[0]) : 5;
		
		return UtilityBean.<Object>builder()
				.appOSString(appOSString)
				.appDisplayVersions(appDisplayVersions)
				.fromTimeString(fromTime)
				.toTimeString(toTime)
				.rowLimit(rowLimit)
				.build();
	}

	@Override
	public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
		return utilityBean;
	}

	@Override
	public List<TopErrorFile> processData(UtilityBean<Object> configData) throws DataProcessingException {
		String appOSString = configData.getAppOSString();
		BigQuery bigQuery = BigQueryService.getInstance(appOSString.split("\\.")[0]);
		log.debug("bigQuery Obj: {}", bigQuery);

		String appDisplayVersionsInParam = BigQueryUtil.getInParameter(configData.getAppDisplayVersions());
		String fromTime = configData.getFromTimeString();
		String toTime = configData.getToTimeString();
        Integer rowLimit = configData.getRowLimit();

        String query = 	"SELECT issue_id AS issueId, issue_title AS issueTitle, is_fatal AS fatal, COUNT (issue_id) AS noOfIssues " +
                "FROM " + Constants.getCrashlyticsTable(appOSString) +
                "WHERE application.display_version IN " + appDisplayVersionsInParam + " " +
                "AND event_timestamp > TIMESTAMP_MILLIS("+ fromTime +") " +
                "AND event_timestamp < TIMESTAMP_MILLIS(" + toTime +") "  +
                "GROUP BY issue_id, issue_title, is_fatal " +
                "ORDER BY noOfIssues DESC ";

		QueryJobConfiguration queryConfig = QueryJobConfiguration.newBuilder(query).setUseLegacySql(false).build();

		List<TopErrorFile> topErrorFilesResult = new ArrayList<>();
		List<TopErrorFile> topErrorFilesResponse = new ArrayList<>();

		TableResult result;
		try {
			result = bigQuery.query(queryConfig);
		} catch(InterruptedException inExc) {
			throw new DataProcessingException("Error in fetching data from BigQuery");
		}
		
		long totalErrorCount = 0L;

		for (FieldValueList resultValue : result.iterateAll()) {
			TopErrorFile topErrorFile = new TopErrorFile();

			topErrorFile.setIssueTitle(resultValue.get("issueTitle").getStringValue());
			topErrorFile.setFatal(resultValue.get("fatal").getBooleanValue());
			topErrorFile.setNoOfIssues(resultValue.get("noOfIssues").getLongValue());
			totalErrorCount += resultValue.get("noOfIssues").getLongValue();

			topErrorFilesResult.add(topErrorFile);
		}
		Iterator<TopErrorFile> topErrorFilesIterator = topErrorFilesResult.iterator();
		while (topErrorFilesIterator.hasNext() && rowLimit > 0) {
			TopErrorFile topErrorFile = topErrorFilesIterator.next();
			TopErrorFile topErrorFileResult = new TopErrorFile();

			topErrorFileResult.setIssueTitle(topErrorFile.getIssueTitle());
			topErrorFileResult.setFatal(topErrorFile.isFatal());
			topErrorFileResult.setNoOfIssues(topErrorFile.getNoOfIssues());

			Long noOfIssues = topErrorFile.getNoOfIssues();
            String percentageOfTotal = (totalErrorCount != 0) ? String.format("%.2f", noOfIssues.doubleValue()/ (double) totalErrorCount *100.00) + "%" : "0.00%";
			topErrorFileResult.setPercentage(percentageOfTotal);
			topErrorFilesResponse.add(topErrorFileResult);
			rowLimit--;
		}
		return topErrorFilesResponse;
	}
}
