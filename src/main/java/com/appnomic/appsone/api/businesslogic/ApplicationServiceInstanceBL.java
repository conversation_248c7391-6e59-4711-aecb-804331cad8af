package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.ApplicationRepo;
import com.appnomic.appsone.api.dao.redis.ServiceRepo;
import com.appnomic.appsone.api.pojo.*;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.StringUtils;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.MessageFormat;
import java.util.List;
import java.util.stream.Collectors;

public class ApplicationServiceInstanceBL implements BusinessLogic<Object, UtilityBean<Object>, List<ServiceInstanceDetailsResponse>> {
    private static final Logger LOGGER = LoggerFactory.getLogger(ApplicationServiceInstanceBL.class);

    @Override
    public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
        LOGGER.debug("Inside Client validation");

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION_HEADER);
        String accountIdPattern = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        String applicationIdStr = requestObject.getParams().get(Constants.REQUEST_PARAM_APPLICATION_ID);
        String serviceIdPattern = requestObject.getParams().get(Constants.REQUEST_PARAM_SERVICE_ID);
        int applicationId, serviceId;

        if (StringUtils.isEmpty(authKey)) {
            LOGGER.error(Constants.MESSAGE_EMPTY_AUTH_TOKEN);
            throw new ClientException(Constants.MESSAGE_EMPTY_AUTH_TOKEN);
        }

        if (StringUtils.isEmpty(accountIdPattern)) {
            LOGGER.error(Constants.MESSAGE_EMPTY_ACCOUNT);
            throw new ClientException(Constants.MESSAGE_EMPTY_ACCOUNT);
        }

        if (applicationIdStr == null || applicationIdStr.trim().isEmpty()) {
            LOGGER.error("Application id should not be empty or null.");
            throw new ClientException("Application id should not be empty or null.");
        } else {
            try {
                applicationId = Integer.parseInt(applicationIdStr);
            } catch (NumberFormatException e) {
                LOGGER.error("Error occurred while converting the application id {}. Reason: {}", applicationIdStr, e.getMessage());
                throw new ClientException("Application id should not be empty or null.");
            }
        }

        if (StringUtils.isEmpty(serviceIdPattern)) {
            LOGGER.error(UIMessages.SERVICE_EMPTY_ERROR);
            throw new ClientException(UIMessages.SERVICE_EMPTY_ERROR);
        }
        try {
            serviceId = Integer.parseInt(serviceIdPattern);
        } catch (NumberFormatException e) {
            LOGGER.error("Error occurred while converting the service id {}. Reason: {}", serviceIdPattern, e.getMessage());
            throw new ClientException(UIMessages.SERVICE_EMPTY_ERROR);
        }

        return UtilityBean.builder()
                .authToken(authKey)
                .accountIdString(accountIdPattern)
                .applicationId(applicationId)
                .serviceIdString(serviceIdPattern)
                .serviceId(serviceId)
                .build();
    }

    @Override
    public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
        LOGGER.debug("Inside Server validation");

        AccountRepo accountRepo = new AccountRepo();
        ApplicationRepo applicationRepo = new ApplicationRepo();
        ServiceRepo serviceRepo = new ServiceRepo();
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            throw new ServerException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }
        utilityBean.setUserId(userId);

        com.heal.configuration.pojos.Account account = accountRepo.getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
        }
        utilityBean.setAccount(account);

        com.heal.configuration.pojos.Application application = applicationRepo.getApplicationDetailsWithAppId(account.getIdentifier(), utilityBean.getApplicationId());
        if (application == null) {
            LOGGER.error("Error occurred while getting application list for account: {}", account.getIdentifier());
            throw new ServerException(MessageFormat.format("Invalid application id: {0}", utilityBean.getApplicationId()));
        }

        BasicEntity basicEntity = serviceRepo.getAllServices(account.getIdentifier()).stream().filter(s -> s.getId() == utilityBean.getServiceId()).findAny().orElse(null);
        if (basicEntity == null) {
            LOGGER.error("Invalid service id: {}", utilityBean.getServiceId());
            throw new ServerException(UIMessages.ERROR_INVALID_SERVICE_ID);
        }

        Service service = HealUICache.INSTANCE.getServiceDetails(account.getIdentifier(), basicEntity.getIdentifier());
        if (service == null) {
            LOGGER.error("Invalid service id: {}, identifier:{}", utilityBean.getServiceId(), basicEntity.getIdentifier());
            throw new ServerException(UIMessages.ERROR_INVALID_SERVICE_ID);
        }
        utilityBean.setService(service);
        return utilityBean;
    }

    @Override
    public List<ServiceInstanceDetailsResponse> processData(UtilityBean<Object> configData) throws DataProcessingException {
        LOGGER.trace("Inside Process Data");
        return HealUICache.INSTANCE.getServiceInstanceList(configData.getAccount().getIdentifier(), configData.getService().getIdentifier(), true)
                .stream()
                .map(list -> ServiceInstanceDetailsResponse.builder()
                        .id(list.getId())
                        .identifier(list.getIdentifier())
                        .name(list.getName())
                        .build()
                )
                .collect(Collectors.toList());

    }
}
