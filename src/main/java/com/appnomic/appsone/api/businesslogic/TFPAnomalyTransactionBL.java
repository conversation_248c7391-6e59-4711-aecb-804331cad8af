package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.beans.tfp.TFPAnomalyTransactionRequestData;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.opensearch.AnomalySearchRepo;
import com.appnomic.appsone.api.dao.opensearch.CollatedTransactionsSearchRepo;
import com.appnomic.appsone.api.dao.opensearch.RawTransactionSearchRepo;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.ServiceRepo;
import com.appnomic.appsone.api.dao.redis.TimeRangeDetailsDao;
import com.appnomic.appsone.api.dao.redis.TransactionRepo;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.TabularResultsTypePojo;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.pojo.tfp.TFPAnomalyTransactionDetails;
import com.appnomic.appsone.api.util.*;
import com.appnomic.appsone.opeasearchquery.results.TabularResults;
import com.heal.configuration.enums.KPIAttributes;
import com.heal.configuration.pojos.*;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class TFPAnomalyTransactionBL implements BusinessLogic<TFPAnomalyTransactionRequestData, UtilityBean<TFPAnomalyTransactionRequestData>, List<TFPAnomalyTransactionDetails>> {

    private static final int TRANSACTION_COUNT = ConfProperties.getInt(Constants.TFP_TRANSACTION_COUNT,
            Constants.TFP_TRANSACTION_COUNT_DEFAULT);
    private static final String RESPONSE_TYPE_DEFAULT = ConfProperties.getString(Constants.TRANSACTION_TYPE,
            Constants.TRANSACTION_TYPE_DEFAULT);

    @Override
    public UtilityBean<TFPAnomalyTransactionRequestData> clientValidation(RequestObject request) throws ClientException {
        if (request == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authToken)) {
            log.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String accId = request.getParams().get(Constants.REQUEST_PARAM_IDENTIFIER.toLowerCase());
        if (StringUtils.isEmpty(accId)) {
            log.error(Constants.MESSAGE_INVALID_ACCOUNT + ": {}", accId);
            throw new ClientException(Constants.MESSAGE_INVALID_ACCOUNT);
        }

        String svcId = request.getParams().get(Constants.REQUEST_PARAM_SERVICE_ID.toLowerCase());
        if (StringUtils.isEmpty(svcId)) {
            log.error(Constants.MESSAGE_INVALID_SERVICE + ": {}", svcId);
            throw new ClientException(Constants.MESSAGE_INVALID_SERVICE);
        }

        String fromTimeString = request.getQueryParams().get(Constants.REQUEST_PARAM_FROM_TIME)[0];
        if (StringUtils.isEmpty(fromTimeString)) {
            log.error(Constants.MESSAGE_INVALID_TIME_RANGE + ": {}", fromTimeString);
            throw new ClientException(Constants.MESSAGE_INVALID_TIME_RANGE);
        }

        String toTimeString = request.getQueryParams().get(Constants.REQUEST_PARAM_TO_TIME)[0];
        if (StringUtils.isEmpty(toTimeString)) {
            log.error(Constants.MESSAGE_INVALID_TIME_RANGE + ": {}", toTimeString);
            throw new ClientException(Constants.MESSAGE_INVALID_TIME_RANGE);
        }

        long fromTime;
        long toTime;
        try {
            Integer.parseInt(svcId);
            fromTime = Long.parseLong(fromTimeString);
            toTime = Long.parseLong(toTimeString);
        } catch (NumberFormatException e) {
            log.error("Invalid value found where number is required. {}", e.getMessage());
            throw new ClientException(String.format("Invalid value found where number is required. %s", e.getMessage()));
        }

        if (fromTime <= 0 || toTime <= 0 || fromTime >= toTime) {
            log.error("Invalid time range provided");
            throw new ClientException("Invalid time range provided");
        }

        String responseType = request.getParams().getOrDefault(Constants.REQUEST_PARAM_RESPONSE_TYPE, RESPONSE_TYPE_DEFAULT);

        return UtilityBean.<TFPAnomalyTransactionRequestData>builder()
                .fromTime(fromTime)
                .toTime(toTime)
                .accountIdString(accId)
                .serviceId(Integer.parseInt(svcId))
                .authToken(authToken)
                .responseType(responseType)
                .build();
    }

    @Override
    public UtilityBean<TFPAnomalyTransactionRequestData> serverValidation(UtilityBean<TFPAnomalyTransactionRequestData> utilityBean) throws ServerException {

        TFPAnomalyTransactionRequestData requestData = new TFPAnomalyTransactionRequestData();
        requestData.setFrom(utilityBean.getFromTime());
        requestData.setTo(utilityBean.getToTime());

        Account account = new AccountRepo().getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            log.error("Invalid account identifier: {}.", utilityBean.getAccountIdString());
            throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
        }
        requestData.setAccount(account);

        String accountIdentifier = account.getIdentifier();

        TimeRangeDetailsDao timeRangeDetailsDao = new TimeRangeDetailsDao();
        List<TimeRangeDetails> timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(accountIdentifier);
        if (timeRangeDetailsList == null) {
            log.error("Exception occurred while fetching time range details for account {}", accountIdentifier);
            throw new ServerException("Exception occurred while fetching time range details for account " + accountIdentifier);
        }

        if (timeRangeDetailsList.isEmpty()) {
            log.trace("No time range details found for account {}. Searching for Global account.", accountIdentifier);
            timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }
        if (timeRangeDetailsList == null) {
            log.error("Exception occurred while fetching time range details for account {}", Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
            throw new ServerException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }

        if (timeRangeDetailsList.isEmpty()) {
            log.error("No time range details found");
            throw new ServerException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
        }
        TimeRangeDetails timeRangeDetails = ValidationUtils.getRollupPojo(utilityBean.getFromTime(), utilityBean.getToTime(), timeRangeDetailsList);
        if (timeRangeDetails == null) {
            log.error("Time range details are invalid.");
            throw new ServerException("Time range details are invalid.");
        }
        utilityBean.setTimeRangeDetails(timeRangeDetails);
        utilityBean.setTimeRangeDetailsList(timeRangeDetailsList);

        BasicEntity service = new ServiceRepo().getBasicServiceDetailsWithServiceId(account.getIdentifier(), utilityBean.getServiceId());
        if (service == null) {
            log.error("Invalid service id: {}", utilityBean.getServiceId());
            throw new ServerException(UIMessages.ERROR_INVALID_SERVICE_ID);
        }
        requestData.setService(service);

        utilityBean.setRequestPayloadObject(requestData);
        return utilityBean;
    }

    @Override
    public List<TFPAnomalyTransactionDetails> processData(UtilityBean<TFPAnomalyTransactionRequestData> utilityBean) throws DataProcessingException {
        TFPAnomalyTransactionRequestData configData = utilityBean.getRequestPayloadObject();

        Account account = configData.getAccount();
        BasicEntity service = configData.getService();
        long fromTime = configData.getFrom();
        long toTime = configData.getTo();

        NewTimeIntervalGenerationUtility t = new NewTimeIntervalGenerationUtility();
        t.setTimezoneOffset(account.getIdentifier());
        t.processTimeRange(fromTime, toTime);
        t.addEndTimeInOSTime();

        List<Long> times = t.getOSTimes();

        if (DateTimeUtil.inRange(fromTime)) {
            return processRawTxnData(configData, utilityBean.getResponseType());
        }

        CollatedTransactionsSearchRepo transactionsSearchRepo = new CollatedTransactionsSearchRepo();
        List<String> columns = new ArrayList<>();
        columns.add("txnId");

        List<TabularResultsTypePojo> results = transactionsSearchRepo.getTransactionCollatedDataByServiceForAllKpi(account.getIdentifier(),
                service.getIdentifier(), columns, utilityBean.getResponseType(), fromTime, toTime - Constants.MINUTE,
                utilityBean.getTimeRangeDetails(), utilityBean.getTimeRangeDetailsList(), times);
        if (results == null || results.isEmpty()) {
            log.debug("No transaction data found in OpenSearch.");
            return Collections.emptyList();
        }

        Map<String, Map<String, Double>> txnKpiMap = new HashMap<>();
        List<String> txnIds = new ArrayList<>();
        for (TabularResultsTypePojo tabularResultsTypePojo : results) {

            TabularResults tabularResults = tabularResultsTypePojo.getTabularResults();
            Map<String, Map<String, Double>> tempTxnKpiMap = new HashMap<>();
            List<String> tempTxnIds = new ArrayList<>();

            for (TabularResults.ResultRow resultRow : tabularResults.getRowResults()) {
                String mapKey = null;
                Map<String, Double> kpiMap = new HashMap<>();
                for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {

                    if (resultRowColumn.getColumnName().equalsIgnoreCase(columns.get(0))) {
                        mapKey = resultRowColumn.getColumnValue();
                    } else {
                        kpiMap.put(resultRowColumn.getColumnName().split("\\.")[1], Double.parseDouble(resultRowColumn.getColumnValue()));
                    }

                }
                tempTxnIds.add(mapKey);
                tempTxnKpiMap.put(mapKey, kpiMap);
            }

            txnIds.addAll(tempTxnIds);
            txnIds = txnIds.parallelStream().distinct().collect(Collectors.toList());

            tempTxnKpiMap.forEach((key, value) -> {
                if (txnKpiMap.containsKey(key)) {
                    Map<String, Double> tMap = new HashMap<>();
                    txnKpiMap.get(key).forEach((k, v) -> tMap.put(k, v + value.getOrDefault(k, 0D)));
                    txnKpiMap.put(key, tMap);
                } else txnKpiMap.put(key, value);
            });

        }


        Map<String, Integer> txnAnomalyMap = getAnomalousTransactions(account.getIdentifier(), fromTime, toTime);

        List<BasicTransactionEntity> transactionEntities = HealUICache.INSTANCE.getServiceTransactionList(account.getIdentifier(), service.getIdentifier())
                .parallelStream().filter(txn -> txn.getStatus() == 1).collect(Collectors.toList());

        for (BasicTransactionEntity c : transactionEntities) {
            if (!txnIds.contains(c.getIdentifier())) {
                txnIds.add(c.getIdentifier());
            }
        }

        List<TFPAnomalyTransactionDetails> anomalyData = getTransactionData(account.getIdentifier(), txnIds, txnKpiMap, txnAnomalyMap);

        anomalyData = anomalyData.parallelStream()
                .sorted(Comparator.comparing(TFPAnomalyTransactionDetails::getIsAnomaly, Comparator.reverseOrder())
                        .thenComparing(TFPAnomalyTransactionDetails::getFailVolume, Comparator.reverseOrder())
                        .thenComparing(TFPAnomalyTransactionDetails::getSlowVolume, Comparator.reverseOrder())
                        .thenComparing(TFPAnomalyTransactionDetails::getVolume, Comparator.reverseOrder())
                        .thenComparing(TFPAnomalyTransactionDetails::getTransactionName, Comparator.naturalOrder()))
                .collect(Collectors.toList());
        anomalyData = anomalyData.size() > 10 ? anomalyData.subList(0, TRANSACTION_COUNT) : anomalyData;

        return anomalyData;
    }

    private List<TFPAnomalyTransactionDetails> processRawTxnData(TFPAnomalyTransactionRequestData configData, String responseType) {

        Account account = configData.getAccount();
        BasicEntity service = configData.getService();
        long from = configData.getFrom();
        long to = configData.getTo();

        TabularResults results = new RawTransactionSearchRepo().getTransactionListUnderInbound(account.getIdentifier(),
                service.getIdentifier(), responseType, from, to);
        if (results == null || results.getRowResults() == null || results.getRowResults().isEmpty()) {
            log.debug("No transaction data found in OpenSearch.");
            return Collections.emptyList();
        }

        Map<String, Map<String, Double>> txnKpiMap = new HashMap<>();
        Set<String> txnIds = new HashSet<>();
        for (TabularResults.ResultRow resultRow : results.getRowResults()) {
            String txnIdentifier = null;
            Map<String, Double> kpiMap = new HashMap<>();
            String kpiName = "";
            for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {

                if (resultRowColumn.getColumnName().equalsIgnoreCase("txnIdentifier")) {
                    txnIdentifier = resultRowColumn.getColumnValue();
                } else if (resultRowColumn.getColumnName().equalsIgnoreCase("responseTimes.responseStatusTag")) {
                    kpiName = resultRowColumn.getColumnValue().toLowerCase();
                    kpiMap.put(kpiName, Double.valueOf(resultRow.getCountValue()));
                } else if (resultRowColumn.getColumnName().equalsIgnoreCase("AVERAGE: responseTimes.responseInMicroseconds")) {
                    kpiMap.put(KPIAttributes.RESPONSE_TIME.getColumnName().toLowerCase(), Double.parseDouble(resultRowColumn.getColumnValue()));
                }
            }

            //Show response time only for SLOW and GOOD transaction
            if (!kpiName.equalsIgnoreCase(KPIAttributes.SLOW_VOLUME.getColumnName())
                    && !kpiName.equalsIgnoreCase(KPIAttributes.SUCCESS_VOLUME.getColumnName())) {
                kpiMap.remove(KPIAttributes.RESPONSE_TIME.getColumnName().toLowerCase());
            }

            if (txnKpiMap.containsKey(txnIdentifier)) {
                Map<String, Double> tmp = txnKpiMap.get(txnIdentifier);
                kpiMap.forEach((key, value) -> {
                    if (key.equalsIgnoreCase(KPIAttributes.TIMEOUT_VOLUME.getColumnName().toLowerCase()))
                        tmp.put(key, (tmp.getOrDefault(key, 0D) + value) / 2);
                    else
                        tmp.put(key, tmp.getOrDefault(key, 0D) + value);
                });
                txnKpiMap.put(txnIdentifier, tmp);
            } else {
                txnKpiMap.put(txnIdentifier, kpiMap);
            }

            txnIds.add(txnIdentifier);
        }

        Map<String, Integer> txnAnomalyMap = getAnomalousTransactions(account.getIdentifier(), from, to);
        //add other transaction from redis for which data is not collected
        List<BasicTransactionEntity> transactionEntities = HealUICache.INSTANCE.getServiceTransactionList(account.getIdentifier(), service.getIdentifier())
                .parallelStream().filter(txn -> txn.getStatus() == 1).collect(Collectors.toList());

        for (BasicTransactionEntity c : transactionEntities) {
            txnIds.add(c.getIdentifier());
        }

        List<TFPAnomalyTransactionDetails> anomalyData = getRawTransactionData(account.getIdentifier(), txnIds, txnKpiMap, txnAnomalyMap);

        anomalyData = anomalyData.parallelStream()
                .sorted(Comparator.comparing(TFPAnomalyTransactionDetails::getIsAnomaly, Comparator.reverseOrder())
                        .thenComparing(TFPAnomalyTransactionDetails::getFailVolume, Comparator.reverseOrder())
                        .thenComparing(TFPAnomalyTransactionDetails::getSlowVolume, Comparator.reverseOrder())
                        .thenComparing(TFPAnomalyTransactionDetails::getVolume, Comparator.reverseOrder())
                        .thenComparing(TFPAnomalyTransactionDetails::getTransactionName, Comparator.naturalOrder()))
                .collect(Collectors.toList());
        anomalyData = anomalyData.size() > 10 ? anomalyData.subList(0, TRANSACTION_COUNT) : anomalyData;

        return anomalyData;

    }

    protected Map<String, Integer> getAnomalousTransactions(String accountIdentifier, long from, long to) {

        Map<String, Integer> txnAnomalyMap = new HashMap<>();
        AnomalySearchRepo anomalySearchRepo = new AnomalySearchRepo();
        TabularResults tabularResults = anomalySearchRepo.isAnomalousTransaction(accountIdentifier, from, to);
        if (tabularResults != null && tabularResults.getRowResults() != null && !tabularResults.getRowResults().isEmpty()) {
            for (TabularResults.ResultRow resultRow : tabularResults.getRowResults()) {
                for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {
                    if (resultRowColumn.getColumnName().equalsIgnoreCase("transactionId")) {
                        txnAnomalyMap.put(resultRowColumn.getColumnValue(), 1);
                    }
                }
            }
        }

        return txnAnomalyMap;
    }

    protected List<TFPAnomalyTransactionDetails> getTransactionData(String accIdentifier, List<String> txnIds, Map<String, Map<String, Double>> txnKpiMap, Map<String, Integer> txnAnomalyMap) {

        TransactionRepo transactionRepo = new TransactionRepo();
        List<TFPAnomalyTransactionDetails> result = new ArrayList<>();
        for (String txnId : txnIds) {
            Transaction transactionDetails = transactionRepo.getTransactionDetails(accIdentifier, txnId);

            if (transactionDetails != null) {
                result.add(TFPAnomalyTransactionDetails.builder()
                        .transactionName(transactionDetails.getName())
                        .transactionId(transactionDetails.getId())
                        .slowVolume(txnKpiMap.containsKey(txnId) ? txnKpiMap.get(txnId).getOrDefault(KPIAttributes.SLOW_VOLUME.toString(), 0D).longValue() : 0)
                        .failVolume(txnKpiMap.containsKey(txnId) ? txnKpiMap.get(txnId).getOrDefault(KPIAttributes.FAIL_VOLUME.toString(), 0D).longValue() : 0)
                        .successVolume(txnKpiMap.containsKey(txnId) ? txnKpiMap.get(txnId).getOrDefault(KPIAttributes.SUCCESS_VOLUME.toString(), 0D).longValue() : 0)
                        .timeoutVolume(txnKpiMap.containsKey(txnId) ? txnKpiMap.get(txnId).getOrDefault(KPIAttributes.TIMEOUT_VOLUME.toString(), 0D).longValue() : 0)
                        .unknownVolume(txnKpiMap.containsKey(txnId) ? txnKpiMap.get(txnId).getOrDefault(KPIAttributes.UNKNOWN_VOLUME.toString(), 0D).longValue() : 0)
                        .responseTime(txnKpiMap.containsKey(txnId) ? txnKpiMap.get(txnId).getOrDefault(KPIAttributes.RESPONSE_TIME.toString(), 0D) / 1000 : 0)
                        .volume(txnKpiMap.containsKey(txnId) ? txnKpiMap.get(txnId).getOrDefault(KPIAttributes.TOTAL_VOLUME.toString(), 0D).longValue() : 0)
                        .isAnomaly(txnAnomalyMap.containsKey(txnId) ? txnAnomalyMap.getOrDefault(txnId, 0) : 0)
                        .build());
            }

        }
        return result;
    }

    protected List<TFPAnomalyTransactionDetails> getRawTransactionData(String accIdentifier, Set<String> txnIds,
                                                                       Map<String, Map<String, Double>> txnKpiMap, Map<String, Integer> txnAnomalyMap) {

        TransactionRepo transactionRepo = new TransactionRepo();
        List<TFPAnomalyTransactionDetails> result = new ArrayList<>();
        for (String txnId : txnIds) {
            Transaction transactionDetails = transactionRepo.getTransactionDetails(accIdentifier, txnId);

            if (transactionDetails != null) {
                TFPAnomalyTransactionDetails tfpAnomalyTransactionDetails = TFPAnomalyTransactionDetails.builder()
                        .transactionName(transactionDetails.getName())
                        .transactionId(transactionDetails.getId())
                        .slowVolume(txnKpiMap.containsKey(txnId) ? txnKpiMap.get(txnId).getOrDefault(KPIAttributes.SLOW_VOLUME.getColumnName().toLowerCase(), 0D).longValue() : 0)
                        .failVolume(txnKpiMap.containsKey(txnId) ? txnKpiMap.get(txnId).getOrDefault(KPIAttributes.FAIL_VOLUME.getColumnName().toLowerCase(), 0D).longValue() : 0)
                        .successVolume(txnKpiMap.containsKey(txnId) ? txnKpiMap.get(txnId).getOrDefault(KPIAttributes.SUCCESS_VOLUME.getColumnName().toLowerCase(), 0D).longValue() : 0)
                        .timeoutVolume(txnKpiMap.containsKey(txnId) ? txnKpiMap.get(txnId).getOrDefault(KPIAttributes.TIMEOUT_VOLUME.getColumnName().toLowerCase(), 0D).longValue() : 0)
                        .unknownVolume(txnKpiMap.containsKey(txnId) ? txnKpiMap.get(txnId).getOrDefault(KPIAttributes.UNKNOWN_VOLUME.getColumnName().toLowerCase(), 0D).longValue() : 0)
                        .responseTime(txnKpiMap.containsKey(txnId) ? txnKpiMap.get(txnId).getOrDefault(KPIAttributes.RESPONSE_TIME.getColumnName().toLowerCase(), 0D) : 0)
                        .isAnomaly(txnAnomalyMap.containsKey(txnId) ? txnAnomalyMap.getOrDefault(txnId, 0) : 0)
                        .build();
                tfpAnomalyTransactionDetails.setVolume(tfpAnomalyTransactionDetails.getSlowVolume() + tfpAnomalyTransactionDetails.getFailVolume()
                        + tfpAnomalyTransactionDetails.getSuccessVolume() + tfpAnomalyTransactionDetails.getTimeoutVolume() + tfpAnomalyTransactionDetails.getUnknownVolume());
                result.add(tfpAnomalyTransactionDetails);
            }

        }
        return result;
    }
}
