package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.beans.TimezoneDetail;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.AppsoneException;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.ServiceRepo;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.ServiceTimezonePojo;
import com.appnomic.appsone.api.pojo.TimezonePojo;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.service.mysql.MasterDataService;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.api.util.StringUtils;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.Service;
import com.heal.configuration.pojos.Tags;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

public class TimezoneByServiceBL implements BusinessLogic<Object, UtilityBean<Object>, List<ServiceTimezonePojo>> {
    private static final Logger LOGGER = LoggerFactory.getLogger(TimezoneByServiceBL.class);
    private static final String timezoneKey = ConfProperties.getString(Constants.TIMEZONE_TAG_DETAILS_IDETIFIER,
            Constants.TIMEZONE_TAG_DETAILS_IDETIFIER_DEFAULT);

    @Override
    public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
        LOGGER.debug("Inside Client validation");

        int serviceId;
        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION_HEADER.toLowerCase());
        String identifier = requestObject.getParams().get(Constants.REQUEST_PARAM_IDENTIFIER);
        String serviceIdStr = requestObject.getParams().get(Constants.REQUEST_PARAM_SERVICE_ID.toLowerCase());

        if (StringUtils.isEmpty(authKey)) {
            LOGGER.error(Constants.MESSAGE_EMPTY_AUTH_TOKEN);
            throw new ClientException(Constants.MESSAGE_EMPTY_AUTH_TOKEN);
        }

        if (StringUtils.isEmpty(identifier)) {
            LOGGER.error(Constants.MESSAGE_EMPTY_ACCOUNT);
            throw new ClientException(Constants.MESSAGE_EMPTY_ACCOUNT);
        }


        if (serviceIdStr == null || serviceIdStr.trim().isEmpty()) {
            LOGGER.error("Service id should not be empty or null.");
            throw new ClientException(UIMessages.SERVICE_EMPTY_ERROR);
        } else {
            try {
                serviceId = Integer.parseInt(serviceIdStr);
            } catch (NumberFormatException e) {
                LOGGER.error("Error occurred while converting the service id {}. Reason: {}", serviceIdStr, e.getMessage());
                throw new ClientException(UIMessages.INVALID_SERVICE);
            }
        }

        return UtilityBean.builder().authToken(authKey)
                .accountIdString(identifier)
                .serviceId(serviceId)
                .build();
    }

    @Override
    public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
        LOGGER.debug("Inside Server validation");
        ServiceRepo serviceRepo = new ServiceRepo();
        AccountRepo accountRepo = new AccountRepo();
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            throw new AppsoneException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
        }
        utilityBean.setUserId(userId);

        com.heal.configuration.pojos.Account account = accountRepo.getAccount(utilityBean.getAccountIdString());
        if (account == null) {
            throw new AppsoneException(Constants.MESSAGE_INVALID_ACCOUNT);
        }
        utilityBean.setAccount(account);

        BasicEntity basicEntity = serviceRepo.getAllServices(account.getIdentifier()).stream().filter(s -> s.getId() == utilityBean.getServiceId()).findAny().orElse(null);
        if (basicEntity == null) {
            LOGGER.error("Invalid service id: {}", utilityBean.getServiceId());
            throw new ServerException(UIMessages.ERROR_INVALID_SERVICE_ID);
        }

        Service service = HealUICache.INSTANCE.getServiceDetails(account.getIdentifier(), basicEntity.getIdentifier());
        if (service == null) {
            LOGGER.error("Invalid service id: {}, identifier:{}", utilityBean.getServiceId(), basicEntity.getIdentifier());
            throw new ServerException(UIMessages.ERROR_INVALID_SERVICE_ID);
        }
        utilityBean.setService(service);
        return utilityBean;
    }

    @Override
    public List<ServiceTimezonePojo> processData(UtilityBean<Object> configData) throws DataProcessingException {
        LOGGER.debug("Inside getTimezoneByService");

        Service service = configData.getService();
        Tags serviceTimeZone = Objects.requireNonNull(service.getTags()).stream().filter((t) -> t.getType().equals(timezoneKey)).findAny().orElse(null);
        int timeZoneId = -1;
        if (serviceTimeZone != null)
            timeZoneId = Integer.parseInt(serviceTimeZone.getKey());
        else {
            LOGGER.info("Tag {} for service {} does not exist in database.", timezoneKey, service);
            Tags accountTag = configData.getAccount().getTags().stream().filter((t) -> t.getType().equals(timezoneKey)).findAny().orElse(null);
            if (accountTag != null)
                timeZoneId = Integer.parseInt(accountTag.getKey());
        }
        if (timeZoneId == -1) {
            LOGGER.info("TimezoneKey:{} for service:{} and accountId:{} does not exist in database.", timezoneKey, service.getIdentifier(), configData.getAccount().getIdentifier());
            return Collections.emptyList();
        }
        TimezoneDetail timezoneDetail = MasterDataService.getTimezoneByTagID(timeZoneId);
        if (timezoneDetail == null) {
            return Collections.emptyList();
        }
        return Collections.singletonList(ServiceTimezonePojo.builder()
                .serviceIdentifier(service.getIdentifier())
                .timezoneDetails(TimezonePojo.builder()
                        .id(timezoneDetail.getId())
                        .timeZoneName(timezoneDetail.getTimeZoneName())
                        .offsetName(timezoneDetail.getOffsetName())
                        .timeOffset(timezoneDetail.getTimeOffset())
                        .abbreviation(timezoneDetail.getAbbreviation())
                        .build())
                .build());
    }
}
