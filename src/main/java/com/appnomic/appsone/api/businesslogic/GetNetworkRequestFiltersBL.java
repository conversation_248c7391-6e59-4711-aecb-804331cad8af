package com.appnomic.appsone.api.businesslogic;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.mobile.util.BigQueryService;
import com.appnomic.appsone.api.mobile.util.BigQueryUtil;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.QueryJobConfiguration;
import com.google.cloud.bigquery.TableResult;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class GetNetworkRequestFiltersBL implements BusinessLogic<Object, UtilityBean<Object>, Map<String, Set<String>>> {

	@Override
	public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
		List<String> errorMessages = new ArrayList<>();
		if (!BigQueryUtil.appOSStringValidation(requestObject, errorMessages)
			|| !BigQueryUtil.appDisplayVersionValidation(requestObject, errorMessages)
			|| !BigQueryUtil.fromTimeToTimeValidation(requestObject, errorMessages)) {
				throw new ClientException(String.join(";", errorMessages));
		}
		String appOSString = requestObject.getQueryParams().get("app-os")[0];
		String appDisplayVersions = requestObject.getQueryParams().get("app-build-version")[0];
		String fromTime = requestObject.getQueryParams().get("from-time")[0];
		String toTime = requestObject.getQueryParams().get("to-time")[0];
		
		return UtilityBean.<Object>builder()
				.appOSString(appOSString)
				.appDisplayVersions(appDisplayVersions)
				.fromTimeString(fromTime)
				.toTimeString(toTime)
				.build();
	}

	@Override
	public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
		return utilityBean;
	}

	@Override
	public Map<String, Set<String>> processData(UtilityBean<Object> configData) throws DataProcessingException {
		String appOSString = configData.getAppOSString();
		BigQuery bigQuery = BigQueryService.getInstance(appOSString.split("\\.")[0]);
		log.debug("bigQuery Obj: {}", bigQuery);

		String appDisplayVersionsInParam = BigQueryUtil.getInParameter(configData.getAppDisplayVersions());
		String fromTime = configData.getFromTimeString();
		String toTime = configData.getToTimeString();
		
		String query =	"SELECT " + 
						"    DISTINCT country, " + 
						"    device_name AS deviceName, " + 
						"    carrier, " + 
						"    radio_type AS radioType " + 
						"FROM  " + Constants.getPerfMonTable(appOSString) + 
						"WHERE  " + 
						"    event_type = 'NETWORK_REQUEST' " + 
						"    AND event_timestamp > TIMESTAMP_MILLIS(" + fromTime + ") " + 
						"    AND event_timestamp < TIMESTAMP_MILLIS(" + toTime + ") " + 
						"    AND app_display_version IN " + appDisplayVersionsInParam;
		
		QueryJobConfiguration queryConfig = QueryJobConfiguration.newBuilder(query).setUseLegacySql(false).build();
		
		TableResult result;
		try {
			result = bigQuery.query(queryConfig);
		} catch(InterruptedException inExc) {
			throw new DataProcessingException("Error in fetching data from BigQuery");
		}
		
		Map<String, Set<String>> response = new HashMap<>();
		Set<String> country = new HashSet<>();
		Set<String> deviceName = new HashSet<>();
		Set<String> carrier = new HashSet<>();
		Set<String> radioType = new HashSet<>();
		
		result.iterateAll().forEach(value -> {
			country.add(null != value.get("country").getValue() ? value.get("country").getStringValue() : new String());
			deviceName.add(null != value.get("deviceName").getValue() ? value.get("deviceName").getStringValue() : new String());
			carrier.add(null != value.get("carrier").getValue() ? value.get("carrier").getStringValue() : new String());
			radioType.add(null != value.get("radioType").getValue() ? value.get("radioType").getStringValue() : new String());
		});
		
		response.put("country", country);
		response.put("deviceName", deviceName);
		response.put("carrier", carrier);
		response.put("radioType", radioType);
		
		return response;
	}

}
