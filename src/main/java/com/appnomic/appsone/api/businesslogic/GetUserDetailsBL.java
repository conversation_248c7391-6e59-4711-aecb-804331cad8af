package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.beans.*;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.cache.MasterCache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.mysql.entity.UserDetailsBean;
import com.appnomic.appsone.api.dao.redis.MasterRepo;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.UserTimezonePojo;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.service.mysql.TimezoneDataService;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.api.util.StringUtils;
import com.heal.configuration.pojos.TagDetails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.MessageFormat;

public class GetUserDetailsBL implements BusinessLogic<String, UserTimezoneRequestData, UserTimezonePojo> {
    private static final Logger LOGGER = LoggerFactory.getLogger(GetUserDetailsBL.class);
    private static String timezoneKey = ConfProperties.getString(Constants.TIMEZONE_TAG_DETAILS_IDETIFIER,
            Constants.TIMEZONE_TAG_DETAILS_IDETIFIER_DEFAULT);

    @Override
    public UtilityBean<String> clientValidation(RequestObject requestObject) throws ClientException {
        LOGGER.debug("Inside Client validation");

        String userIdentifier = requestObject.getParams().get(Constants.REQUEST_PARAM_USER_ID);
        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION_HEADER.toLowerCase());
        if (StringUtils.isEmpty(userIdentifier)) {
            throw new ClientException(MessageFormat.format(UIMessages.ERROR_INVALID_INPUT_PARAM,
                    Constants.REQUEST_PARAM_USER_ID, userIdentifier));
        }

        return UtilityBean.<String>builder()
                .authToken(authKey)
                .requestPayloadObject(userIdentifier).build();
    }

    @Override
    public UserTimezoneRequestData serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        LOGGER.debug("Inside Server validation.");
        UserAttributeBean userAttributeBean = new TimezoneDataService().getUserAttributesForUserIdentifier(utilityBean.getRequestPayloadObject());
        if (userAttributeBean == null) {
            LOGGER.error(Constants.MESSAGE_INVALID_PARAMETERS, utilityBean.getRequestPayloadObject());
            throw new ServerException(MessageFormat.format(UIMessages.ERROR_INVALID_INPUT_PARAM, Constants.REQUEST_PARAM_USER_ID, utilityBean.getRequestPayloadObject()));
        }
        UserDetailsBean userDetailsBean = getUserDetails(utilityBean.getAuthToken());

        UserTimezoneRequestData userTimezoneRequestData = new UserTimezoneRequestData();
        userTimezoneRequestData.setUserAttributeBean(userAttributeBean);
        userTimezoneRequestData.setUserDetailsBean(userDetailsBean);
        return userTimezoneRequestData;
    }

    @Override
    public UserTimezonePojo processData(UserTimezoneRequestData configData) throws DataProcessingException {
        TagDetails tagDetail = new MasterRepo().getTagDetails().stream()
                .filter(t -> t.getName().equalsIgnoreCase(timezoneKey))
                .findAny()
                .orElse(null);
        if (tagDetail == null) {
            LOGGER.error("Tag {} does not exist in database.", timezoneKey);
            throw new DataProcessingException(MessageFormat.format(UIMessages.TAG_DOES_NOT_EXIST_MESSAGE, timezoneKey));
        }
        TimezoneDetail timezoneDetail = new TimezoneDataService().getTimezoneByUser(Constants.USER_ATTRIBUTES_TABLE_NAME_MYSQL,
                configData.getUserDetailsBean().getId(), tagDetail.getId());

        UserTimezonePojo userTimezonePojo = new UserTimezonePojo();

        if(null==timezoneDetail){
            userTimezonePojo.setTimezoneId(0);
        }
        else {
            userTimezonePojo.setTimezoneId(timezoneDetail.getId());
        }
        /*`isTimezoneMychoice` & `isNotificationsTimezoneMychoice` is saved inverted in DB as per the UI-English
         * if checkbox is checked then `isTimezoneMychoice`=0 & `isNotificationsTimezoneMychoice`=0
         * if checkbox is un-checked then `isTimezoneMychoice`=1 & `isNotificationsTimezoneMychoice`=1
         * */
        userTimezonePojo.setIsTimezoneMychoice(configData.getUserAttributeBean().getIsTimezoneMychoice()^1);
        userTimezonePojo.setIsNotificationsTimezoneMychoice(configData.getUserAttributeBean().getIsNotificationsTimezoneMychoice()^1);
        return userTimezonePojo;
    }

    protected UserDetailsBean getUserDetails(String authToken) throws ServerException {
        return CommonUtils.getUserDetails(authToken);
    }
}
