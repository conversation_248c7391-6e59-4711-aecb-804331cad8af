package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.beans.HeatMapRequestData;
import com.appnomic.appsone.api.businesslogic.HeatMapBL;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.heatmap.AnomalyDetailedCategory;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.List;

public class HeatMapService {
    private static final Logger LOGGER = LoggerFactory.getLogger(HeatMapService.class);

    public GenericResponse<List<AnomalyDetailedCategory>> getHeatMapData(Request request, Response response) {
        LOGGER.trace("{} getHeatMapData().", Constants.INVOKED_METHOD);
        GenericResponse<List<AnomalyDetailedCategory>> responseObject = new GenericResponse<>();
        try {
            HeatMapBL bl = new HeatMapBL();
            RequestObject requestObject = new RequestObject(request);
            UtilityBean<Object> utilityBean = bl.clientValidation(requestObject);
            HeatMapRequestData configData = bl.serverValidation(utilityBean);

            responseObject.setData(bl.processData(configData));
            responseObject.setMessage(Constants.SUCCESS_MESSAGE);
            responseObject.setResponseStatus(StatusResponse.SUCCESS.toString());

        } catch (DataProcessingException de) {

            LOGGER.error("Error occurred while processing data for heat map.", de);
            responseObject.setMessage(de.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);

        } catch (ServerException se) {

            LOGGER.error("Error occurred while server validation for heat map data.", se);
            responseObject.setMessage(se.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);

        } catch (ClientException ce) {

            LOGGER.error("Error occurred while client validation for heat map data.", ce);
            responseObject.setMessage(ce.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);

        } catch (Exception e) {

            LOGGER.error("Error occurred while getting for heat map data.", e);
            responseObject.setMessage("Error occurred while getting the for heat map data, " + e.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(500);

        }
        return responseObject;
    }
}
