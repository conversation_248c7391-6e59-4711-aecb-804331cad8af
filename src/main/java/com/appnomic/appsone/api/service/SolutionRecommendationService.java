package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.businesslogic.GetSolutionFeedbackBL;
import com.appnomic.appsone.api.businesslogic.TakeSolutionCommentFeedbackBL;
import com.appnomic.appsone.api.businesslogic.TakeSolutionIsUsefulFeedbackBL;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.beans.SolutionRecommendationFeedbackBean;
import com.appnomic.appsone.api.pojo.SolutionCommentFeedbackPojo;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

@Slf4j
public class SolutionRecommendationService {

    public GenericResponse<Object> takeSolutionCommentFeedback(Request request, Response response) {

        GenericResponse<Object> responseObject = new GenericResponse<>();

        try{
            RequestObject requestObject = new RequestObject(request);
            TakeSolutionCommentFeedbackBL takeSolutionCommentFeedbackBL = new TakeSolutionCommentFeedbackBL();

            UtilityBean<SolutionRecommendationFeedbackBean> feedbackBean = takeSolutionCommentFeedbackBL.clientValidation(requestObject);
            UtilityBean<SolutionRecommendationFeedbackBean> data = takeSolutionCommentFeedbackBL.serverValidation(feedbackBean);
            takeSolutionCommentFeedbackBL.processData(data);

            responseObject.setData("Feedback taken successfully.");
            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (Exception e) {
            log.error("Error occurred while taking feedback.", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    "Error while taking feedback.", Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }
        return responseObject;
    }

    public GenericResponse<SolutionCommentFeedbackPojo> getSolutionFeedback(Request request, Response response) {

        GenericResponse<SolutionCommentFeedbackPojo> responseObject = new GenericResponse<>();

        try{
            RequestObject requestObject = new RequestObject(request);
            GetSolutionFeedbackBL getSolutionFeedbackBL = new GetSolutionFeedbackBL();

            UtilityBean<String> feedbackBean = getSolutionFeedbackBL.clientValidation(requestObject);
            UtilityBean<String> data = getSolutionFeedbackBL.serverValidation(feedbackBean);
            SolutionCommentFeedbackPojo feedback = getSolutionFeedbackBL.processData(data);

            responseObject.setData(feedback);
            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage("User feedback fetched successfully.");
            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (Exception e) {
            log.error("Error occurred while getting user feedback.", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    "Error while getting user feedback.", Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }
        return responseObject;
    }

    public GenericResponse<Object> takeSolutionIsUsefulFeedback(Request request, Response response) {

        GenericResponse<Object> responseObject = new GenericResponse<>();

        try{
            RequestObject requestObject = new RequestObject(request);
            TakeSolutionIsUsefulFeedbackBL takeSolutionIsUsefulFeedbackBL = new TakeSolutionIsUsefulFeedbackBL();

            UtilityBean<SolutionRecommendationFeedbackBean> feedbackBean = takeSolutionIsUsefulFeedbackBL.clientValidation(requestObject);
            UtilityBean<SolutionRecommendationFeedbackBean> data = takeSolutionIsUsefulFeedbackBL.serverValidation(feedbackBean);
            takeSolutionIsUsefulFeedbackBL.processData(data);

            responseObject.setData("Feedback taken successfully.");
            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (Exception e) {
            log.error("Error occurred while taking feedback.", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    "Error occurred while taking feedback.", Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }
        return responseObject;
    }
}
