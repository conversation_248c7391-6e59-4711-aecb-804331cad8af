package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.businesslogic.ApplicationHealthBL;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.ApplicationHealthRequestPojo;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.applicationhealth.ApplicationHealthDetail;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

import java.util.Comparator;
import java.util.List;

@Slf4j
@NoArgsConstructor
public class ApplicationHealthService {
    public GenericResponse<List<ApplicationHealthDetail>> getApplicationHealthStatus(Request request, Response response) {
        log.trace("{} getApplicationHealthStatus. PARAMS: {}", Constants.INVOKED_METHOD, request);
        long st = System.currentTimeMillis();
        GenericResponse<List<ApplicationHealthDetail>> genericResponse = new GenericResponse<>();
        try {
            ApplicationHealthBL bl = new ApplicationHealthBL();
            RequestObject requestObject = new RequestObject(request);
            UtilityBean<ApplicationHealthRequestPojo> clientData = bl.clientValidation(requestObject);
            UtilityBean<ApplicationHealthRequestPojo> serverData = bl.serverValidation(clientData);
            genericResponse.setData(bl.processData(serverData));
            genericResponse.setResponseStatus(StatusResponse.SUCCESS.name());
            genericResponse.setMessage(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (DataProcessingException de) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while processing data for get signal list.", de);
            CommonUtils.populateErrorResponse(genericResponse, response, de.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ServerException se) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while server validation for get signal list.", se);
            CommonUtils.populateErrorResponse(genericResponse, response, se.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ClientException ce) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while client validation for get signal list.", ce);
            CommonUtils.populateErrorResponse(genericResponse, response, ce.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Exception for get signal list.", e);
            CommonUtils.populateErrorResponse(genericResponse, response, e.getMessage(), 500);
        } finally {
            log.debug("Total time taken to load application health summary: {}", System.currentTimeMillis() - st);
        }
        return genericResponse;
    }

    public List<ApplicationHealthDetail> sortApplicationHealthData(List<ApplicationHealthDetail> appHealthData) {
        /*
         * Sorting is done in the following order:
         * 1. Severe problem count
         * 2. Default problem count
         * 3. Severe warning count
         * 4. Default warning count
         * 5. Healthy applications
         * 6. Maintenance Window : Severe problem count
         * 7. Maintenance Window : Default problem count
         * 8. Maintenance Window : Severe warning count
         * 9. Maintenance Window : Default warning count
         * 10. Maintenance window for healthy application
         */
        Comparator<ApplicationHealthDetail> severeProblem = Comparator.comparing(ApplicationHealthDetail::getSevereProblemCount).reversed();
        Comparator<ApplicationHealthDetail> defaultProblem = Comparator.comparing(ApplicationHealthDetail::getDefaultProblemCount).reversed();
        Comparator<ApplicationHealthDetail> severeWarning = Comparator.comparing(ApplicationHealthDetail::getSevereWarningCount).reversed();
        Comparator<ApplicationHealthDetail> defaultWarning = Comparator.comparing(ApplicationHealthDetail::getDefaultWarningCount).reversed();
        Comparator<ApplicationHealthDetail> severeBatch = Comparator.comparing(ApplicationHealthDetail::getSevereBatchCount).reversed();
        Comparator<ApplicationHealthDetail> defaultBatch = Comparator.comparing(ApplicationHealthDetail::getDefaultBatchCount).reversed();

        Comparator<ApplicationHealthDetail> maintenanceStatus = Comparator.comparing(ApplicationHealthDetail::isMaintenanceWindowStatus);
        Comparator<ApplicationHealthDetail> severeProblemCountForMaintenance = Comparator.comparing(ApplicationHealthDetail::getSevereProblemCountForMaintenanceWindow).reversed();
        Comparator<ApplicationHealthDetail> severeWarningCountForMaintenance = Comparator.comparing(ApplicationHealthDetail::getSevereWarningCountForMaintenanceWindow).reversed();
        Comparator<ApplicationHealthDetail> severeBatchCountForMaintenance = Comparator.comparing(ApplicationHealthDetail::getSevereBatchCountForMaintenanceWindow).reversed();
        Comparator<ApplicationHealthDetail> batchCountForMaintenance = Comparator.comparing(ApplicationHealthDetail::getBatchCountForMaintenance).reversed();
        Comparator<ApplicationHealthDetail> problemCountForMaintenance = Comparator.comparing(ApplicationHealthDetail::getProblemCountForMaintenance).reversed();
        Comparator<ApplicationHealthDetail> warningCountForMaintenance = Comparator.comparing(ApplicationHealthDetail::getWarningCountForMaintenance).reversed();
        Comparator<ApplicationHealthDetail> name = Comparator.comparing(ApplicationHealthDetail::getName);

        Comparator<ApplicationHealthDetail> values = severeProblem
                .thenComparing(severeBatch)
                .thenComparing(defaultProblem)
                .thenComparing(severeWarning)
                .thenComparing(defaultWarning)
                .thenComparing(severeBatch)
                .thenComparing(defaultBatch)
                .thenComparing(maintenanceStatus)
                .thenComparing(severeProblemCountForMaintenance)
                .thenComparing(severeBatchCountForMaintenance)
                .thenComparing(severeWarningCountForMaintenance)
                .thenComparing(problemCountForMaintenance)
                .thenComparing(batchCountForMaintenance)
                .thenComparing(warningCountForMaintenance)
                .thenComparing(name);

        appHealthData.sort(values);

        return appHealthData;
    }

}