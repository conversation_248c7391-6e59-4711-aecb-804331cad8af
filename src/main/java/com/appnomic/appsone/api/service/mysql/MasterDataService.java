package com.appnomic.appsone.api.service.mysql;

import com.appnomic.appsone.api.beans.*;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.dao.CompInstanceKpiDao;
import com.appnomic.appsone.api.dao.MasterDataDao;
import com.appnomic.appsone.api.manager.MySQLConnectionManager;
import com.appnomic.appsone.api.pojo.*;
import com.appnomic.appsone.api.util.ConfProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * Created by Sonu J on 28/12/2018
 */

public class MasterDataService {
    private static final Logger logger = LoggerFactory.getLogger(MasterDataService.class);
    private static final String timezoneKey =
            ConfProperties.getString(Constants.TIMEZONE_TAG_DETAILS_IDETIFIER,
                    Constants.TIMEZONE_TAG_DETAILS_IDETIFIER_DEFAULT);
    private static final String accountTableRefName =
            ConfProperties.getString(Constants.ACCOUNT_TABLE_NAME_MYSQL,
                    Constants.ACCOUNT_TABLE_NAME_MYSQL_DEFAULT);

    public static List<Account> getAccountList() {
        MasterDataDao masterDataDao =
                MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        List<Account> accounts = new ArrayList<>();
        try {
            accounts = masterDataDao.getAccountList(timezoneKey, accountTableRefName);
        } catch (Exception e) {
            logger.error("Exception while getting component instance data from table" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return accounts;
    }

    public static List<ConnectionDetails> getConnectionDetails(Integer accountId) {
        MasterDataDao masterDataDao =
                MySQLConnectionManager.getInstance().open(MasterDataDao.class);

        try {
            return masterDataDao.getConnectionDetails(accountId);
        } catch (Exception e) {
            logger.error("Exception while getting master kpi details data from table" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return null;
    }

    public static List<CompInstClusterDetails> getCompInstanceDetails(Integer accountId) {
        MasterDataDao masterDataDao =
                MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getCompInstClusterList(accountId);
        } catch (Exception e) {
            logger.error("Exception while getting master kpi details data from table, accountId:{}", accountId, e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return Collections.emptyList();

    }

    public static TagDetailsBean getTagDetails(String tagName) {
        MasterDataDao masterDataDao =
                MySQLConnectionManager.getInstance().open(MasterDataDao.class);

        try {
            return masterDataDao.getTagDetails(tagName, Constants.DEFAULT_ACCOUNT_ID);
        } catch (Exception e) {
            logger.error("Exception while tag details data from table, tagName:{}", tagName, e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return null;

    }

    public static MasterSubTypeBean getMasterSubTypeDetailsForId(int subTypeId) {
        MasterDataDao masterDataDao =
                MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getMasterSubTypeForId(subTypeId);
        } catch (Exception e) {
            logger.error("Error occured while fetching details for sub type id: " + subTypeId +
                    "\n" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return null;
    }

    public static int getAccountId(String accountName) {
        MasterDataDao masterDataDao =
                MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getAccountId(accountName);
        } catch (Exception e) {
            logger.error("Exception while getting account" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return 0;
    }

    public static List<RowDetails> getTransactionList(Integer txnId) {
        MasterDataDao masterDataDao =
                MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getTransactionList(txnId);
        } catch (Exception e) {
            logger.error("Exception while getting transaction list " + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return null;
    }

    public static List<ViewTypes> getAllTypes() {
        MasterDataDao masterDataDao =
                MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getAllTypes();
        } catch (Exception e) {
            logger.error("Exception occurred while fetching getAllTypes ");
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return null;
    }

    public static List<DateComponent> getDateComponentList() {
        MasterDataDao masterDataDao =
                MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getDateComponentList();
        } catch (Exception e) {
            logger.error("Error occurred while fetching date time dropdown list from database", e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return null;
    }

    public static List<MasterFeaturesBean> getMasterFeatures() {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance()
                .open(MasterDataDao.class);
        try {
            return masterDataDao.getMasterFeatures();
        } catch (Exception e) {
            logger.error("Error occurred while fetching mst features");
            List<MasterFeaturesBean> mstDefault = new ArrayList<>();
            MasterFeaturesBean masterFeaturesBean = new MasterFeaturesBean();
            masterFeaturesBean.setName("uploadPage");
            masterFeaturesBean.setEnabled(false);
            mstDefault.add(masterFeaturesBean);
            return mstDefault;
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
    }

    public static KpiCategoryDetailBean getKpiCategoryDetails(int kpiId) {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance()
                .open(MasterDataDao.class);
        try {
            return masterDataDao.getCategoryDetailsForKpi(kpiId);
        } catch (Exception e) {
            logger.error("Error occurred while fetching kpi category details: ", e);
            return null;
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
    }

    public static List<CategoryDetailBean> getCategoryDetails(int accountId) {
        CompInstanceKpiDao dao = MySQLConnectionManager.getInstance().open(CompInstanceKpiDao.class);
        try {
            return dao.getCategoryDetails(accountId);
        } catch (Exception e) {
            logger.error("Error occurred while fetching  category details: ", e);
            return Collections.emptyList();
        } finally {
            MySQLConnectionManager.getInstance().close(dao);
        }
    }

    public static List<Integer> getTransactionsIdsForAccount(int accountId) {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getTransactionsIdsForAccount(accountId);
        } catch (Exception e) {
            logger.error("Exception while getting service list details" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return new ArrayList<>();
    }

    public static TimezoneDetail getTimezoneByTagID(int timezoneId) {
        MasterDataDao masterDataDao =
                MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getTimezoneByTagID(timezoneId);
        } catch (Exception e) {
            logger.error("Error occurred while fetching time zones by id from database." +
                    " timezoneId: {}",  timezoneId, e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return null;
    }
}
