package com.appnomic.appsone.api.service.mysql;

import com.appnomic.appsone.api.businesslogic.ServiceTxnGroupBL;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.pojo.transaction.TransactionTag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.List;

public class TransactionTagDataService {
    private static final Logger LOGGER = LoggerFactory.getLogger(TransactionTagDataService.class);

    public GenericResponse<List<TransactionTag>> getTransactionTagDetails(Request request, Response response) {
        GenericResponse<List<TransactionTag>> responseObject = new GenericResponse<>();
        long startAPI = System.currentTimeMillis();
        try {
            RequestObject requestObject = new RequestObject(request);
            ServiceTxnGroupBL bl = new ServiceTxnGroupBL();
            UtilityBean<Object> clientValidation = bl.clientValidation(requestObject);
            UtilityBean<Object> serverValidation = bl.serverValidation(clientValidation);
            responseObject.setData(bl.processData(serverValidation));
            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);
            LOGGER.info("Tags successfully fetched.");
        } catch (Exception e) {
            LOGGER.error("Error occurred while fetching transaction tag data.",e);
            responseObject.setMessage(Constants.MESSAGE_INTERNAL_ERROR);
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
        }
        LOGGER.info("Time taken to fetch txn tags: {} ms.", (System.currentTimeMillis() - startAPI));
        return responseObject;
    }
}
