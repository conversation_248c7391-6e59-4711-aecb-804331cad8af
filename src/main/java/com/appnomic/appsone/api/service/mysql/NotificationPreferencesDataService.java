package com.appnomic.appsone.api.service.mysql;

import com.appnomic.appsone.api.beans.*;
import com.appnomic.appsone.api.custom.exceptions.AppsoneException;
import com.appnomic.appsone.api.dao.SignalNotificationPreferenceDao;
import com.appnomic.appsone.api.dao.mysql.entity.UserForensicNotificationMappingBean;
import com.appnomic.appsone.api.manager.MySQLConnectionManager;
import com.appnomic.appsone.api.pojo.UserAttributesPojo;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

public class NotificationPreferencesDataService {

    private NotificationPreferencesDataService() {
        //Dummy constructor to hide the implicit one
    }

    private static final Logger LOGGER = LoggerFactory.getLogger(NotificationPreferencesDataService.class);

    public static UserNotificationDetailsBean getUserNotificationDetails(String userIdentifierFromQuery, Handle handle) {
        SignalNotificationPreferenceDao notificationPreferenceDao = getSignalNotificationPreferenceDao(handle);
        try {
            return notificationPreferenceDao.getUserNotificationDetails(userIdentifierFromQuery);
        } catch (Exception e) {
            LOGGER.error("Exception encountered while fetching user notification details. Reason: {}", e.getMessage(), e);
            return null;
        } finally {
            closeDaoConnection(handle, notificationPreferenceDao);
        }
    }

    public static List<NotificationSettingsBean> getNotificationSettingsForAccount(int accountId) {
        SignalNotificationPreferenceDao notificationPreferenceDao = getSignalNotificationPreferenceDao(null);
        try {
            return notificationPreferenceDao.getNotificationSettings(accountId);
        } catch (Exception e) {
            LOGGER.error("Exception encountered while fetching notification settings for account. Reason: {}", accountId, e);
            return new ArrayList<>();
        } finally {
            if (notificationPreferenceDao != null) {
                MySQLConnectionManager.getInstance().close(notificationPreferenceDao);
            }
        }
    }

    public static Set<NotificationBean> getApplicationNotificationPreferences(int accountId, Handle handle) {
        SignalNotificationPreferenceDao userNotificationDao = getSignalNotificationPreferenceDao(handle);
        try {
            List<NotificationBean> notificationBeanList = userNotificationDao.getApplicationNotificationPreferences(accountId);
            if (notificationBeanList != null) {
                return notificationBeanList.stream().filter(Objects::nonNull).collect(Collectors.toSet());
            }
        } catch (Exception e) {
            LOGGER.error("Error in getting application_notification_mapping details from percona for account {}", accountId, e);
        } finally {
            closeDaoConnection(handle, userNotificationDao);
        }
        return new HashSet<>();
    }

    public static Set<NotificationBean> getUserNotificationMapping(int accountId, String userIdentifier, Handle handle) {
        SignalNotificationPreferenceDao userNotificationDao = getSignalNotificationPreferenceDao(handle);
        Set<NotificationBean> notificationBeans = new HashSet<>();
        try {
            notificationBeans.addAll(userNotificationDao.getUserNotificationDetails(accountId, userIdentifier));
        } catch (Exception e) {
            LOGGER.error("Error in getting user application preference from DB, userIdentifier:{}", userIdentifier, e);
        } finally {
            closeDaoConnection(handle, userNotificationDao);
        }

        return notificationBeans;
    }

    public static int[] addNotificationDetails(List<NotificationBean> notificationBean, Handle handle) throws AppsoneException {
        SignalNotificationPreferenceDao signalNotificationPreferenceDao = getSignalNotificationPreferenceDao(handle);
        try {
            return signalNotificationPreferenceDao.addNotificationDetails(notificationBean);
        } catch (Exception e) {
            LOGGER.error("Error while inserting user notification mapping from DB. Reason :{}", e.getMessage(), e);
            throw new AppsoneException("Error while inserting user notification mapping from DB");
        } finally {
            closeDaoConnection(handle, signalNotificationPreferenceDao);
        }
    }

    public static void updateNotifications(List<NotificationBean> notificationBean) throws AppsoneException {

        SignalNotificationPreferenceDao signalNotificationPreferenceDao = null;
        try {
            signalNotificationPreferenceDao =
                    MySQLConnectionManager.getInstance().getHandle().open(SignalNotificationPreferenceDao.class);
            signalNotificationPreferenceDao.updateNotifications(notificationBean);
        } catch (Exception e) {
            LOGGER.error("Error while updating user notification mapping from DB. Reason :{}", e.getMessage(), e);
            throw new AppsoneException("Error while updating user notification mapping from DB");
        } finally {
            if (signalNotificationPreferenceDao != null) {
                MySQLConnectionManager.getInstance().close(signalNotificationPreferenceDao);
            }
        }
    }

    public static int getNotificationPreferencesForUser(int signalTypeId, int severityTypeId, int applicationId, String applicableUserId, int accountId, Handle handle) {
        SignalNotificationPreferenceDao userNotificationDao = getSignalNotificationPreferenceDao(handle);
        try {
            return userNotificationDao.getNotificationPreferencesForUser(signalTypeId, severityTypeId, applicationId, applicableUserId, accountId);
        } catch (Exception e) {
            LOGGER.error("Error in getting user application details from DB. application id:{}", applicationId, e);
        } finally {
            closeDaoConnection(handle, userNotificationDao);
        }

        return -1;
    }

    public static int addNotificationUserDetails(UserNotificationDetailsBean userNotificationDetailsBean, Handle handle) throws AppsoneException {
        SignalNotificationPreferenceDao signalNotificationPreferenceDao = getSignalNotificationPreferenceDao(handle);
        try {
            return signalNotificationPreferenceDao.addNotificationUserDetails(userNotificationDetailsBean);
        } catch (Exception e) {
            LOGGER.error("Error while inserting user notification details from DB. Reason :{}", e.getMessage(), e);
            throw new AppsoneException("Error while inserting user notification details from DB");
        } finally {
            closeDaoConnection(handle, signalNotificationPreferenceDao);
        }
    }

    public static void updateNotificationUserDetails(boolean smsEnabled, boolean emailEnabled, String user, Date updatedTime, int notificationPreferenceId, Handle handle) throws AppsoneException {

        SignalNotificationPreferenceDao signalNotificationPreferenceDao = null;
        try {
            signalNotificationPreferenceDao = getSignalNotificationPreferenceDao(handle);
            signalNotificationPreferenceDao.updateNotificationUserDetails(smsEnabled, emailEnabled, user, updatedTime, notificationPreferenceId);
        } catch (Exception e) {
            LOGGER.error("Error while update user notification details from DB. Reason :{}", e.getMessage(), e);
            throw new AppsoneException("Error while updating user notification details from DB");
        } finally {
            closeDaoConnection(handle, signalNotificationPreferenceDao);
        }
    }

    public static UserNotificationChoiceBean getUserNotificationChoice(String applicableUserId, Handle handle) throws AppsoneException {
        SignalNotificationPreferenceDao signalNotificationPreferenceDao = getSignalNotificationPreferenceDao(handle);
        try {
            return signalNotificationPreferenceDao.getUserNotificationChoice(applicableUserId);
        } catch (Exception e) {
            throw new AppsoneException(e, MessageFormat.format("Error while fetching record from table user_notification_choice. Reason :{0}", e.getMessage()));
        } finally {
            closeDaoConnection(handle, signalNotificationPreferenceDao);
        }
    }

    public static int addNotificationUserChoice(UserNotificationChoiceBean userNotificationChoiceBean, Handle handle) throws AppsoneException {
        SignalNotificationPreferenceDao signalNotificationPreferenceDao = getSignalNotificationPreferenceDao(handle);
        try {
            return signalNotificationPreferenceDao.addNotificationUserChoice(userNotificationChoiceBean);
        } catch (Exception e) {
            throw new AppsoneException(e, MessageFormat.format("Error while inserting in table user_notification_choice. Reason :{0}", e.getMessage()));
        } finally {
            closeDaoConnection(handle, signalNotificationPreferenceDao);
        }
    }

    public static void updateNotificationUserChoice(Handle handle, String applicableUserId, String componentSelection
            , String categoryIds, Date updatedTime, String userDetailsId, String componentTypeIds,
                                                    String componentIds) throws AppsoneException {

        SignalNotificationPreferenceDao signalNotificationPreferenceDao = null;
        try {
            signalNotificationPreferenceDao = getSignalNotificationPreferenceDao(handle);
            signalNotificationPreferenceDao.updateNotificationUserChoice(componentTypeIds, componentIds, applicableUserId, componentSelection, categoryIds, updatedTime, userDetailsId);
        } catch (Exception e) {
            LOGGER.error("Error while update user notification details from DB. Reason :{}", e.getMessage(), e);
            throw new AppsoneException("Error while updating user notification details from DB");
        } finally {
            closeDaoConnection(handle, signalNotificationPreferenceDao);
        }
    }

    public static void updateForensicNotificationUserDetails(int forensicEnabled, int suppressionInterval, String applicableUserId, String user,
                                                             Date updatedTime, Handle handle) throws AppsoneException {

        SignalNotificationPreferenceDao signalNotificationPreferenceDao = null;
        try {
            signalNotificationPreferenceDao = getSignalNotificationPreferenceDao(handle);
            signalNotificationPreferenceDao.updateForensicNotificationUserDetails(suppressionInterval, updatedTime, applicableUserId, forensicEnabled, user);
        } catch (Exception e) {
            LOGGER.error("Error while update user forensic notification details in DB. Reason :{}", e.getMessage(), e);
            throw new AppsoneException("Error while updating user forensic notification details in DB");
        } finally {
            closeDaoConnection(handle, signalNotificationPreferenceDao);
        }
    }

    public static void addForensicNotificationConfigurations(List<UserForensicNotificationMappingBean> beans, Handle handle) throws AppsoneException {

        SignalNotificationPreferenceDao signalNotificationPreferenceDao = null;
        try {
            signalNotificationPreferenceDao = getSignalNotificationPreferenceDao(handle);
            signalNotificationPreferenceDao.addForensicNotificationConfigurations(beans);
        } catch (Exception e) {
            LOGGER.error("Error while adding user forensic notification details in DB. Reason :{}", e.getMessage(), e);
            throw new AppsoneException("Error while adding user forensic notification details in DB");
        } finally {
            closeDaoConnection(handle, signalNotificationPreferenceDao);
        }
    }

    public static void deleteForensicNotificationConfigurations(int applicationId, String applicableUserId, Handle handle) throws AppsoneException {

        SignalNotificationPreferenceDao signalNotificationPreferenceDao = null;
        try {
            signalNotificationPreferenceDao = getSignalNotificationPreferenceDao(handle);
            signalNotificationPreferenceDao.deleteForensicNotificationConfigurations(applicationId, applicableUserId);
        } catch (Exception e) {
            LOGGER.error("Error while deleting user forensic notification details in DB. Reason :{}", e.getMessage(), e);
            throw new AppsoneException("Error while deleting user forensic notification details in DB");
        } finally {
            closeDaoConnection(handle, signalNotificationPreferenceDao);
        }
    }

    private static SignalNotificationPreferenceDao getSignalNotificationPreferenceDao(Handle handle) {
        if (handle == null) {
            return MySQLConnectionManager.getInstance().open(SignalNotificationPreferenceDao.class);
        } else {
            return handle.attach(SignalNotificationPreferenceDao.class);
        }
    }

    private static void closeDaoConnection(Handle handle, SignalNotificationPreferenceDao dao) {
        if (handle == null) {
            MySQLConnectionManager.getInstance().close(dao);
        }
    }

    public static UserAttributesPojo getEmailNotificationDetails(String userIdentifier, Handle handle) throws AppsoneException {
        SignalNotificationPreferenceDao signalNotificationPreferenceDao = getSignalNotificationPreferenceDao(handle);
        try {
            return signalNotificationPreferenceDao.getEmailNotificationDetails(userIdentifier);
        } catch (Exception e) {
            LOGGER.error("Error while getting user email notification details from user_attributes table of Percona. Reason :{}", e.getMessage(), e);
            throw new AppsoneException("Error while getting user email notification details from user_attributes table of Percona.");
        } finally {
            closeDaoConnection(handle, signalNotificationPreferenceDao);
        }
    }

    public static void updateEmailNotificationDetails(UserAttributesPojo userAttributes, Handle handle) throws AppsoneException {

        SignalNotificationPreferenceDao signalNotificationPreferenceDao = null;
        try {
            signalNotificationPreferenceDao = getSignalNotificationPreferenceDao(handle);
            signalNotificationPreferenceDao.updateEmailNotificationDetails(userAttributes);
        } catch (Exception e) {
            LOGGER.error("Error while update user notification details from DB. Reason :{}", e.getMessage(), e);
            throw new AppsoneException("Error while updating user notification details from DB");
        } finally {
            closeDaoConnection(handle, signalNotificationPreferenceDao);
        }
    }
}
