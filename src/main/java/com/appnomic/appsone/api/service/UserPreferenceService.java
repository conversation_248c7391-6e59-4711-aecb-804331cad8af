package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.beans.UtilityBean;
import com.appnomic.appsone.api.businesslogic.UserPreferenceBL;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.AppsoneException;
import com.appnomic.appsone.api.pojo.TagMappingDetails;
import com.appnomic.appsone.api.pojo.UserPreferencePojo;
import com.appnomic.appsone.api.util.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.List;

public class UserPreferenceService {
    private static final Logger logger = LoggerFactory.getLogger(UserPreferenceService.class);

    public GenericResponse getPreferenceList(Request request, Response response) {
        GenericResponse<List<UserPreferencePojo>> userPreferenceResponse = new GenericResponse<>();
        try {
            UserPreferenceBL businessLogic = new UserPreferenceBL();
            UtilityBean utilityBean = businessLogic.clientValidationsForGET(request);
            TagMappingDetails tagMappingDetails = businessLogic.serverValidationsForGET(utilityBean);

            List<UserPreferencePojo> preferences = businessLogic.getUserPreferences(tagMappingDetails);

            userPreferenceResponse.setData(preferences);
            userPreferenceResponse.setResponseStatus(StatusResponse.SUCCESS.name());
            userPreferenceResponse.setMessage(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (AppsoneException e) {
            logger.error(UIMessages.ERROR_INTERNAL, e);
            CommonUtils.populateErrorResponse(userPreferenceResponse, response, e.getMessage(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return userPreferenceResponse;
        } catch (Exception e) {
            logger.error("Error retrieving user preferences.", e);
            CommonUtils.populateErrorResponse(userPreferenceResponse, response,
                    Constants.MESSAGE_INTERNAL_ERROR, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return userPreferenceResponse;
        }
        return userPreferenceResponse;

    }

    public GenericResponse addPreference(Request request, Response response) {
        GenericResponse<Integer> userPreferenceResponse = new GenericResponse<>();

        try {
            UserPreferenceBL businessLogic = new UserPreferenceBL();
            UtilityBean<UserPreferencePojo> utilityBean = businessLogic.clientValidationsForPOST(request);
            TagMappingDetails tagMappingDetails = businessLogic.serverValidationsForPOST(utilityBean);

            int id = businessLogic.addUserPreferences(tagMappingDetails);

            userPreferenceResponse.setData(id);
            userPreferenceResponse.setResponseStatus(StatusResponse.SUCCESS.name());
            userPreferenceResponse.setMessage(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (AppsoneException e) {
            logger.error(UIMessages.ERROR_INTERNAL, e);
            CommonUtils.populateErrorResponse(userPreferenceResponse, response, e.getMessage(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return userPreferenceResponse;
        } catch (Exception e) {
            logger.error("Error adding user preference.", e);
            CommonUtils.populateErrorResponse(userPreferenceResponse, response,
                    Constants.MESSAGE_INTERNAL_ERROR, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return userPreferenceResponse;
        }
        return userPreferenceResponse;
    }

    public GenericResponse updatePreference(Request request, Response response) {
        GenericResponse<Integer> userPreferenceResponse = new GenericResponse<>();

        try {
            UserPreferenceBL businessLogic = new UserPreferenceBL();
            UtilityBean<UserPreferencePojo> utilityBean = businessLogic.clientValidationsForPUT(request);
            TagMappingDetails tagMappingDetails = businessLogic.serverValidationsForPUT(utilityBean);

            businessLogic.updateUserPreferences(tagMappingDetails);

            userPreferenceResponse.setResponseStatus(StatusResponse.SUCCESS.name());
            userPreferenceResponse.setMessage(StatusResponse.SUCCESS.name());
            userPreferenceResponse.setData(0);
            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (AppsoneException e) {
            logger.error("Invalid Request.", e);
            CommonUtils.populateErrorResponse(userPreferenceResponse, response, e.getMessage(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return userPreferenceResponse;
        } catch (Exception e) {
            logger.error("Error updating user preference.", e);
            CommonUtils.populateErrorResponse(userPreferenceResponse, response,
                    Constants.MESSAGE_INTERNAL_ERROR, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return userPreferenceResponse;
        }
        return userPreferenceResponse;
    }

    public GenericResponse deletePreference(Request request, Response response) {
        GenericResponse<Integer> userPreferenceResponse = new GenericResponse<>();

        try {
            UserPreferenceBL businessLogic = new UserPreferenceBL();
            UtilityBean<UserPreferencePojo> utilityBean = businessLogic.clientValidationsForDELETE(request);
            TagMappingDetails tagMappingDetails = businessLogic.serverValidationsForDELETE(utilityBean);

            businessLogic.deleteUserPreferences(tagMappingDetails);

            userPreferenceResponse.setResponseStatus(StatusResponse.SUCCESS.name());
            userPreferenceResponse.setMessage(StatusResponse.SUCCESS.name());
            userPreferenceResponse.setData(0);
            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (AppsoneException e) {
            logger.error("Invalid Request.", e);
            CommonUtils.populateErrorResponse(userPreferenceResponse, response, e.getMessage(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return userPreferenceResponse;
        } catch (Exception e) {
            logger.error("Error updating user preference.", e);
            CommonUtils.populateErrorResponse(userPreferenceResponse, response,
                    Constants.MESSAGE_INTERNAL_ERROR, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return userPreferenceResponse;
        }
        return userPreferenceResponse;
    }

}
