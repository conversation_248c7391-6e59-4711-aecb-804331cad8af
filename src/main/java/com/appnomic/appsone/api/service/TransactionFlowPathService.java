package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.beans.tfp.TFPRequestData;
import com.appnomic.appsone.api.businesslogic.TFPPerformanceDetailsBL;
import com.appnomic.appsone.api.businesslogic.TransactionFlowPathInboundBL;
import com.appnomic.appsone.api.businesslogic.TransactionFlowPathOutboundBL;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.pojo.tfp.TFPPerformanceDetails;
import com.appnomic.appsone.api.pojo.tfp.TFPServiceDetails;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

import java.util.List;

@Slf4j
public class TransactionFlowPathService {
    
    public GenericResponse<List<TFPServiceDetails>> getInbounds(Request request, Response response) {
        log.trace("{} getInbounds().", Constants.INVOKED_METHOD);
        GenericResponse<List<TFPServiceDetails>> responseObject = new GenericResponse<>();
        try {

            TransactionFlowPathInboundBL bl = new TransactionFlowPathInboundBL();
            RequestObject requestObject = new RequestObject(request);
            UtilityBean<TFPRequestData> utilityBean = bl.clientValidation(requestObject);
            UtilityBean<TFPRequestData> tfpRequestData = bl.serverValidation(utilityBean);

            responseObject.setData(bl.processData(tfpRequestData));
            responseObject.setResponseStatus(StatusResponse.SUCCESS.toString());
            responseObject.setMessage(StatusResponse.SUCCESS.toString());

        } catch (DataProcessingException de) {

            log.error("Error occurred while processing data for inbound services.", de);
            responseObject.setMessage(de.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);

        } catch (ServerException se) {

            log.error("Error occurred while server validation for inbound services.", se);
            responseObject.setMessage(se.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);

        } catch (ClientException ce) {

            log.error("Error occurred while client validation for inbound services.", ce);
            responseObject.setMessage(ce.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);

        } catch (Exception e) {

            log.error("Error occurred while getting inbound services.", e);
            responseObject.setMessage(UIMessages.ERROR_INTERNAL + e.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);

        }
        return responseObject;
    }

    public GenericResponse<List<TFPServiceDetails>> getOutbounds(Request request, Response response) {
        log.trace("{} outbound().", Constants.INVOKED_METHOD);
        GenericResponse<List<TFPServiceDetails>> responseObject = new GenericResponse<>();
        try {

            TransactionFlowPathOutboundBL bl = new TransactionFlowPathOutboundBL();
            RequestObject requestObject = new RequestObject(request);
            UtilityBean<TFPRequestData> utilityBean = bl.clientValidation(requestObject);
            UtilityBean<TFPRequestData> tfpRequestData = bl.serverValidation(utilityBean);

            responseObject.setData(bl.processData(tfpRequestData));
            responseObject.setResponseStatus(StatusResponse.SUCCESS.toString());
            responseObject.setMessage(StatusResponse.SUCCESS.toString());

        } catch (DataProcessingException de) {

            log.error("Error occurred while processing data for outbound services.", de);
            responseObject.setMessage(de.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);

        } catch (ServerException se) {

            log.error("Error occurred while server validation for outbound services.", se);
            responseObject.setMessage(se.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);

        } catch (ClientException ce) {

            log.error("Error occurred while client validation for outbound services.", ce);
            responseObject.setMessage(ce.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);

        } catch (Exception e) {

            log.error("Error occurred while getting outbound services.", e);
            responseObject.setMessage(UIMessages.ERROR_INTERNAL + e.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);

        }
        return responseObject;
    }

    public GenericResponse<TFPPerformanceDetails> getTfpPerformance(Request request, Response response) {
        log.trace("{} getTfpPerformance().", Constants.INVOKED_METHOD);
        GenericResponse<TFPPerformanceDetails> responseObject = new GenericResponse<>();
        try {

            TFPPerformanceDetailsBL bl = new TFPPerformanceDetailsBL();
            RequestObject requestObject = new RequestObject(request);
            UtilityBean<TFPPerformanceDetails> clientValidation = bl.clientValidation(requestObject);
            UtilityBean<TFPPerformanceDetails> serverValidation = bl.serverValidation(clientValidation);

            responseObject.setData(bl.processData(serverValidation));
            responseObject.setResponseStatus(StatusResponse.SUCCESS.toString());
            responseObject.setMessage(StatusResponse.SUCCESS.toString());

        } catch (DataProcessingException de) {

            log.error("Error occurred while processing data for TFP Performance.", de);
            responseObject.setMessage(de.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);

        } catch (ServerException se) {

            log.error("Error occurred while server validation for TFP Performance.", se);
            responseObject.setMessage(se.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);

        } catch (ClientException ce) {

            log.error("Error occurred while client validation for TFP Performance.", ce);
            responseObject.setMessage(ce.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);

        } catch (Exception e) {

            log.error("Error occurred while getting TFP Performance.", e);
            responseObject.setMessage(UIMessages.ERROR_INTERNAL + e.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);

        }
        return responseObject;
    }
}
