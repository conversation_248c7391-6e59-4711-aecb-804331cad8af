package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.ApplicationRepo;
import com.appnomic.appsone.api.dao.redis.ServiceRepo;
import com.appnomic.appsone.api.pojo.*;
import com.heal.configuration.pojos.BasicTransactionEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class TransactionGroupService {

    private static final Logger log = LoggerFactory.getLogger(TransactionGroupService.class);

    public static TransactionGroupResponse getTxnGroupList(Request request){

        TransactionGroupResponse transactionGroupResponse = new TransactionGroupResponse();
        AccountRepo accountRepo = new AccountRepo();
        ServiceRepo serviceRepo = new ServiceRepo();
        try{

            String accountIdString = request.params(":identifier");
            com.heal.configuration.pojos.Account account = accountRepo.getAccount(accountIdString);
             //Is given account id is valid
            if(account == null)    {
                log.error("Invalid account id provided");
                transactionGroupResponse.setResponseMessage("Invalid account id provided.");
                transactionGroupResponse.setResponseStatus("FAILURE");
                transactionGroupResponse.setData(null);
                return transactionGroupResponse;
            }

            List<ApplicationTxnGroup> applicationTxnGroups = new ApplicationRepo()
                    .getAllApplicationDetails(account.getIdentifier())
                    .stream()
                    .map(application -> {

                        ApplicationTxnGroup applicationTxnGroup = new ApplicationTxnGroup();
                        List<TransactionGroup> txnGroupList = HealUICache.INSTANCE.getApplicationServiceList(account.getIdentifier(), application.getIdentifier())
                                .stream()
                                .map(s -> serviceRepo.getTransactionsByServiceIdentifier(account.getIdentifier(), s.getIdentifier())
                                        .parallelStream()
                                        .filter(t -> t.getStatus() == 1)
                                        .collect(Collectors.toList()))
                                .flatMap(Collection::stream)
                                .map(BasicTransactionEntity::getTransactionGroups)
                                .flatMap(Collection::stream)
                                .map(g -> TransactionGroup.builder().id(g.getTransactionGroupId()).name(g.getTransactionGroupName()).build())
                                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
                                .entrySet()
                                .stream()
                                .map(e -> TransactionGroup.builder()
                                        .id(e.getKey().getId())
                                        .name(e.getKey().getName())
                                        .count(e.getValue().intValue())
                                        .build())
                                .collect(Collectors.toList());

                        applicationTxnGroup.setId(application.getId());
                        applicationTxnGroup.setName(application.getIdentifier());
                        applicationTxnGroup.setTxnGroupList(txnGroupList);
                        return applicationTxnGroup;
                    })
                    .collect(Collectors.toList());

            transactionGroupResponse.setResponseMessage("");
            transactionGroupResponse.setResponseStatus("SUCCESS");
            transactionGroupResponse.setData(applicationTxnGroups);

        } catch (Exception e){
            log.error("");
            transactionGroupResponse.setResponseMessage(e.getMessage());
            transactionGroupResponse.setResponseStatus("FAILURE");
            transactionGroupResponse.setData(null);
        }

        return transactionGroupResponse;
    }
}
