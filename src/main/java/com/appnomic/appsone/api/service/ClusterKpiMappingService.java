package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.ComponentRepo;
import com.appnomic.appsone.api.dao.redis.InstanceRepo;
import com.appnomic.appsone.api.dao.redis.ServiceRepo;
import com.appnomic.appsone.api.pojo.BehaviourType;
import com.appnomic.appsone.api.pojo.ClusterDetails;
import com.appnomic.appsone.api.pojo.ClusterKpiResponse;
import com.appnomic.appsone.api.pojo.InstanceDetailsResponse;
import com.appnomic.appsone.api.pojo.KpiCategory;
import com.appnomic.appsone.api.pojo.KpiNames;
import com.appnomic.appsone.api.util.CommonUtils;
import com.heal.configuration.enums.KpiType;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.BasicKpiEntity;
import com.heal.configuration.pojos.CompInstClusterDetails;
import com.heal.configuration.pojos.InstanceAttributes;
import com.heal.configuration.pojos.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

public class ClusterKpiMappingService {

    private static final Logger logger = LoggerFactory.getLogger(ClusterKpiMappingService.class);

    public static ClusterKpiResponse getClusterKpiList(Request request) {

        AccountRepo accountRepo = new AccountRepo();
        ServiceRepo serviceRepo = new ServiceRepo();
        InstanceRepo instanceRepo = new InstanceRepo();
        ComponentRepo componentRepo = new ComponentRepo();
        ClusterKpiResponse clusterKpiResponse = new ClusterKpiResponse();
        try {

            String accountIdentifier = request.params(":identifier");
            int serviceId = Integer.parseInt(request.params(":serviceId"));
            if (accountIdentifier == null || accountIdentifier.trim().isEmpty()) {
                logger.error("Invalid account id provided");
            }

            Account account = accountRepo.getAccount(accountIdentifier);
            if (account == null) {
                logger.error("Invalid account identifier: {}.", accountIdentifier);
                throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
            }
            BasicEntity basicEntity = serviceRepo.getAllServices(account.getIdentifier()).stream().filter(s -> s.getId() == serviceId).findAny().orElse(null);
            if (basicEntity == null) {
                logger.error("Invalid service id: {}", serviceId);
                throw new ServerException(UIMessages.ERROR_INVALID_SERVICE_ID);
            }
            Service service = HealUICache.INSTANCE.getServiceDetails(account.getIdentifier(), basicEntity.getIdentifier());
            if (service == null) {
                logger.error("Invalid service id: {}, identifier:{}", basicEntity.getId(), basicEntity.getIdentifier());
                throw new ServerException(UIMessages.ERROR_INVALID_SERVICE_ID);
            }

            List<ClusterDetails> clusterList = new ArrayList<>();

            //GET Workload KPI and Category details first
            List<BasicKpiEntity> basicKpiEntityList = new ComponentRepo().getComponentKpis(accountIdentifier, Constants.TRANSACTION_IDENTIFIER_DEFAULT);
            List<KpiCategory> kpiCategoryList = basicKpiEntityList.parallelStream()
                    .filter(c -> c.getStatus() == 1)
                    .map(kpi -> KpiCategory.builder()
                            .id(kpi.getCategoryDetails().getId())
                            .name(kpi.getCategoryDetails().getName())
                            .forensicEnabled(CommonUtils.forensicExistsForCategory(account.getIdentifier(), kpi.getCategoryDetails().getIdentifier()))
                            .isInfo(kpi.getIsInfo() == 1)
                            .type(kpi.getType())
                            .isDeepDive(kpi.getType().equalsIgnoreCase(Constants.MST_SUB_TYPE_FORENSIC))
                            .build())
                    .distinct().collect(Collectors.toList());

            BehaviourType workload = BehaviourType.WL;
            ClusterDetails workloadDetails = ClusterDetails.builder()
                    .name(workload.name())
                    .behaviourType(workload.toString())
                    .type(workload.name().toUpperCase())
                    .categories(kpiCategoryList)
                    .build();

            List<KpiNames> kpiDataList = basicKpiEntityList
                    .parallelStream().map(c -> KpiNames.builder()
                            .id(c.getId())
                            .description(c.getDescription())
                            .name(c.getName())
                            .unit(c.getUnit())
                            .clusterOperation(c.getClusterAggType())
                            .type(c.getType())
                            .isGroupKpi(c.getGroupId() != 0 ? 1 : 0)
                            .groupName(c.getGroupName())
                            .isDiscovery(c.getDiscovery())
                            .groupId(c.getGroupId())
                            .categoryId(c.getCategoryDetails().getId())
                            .categoryName(c.getCategoryDetails().getName())
                            .isInfo(c.getIsInfo() == 1)
                            .build())
                    .distinct().collect(Collectors.toList());
            workloadDetails.setKPI(kpiDataList);
            clusterList.add(workloadDetails);


            //GET Host and Component KPI Details
            clusterList.addAll(HealUICache.INSTANCE.getServiceInstanceList(account.getIdentifier(), basicEntity.getIdentifier(), true)
                    .stream()
                    .map(instanceBean -> instanceRepo.getInstanceDetailsWithInstIdentifier(account.getIdentifier(), instanceBean.getIdentifier()))
                    .filter(Objects::nonNull)
                    .filter(CompInstClusterDetails::isCluster)
                    .map(inst -> {
                        try {
                            ClusterDetails.ClusterDetailsBuilder builder = ClusterDetails.builder();
                            if (inst.getComponentTypeName().equalsIgnoreCase("host") || inst.getComponentTypeName().equalsIgnoreCase("pod")) {
                                builder.behaviourType(BehaviourType.HB.name());
                            } else {
                                builder.behaviourType(BehaviourType.CB.name());
                            }

                            List<BasicKpiEntity> kpiEntities = componentRepo.getComponentKpis(account.getIdentifier(), inst.getComponentName());
                            List<KpiCategory> categories = kpiEntities
                                    .stream()
                                    .filter(kpi -> kpi.getStatus() == 1)
                                    .map(kpi -> KpiCategory.builder()
                                            .id(kpi.getCategoryDetails().getId())
                                            .name(kpi.getCategoryDetails().getName())
                                            .forensicEnabled(CommonUtils.forensicExistsForCategory(account.getIdentifier(), kpi.getCategoryDetails().getIdentifier()))
                                            .isInfo(kpi.getIsInfo() == 1)
                                            .type(kpi.getType())
                                            .isDeepDive(kpi.getType().equalsIgnoreCase(Constants.MST_SUB_TYPE_FORENSIC))
                                            .build())
                                    .distinct()
                                    .collect(Collectors.toList());

                            if (kpiEntities.stream().anyMatch(kpi -> kpi.getType().equalsIgnoreCase(KpiType.Availability.name()))) {
                                categories.add(KpiCategory.builder()
                                        .id(Constants.AVAILABILITY_LOGICAL_CATEGORY_ID)
                                        .name(Constants.AVAILABILITY_KPI_IDENTIFIER)
                                        .type(Constants.AVAILABILITY_KPI_IDENTIFIER)
                                        .forensicEnabled(false)
                                        .isDeepDive(false)
                                        .build());
                            }
                            if (kpiEntities.stream().anyMatch(kpi -> kpi.getType().equalsIgnoreCase(KpiType.FileWatch.name()) || kpi.getType().equalsIgnoreCase(KpiType.ConfigWatch.name()))) {
                                categories.add(KpiCategory.builder()
                                        .id(Constants.CONFIG_WATCH_LOGICAL_CATEGORY_ID)
                                        .name(Constants.CONFIGWATCH_IDENTIFIER)
                                        .type(Constants.CONFIGWATCH_IDENTIFIER)
                                        .forensicEnabled(false)
                                        .isDeepDive(false)
                                        .build());
                            }
                            return builder
                                    .id(inst.getId())
                                    .name(inst.getName())
                                    .type(inst.getComponentTypeName())
                                    .compType(inst.getComponentTypeName())
                                    .compName(inst.getComponentName())
                                    .compVersionName(inst.getComponentVersionName())
                                    .categories(categories)
                                    .build();
                        } catch (Exception e) {
                            HealUICache.INSTANCE.updateHealUIErrors(1);
                            logger.error("Exception occurred while populating the cluster wise configurations.Cluster:{}", inst, e);
                        }
                        return null;
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList()));

            clusterKpiResponse.setData(clusterList);
            clusterKpiResponse.setResponse_message("");
            clusterKpiResponse.setResponse_status("SUCCESS");

        } catch (Exception e) {
            logger.error("Error occurred while getting cluster kpi details", e);
            clusterKpiResponse.setData(new ArrayList<>());
            clusterKpiResponse.setResponse_message(e.getMessage());
            clusterKpiResponse.setResponse_status(StatusResponse.FAILURE.toString());
            return clusterKpiResponse;
        }

        return clusterKpiResponse;
    }

    public static InstanceDetailsResponse getInstanceList(Request request) {

        InstanceDetailsResponse instanceDetailsResponse = new InstanceDetailsResponse();
        int serviceId = Integer.parseInt(request.params(":serviceId"));
        String accountIdString = request.params(":identifier");
        Account account = new AccountRepo().getAccount(accountIdString);

        if (account == null) {
            logger.error("Invalid account id provided");
            instanceDetailsResponse.setResponse_message(Constants.MESSAGE_INVALID_ACCOUNT);
            instanceDetailsResponse.setResponse_status(StatusResponse.FAILURE.toString());
            return instanceDetailsResponse;
        }

        int clusterId = Integer.parseInt(request.queryParams("cluster_id"));
        try {
            instanceDetailsResponse.setResponse_message(Constants.MESSAGE_SUCCESS);
            instanceDetailsResponse.setResponse_status(StatusResponse.SUCCESS.toString());
            instanceDetailsResponse.setData(fetchInstanceList(account, clusterId));

        } catch (Exception e) {
            logger.error("Error occurred while getting comp instance for the clusterId  {},  serviceId {}. ", clusterId, serviceId, e);
            instanceDetailsResponse.setResponse_message(Constants.MESSAGE_INTERNAL_ERROR);
            instanceDetailsResponse.setResponse_status(StatusResponse.FAILURE.toString());
            instanceDetailsResponse.setData(null);
        }

        return instanceDetailsResponse;
    }

    public static List<InstanceDetailsResponse.InstanceDetails> fetchInstanceList(Account account, int clusterId) {
        long start = System.currentTimeMillis();
        InstanceRepo instanceRepo = new InstanceRepo();
        List<CompInstClusterDetails> accountInstances = instanceRepo.getInstancesByAccount(account.getIdentifier());
        Map<Integer, CompInstClusterDetails> instancesIdMap = accountInstances.stream().collect(Collectors.toMap(CompInstClusterDetails::getId, Function.identity()));

        CompInstClusterDetails clusterDetails = instancesIdMap.get(clusterId);
        if (clusterDetails == null) {
            return Collections.emptyList();
        }

        List<CompInstClusterDetails> allInstancesOfCluster = accountInstances.stream()
                .filter(i -> !i.isCluster())
                .filter(i -> i.getClusterIdentifiers() != null && i.getClusterIdentifiers().contains(clusterDetails.getIdentifier()))
                .collect(Collectors.toList());

        instanceRepo.getClusterInstances(account.getIdentifier(), clusterDetails.getIdentifier());
        logger.debug("Time taken to fetch instance list for cluster: {} , is {} ms. fetched from DB {} instances.", clusterId, (System.currentTimeMillis() - start), allInstancesOfCluster.size());

        List<InstanceDetailsResponse.InstanceDetails> instanceDetailsList;
        start = System.currentTimeMillis();
        instanceDetailsList = allInstancesOfCluster.parallelStream()
                .map(instanceDetail -> {

                    InstanceDetailsResponse.InstanceDetails instanceDetails = new InstanceDetailsResponse.InstanceDetails();
                    instanceDetails.setId(instanceDetail.getId());
                    instanceDetails.setName(instanceDetail.getName());
                    instanceDetails.setHostId(instanceDetail.getHostId());
                    //IO-2001 componentId was removed as the implementation was changed for config-compare
                    instanceDetails.setIp(instanceDetail.getHostAddress());

                    if (instanceDetail.getHostId() != 0) {
                        CompInstClusterDetails hostInstance = instancesIdMap.get(instanceDetail.getHostId());
                        instanceDetails.setHostName(hostInstance != null ? hostInstance.getName() : null);
                    }

                    if (instanceDetail.getAgentIds() != null && !instanceDetail.getAgentIds().isEmpty()) {
                        instanceDetails.setAgentCount(instanceDetail.getAgentIds().size());
                    }
                    return instanceDetails;
                }).collect(Collectors.toList());


        logger.debug("Time taken for transforming instances to response is {} ms. {} instances found.", (System.currentTimeMillis() - start), instanceDetailsList.size());
        return instanceDetailsList;
    }

    public static GenericResponse<Map<String, String>> getAttributes(Request request, Response response) {
        logger.trace(Constants.INVOKED_METHOD + "getAttributes");
        GenericResponse<Map<String, String>> responseObject = new GenericResponse<>();
        Map<String, String> result = new HashMap<>();

        try {
            String accountIdString = request.params(":identifier");
            String instanceIdString = request.params(":instanceId");

            if (!validateParam(instanceIdString)) {
                logger.error("Invalid input parameter received.");
                response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
                return responseObject.setMessage("Invalid input parameter received.")
                        .setResponseStatus(StatusResponse.FAILURE.toString())
                        .setData(result);
            }

            Account account = new AccountRepo().getAccount(accountIdString);
            if (account == null) {
                logger.error("Invalid account id.");
                response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
                return responseObject.setMessage("Invalid account id.")
                        .setResponseStatus(StatusResponse.FAILURE.toString())
                        .setData(result);
            }
            CompInstClusterDetails instClusterDetails = new InstanceRepo().getInstancesByAccount(account.getIdentifier())
                    .stream()
                    .filter(instance -> instance.getId() == Integer.parseInt(instanceIdString))
                    .findAny().orElse(null);
            if (instClusterDetails == null) {
                logger.error("Invalid instance id :{}.", instanceIdString);
                response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
                return responseObject.setMessage("Invalid instance id.")
                        .setResponseStatus(StatusResponse.FAILURE.toString())
                        .setData(result);
            }

            result = getAttributesForInstance(account, instClusterDetails);
            response.status(Constants.SUCCESS_STATUS_CODE);
            responseObject.setMessage("SUCCESS").setResponseStatus(StatusResponse.SUCCESS.toString()).setData(result);

        } catch (Exception e) {
            logger.error("Error occurred while fetching attributes.", e);
            responseObject.setMessage(e.getMessage())
                    .setResponseStatus(StatusResponse.FAILURE.toString())
                    .setData(result);
        }
        return responseObject;
    }

    private static boolean validateParam(String instanceId) {
        try {
            Integer.parseInt(instanceId);
        } catch (NumberFormatException ne) {
            logger.error("Invalid number received as input.", ne);
            return false;
        }
        return true;
    }

    private static Map<String, String> getAttributesForInstance(Account account, CompInstClusterDetails instanceDetails) {
        logger.trace(Constants.INVOKED_METHOD + "getAttributesForInstance account: {}, instance: {}.",
                account.getId(), instanceDetails.getId());
        Map<String, String> result = new HashMap<>();
        try {
            //Mandatory fields are manually put into the attribute map
            result.put(Constants.COMPONENT_KEY, instanceDetails.getComponentName());
            result.put(Constants.COMPONENT_TYPE_KEY, instanceDetails.getComponentTypeName());
            result.put(Constants.COMPONENT_VERSION_KEY, instanceDetails.getComponentVersionName());
            result.put(Constants.COMPONENT_ID_KEY, String.valueOf(instanceDetails.getComponentId()));

            Map<String, String> instanceAttributesMap = new InstanceRepo()
                    .getInstanceAttributesWithInstanceIdentifier(account.getIdentifier(), instanceDetails.getIdentifier())
                    .parallelStream().distinct()
                    .filter(attribute -> attribute.getIsUiVisible() == 1)
                    .collect(Collectors.toMap(InstanceAttributes::getAttributeName, InstanceAttributes::getAttributeValue));
            result.putAll(instanceAttributesMap);
        } catch (Exception e) {
            logger.error("Error occurred while fetching attributes for instance: {}", instanceDetails.getId(), e);
        }
        return result;
    }
}
