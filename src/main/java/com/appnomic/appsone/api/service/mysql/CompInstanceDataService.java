package com.appnomic.appsone.api.service.mysql;

import com.appnomic.appsone.api.beans.*;
import com.appnomic.appsone.api.dao.ComponentInstanceDao;
import com.appnomic.appsone.api.manager.MySQLConnectionManager;
import com.appnomic.appsone.api.pojo.CompInstClusterDetails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> : 17/1/19
 */
public class CompInstanceDataService {
    private static final Logger logger = LoggerFactory.getLogger(CompInstanceDataService.class);

    public static ComponentInstanceBean getComponentInstanceById(int compInstanceId) {
        ComponentInstanceDao instanceDao = MySQLConnectionManager.getInstance().open(ComponentInstanceDao.class);
        try {
            return instanceDao.getComponentInstanceById(compInstanceId);
        } catch (Exception e) {
            logger.error("Exception while getting component instance id:{}", compInstanceId, e);
        } finally {
            MySQLConnectionManager.getInstance().close(instanceDao);
        }
        return null;
    }
}
