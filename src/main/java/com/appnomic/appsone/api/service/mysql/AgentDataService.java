package com.appnomic.appsone.api.service.mysql;

import com.appnomic.appsone.api.dao.AgentDao;
import com.appnomic.appsone.api.beans.AgentBean;
import com.appnomic.appsone.api.manager.MySQLConnectionManager;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;

@Slf4j
public class AgentDataService {
    public static List<AgentBean> getAgentsByAccountId(int accountId) {
        AgentDao agentDao = MySQLConnectionManager.getInstance().open(AgentDao.class);
        try {
            return agentDao.getAgentsByAccountId(accountId);
        } catch (Exception e) {
            log.error("Exception while getting agents for account [{}]. Details: ", accountId, e);
        } finally {
            MySQLConnectionManager.getInstance().close(agentDao);
        }
        return Collections.emptyList();
    }

}
