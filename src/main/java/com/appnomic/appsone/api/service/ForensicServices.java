package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.beans.ForensicCommandOutputData;
import com.appnomic.appsone.api.beans.ForensicData;
import com.appnomic.appsone.api.beans.ForensicExecutionBean;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.dao.opensearch.AnomalySearchRepo;
import com.appnomic.appsone.api.dao.opensearch.ForensicSearchRepo;
import com.appnomic.appsone.api.dao.redis.*;
import com.appnomic.appsone.api.pojo.CompInstClusterDetails;
import com.appnomic.appsone.api.pojo.KpiAnomalyData;
import com.appnomic.appsone.api.pojo.request.ForensicGridDataRequest;
import com.appnomic.appsone.api.service.mysql.MasterDataService;
import com.appnomic.appsone.api.service.mysql.TransactionDataService;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.api.util.DateTimeUtil;
import com.heal.configuration.pojos.*;
import com.heal.configuration.pojos.opensearch.Anomalies;
import com.heal.configuration.pojos.opensearch.ForensicPojo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.zip.GZIPInputStream;


public class ForensicServices {
    private static final Logger logger = LoggerFactory.getLogger(ForensicServices.class);
    private static final int cmdOutputLength = ConfProperties.getInt(Constants.FORENSIC_COMMAND_DATA_LENGTH,
            Constants.DEFAULT_FORENSIC_COMMAND_DATA_LENGTH);
    private static final int FORENSIC_ACTION_SEARCH_LOOKAHEAD_OFFSET = Integer.parseInt(ConfProperties.getString(Constants
            .FORENSIC_ACTION_TRIGGER_TIME_LOOKAHEAD, Constants.FORENSIC_ACTION_TRIGGER_TIME_LOOKAHEAD_DEFAULT));
    private static final int FORENSIC_ACTION_SEARCH_LOOKBACK_OFFSET = Integer.parseInt(ConfProperties.getString(Constants
            .FORENSIC_ACTION_TRIGGER_TIME_LOOKBACK, Constants.FORENSIC_ACTION_TRIGGER_TIME_LOOKBACK_DEFAULT));
    private static final int ANOMALY_COUNT = ConfProperties.getInt(Constants.NUMBER_OF_TOP_ANOMALIES_FROM_OPENSEARCH,
            Constants.NUMBER_OF_TOP_ANOMALIES_FROM_OPENSEARCH_DEFAULT);
    private static final String FORENSIC_LEVEL = ConfProperties.getString(Constants.FORENSIC_ACTION_LEVEL,
            Constants.FORENSIC_ACTION_LEVEL_DEFAULT);

    public static GenericResponse<ForensicData> getForensicDetailsList(Request request, Response response) {
        logger.trace(Constants.INVOKED_METHOD + "getForensicList");
        GenericResponse<ForensicData> responseObject;
        ForensicData forensicData = new ForensicData();
        InstanceRepo instanceRepo = new InstanceRepo();
        try {
            String accountIdString = request.params(":identifier");
            String instanceId = request.params("instanceId");
            String categoryId = request.params("categoryId");
            String fromTimeString = request.queryParams("time");
            String kpiId = request.queryParams("kpiId");
            long fromTime = Long.parseLong(fromTimeString);
            Timestamp fromTimestamp = new Timestamp(fromTime);

            com.heal.configuration.pojos.Account account = new AccountRepo().getAccount(accountIdString);
            if (account == null) {
                return CommonUtils.getGenericResponse(response, StatusResponse.SUCCESS.name(), 400, "Invalid account id " + accountIdString, null, true);
            }

            List<CompInstClusterDetails> compInstanceList = MasterDataService.getCompInstanceDetails(account.getId());
            CompInstClusterDetails compInstanceDetails = compInstanceList.stream().filter(c -> c.getInstanceId() == (Integer.parseInt(instanceId))).findAny().orElse(null);
            if (compInstanceDetails == null) {
                return CommonUtils.getGenericResponse(response, StatusResponse.SUCCESS.name(), 400, "Invalid compInstanceId " + instanceId, null, true);
            }

            CompInstKpiEntity kpiDetailByAccInstKpiIdentifier = new KpiRepo().getKpiDetailByAccInstKpiId(accountIdString, compInstanceDetails.getIdentifier(), Long.parseLong(kpiId));
            if (kpiDetailByAccInstKpiIdentifier == null) {
                return CommonUtils.getGenericResponse(response, StatusResponse.SUCCESS.name(), 400, "Invalid kpi id " + kpiId, null, true);
            }

            String kpiIdentifier = kpiDetailByAccInstKpiIdentifier.getIdentifier();

            Category category = new CategoryRepo().getCategoryDetails(account.getIdentifier()).parallelStream().filter(c -> c.getId() == Integer.parseInt(categoryId))
                    .findAny().orElse(null);
            if (category == null) {
                return CommonUtils.getGenericResponse(response, StatusResponse.SUCCESS.name(), 400, "Invalid category for given categoryId " + categoryId, null, true);
            }

            TagDetails tagDetail = new MasterRepo().getTagDetails().stream()
                    .filter(t -> t.getName().equalsIgnoreCase(Constants.CONTROLLER_TAG))
                    .findAny()
                    .orElse(null);
            if (tagDetail == null) {
                return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(), 400, "unable to fetch tag details for controller." + Constants.CONTROLLER_TAG, null, true);
            }

            String serviceName = instanceRepo.getServiceDetailsWithInstanceIdentifier(account.getIdentifier(), compInstanceDetails.getIdentifier())
                    .stream()
                    .map(BasicEntity::getName)
                    .distinct()
                    .collect(Collectors.joining(","));


            String categoryName = category.getIdentifier();

            forensicData.setCategory(categoryName);
            forensicData.setService(serviceName);
            LinkedHashMap<String, String> forensicValMap = new LinkedHashMap<>();
            forensicValMap.put("Component Instance", compInstanceDetails.getInstanceName());
            forensicValMap.put("Component Name", compInstanceDetails.getComponentName());
            forensicValMap.put("Component Type", compInstanceDetails.getComponentTypeName());
            forensicValMap.put("Category", categoryName);
            forensicValMap.put("Host Address", compInstanceDetails.getHostAddress());
            forensicData.setHeader(forensicValMap);

            ForensicData forensicDataTemp = getForensicCommandsOutput(account.getIdentifier(), compInstanceDetails.getIdentifier(), category.getIdentifier(), fromTimestamp, false, kpiIdentifier, fromTime);
            if (forensicDataTemp != null) {
                forensicData.setCommandOutput(forensicDataTemp.getCommandOutput());
                forensicData.setForensicExecuteMessage(forensicDataTemp.getForensicExecuteMessage().trim());
                if (forensicDataTemp.getHeader() != null) {
                    forensicData.getHeader().putAll(forensicDataTemp.getHeader());
                }
            }

            responseObject = CommonUtils.getGenericResponse(response, StatusResponse.SUCCESS.name(), 200, "", null, false);
            responseObject.setData(forensicData);
            return responseObject;
        } catch (Exception e) {
            logger.error("Error occurred while fetching Forensic list", e);
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(), 400, e.getMessage(), e, true);
        }
    }

    public static ForensicData getForensicCommandsOutput(String accId, String compInstanceId, String categoryId, Timestamp forensicTriggerTime, boolean isFullContent, String kpiIdentifier, long inputTime) {
        ForensicData forensicData = new ForensicData();

        List<ForensicCommandOutputData> finalCommandOutputDataList = new ArrayList<>();
        try {
            List<ForensicPojo> forensicPojo = new ForensicSearchRepo().getForensicDataByInstanceCategoryAndTriggerTime(accId, compInstanceId, categoryId, forensicTriggerTime, kpiIdentifier, FORENSIC_LEVEL);

            if (forensicPojo == null) {
                logger.info("There was no forensic found for the inst: {}, category: {}, timestamp: {}", compInstanceId,
                        categoryId, forensicTriggerTime);
                return null;
            }

            for (ForensicPojo pojo : forensicPojo) {
                List<ForensicCommandOutputData> commandOutputDataList;
                String cmdOutputStr = pojo.getCommandOutput();

                if(cmdOutputStr == null || cmdOutputStr.isEmpty()) {
                    logger.debug("Output of the forensics command is either NULL/EMPTY or exit code is not 0. Sending the error output from the command.");
                    cmdOutputStr = pojo.getStdErr() != null ? pojo.getStdErr() : "NA";
                }

                byte[] decoded = Base64.getDecoder().decode(cmdOutputStr);
                String decompressStr = ForensicServices.decompress(decoded);

                if(pojo.getCommandOutput() == null || !pojo.getCommandOutput().isEmpty()) {
                    commandOutputDataList = Stream.of(decompressStr.split(Constants.COMMAND_OUTPUT_SPLITER))
                            .filter(s -> !s.trim().isEmpty())
                            .map(s -> {
                                Map<String, String> kevValue =
                                        Stream.of(s.split(Constants.COMMAND_DATA_SPLITER))
                                                .map(l -> l.split(Constants.NAME_VALUE_SPLITER, 2))
                                                .collect(Collectors.toMap(str -> str[0], str -> str[1]));
                                String cmdOutput = kevValue.getOrDefault("Output", "NA").trim();
                                boolean isMoreData = cmdOutput.length() >= cmdOutputLength;
                                return new ForensicCommandOutputData(
                                        kevValue.getOrDefault("Description", "NA").trim(),
                                        kevValue.getOrDefault("Name", "NA").trim(),
                                        convertSecondsToZero(kevValue.getOrDefault("Start Time", "NA").trim()),
                                        cmdOutput.substring(0, isMoreData && !isFullContent ? cmdOutputLength : cmdOutput.length()),
                                        isMoreData,
                                        Constants.EXTENSION_TYPE);
                            })
                            .filter(command -> !command.getTime().equals("NA"))
                            .collect(Collectors.toList());
                }
                else {
                    String time = DateTimeUtil.convertEpochToFormattedTime(inputTime);
                    String formattedTime = convertSecondsToZero(time);
                    String title;
                    if(pojo.getMetadata().get("Type").equals("External_Forensics")) {
                        title = pojo.getMetadata().get("TargetCategoryId");
                    } else {
                        title = pojo.getMetadata().get("CategoryName");
                    }
                    commandOutputDataList = Collections.singletonList(new ForensicCommandOutputData(title, "NA", formattedTime, decompressStr, false, Constants.EXTENSION_TYPE));
                }

                finalCommandOutputDataList.addAll(commandOutputDataList);
                //ForensicExecuteMessage forensicStatus =getForensicExecuteMessage(exitCode);
                if (pojo.getMetadata().containsKey("TriggerSource")
                        && !pojo.getMetadata().get("TriggerSource").isEmpty()) {
                    List<InstallationAttributes> installationAttributesList = new MasterRepo().getInstallationAttributes();
                    Map<String, String> installAttributeMap = installationAttributesList.parallelStream()
                            .collect(Collectors.toMap(InstallationAttributes::getName, InstallationAttributes::getValue));
                    forensicData.setHeader(getExtraFieldForForensicHeader(pojo, installAttributeMap.getOrDefault(
                            "show.forensic.execution.time", "0")));
                }
            }
            forensicData.setForensicExecuteMessage(" ");
            forensicData.setCommandOutput(finalCommandOutputDataList);

        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            logger.error("Error occurred while fetching forensic command output for instId : {}, categoryId: {} - ", compInstanceId, categoryId, e);
        }
        return forensicData;
    }

    private static String convertSecondsToZero(String originalDateTimeString) {
        if (originalDateTimeString.equals("NA")) {
            return "NA";
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd-yyyy, HH:mm:ss");
        LocalDateTime originalDateTime = LocalDateTime.parse(originalDateTimeString, formatter);

        // Set seconds to 00
        LocalDateTime modifiedDateTime = originalDateTime.withSecond(0);

        return formatter.format(modifiedDateTime);
    }

    public static LinkedHashMap<String, String> getExtraFieldForForensicHeader(ForensicPojo forensicPojo,
                                                                               String installAttributeVal) {
        LinkedHashMap<String, String> forensicHeader = new LinkedHashMap<>();
        forensicHeader.put("Source", forensicPojo.getMetadata().get("TriggerSource"));
        if (forensicPojo.getMetadata().containsKey("ThresholdType")) {
            forensicHeader.put("Threshold Type", forensicPojo.getMetadata().get("ThresholdType").equals("SOR") ? "Static" : "Realtime");
        }
        String violationConfig = "";
        if (forensicPojo.getMetadata().containsKey("persistence") && !forensicPojo.getMetadata().get("persistence").isEmpty()) {
            violationConfig += "Persistence- " + forensicPojo.getMetadata().get("persistence") + ", ";
        }
        if (forensicPojo.getMetadata().containsKey("suppression") && !forensicPojo.getMetadata().get("suppression").isEmpty()) {
            violationConfig += "Suppression- " + forensicPojo.getMetadata().get("suppression") + ", ";
        }
        if (!violationConfig.isEmpty()) {
            violationConfig += "Violation Type: " + forensicPojo.getMetadata().getOrDefault("violationLevel", "");
            forensicHeader.put("Violation Config", violationConfig);
        }

        if (forensicPojo.getMetadata().containsKey("Operation")) {
            switch (forensicPojo.getMetadata().get("Operation")) {
                case "greater than":
                case "lesser than":
                    forensicHeader.put("Threshold", forensicPojo.getMetadata().get("Operation") + " " + forensicPojo.getMetadata().getOrDefault("Lower", "") + ", Value: " + forensicPojo.getMetadata().getOrDefault("KPIValue", ""));
                    break;
                case "not between":
                    forensicHeader.put("Threshold", forensicPojo.getMetadata().get("Operation") + " " + forensicPojo.getMetadata().getOrDefault("Lower", "") + " & " + forensicPojo.getMetadata().getOrDefault("Upper", "") + ", Value: " + forensicPojo.getMetadata().getOrDefault("KPIValue", ""));
                    break;
            }
        }
        if (installAttributeVal.equals("1") && forensicPojo.getMetadata().containsKey(
                "CommandExecutionTime")) {
            try {
                ForensicExecutionBean forensicExecutionBean = convertMillisecondsToHumanReadable(Long.parseLong(forensicPojo.getMetadata().get("CommandExecutionTime")));
                forensicHeader.put("Execution Time (" + forensicExecutionBean.getUnit() + ")", forensicExecutionBean.getValue());
            } catch (Exception ex) {
                logger.error("error occurred while converting forensic execution time {}", ex.getMessage(), ex);
            }
        }

        return forensicHeader;
    }

    private static ForensicExecutionBean convertMillisecondsToHumanReadable(long milliseconds) {
        long seconds = TimeUnit.MILLISECONDS.toSeconds(milliseconds);

        if (seconds >= 60) {
            long minutes = TimeUnit.SECONDS.toMinutes(seconds);
            long remainingSeconds = seconds % 60;

            return ForensicExecutionBean.builder()
                    .value(String.format("%d:%02d", minutes, remainingSeconds))
                    .unit("minute")
                    .build();
        } else {
            return ForensicExecutionBean.builder()
                    .value(String.format("%d", seconds))
                    .unit("seconds")
                    .build();
        }
    }

    public void downloadForensicDetails(Request request, Response response) {
        logger.trace("Called API {} at time {}", request.pathInfo(), System.currentTimeMillis());
        try {
            String identifier = request.params(":identifier");
            String instanceId = request.params(":instanceId");
            String categoryId = request.params(":categoryId");
            String fromTimeString = request.queryParams("time");
            String commandName = request.queryParams("commandName");
            String kpiId = request.queryParams("kpiId");
            String filename = request.queryParams("file");

            long fromTime = Long.parseLong(fromTimeString);
            Timestamp fromTimestamp = new Timestamp(fromTime);
            com.heal.configuration.pojos.Account account = new AccountRepo().getAccount(identifier);
            if (account == null) {
                logger.error("Invalid account id:{} provided.", identifier);
            } else {
                List<CompInstClusterDetails> compInstanceList = MasterDataService.getCompInstanceDetails(account.getId());
                CompInstClusterDetails compInstanceDetails = null;
                if (compInstanceList != null)
                    compInstanceDetails = compInstanceList.stream().filter(c -> c.getInstanceId() == (Integer.parseInt(instanceId))).findAny().orElse(null);
                if (compInstanceDetails == null) {
                    throw new Exception("Invalid compInstanceId for given compInstanceId" + instanceId);
                }
                CompInstKpiEntity kpiDetailByAccInstKpiIdentifier = new KpiRepo().getKpiDetailByAccInstKpiId(identifier, compInstanceDetails.getIdentifier(), Long.parseLong(kpiId));
                if (kpiDetailByAccInstKpiIdentifier == null) {
                    throw new Exception("Invalid kpiId provided in the URL" + kpiId);
                }

                String kpiIdentifier = kpiDetailByAccInstKpiIdentifier.getIdentifier();

                Category category = new CategoryRepo().getCategoryDetails(account.getIdentifier()).parallelStream().filter(c -> c.getId() == Integer.parseInt(categoryId))
                        .findAny().orElse(null);
                if (category == null) {
                    throw new Exception("Invalid category for given categoryId " + categoryId);
                }
                ForensicData forensicData = getForensicCommandsOutput(account.getIdentifier(), compInstanceDetails.getIdentifier(), category.getIdentifier(), fromTimestamp, true, kpiIdentifier, fromTime);
                ForensicCommandOutputData commandOutputData;

                if (forensicData != null) {

                    commandOutputData = forensicData.getCommandOutput().stream().filter(
                            co -> co.getName().equalsIgnoreCase(commandName)).findAny().orElse(null);

                    if (commandOutputData != null) {

                        response.header("Content-disposition", "attachment; filename=" + filename + ";");
                        OutputStream outputStream = response.raw().getOutputStream();
                        outputStream.write(commandOutputData.getContents().getBytes());
                        outputStream.flush();

                    }
                }
            }
        } catch (Exception e) {
            logger.error("Error while downloading file.", e);
        }
    }

    public static GenericResponse<Boolean> getConfigDetails(Request request, Response response) {
        logger.trace(Constants.INVOKED_METHOD + "getForensicList");
        GenericResponse<Boolean> responseObject;

        try {
            String accountIdString = request.params(":identifier");
            String serviceIdString = request.params("serviceId");

            long start = System.currentTimeMillis();
            com.heal.configuration.pojos.Account account = new AccountRepo().getAccount(accountIdString);
            logger.debug("Time taken to validate account: {} ms.", (System.currentTimeMillis() - start));
            if (account == null) {
                return CommonUtils.getGenericResponse(response, StatusResponse.SUCCESS.name(), 400, "Invalid account id " + accountIdString, null, true);
            }

            /*Check the service Id in Controller table*/
            int serviceId = Integer.parseInt(serviceIdString);
            start = System.currentTimeMillis();
            BasicEntity service = new ServiceRepo().getBasicServiceDetailsWithServiceId(account.getIdentifier(), serviceId);
            logger.debug("Time taken to validate service: {} ms.", (System.currentTimeMillis() - start));
            if (service == null) {
                return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(), 403, "Invalid service id provided.", null, true);
            }

            start = System.currentTimeMillis();
            //TODO: Get the tag id from tag name instead of hard code value 1
            int transactionsCount = TransactionDataService.getTxnCountForService(account.getId(), serviceIdString, Constants.TRANSACTION_TABLE, 1);
            logger.debug("There are {} transactions mapped to service id: {}. Time taken to get txn count is {} ms.",
                    transactionsCount, serviceId, (System.currentTimeMillis() - start));

            responseObject = new GenericResponse<>();
            responseObject.setResponseStatus(StatusResponse.SUCCESS.toString());
            responseObject.setMessage(Constants.SUCCESS_MESSAGE);
            responseObject.setData((transactionsCount > 0));
            response.status(Constants.SUCCESS_STATUS_CODE);
            return responseObject;
        } catch (Exception e) {
            logger.error("Error occurred while fetching config detail", e);
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(), 400, e.getMessage(), e, true);
        }
    }

    private static String decompress(byte[] compressed) {
        StringBuilder sb = new StringBuilder();
        try (ByteArrayInputStream bis = new ByteArrayInputStream(compressed);
             GZIPInputStream gis = new GZIPInputStream(bis);
             BufferedReader br = new BufferedReader(new InputStreamReader(gis, StandardCharsets.UTF_8))) {
            int intC;
            //To prevent DOS attack, we are processing one character at a time instead of entire line
            while ((intC = br.read()) != -1) {
                char c = (char) intC;
                if (c != '\n' && c != '\r') {
                    sb.append(c);
                } else if (c == '\n') {
                    sb.append("\n");
                }
            }
        } catch (Exception e) {
            logger.error("Error occurred while decompressing file.", e);
        }
        return sb.toString();
    }

    public GenericResponse<List<KpiAnomalyData>> forensicGridData(Request request, Response response) {
        logger.trace(Constants.INVOKED_METHOD + "forensicGridData");
        GenericResponse<List<KpiAnomalyData>> responseObject = new GenericResponse<>();
        try {
            ForensicGridDataRequest forensicGridDataRequest = new ForensicGridDataRequest(request, response);
            GenericResponse<List<KpiAnomalyData>> validationResponse = forensicGridDataRequest.validateAndPopulate();
            if (validationResponse != null) {
                return validationResponse;
            }

            boolean isCluster = forensicGridDataRequest.getCompInstanceDetails().isCluster();
            boolean isGroup = (forensicGridDataRequest.getKpiData().getGroupStatus() == 1);

            String attributeName;
            if (!isGroup)
                attributeName = Constants.CASSANDRA_ALL_IDENTIFIER;
            else
                attributeName = forensicGridDataRequest.getGroupValue();

            AnomalySearchRepo anomalySearchRepo = new AnomalySearchRepo();
            List<Anomalies> anomalies = anomalySearchRepo.getAnomaliesByKpi(forensicGridDataRequest.getAccount().getIdentifier(),
                    forensicGridDataRequest.getCompInstanceDetails().getIdentifier(), forensicGridDataRequest.getKpiData().getId(),
                    attributeName, ANOMALY_COUNT, forensicGridDataRequest.getFromTime(), forensicGridDataRequest.getToTime());

            List<KpiAnomalyData> violationRawData = anomalies.stream()
                    .map(row -> new KpiAnomalyData()
                            .setTime(row.getAnomalyTime())
                            .setValue(row.getValue())
                            .setLowerThreshold(row.getThresholds().get("Lower"))
                            .setMaxThreshold(row.getThresholds().get("Upper"))
                            .setInstanceId(forensicGridDataRequest.getCompInstanceDetails().getId())
                            .setInstanceName(forensicGridDataRequest.getCompInstanceDetails().getName())
                            .setOperationType(row.getOperationType()))
                    .collect(Collectors.toList());
            List<KpiAnomalyData> violationData;

            // If the given instance is a cluster then we need to process violations and forensic of all it's instances
            // only
            if (isCluster) {
                violationData = violationRawData;
            } else {
                //Check if there are forensic available for given anomalies
                violationData = getForensicCollectedTimeData(forensicGridDataRequest.getAccount().getIdentifier(),
                        forensicGridDataRequest.getCompInstanceDetails().getIdentifier(), anomalies.get(0).getCategoryId(),
                        String.valueOf(forensicGridDataRequest.getKpiData().getId()), attributeName, forensicGridDataRequest.getFromTime(),
                        forensicGridDataRequest.getToTime(), violationRawData);
            }

            //Re-sort required as it has additional instances related information added to it
            violationData.sort(Comparator.comparing(KpiAnomalyData::getTime));

            responseObject.setMessage("SUCCESS")
                    .setResponseStatus(StatusResponse.SUCCESS.toString())
                    .setData(violationData);
            response.status(Constants.SUCCESS_STATUS_CODE);

        } catch (Exception e) {
            response.status(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            responseObject.setMessage("Internal error")
                    .setResponseStatus(StatusResponse.FAILURE.toString())
                    .setData(new ArrayList<>());
            logger.error("Error occurred while fetching forensic grid data.", e);
        }
        return responseObject;
    }

    public List<KpiAnomalyData> getForensicCollectedTimeData(String accountIdentifier, String instanceIdentifier, String categoryIdentifier,
                                                             String kpiId, String kpiAttribute, long fromTime, long toTime,
                                                             List<KpiAnomalyData> anomalyDataList) {
        logger.trace(Constants.INVOKED_METHOD + "getForensicCollectedTimeData with params accountId: {}, instanceId: {}, " +
                "from: {}, to: {}", accountIdentifier, instanceIdentifier, fromTime, toTime);
        List<KpiAnomalyData> result = new ArrayList<>(anomalyDataList);
        try {

            //apply look ahead and look back for forensic action
            long forensicRelaxationMillisAhead = 60000L * FORENSIC_ACTION_SEARCH_LOOKAHEAD_OFFSET;
            long forensicRelaxationMillisBack = 60000L * FORENSIC_ACTION_SEARCH_LOOKBACK_OFFSET;
            long fromTimeRelaxed = fromTime - forensicRelaxationMillisBack;
            long toTimeRelaxed = toTime + forensicRelaxationMillisAhead;

            List<ForensicPojo> forensicData = new ForensicSearchRepo().getForensicDetails(accountIdentifier, instanceIdentifier, categoryIdentifier, kpiId, kpiAttribute, fromTimeRelaxed, toTimeRelaxed, FORENSIC_LEVEL)
                    .stream()
                    .sorted(Comparator.comparing(ForensicPojo::getForensicTriggerTime))
                    .collect(Collectors.toList());

            mapKpiAnomalyToForensic(result, forensicData, forensicRelaxationMillisAhead);

        } catch (Exception e) {
            logger.error("Error occurred while trying to fetch forensic related", e);
        }
        return result;
    }

    public static void mapKpiAnomalyToForensic(List<KpiAnomalyData> kpiList, List<ForensicPojo> forensicList,
                                               long forensicRelaxationMillisAhead) {
        for (KpiAnomalyData kpiData : kpiList) {
            try {
                // look ahead time to not look for forensic greater than this time.
                long startTimePlusOneMinute = kpiData.getTime() + forensicRelaxationMillisAhead;
                if (forensicList != null && !forensicList.isEmpty()) {
                    logger.debug("Total forensic found: {}, lookahead time: {}, anomaly time: {}", forensicList.size(),
                            startTimePlusOneMinute, kpiData.getTime());

                    /* filter to get all forensic that matches condition for forensic >= anomaly time & forensic <=
                    lookahead time. min with comparator is added to find the closest forensic to the anomaly time. */

                    Optional<ForensicPojo> closestMatch = forensicList.stream()
                            .filter(forensic -> isWithinTimeRange(forensic.getCommandStartTime(), kpiData.getTime(), startTimePlusOneMinute))
                            .min(Comparator.comparingLong(f -> Math.abs(kpiData.getTime() - f.getCommandStartTime())));

                    closestMatch.ifPresent(matchedForensic -> {
                        kpiData.setForensicAvailable(true);
                        kpiData.setForensicTime(matchedForensic.getForensicTriggerTime());
                    });
                } else {
                    logger.debug("No forensic data found.");
                    // If forensicList is empty or null, set forensicAvailable to false
                    kpiData.setForensicAvailable(false);
                }
            } catch (Exception ex) {
                logger.error("Error happened while doing the mapping of anomaly with forensic {}", ex.getMessage());
            }
        }
    }

    private static boolean isWithinTimeRange(Long timestamp, long anomalyTime, long startTimePlusOneMinute) {
        /* check for the forensic time should be equal or greater and anomaly time
         and check for forensic time should be less than or equals look ahead time */
        logger.debug("Forensic time is {}, Anomaly time is {}, lookahead time is {}", timestamp, anomalyTime,
                startTimePlusOneMinute);
        return timestamp >= anomalyTime && timestamp <= startTimePlusOneMinute;
    }

}

