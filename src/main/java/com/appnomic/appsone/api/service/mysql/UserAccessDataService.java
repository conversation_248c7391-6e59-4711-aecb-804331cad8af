package com.appnomic.appsone.api.service.mysql;

import com.appnomic.appsone.api.beans.RoutesInformation;
import com.appnomic.appsone.api.beans.UserAccessBean;
import com.appnomic.appsone.api.beans.UserAttributesBean;
import com.appnomic.appsone.api.dao.mysql.entity.UserDetailsBean;
import com.appnomic.appsone.api.custom.exceptions.AppsoneException;
import com.appnomic.appsone.api.dao.UserAccessDao;
import com.appnomic.appsone.api.manager.MySQLConnectionManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.List;

public class UserAccessDataService {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserAccessDataService.class);
    private static final String ERROR_MESSAGE = "Exception while getting Users. {} ";

    public static UserAccessBean getUserAccessDetails(String userIdentifier) throws AppsoneException {
        UserAccessDao userAccessDao = MySQLConnectionManager.getInstance().open(UserAccessDao.class);

        try {
            return userAccessDao.getUserAccessDetails(userIdentifier);
        } catch (Exception e) {
            LOGGER.error("Exception encountered while fetching action names. Reason: {}", e.getMessage(), e);
            throw new AppsoneException("Exception encountered while fetching action names");
        } finally {
            MySQLConnectionManager.getInstance().close(userAccessDao);
        }
    }

    public UserAttributesBean getUserAttributeDetails(String userIdentifier) throws AppsoneException {
        UserAccessDao userAccessDao = MySQLConnectionManager.getInstance().open(UserAccessDao.class);

        try {
            return userAccessDao.getRoleProfileInfoForUserId(userIdentifier);
        } catch (Exception e) {
            LOGGER.error("Exception encountered while fetching user role and profile. Reason: {}", e.getMessage(), e);
            throw new AppsoneException("Exception encountered while fetching user role and profile");
        } finally {
            MySQLConnectionManager.getInstance().close(userAccessDao);
        }
    }

    public List<RoutesInformation> getAccessibleRoutesForUser(int profileId) throws AppsoneException {
        UserAccessDao userAccessDao = MySQLConnectionManager.getInstance().open(UserAccessDao.class);

        try {
            return userAccessDao.getAccessibleRoutesForUser(profileId);
        } catch (Exception e) {
            LOGGER.error("Exception encountered while fetching action names. Reason: {}", e.getMessage(), e);
            throw new AppsoneException("Exception encountered while fetching action names");
        } finally {
            MySQLConnectionManager.getInstance().close(userAccessDao);
        }
    }

    public static String getUserIdentifierFromName(String username) throws AppsoneException {
        UserAccessDao userAccessDao = MySQLConnectionManager.getInstance().open(UserAccessDao.class);

        try {
            return userAccessDao.getUserIdentifierFromName(username);
        } catch (Exception e) {
            LOGGER.error("Error while getting user profile mapping details from DB", e);
            throw new AppsoneException("Error in fetching user profile mapping");
        } finally {
            MySQLConnectionManager.getInstance().close(userAccessDao);
        }
    }

    public List<String> getUserAccessibleActions(int profileId) throws AppsoneException {
        UserAccessDao userAccessDao = MySQLConnectionManager.getInstance().open(UserAccessDao.class);

        try {
            return userAccessDao.getUserAccessibleActions(profileId);
        } catch (Exception e) {
            LOGGER.error("Exception encountered while fetching user attributes information. Reason: {}", e.getMessage(), e);
            throw new AppsoneException("Exception encountered while fetching user attributes information");
        } finally {
            MySQLConnectionManager.getInstance().close(userAccessDao);
        }
    }

    public static List<UserDetailsBean> getUsers() {
        UserAccessDao userDao = MySQLConnectionManager.getInstance().open(UserAccessDao.class);
        try {
            List<UserDetailsBean> users = userDao.getUsers();
            if (users == null) {
                return Collections.emptyList();
            }
            return users;
        } catch (Exception e) {
            LOGGER.error(ERROR_MESSAGE, e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(userDao);
        }
        return Collections.emptyList();
    }
}

