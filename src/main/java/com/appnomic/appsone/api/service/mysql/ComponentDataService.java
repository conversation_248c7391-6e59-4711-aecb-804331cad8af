package com.appnomic.appsone.api.service.mysql;
import com.appnomic.appsone.api.beans.MasterComponentBean;
import com.appnomic.appsone.api.beans.MasterComponentTypeBean;
import com.appnomic.appsone.api.dao.MasterDataDao;
import lombok.extern.slf4j.Slf4j;
import java.util.ArrayList;
import java.util.List;
/**
 * <AUTHOR> Suman - 07-05-2024
 */
@Slf4j
public class ComponentDataService extends AbstractDaoService<MasterDataDao> {
    public List<MasterComponentBean> getAllComponents(int accountId) {
        MasterDataDao masterDataDao = getDaoConnection(null, MasterDataDao.class);
        try {
            return masterDataDao.getComponentByAccountId(accountId);
        } catch (Exception e) {
            log.error("Error occurred while fetching all components from database.", e);
        } finally {
            closeDaoConnection(null, masterDataDao);
        }
        return new ArrayList<>();
    }
    public List<MasterComponentTypeBean> getAllComponentTypes(int accountId) {
        MasterDataDao masterDataDao = getDaoConnection(null, MasterDataDao.class);
        try {
            return masterDataDao.getMasterComponentTypesData(accountId);
        } catch (Exception e) {
            log.error("Error occurred while fetching all component types from database.", e);
        } finally {
            closeDaoConnection(null, masterDataDao);
        }
        return new ArrayList<>();
    }
}