package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.businesslogic.TransactionNonTimeSeriesDataBL;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.TransactionNonTimeSeriesDataRequest;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

import java.util.Map;

@Slf4j
public class TransactionNonTimeSeriesDataService {

    public GenericResponse<Map<String, String>> getTransactionNtsData(Request request, Response response) {
        log.trace("{} getTransactionNtsData()", Constants.INVOKED_METHOD);
        long st = System.currentTimeMillis();
        GenericResponse<Map<String, String>> genericResponse = new GenericResponse<>();
        try {
            RequestObject requestObject = new RequestObject(request);
            TransactionNonTimeSeriesDataBL ntsBL = new TransactionNonTimeSeriesDataBL();

            UtilityBean<TransactionNonTimeSeriesDataRequest> ntsBeanCV = ntsBL.clientValidation(requestObject);
            UtilityBean<TransactionNonTimeSeriesDataRequest> ntsBeanSV = ntsBL.serverValidation(ntsBeanCV);
            Map<String, String> result = ntsBL.processData(ntsBeanSV);
            genericResponse.setData(result);

            genericResponse.setResponseStatus(StatusResponse.SUCCESS.name());
            genericResponse.setMessage(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);

        } catch (DataProcessingException de) {

            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while processing get request for transaction non-time-series data.", de);
            CommonUtils.populateErrorResponse(genericResponse, response, de.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);

        } catch (ServerException se) {

            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while server validation for transaction non-time-series data.", se);
            CommonUtils.populateErrorResponse(genericResponse, response, se.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);

        } catch (ClientException ce) {

            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while client validation for transaction non-time-series data.", ce);
            CommonUtils.populateErrorResponse(genericResponse, response, ce.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);

        } catch (Exception e) {

            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Exception for transaction non-time-series data.", e);
            CommonUtils.populateErrorResponse(genericResponse, response, e.getMessage(), 500);

        } finally {

            log.debug("Total time taken to load transaction non-time-series data.: {}", System.currentTimeMillis() - st);

        }
        return genericResponse;
    }
}
