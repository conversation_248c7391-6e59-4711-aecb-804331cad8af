package com.appnomic.appsone.api.service.scheduler;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.util.ConfProperties;
import com.google.common.util.concurrent.AbstractScheduledService;

import java.util.concurrent.TimeUnit;

public class HealthMetricsScheduler extends AbstractScheduledService {

    @Override
    protected void runOneIteration() {
        HealUICache.INSTANCE.logHealthMetrics();
        HealUICache.INSTANCE.resetStatusCodes();
        HealUICache.INSTANCE.resetSlowRequestDetails();
//        HealUICache.INSTANCE.resetRedisCallCount();
    }

    @Override
    protected Scheduler scheduler() {
        long schedulerPeriodInMS = ConfProperties.getInt(Constants.HEAL_METRICS_SCHEDULER_PROPERTY_NAME, Constants.HEAL_METRICS_SCHEDULER_PROPERTY_NAME_DEFAULT_VALUE);
        return Scheduler.newFixedRateSchedule(1000L, schedulerPeriodInMS, TimeUnit.MILLISECONDS);
    }
}
