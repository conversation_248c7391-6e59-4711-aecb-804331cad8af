package com.appnomic.appsone.api.service.mysql;

import com.appnomic.appsone.api.beans.TimezoneDetail;
import com.appnomic.appsone.api.beans.UserAttributeBean;
import com.appnomic.appsone.api.dao.TimezoneDao;
import com.appnomic.appsone.api.pojo.TagMappingDetails;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class TimezoneDataService extends AbstractDaoService<TimezoneDao> {
    private static final Logger LOGGER = LoggerFactory.getLogger(TimezoneDataService.class);

    public List<TimezoneDetail> getAllTimezones() {
        TimezoneDao timezoneDao = getDaoConnection(null, TimezoneDao.class);
        try {
            return timezoneDao.getAllTimezones();
        } catch (Exception e) {
            LOGGER.error("Error occurred while fetching all time zones from database.", e);
        } finally {
            closeDaoConnection(null, timezoneDao);
        }
        return new ArrayList<>();
    }

    public TimezoneDetail getTimezonesById(String id) {
        TimezoneDao timezoneDao = getDaoConnection(null, TimezoneDao.class);
        try {
            return timezoneDao.getTimezonesById(id);
        } catch (Exception e) {
            LOGGER.error("Error occurred while fetching time zones by id from database", e);
        } finally {
            closeDaoConnection(null, timezoneDao);
        }
        return null;
    }

    public UserAttributeBean getUserAttributes(String userName){
        TimezoneDao timezoneDao = getDaoConnection(null, TimezoneDao.class);
        try {
            return timezoneDao.getUserAttributes(userName);
        } catch (Exception e) {
            LOGGER.error("Error occurred while fetching user attributes from database", e);
        } finally {
            closeDaoConnection(null, timezoneDao);
        }
        return null;
    }

    public UserAttributeBean getUserAttributesForUserIdentifier(String userIdentifier){
        TimezoneDao timezoneDao = getDaoConnection(null, TimezoneDao.class);
        try {
            return timezoneDao.getUserAttributesForUserIdentifier(userIdentifier);
        } catch (Exception e) {
            LOGGER.error("Error occurred while fetching user attributes from database", e);
        } finally {
            closeDaoConnection(null, timezoneDao);
        }
        return null;
    }

    public void updateUserLastLoginTime(String userIdentifier, String lastLoginTime, Handle handle) {
        TimezoneDao timezoneDao = getDaoConnection(null, TimezoneDao.class);
        try {
            LOGGER.debug("Update user last login time column in user_attributes.");
            timezoneDao.updateUserLastLoginTime(userIdentifier, lastLoginTime);
        } catch (Exception e) {
            String ERROR_UPDATE_USER = "Error while updating user : user_attributes";
            LOGGER.error(ERROR_UPDATE_USER, e);
        } finally {
            closeDaoConnection(handle, timezoneDao);
        }
    }

    public int updateUserTimezoneChoice(Integer isTimezoneMychoice, Integer isNotificationsTimezoneMychoice, String username, String updatingUserIdentifier, String timeInGMT, Handle handle){
        TimezoneDao timezoneDao = getDaoConnection(handle, TimezoneDao.class);
        try {
            return timezoneDao.updateUserTimezoneChoice(isTimezoneMychoice, isNotificationsTimezoneMychoice, username, updatingUserIdentifier, timeInGMT);
        } catch (Exception e) {
            LOGGER.error("Error occurred while updating user timezone choice.", e);
        } finally {
            closeDaoConnection(handle, timezoneDao);
        }
        return 0;
    }

    public int getUserTagMappingId(String objectRefTable, Integer objectId, Integer tagId){
        TimezoneDao timezoneDao = getDaoConnection(null, TimezoneDao.class);
        try {
            return timezoneDao.getUserTagMappingId(objectRefTable, objectId, tagId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while updating user timezone choice.", e);
        } finally {
            closeDaoConnection(null, timezoneDao);
        }
        return 0;
    }

    public int addUserTagMapping(TagMappingDetails tagMappingDetails, Handle handle){
        TimezoneDao timezoneDao = getDaoConnection(handle, TimezoneDao.class);
        try {
            return timezoneDao.addUserTagMapping(tagMappingDetails);
        } catch (Exception e) {
            LOGGER.error("Error occurred while adding user to tag mapping", e);
        } finally {
            closeDaoConnection(handle, timezoneDao);
        }
        return -1;
    }

    public TimezoneDetail getTimezoneByUser(String objRefTable, Integer objectId, Integer tagId){
        TimezoneDao timezoneDao = getDaoConnection(null, TimezoneDao.class);
        try {
            return timezoneDao.getTimezoneByUser(objRefTable, objectId, tagId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while fetching user timezone from database", e);
        } finally {
            closeDaoConnection(null, timezoneDao);
        }
        return null;
    }

    public int updateUserTagMapping(Integer  tagKey, String updatedTime, Integer id, String userDetailsId, Handle handle){
        TimezoneDao timezoneDao = getDaoConnection(handle, TimezoneDao.class);
        try {
            return timezoneDao.updateUserTagMapping(tagKey, updatedTime, id, userDetailsId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while updating user timezone from database", e);
        } finally {
            closeDaoConnection(handle, timezoneDao);
        }
        return -1;
    }

    public int deleteUserTagMapping(Integer id, Handle handle){
        TimezoneDao timezoneDao = getDaoConnection(handle, TimezoneDao.class);
        try {
            return timezoneDao.deleteTagMapping(id);
        } catch (Exception e) {
            LOGGER.error("Error occurred while fetching time zones from database", e);
        } finally {
            closeDaoConnection(handle, timezoneDao);
        }
        return 0;
    }
}
