package com.appnomic.appsone.api.service.mysql;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.dao.redis.ServiceRepo;
import com.appnomic.appsone.api.pojo.RowDetails;
import com.appnomic.appsone.api.pojo.TransactionConfigData;
import com.appnomic.appsone.api.pojo.request.TransactionConfigRequest;
import com.appnomic.appsone.api.util.CommonUtils;
import com.heal.configuration.pojos.BasicAgentEntity;
import com.heal.configuration.pojos.BasicEntity;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@NoArgsConstructor
public class TransactionConfigService {
    private static final Logger LOGGER = LoggerFactory.getLogger(TransactionConfigService.class);
    private static final String URL_LITERAL = "url";
    TransactionConfigRequest transactionConfigRequest;

    public TransactionConfigService(Request request, Response response) {
        transactionConfigRequest = new TransactionConfigRequest(request, response);
    }

    public void setTransactionConfigRequest(TransactionConfigRequest transactionConfigRequest) {
        this.transactionConfigRequest = transactionConfigRequest;
    }

    public GenericResponse<TransactionConfigData> getTransactionConfig() {
        LOGGER.trace("{}: getTransactionConfig()", Constants.INVOKED_METHOD);
        GenericResponse<TransactionConfigData> responseObject = new GenericResponse<>();
        try {
            GenericResponse tempResponse = transactionConfigRequest.validateAndPopulate();
            if( tempResponse != null ) {
                return tempResponse;
            }

            responseObject.setResponseStatus(StatusResponse.SUCCESS.toString());
            responseObject.setMessage(Constants.MESSAGE_SUCCESS);
            responseObject.setData(getTransactionAttributes(transactionConfigRequest));
            return responseObject;

        } catch (Exception e) {
            return CommonUtils.getGenericResponse(transactionConfigRequest.getResponse(),
                    StatusResponse.FAILURE.toString(), Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
                    Constants.MESSAGE_INTERNAL_ERROR, e, true);
        }
    }

    protected TransactionConfigData getTransactionAttributes(TransactionConfigRequest transactionConfigRequest) {
        LOGGER.trace("{}: getTransactionAttributes(), PARAM - {}", Constants.INVOKED_METHOD, transactionConfigRequest);
        TransactionConfigData result = new TransactionConfigData();
        Map<String, String> transactionAttributes = new HashMap<>();
        List<RowDetails> txnAttributeDetails = getAttributes(transactionConfigRequest.getTransaction().getId());

        if (txnAttributeDetails != null && !txnAttributeDetails.isEmpty()) {

            txnAttributeDetails.forEach( attributeDetails -> {

                if( attributeDetails.getName() != null && attributeDetails.getName().trim().length() > 0) {

                    transactionAttributes.put(attributeDetails.getName(), attributeDetails.getValue());
                }
                transactionAttributes.put(URL_LITERAL, attributeDetails.getUrl());

            });

        } else {
            LOGGER.warn("Unable to fetch any attribute related details for transaction: {}, with id: {}",
                    transactionConfigRequest.getTransaction().getName(),
                    transactionConfigRequest.getTransaction().getId());
        }


        ServiceRepo serviceRepo = new ServiceRepo();
        BasicEntity serviceDetails = serviceRepo.getBasicServiceDetailsWithServiceId(transactionConfigRequest.getAccountIdentifier(),
                transactionConfigRequest.getTransaction().getServiceId());
        if (serviceDetails == null) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            LOGGER.error("service details is not found for account identifier: {} and service id: {}", transactionConfigRequest.getAccountIdentifier(),
                    transactionConfigRequest.getTransaction().getServiceId());
            return null;
        }

        List<BasicAgentEntity> serviceAgents = serviceRepo.getAgents(transactionConfigRequest.getAccountIdentifier(), serviceDetails.getIdentifier());

        result.setIsJIMEnabled(serviceAgents.parallelStream().anyMatch(e -> e.getType().equalsIgnoreCase("JIMAgent")) ? 1 : 0);

        result.setId(transactionConfigRequest.getTransaction().getId());
        result.setName(transactionConfigRequest.getTransaction().getName());
        result.setPattern(transactionAttributes);
        return result;
    }

    protected List<RowDetails> getAttributes(int txnId) {
        return MasterDataService.getTransactionList(txnId);
    }
}
