package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.businesslogic.ApplicationServiceInstanceBL;
import com.appnomic.appsone.api.businesslogic.ApplicationServicesBL;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.ServiceInstanceDetailsResponse;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.List;

public class ApplicationDataService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ApplicationDataService.class);

    public GenericResponse<List<ServiceInstanceDetailsResponse>> getApplicationServiceDetails(Request request, Response response) {
        List<ServiceInstanceDetailsResponse> dataBeans;
        ApplicationServicesBL bl = new ApplicationServicesBL();
        try {
            RequestObject requestObject = new RequestObject(request);
            UtilityBean<Object> clientValidation = bl.clientValidation(requestObject);
            UtilityBean<Object> serverValidation = bl.serverValidation(clientValidation);
            dataBeans = bl.processData(serverValidation);
        } catch (ClientException | ServerException | DataProcessingException e) {
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
                    e.getMessage(), null, true);
        }
        GenericResponse<List<ServiceInstanceDetailsResponse>> genericResponse = CommonUtils.getGenericResponse(response, StatusResponse.SUCCESS.name(),
                Constants.SUCCESS_STATUS_CODE, "Application Service Detail(s)", null, false);
        genericResponse.setData(dataBeans);
        return genericResponse;
    }

    public GenericResponse<List<ServiceInstanceDetailsResponse>> getApplicationServiceInstanceDetails(Request request, Response response) {
        List<ServiceInstanceDetailsResponse> dataBeans;
        ApplicationServiceInstanceBL bl = new ApplicationServiceInstanceBL();
        long st = System.currentTimeMillis();
        try {
            RequestObject requestObject = new RequestObject(request);
            UtilityBean<Object> clientValidation = bl.clientValidation(requestObject);
            UtilityBean<Object> serverValidation = bl.serverValidation(clientValidation);
            dataBeans = bl.processData(serverValidation);
        } catch (ClientException | ServerException | DataProcessingException e) {
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
                    e.getMessage(), null, true);
        } finally {
            LOGGER.debug("Time taken for getApplicationServiceInstanceDetails() method is {} ms.", System.currentTimeMillis() - st);
        }
        GenericResponse<List<ServiceInstanceDetailsResponse>> genericResponse = CommonUtils.getGenericResponse(response, StatusResponse.SUCCESS.name(),
                Constants.SUCCESS_STATUS_CODE, "Application Service Instance Detail(s)", null, false);
        genericResponse.setData(dataBeans);
        return genericResponse;
    }
}
