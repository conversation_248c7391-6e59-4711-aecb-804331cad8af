package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.businesslogic.UpdateForensicNotificationConfiguration;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.ForensicNotificationConfiguration;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import spark.Request;
import spark.Response;

public class UpdateForensicNotificationConfigurationService {

    public GenericResponse<String> updateForensicNotificationConfiguration(Request request, Response response) {

        try {
            UpdateForensicNotificationConfiguration bl = new UpdateForensicNotificationConfiguration();
            RequestObject requestObject = new RequestObject(request);

            UtilityBean<ForensicNotificationConfiguration> clientValidation = bl.clientValidation(requestObject);
            ForensicNotificationConfiguration bean = bl.serverValidation(clientValidation);
            String data = bl.processData(bean);

            GenericResponse<String> genericResponse = CommonUtils.getGenericResponse(response,
                    StatusResponse.SUCCESS.name(), Constants.SUCCESS_STATUS_CODE, "Forensic notification " +
                            "configurations for the user updated successfully.", null, false);
            genericResponse.setData(data);
            return genericResponse;

        } catch (ClientException | ServerException | DataProcessingException e) {
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, e.getMessage(), null, true);
        } catch (Exception e) {
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.INTERNAL_SERVER_ERROR_STATUS_CODE, e.getMessage(), null, true);
        }

    }
}
