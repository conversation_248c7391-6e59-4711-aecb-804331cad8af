package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.businesslogic.UserNotificationPreferenceBL;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.NotificationPreferences;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

public class UserNotificationPreferenceService {
    private Logger logger = LoggerFactory.getLogger(UserNotificationPreferenceService.class);

    public GenericResponse<NotificationPreferences> getNotificationPreferences(Request request, Response response) {
        logger.debug("{} getCategories().", Constants.INVOKED_METHOD);
        GenericResponse<NotificationPreferences> genericResponse = new GenericResponse<>();
        try {
            RequestObject requestObject = new RequestObject(request);
            UserNotificationPreferenceBL bl = new UserNotificationPreferenceBL();
            UtilityBean<Object> clientValidation = bl.clientValidation(requestObject);
            UtilityBean<Object> serverValidation = bl.serverValidation(clientValidation);
            genericResponse.setData(bl.processData(serverValidation));
            genericResponse.setResponseStatus(StatusResponse.SUCCESS.name());
            genericResponse.setMessage(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (DataProcessingException de) {
            logger.error("Error occurred while processing data for get user timezone.", de);
            CommonUtils.populateErrorResponse(genericResponse, response, de.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ServerException se) {
            logger.error("Error occurred while server validation for get user timezone.", se);
            CommonUtils.populateErrorResponse(genericResponse, response, se.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ClientException ce) {
            logger.error("Error occurred while client validation for get user timezone.", ce);
            CommonUtils.populateErrorResponse(genericResponse, response, ce.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (Exception e) {
            logger.error("Exception occurred for get user timezone.", e);
            CommonUtils.populateErrorResponse(genericResponse, response, e.getMessage(), 500);
        }
        return genericResponse;
    }
}
