package com.appnomic.appsone.api.service.mysql;

import com.appnomic.appsone.api.dao.ApplicationMigrationDao;
import com.appnomic.appsone.api.manager.MySQLConnectionManager;
import com.heal.configuration.entities.ApplicationMigrationDateRangesBean;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class ApplicationMigrationService {

    public List<ApplicationMigrationDateRangesBean> getApplicationMigrationDateRanges() {
        ApplicationMigrationDao applicationMigrationDao = MySQLConnectionManager.getInstance().open(ApplicationMigrationDao.class);
        try {
            return applicationMigrationDao.getApplicationMigrationDateRanges();
        } catch (Exception e) {
            log.error("Exception while getting application migration date details. Details: ", e);
        } finally {
            MySQLConnectionManager.getInstance().close(applicationMigrationDao);
        }
        return Collections.emptyList();
    }

}
