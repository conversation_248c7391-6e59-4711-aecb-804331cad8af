package com.appnomic.appsone.api.service.mysql;

import com.appnomic.appsone.api.manager.MySQLConnectionManager;
import org.skife.jdbi.v2.Handle;

/**
 * <AUTHOR> on 25/8/20
 */
public class AbstractDaoService<DaoConnection> {

    public DaoConnection getDaoConnection(Handle handle, Class<DaoConnection> dao){
        if(handle == null) {
            return MySQLConnectionManager.getInstance().open(dao);
        }
        else{
            return handle.attach(dao);
        }
    }

    public void closeDaoConnection(Handle handle, DaoConnection dao){
        if(handle == null) {
            MySQLConnectionManager.getInstance().close(dao);
        }
    }
}

