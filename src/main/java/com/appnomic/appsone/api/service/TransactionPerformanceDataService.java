package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.businesslogic.TransactionPerformanceDataBL;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.UIData;
import com.appnomic.appsone.api.pojo.request.TransactionPerformanceDataRequest;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

/**
 * <AUTHOR> on 17/05/22
 */
@Slf4j
public class TransactionPerformanceDataService {

    public GenericResponse<UIData> getTransactionPerformanceData(Request request, Response response) {
        log.trace("{} getTransactionPerformanceData().", Constants.INVOKED_METHOD);
        long st = System.currentTimeMillis();
        GenericResponse<UIData> genericResponse = new GenericResponse<>();
        try {
            TransactionPerformanceDataBL bl = new TransactionPerformanceDataBL();
            RequestObject requestObject = new RequestObject(request);

            UtilityBean<TransactionPerformanceDataRequest> clientData = bl.clientValidation(requestObject);
            UtilityBean<TransactionPerformanceDataRequest> serverData = bl.serverValidation(clientData);
            UIData uiData = bl.processData(serverData);

            genericResponse.setData(uiData);
            genericResponse.setResponseStatus(StatusResponse.SUCCESS.name());
            genericResponse.setMessage(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (DataProcessingException de) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while processing get request for transaction time-series data.", de);
            CommonUtils.populateErrorResponse(genericResponse, response, de.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ServerException se) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while server validation for transaction time-series data.", se);
            CommonUtils.populateErrorResponse(genericResponse, response, se.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ClientException ce) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while client validation for transaction time-series data.", ce);
            CommonUtils.populateErrorResponse(genericResponse, response, ce.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Exception for transaction time-series data.", e);
            CommonUtils.populateErrorResponse(genericResponse, response, e.getMessage(), 500);
        } finally {
            log.debug("Total time taken to load transaction time-series data.: {}", System.currentTimeMillis() - st);
        }
        return genericResponse;
    }

}
