package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.beans.UserNotificationDetails;
import com.appnomic.appsone.api.businesslogic.GetForensicNotificationConfigurations;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.ForensicNotificationConfiguration;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import spark.Request;
import spark.Response;

public class GetForensicNotificationConfigurationsService {

    public GenericResponse<ForensicNotificationConfiguration> getForensicNotificationConfigurations(Request request, Response response) {
        try {
            GetForensicNotificationConfigurations bl = new GetForensicNotificationConfigurations();
            RequestObject requestObject = new RequestObject(request);

            UtilityBean<String> user = bl.clientValidation(requestObject);
            UserNotificationDetails userDetails = bl.serverValidation(user);
            ForensicNotificationConfiguration data = bl.processData(userDetails);

            GenericResponse<ForensicNotificationConfiguration> genericResponse = CommonUtils.getGenericResponse(response,
                    StatusResponse.SUCCESS.name(), Constants.SUCCESS_STATUS_CODE, "Forensic notification " +
                            "configurations for the user fetched successfully.", null, false);
            genericResponse.setData(data);
            return genericResponse;

        } catch (ClientException | ServerException | DataProcessingException e) {
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, e.getMessage(), null, true);
        } catch (Exception e) {
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.INTERNAL_SERVER_ERROR_STATUS_CODE, e.getMessage(), null, true);
        }
    }

}
