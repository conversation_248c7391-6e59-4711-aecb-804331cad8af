package com.appnomic.appsone.api.service.mysql;

import com.appnomic.appsone.api.beans.ControllerBean;
import com.appnomic.appsone.api.dao.TagsDao;
import com.appnomic.appsone.api.manager.MySQLConnectionManager;
import com.appnomic.appsone.api.pojo.TagMappingDetails;
import com.appnomic.appsone.api.pojo.TagPreDefinedData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> : 30/1/19
 */
public class TagsDataService {
    private static final Logger logger = LoggerFactory.getLogger(TagsDataService.class);

    public static void addTagMappingDetails(List<TagMappingDetails> tagMappingDetails, TagsDao tagsDao) {
        try {
            tagsDao.addTagMappingDetails(tagMappingDetails);
        } catch (Exception e) {
            logger.error("Exception while adding tag mapping -" + e.getMessage(), e);
        }
    }

    public static int createNewTag(ControllerBean controllerBean)    {
        TagsDao tagsDaoAtomicConn = MySQLConnectionManager.getInstance().open(TagsDao.class);
        try {
            return tagsDaoAtomicConn.createTagEntry(controllerBean);
        } catch (Exception e)   {
            logger.error("Error occurred while creating new tag"+e.getMessage(), e);
            logger.debug("trace:" , e);
        } finally {
            MySQLConnectionManager.getInstance().close(tagsDaoAtomicConn);
        }
        return -1;
    }

    public static int createNewTag(ControllerBean controllerBean, TagsDao tagsDao)    {
        try {
            return tagsDao.createTagEntry(controllerBean);
        } catch (Exception e)   {
            logger.error("Error occurred while creating new tag"+e.getMessage(), e);
            logger.debug("trace:" , e);
        }
        return -1;
    }

    public static TagPreDefinedData getTagData(String table, String name, String selectSection, String whereSection, int accountId) {
        TagsDao tagsDaoAtomicConn = MySQLConnectionManager.getInstance().open(TagsDao.class);
        logger.debug("Inside getTagData(table:"+table+", name:"+name+", selectSection:"+selectSection+", whereSection:"+whereSection+", accountId:"+accountId+") method.");
        try {

            String whereClause = whereSection.replaceAll(":accountId", accountId+"");
            logger.debug("Where clause:"+whereClause);
            return tagsDaoAtomicConn.getTagData(table, name, selectSection, whereClause);
        } catch (Exception e) {
            logger.error("Exception while tag details data from table" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(tagsDaoAtomicConn);
        }
        return null;
    }

    public static int getTagMappingId(int tagId, int objectId, String objectRefTable, String tagKey, String tagValue, int accountId) {
        TagsDao tagsDaoAtomicConn = MySQLConnectionManager.getInstance().open(TagsDao.class);
        try {
            return tagsDaoAtomicConn.getTagMappingId(tagId, objectId, objectRefTable, tagKey, tagValue, accountId);
        } catch (Exception e) {
            logger.error("Exception while getting tag mapping id from table" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(tagsDaoAtomicConn);
        }
        return 0;
    }

    public static List<TagMappingDetails> getAgentTags(int tagId, String objectRefTable, int accountId) {
        TagsDao tagsDaoAtomicConn = MySQLConnectionManager.getInstance().open(TagsDao.class);
        List<TagMappingDetails> keyList = new ArrayList<>();
        try {
            return tagsDaoAtomicConn.getAgentTags(tagId, objectRefTable, accountId);
        } catch (Exception e) {
            logger.error("Exception while getting tag key  from table" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(tagsDaoAtomicConn);
        }
        return keyList;
    }
}
