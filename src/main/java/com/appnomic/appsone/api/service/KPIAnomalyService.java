package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.AppsoneException;
import com.appnomic.appsone.api.dao.opensearch.AnomalySearchRepo;
import com.appnomic.appsone.api.dao.opensearch.CollatedKpiRepo;
import com.appnomic.appsone.api.dao.redis.AgentRepo;
import com.appnomic.appsone.api.dao.redis.CategoryRepo;
import com.appnomic.appsone.api.dao.redis.KpiRepo;
import com.appnomic.appsone.api.dao.redis.TimeRangeDetailsDao;
import com.appnomic.appsone.api.pojo.*;
import com.appnomic.appsone.api.pojo.request.KPIAnomalyListDataRequest;
import com.appnomic.appsone.api.util.NewTimeIntervalGenerationUtility;
import com.appnomic.appsone.api.util.StringUtils;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.appnomic.appsone.opeasearchquery.results.TabularResults;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.CompInstClusterDetails;
import com.heal.configuration.pojos.Tags;
import com.heal.configuration.pojos.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * <AUTHOR> : 26/05/2019
 */

public class KPIAnomalyService {
    private static final Logger logger = LoggerFactory.getLogger(KPIAnomalyService.class);

    public GenericResponse getSortedKpiList(Request request, Response response) {
        logger.trace("{} getSortedKpiList: {}", Constants.INVOKED_METHOD, request);
        long st = System.currentTimeMillis();

        GenericResponse<List<KpiNames>> responseObject = new GenericResponse<>();
        try {
            KPIAnomalyListDataRequest kpiAnomalyListDataRequest = new KPIAnomalyListDataRequest(request, response);

            long start = System.currentTimeMillis();
            GenericResponse tempResponse = kpiAnomalyListDataRequest.populateAndValidate();
            logger.info("Time taken for validate method in kpis for category is {} ms.", (System.currentTimeMillis() - start));

            String accountIdentifier = kpiAnomalyListDataRequest.getAccount().getIdentifier();

            TimeRangeDetailsDao timeRangeDetailsDao = new TimeRangeDetailsDao();
            List<TimeRangeDetails> timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(accountIdentifier);
            if (timeRangeDetailsList == null) {
                logger.error("Exception occurred while fetching time range details for account {}", accountIdentifier);
                throw new AppsoneException("Exception occurred while fetching time range details for account " + accountIdentifier);
            }

            if (timeRangeDetailsList.isEmpty()) {
                logger.debug("No time range details found for account {}. Searching for Global account.", accountIdentifier);
                timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
            }
            if (timeRangeDetailsList == null) {
                logger.error("Exception occurred while fetching time range details for account {}", Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
                throw new AppsoneException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
            }

            if (timeRangeDetailsList.isEmpty()) {
                logger.error("No time range details found");
                throw new AppsoneException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
            }
            TimeRangeDetails timeRangeDetails = ValidationUtils.getRollupPojo(kpiAnomalyListDataRequest.getFromTime(), kpiAnomalyListDataRequest.getToTime(), timeRangeDetailsList);
            if (timeRangeDetails == null) {
                logger.error("Time range details are invalid.");
                throw new AppsoneException("Time range details are invalid.");
            }
            kpiAnomalyListDataRequest.setTimeRangeDetails(timeRangeDetails);
            kpiAnomalyListDataRequest.setTimeRangeDetailsList(timeRangeDetailsList);

            if (tempResponse != null) return tempResponse;
            start = System.currentTimeMillis();
            responseObject.setData(getSortedKpiListData(kpiAnomalyListDataRequest));
            logger.info("Time taken for process method in kpis for category is {} ms.", (System.currentTimeMillis() - start));
            responseObject.setResponseStatus(StatusResponse.SUCCESS.toString());
            responseObject.setMessage(Constants.MESSAGE_SUCCESS);

        } catch (AppsoneException de) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            logger.error("Exception occurred while getting sorted list of kpis.", de);
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            responseObject.setMessage(de.getMessage());
        } catch (Exception e) {
            logger.error("Error occurred while getting sorted list of kpi's", e);
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            responseObject.setMessage(Constants.MESSAGE_INTERNAL_ERROR);
        } finally {
            logger.info("Time taken for kpis for category is {} ms.", (System.currentTimeMillis() - st));
        }
        return responseObject;
    }

    public List<KpiNames> getSortedKpiListData(KPIAnomalyListDataRequest kpiAnomalyListDataRequest) {
        logger.trace(Constants.INVOKED_METHOD + "getSortedKpiListData: {}", kpiAnomalyListDataRequest);

        Account account = kpiAnomalyListDataRequest.getAccount();
        AnomalySearchRepo anomalySearchRepo = new AnomalySearchRepo();
        CollatedKpiRepo collatedKpiRepo = new CollatedKpiRepo();
        CompInstClusterDetails instanceDetails = kpiAnomalyListDataRequest.getInstanceDetails();
        int categoryId = kpiAnomalyListDataRequest.getCategoryId();
        long fromTime = kpiAnomalyListDataRequest.getFromTime();
        long toTime = kpiAnomalyListDataRequest.getToTime();

        NewTimeIntervalGenerationUtility t = new NewTimeIntervalGenerationUtility();
        t.setTimezoneOffset(account.getIdentifier());
        t.processTimeRange(fromTime, toTime);
        t.addEndTimeInOSTime();

        List<Long> times = t.getOSTimes();

        AggregationLevel aggregationLevel = t.getNewTimeRangeDefinition().getAggregationLevel();
        int aggregationValue = aggregationLevel.getAggregrationValue();

        UIData uiData = new UIData();
        uiData.setAggregationLevel(aggregationValue);
        uiData.setDateFormat(aggregationLevel.getDataTimePattern());
        uiData.setTime(t.getDisplayTimes());

        long st = System.currentTimeMillis();
        //get a list of all the Kpi's for the instance that belong to the category
        AgentRepo agentRepo = new AgentRepo();

        Category category = null;
        if (categoryId != Constants.CONFIG_WATCH_LOGICAL_CATEGORY_ID) {
            category = new CategoryRepo().getCategoryDetails(account.getIdentifier()).parallelStream().filter(c -> c.getId() == categoryId).findAny().orElse(null);
            if (category == null) {
                logger.error("No category details found for category id {}", categoryId);
                return new ArrayList<>();
            }
        }


        List<CompInstKpiEntity> kpiList = new KpiRepo().getKpiDetailByAccInst(kpiAnomalyListDataRequest.getAccount().getIdentifier(),
                kpiAnomalyListDataRequest.getInstanceDetails().getIdentifier());

        List<Integer> groupKpiList = kpiList.parallelStream().filter(BasicKpiEntity::getIsGroup).map(BasicKpiEntity::getId).collect(Collectors.toList());

        //Data exists check Operations----------------
        Map<Integer, AgentValuePojo> nonGroupKpiAgentValueMap = new HashMap<>();
        Map<Integer, Map<String, AgentValuePojo>> groupKpiAgentValueMap = new HashMap<>();

        List<TabularResultsTypePojo> tabularResultsTypePojoList = collatedKpiRepo.getIsDataAvailableInCategory(account.getIdentifier(), instanceDetails.getIdentifier(), fromTime, toTime, kpiAnomalyListDataRequest.getTimeRangeDetails(),
                kpiAnomalyListDataRequest.getTimeRangeDetailsList(), times);

        for (TabularResultsTypePojo entry : tabularResultsTypePojoList) {
            TabularResults tabularResults = entry.getTabularResults();

            if (tabularResults != null && tabularResults.getRowResults() != null && !tabularResults.getRowResults().isEmpty()) {

                for (TabularResults.ResultRow resultRow : tabularResults.getRowResults()) {
                    int kpiId = 0;
                    String groupAttribute = "";
                    List<String> agentIdentifierList = new ArrayList<>();
                    for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {
                        if (resultRowColumn.getColumnName().equalsIgnoreCase("kpiId")) {

                            kpiId = Integer.parseInt(resultRowColumn.getColumnValue());

                        } else if (resultRowColumn.getColumnName().equalsIgnoreCase("groupAttribute")) {

                            groupAttribute = resultRowColumn.getColumnValue();

                        } else if (resultRowColumn.getColumnName().equalsIgnoreCase("UNIQUE" + ": agentIdentifier")) {

                            agentIdentifierList.add(resultRowColumn.getColumnValue());

                        }
                    }

                    //Non Group KPI
                    if (!groupKpiList.contains(kpiId)) {
                        if (nonGroupKpiAgentValueMap.containsKey(kpiId)) {
                            agentIdentifierList.addAll(nonGroupKpiAgentValueMap.get(kpiId).getAgentIdentifierList());
                            nonGroupKpiAgentValueMap.put(kpiId, AgentValuePojo.builder()
                                    .agentIdentifierList(agentIdentifierList.parallelStream().collect(Collectors.toSet()))
                                    .dataExists(nonGroupKpiAgentValueMap.get(kpiId).isDataExists())
                                    .build());
                        } else {
                            nonGroupKpiAgentValueMap.put(kpiId, AgentValuePojo.builder()
                                    .agentIdentifierList(agentIdentifierList.parallelStream().collect(Collectors.toSet()))
                                    .dataExists(resultRow.getCountValue() > 0)
                                    .build());
                        }
                    } else {
                        //Group Kpi
                        Map<String, AgentValuePojo> grpAttributeAgentDetails = new HashMap<>();
                        if (groupKpiAgentValueMap.containsKey(kpiId)) {
                            if (groupKpiAgentValueMap.get(kpiId).containsKey(groupAttribute)) {
                                agentIdentifierList.addAll(groupKpiAgentValueMap.get(kpiId).get(groupAttribute).getAgentIdentifierList());
                                grpAttributeAgentDetails.put(groupAttribute, AgentValuePojo.builder()
                                        .agentIdentifierList(agentIdentifierList.parallelStream().collect(Collectors.toSet()))
                                        .dataExists(resultRow.getCountValue() > 0)
                                        .build());
                            } else {
                                grpAttributeAgentDetails.put(groupAttribute, AgentValuePojo.builder()
                                        .agentIdentifierList(agentIdentifierList.parallelStream().collect(Collectors.toSet()))
                                        .dataExists(resultRow.getCountValue() > 0)
                                        .build());
                            }
                            groupKpiAgentValueMap.get(kpiId).putAll(grpAttributeAgentDetails);
                        } else {
                            grpAttributeAgentDetails.put(groupAttribute, AgentValuePojo.builder()
                                    .agentIdentifierList(agentIdentifierList.parallelStream().collect(Collectors.toSet()))
                                    .dataExists(resultRow.getCountValue() > 0)
                                    .build());
                            groupKpiAgentValueMap.put(kpiId, grpAttributeAgentDetails);
                        }
                    }
                }
            }
        }
        //-------------------------------------------------------------------

        //Anomaly Exists check Operations----------------
        Map<Integer, Long> nonGroupKpiAnomalyCountMap = new HashMap<>();
        Map<Integer, Map<String, Long>> groupKpiAnomalyCountMap = new HashMap<>();

        TabularResults anomaliesTabularResults;
        if (categoryId == Constants.CONFIG_WATCH_LOGICAL_CATEGORY_ID) {
            anomaliesTabularResults = anomalySearchRepo.getAnomaliesCountInCategories(account.getIdentifier(),
                    instanceDetails.getIdentifier(), null, false, true, fromTime, toTime);
        } else {
            anomaliesTabularResults = anomalySearchRepo.getAnomaliesCountInCategories(account.getIdentifier(),
                    instanceDetails.getIdentifier(), category.getIdentifier(), false, false, fromTime, toTime);
        }


        if (anomaliesTabularResults != null && anomaliesTabularResults.getRowResults() != null && !anomaliesTabularResults.getRowResults().isEmpty()) {
            for (TabularResults.ResultRow resultRow : anomaliesTabularResults.getRowResults()) {
                int kpiId = 0;
                String kpiAttribute = "";
                for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {
                    if (resultRowColumn.getColumnName().equalsIgnoreCase("kpiId")) {

                        kpiId = Integer.parseInt(resultRowColumn.getColumnValue());

                    } else if (resultRowColumn.getColumnName().equalsIgnoreCase("kpiAttribute")) {

                        kpiAttribute = resultRowColumn.getColumnValue();

                    }
                }

                //Non Group KPI
                if (!groupKpiList.contains(kpiId)) {
                    nonGroupKpiAnomalyCountMap.put(kpiId, nonGroupKpiAnomalyCountMap.getOrDefault(kpiId, 0L) + resultRow.getCountValue());
                } else {
                    //Group Kpi
                    if (groupKpiAnomalyCountMap.containsKey(kpiId)) {
                        groupKpiAnomalyCountMap.get(kpiId).put(kpiAttribute, groupKpiAnomalyCountMap.get(kpiId).getOrDefault(kpiAttribute, 0L) + resultRow.getCountValue());
                    } else {
                        Map<String, Long> kpiAttributeAnomalyMap = new HashMap<>();
                        kpiAttributeAnomalyMap.put(kpiAttribute, resultRow.getCountValue());
                        groupKpiAnomalyCountMap.put(kpiId, kpiAttributeAnomalyMap);
                    }
                }

            }
        }
        //-------------------------------------------------------------------


        List<KpiNames> filteredKpiListForCategory = kpiList
                .parallelStream()
                .filter(categoryId == Constants.CONFIG_WATCH_LOGICAL_CATEGORY_ID ?
                        kpi -> kpi.getType().equalsIgnoreCase("ConfigWatch") || kpi.getType().equalsIgnoreCase("FileWatch")
                        : kpi -> kpi.getCategoryDetails().getId() == categoryId)
                .filter(kpi -> {
                    if (kpi.getType().equalsIgnoreCase("ConfigWatch") || kpi.getType().equalsIgnoreCase("FileWatch")) {
                        return true;
                    } else return !(kpi.getRollupOperation().getOperation().equalsIgnoreCase("NONE")
                            && kpi.getAggOperation().getOperation().equalsIgnoreCase("NONE") && aggregationValue != 1);
                })
                .map(kpi -> {

                    AtomicLong totalAnomalyCount = new AtomicLong();
                    boolean dataExists = false;
                    String source = Constants.UNKNOWN_SOURCE;
                    List<KpiAttribute> kpiAttributes = new ArrayList<>();
                    if (kpi.getIsGroup()) {
                        Map<String, Long> attributeAnomalyCount = groupKpiAnomalyCountMap.getOrDefault(kpi.getId(), Collections.emptyMap());
                        Map<String, AgentValuePojo> attributeDataCheck = groupKpiAgentValueMap.getOrDefault(kpi.getId(), Collections.emptyMap());

                        Set<String> sourceSet = attributeDataCheck.values().parallelStream()
                                .map(AgentValuePojo::getAgentIdentifierList)
                                .distinct()
                                .flatMap(Collection::parallelStream).collect(Collectors.toSet());
                        source = getSourceName(sourceSet, agentRepo);
                        Set<String> attributes = getAttributesByDiscovery(kpi, attributeDataCheck);

                        kpiAttributes = attributes.stream()
                                .map(s -> {
                                    totalAnomalyCount.addAndGet(attributeAnomalyCount.getOrDefault(s, 0L));
                                    return KpiAttribute.builder()
                                            .name(s)
                                            .aliasName(kpi.getAttributeValues() != null ? kpi.getAttributeValues().getOrDefault(s, s) : s)
                                            .anomalyCount(attributeAnomalyCount.getOrDefault(s, 0L))
                                            .isDataAvailable(attributeDataCheck.containsKey(s) && attributeDataCheck.get(s).isDataExists())
                                            .build();
                                })
                                .collect(Collectors.toList());

                        dataExists = !attributeDataCheck.isEmpty();
                    } else {
                        totalAnomalyCount.set(nonGroupKpiAnomalyCountMap.getOrDefault(kpi.getId(), 0L));

                        KpiAttribute attribute = KpiAttribute.builder()
                                .anomalyCount(totalAnomalyCount.intValue())
                                .name("ALL")
                                .aliasName("ALL")
//                                .isDataAvailable(true)
                                .build();
                        logger.debug("Total anomaly for kpi id {} is {}", kpi.getId(), totalAnomalyCount.get());

                        if (nonGroupKpiAgentValueMap.containsKey(kpi.getId())) {
                            dataExists = nonGroupKpiAgentValueMap.get(kpi.getId()).isDataExists();
                            source = getSourceName(nonGroupKpiAgentValueMap.get(kpi.getId()).getAgentIdentifierList(), agentRepo);
                            logger.debug("source {}, uniqueAgentIdentifiers {}", source, nonGroupKpiAgentValueMap.get(kpi.getId()).getAgentIdentifierList());
                        }

                        attribute.setIsDataAvailable(dataExists);
                        kpiAttributes.add(attribute);
                    }
                    return KpiNames.builder()
                            .id(kpi.getId())
                            .categoryId(kpi.getCategoryDetails().getId())
                            .categoryIdentifier(kpi.getCategoryDetails().getIdentifier())
                            .categoryName(kpi.getCategoryDetails().getName())
                            .status(kpi.getStatus())
                            .isDiscovery(kpi.getDiscovery())
                            .groupId(kpi.getGroupId())
                            .clusterOperation(kpi.getClusterAggType())
                            .name(kpi.getName())
                            .groupName(kpi.getGroupName())
                            .unit(kpi.getUnit())
                            .type(kpi.getType())
                            .anomalyCount(totalAnomalyCount.intValue())
                            .isDataAvailable(kpi.getStatus() == 1 && dataExists) // if KPI will be inactive then we will not display data in UI.
                            .isGroupKpi(kpi.getIsGroup() ? 1 : 0)
                            .attribute(!kpiAttributes.isEmpty() ? kpiAttributes : Collections.emptyList())
                            .description(kpi.getDescription())
                            .source(source)
                            .build();
                })
                .collect(Collectors.toList());

        logger.info("Time taken for get filter kpi list:{} ms.", (System.currentTimeMillis() - st));

        st = System.currentTimeMillis();
        logger.info("Get filter kpi list:{}", (System.currentTimeMillis() - st));

        //merge group instances
        return mergeGroupKpiNames(filteredKpiListForCategory);

    }

    public String getSourceName(Set<String> agentIds, AgentRepo agentRepo) {
        if (agentIds == null || agentIds.isEmpty()) {
            return Constants.UNKNOWN_SOURCE;
        }
        Optional<String> agentSources = agentIds.parallelStream()
                .map(agentRepo::getAgentDetails)
                .filter(Objects::nonNull)
                .map(agent -> {
                    String dataSource = agent.getTags().stream()
                            .filter(Objects::nonNull)
                            .filter(t -> t.getType().equals("DataSources"))
                            .map(Tags::getValue)
                            .distinct()
                            .collect(Collectors.joining(", "));
                    if (StringUtils.isEmpty(dataSource)) {
                        dataSource = agent.getName();
                    }
                    return dataSource;
                })
                .findFirst();

        return agentSources.orElse(Constants.UNKNOWN_SOURCE);
    }

    private Set<String> getAttributesByDiscovery(CompInstKpiEntity kpi, Map<String, AgentValuePojo> attributeDataCheck) {
        if (kpi.getDiscovery() == 1) {
            return attributeDataCheck.keySet();
        } else if (kpi.getAttributeValues() == null && !attributeDataCheck.isEmpty()) {
            return kpi.getInactiveAttributeValues() == null ? Collections.emptySet() : kpi.getInactiveAttributeValues().keySet().parallelStream().filter(c -> !c.equalsIgnoreCase("ALL")).collect(Collectors.toSet());
        } else {
            return kpi.getAttributeValues() == null ? Collections.emptySet() : kpi.getAttributeValues().keySet().parallelStream().filter(c -> !c.equalsIgnoreCase("ALL")).collect(Collectors.toSet());
        }
    }

    private List<KpiNames> mergeGroupKpiNames(List<KpiNames> kpiDetails) {
        long st = System.currentTimeMillis();
        List<KpiNames> result = new ArrayList<>();
        Map<Integer, KpiNames> map = new HashMap<>();

        try {
            if (kpiDetails != null && !kpiDetails.isEmpty()) {

                for (KpiNames kpi : kpiDetails) {
                    KpiNames currentKpi = map.get(kpi.getId());

                    if (currentKpi != null) {

                        long sumViolations = currentKpi.getAnomalyCount() + kpi.getAnomalyCount();
                        currentKpi.setAnomalyCount(sumViolations);
                        currentKpi.getAttribute().addAll(kpi.getAttribute());

                    } else {

                        result.add(kpi);
                        map.put(kpi.getId(), kpi);

                    }
                }
            }
        } catch (Exception e) {
            logger.error("Error occurred while merging violation counts for group attributes ", e);
        } finally {
            logger.info("Time taken for merge group kpis is {} ms.", System.currentTimeMillis() - st);
        }
        return result;
    }

}
