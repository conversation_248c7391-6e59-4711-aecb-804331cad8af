package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.beans.keys.AccountServiceKey;
import com.appnomic.appsone.api.businesslogic.GetServiceInstanceHealth;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.ServiceInstanceHealthDetails;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.List;

public class GetServiceInstanceHealthService {
    private static final Logger LOGGER = LoggerFactory.getLogger(GetServiceInstanceHealthService.class);
    public GenericResponse<List<ServiceInstanceHealthDetails>> getServiceInstanceHealth(Request request, Response response) {
        GenericResponse<List<ServiceInstanceHealthDetails>> responseObject = new GenericResponse<>();
        GetServiceInstanceHealth getServiceInstanceHealth = new GetServiceInstanceHealth();

        try {
            RequestObject requestObject = new RequestObject(request);
            UtilityBean<String> utilityBean = getServiceInstanceHealth.clientValidation(requestObject);
            AccountServiceKey accountServiceKey = getServiceInstanceHealth.serverValidation(utilityBean);
            responseObject.setData(getServiceInstanceHealth.processData(accountServiceKey));
            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage(UIMessages.SERVICE_INSTANCE_HEALTH_GET_SUCCESS);
            response.status(Constants.SUCCESS_STATUS_CODE);
            return responseObject;
        }
        catch (ClientException | ServerException | DataProcessingException e) {
            LOGGER.error("Error while fetching service level instance health data. Details: {}", e.getMessage(), e);
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            responseObject.setMessage(e.getMessage());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        }
        catch (Exception e) {
            LOGGER.error("Error while fetching service level instance health data.", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    UIMessages.INTERNAL_SERVER_ERROR, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }
    }
}
