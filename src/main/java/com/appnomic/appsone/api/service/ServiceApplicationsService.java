package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.businesslogic.ServiceApplicationsBL;
import com.appnomic.appsone.api.cache.keys.ServiceTransactionKey;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.Application;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.Set;

public class ServiceApplicationsService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceApplicationsService.class);

    public GenericResponse<Set<Application>> getServiceApplications(Request request, Response response) {
        GenericResponse<Set<Application>> genericResponse = new GenericResponse<>();
        try {
            ServiceApplicationsBL bl = new ServiceApplicationsBL();
            RequestObject requestObject = new RequestObject(request);
            UtilityBean<Integer> utilityBean = bl.clientValidation(requestObject);
            ServiceTransactionKey key = bl.serverValidation(utilityBean);
            genericResponse.setData(bl.processData(key));
            genericResponse.setResponseStatus(StatusResponse.SUCCESS.name());
            genericResponse.setMessage(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (DataProcessingException de) {
            LOGGER.error("Error occurred while fetching applications for the service.", de);
            CommonUtils.populateErrorResponse(genericResponse, response, de.getMessage(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ServerException se) {
            LOGGER.error("Error occurred while server validation for fetching applications for the service.", se);
            CommonUtils.populateErrorResponse(genericResponse, response, se.getMessage(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ClientException ce) {
            LOGGER.error("Error occurred while client validation fetching applications for the service.", ce);
            CommonUtils.populateErrorResponse(genericResponse, response, ce.getMessage(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (Exception e) {
            LOGGER.error("Exception while fetching applications for the service.", e);
            CommonUtils.populateErrorResponse(genericResponse, response, e.getMessage(), 500);
        }
        return genericResponse;
    }
}
