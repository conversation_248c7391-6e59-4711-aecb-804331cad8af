package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.businesslogic.TransactionAuditDataBL;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.TransactionAuditData;
import com.appnomic.appsone.api.pojo.TransactionQueryParams;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.List;

public class TransactionAuditDataService {

    private static final Logger LOGGER = LoggerFactory.getLogger(TransactionAuditDataService.class);

    public GenericResponse<List<TransactionAuditData>> getTransactionAuditDetails(Request request, Response response) {
        GenericResponse<List<TransactionAuditData>> responseObject = new GenericResponse<>();
        TransactionAuditDataBL transactionAuditDataBL = new TransactionAuditDataBL();

        try {
            RequestObject requestObject = new RequestObject(request);

            UtilityBean<Integer> utilityBean = transactionAuditDataBL.clientValidation(requestObject);
            TransactionQueryParams params = transactionAuditDataBL.serverValidation(utilityBean);

            responseObject.setData(transactionAuditDataBL.processData(params));

            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage("Transaction audit data fetched successfully.");
            response.status(Constants.SUCCESS_STATUS_CODE);
            return responseObject;

        } catch (ClientException | ServerException | DataProcessingException e) {
            LOGGER.error("Error while getting audit details for transaction. Details: {}", e.getMessage(), e);
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            responseObject.setMessage(e.getMessage());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (Exception e) {
            LOGGER.error("Error while getting audit details for transaction. ", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    UIMessages.ERROR_INTERNAL, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }
    }
}