package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.businesslogic.TransactionDetailsBL;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.RowDetails;
import com.appnomic.appsone.api.pojo.TransactionSortedStats;
import com.appnomic.appsone.api.pojo.request.TransactionDetailDataRequest;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;


/**
 * This service is responsible for fetching transaction-wise statistics for a given instance or service, this service
 * will also provide Top N data for slow and failed transaction.
 */
@Slf4j
public class TransactionDetailDataService {

    public GenericResponse<TransactionSortedStats> getTransactionStatistics(Request request, Response response)
    {
        log.trace("{} getTranStatistics. PARAMS: {}", Constants.INVOKED_METHOD, request);
        long st = System.currentTimeMillis();
        GenericResponse<TransactionSortedStats> genericResponse = new GenericResponse<>();
        try {
            TransactionDetailsBL bl = new TransactionDetailsBL();
            RequestObject requestObject = new RequestObject(request);

            UtilityBean<TransactionDetailDataRequest> clientData = bl.clientValidation(requestObject);
            UtilityBean<TransactionDetailDataRequest> serverData = bl.serverValidation(clientData);
            genericResponse.setData(bl.processData(serverData));

            genericResponse.setResponseStatus(StatusResponse.SUCCESS.name());
            genericResponse.setMessage(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);
        }catch (DataProcessingException de) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while processing data for get Transaction Statistics.", de);
            CommonUtils.populateErrorResponse(genericResponse, response, de.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ServerException se) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while server validation for get Transaction Statistics.", se);
            CommonUtils.populateErrorResponse(genericResponse, response, se.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ClientException ce) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while client validation for get Transaction Statistics.", ce);
            CommonUtils.populateErrorResponse(genericResponse, response, ce.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Exception for get Transaction Statistics.", e);
            CommonUtils.populateErrorResponse(genericResponse, response, e.getMessage(), 500);
        }
        finally {
            log.debug("Total time taken to load Transaction Statistics: {}", System.currentTimeMillis() - st);
        }
        return genericResponse;
    }
    public List<RowDetails> getFailTxns(List<RowDetails> allTxnList, TransactionDetailDataRequest request) {
        List<RowDetails> stagingList = (allTxnList.size() <= request.getTopNCount()) ?
                allTxnList : allTxnList.subList(0, request.getTopNCount());
        List<RowDetails> result = new ArrayList<>();
        for (RowDetails transaction : stagingList) {
            if (transaction.getFailurePercentage() > 0.0f) result.add(transaction);
        }
        return result;
    }

    public List<RowDetails> getSlowTxns(List<RowDetails> allTxnList, TransactionDetailDataRequest request) {
        List<RowDetails> stagingList = allTxnList.stream()
                .filter(it -> it.getSlowPercentage() > 0.0f)
                .collect(Collectors.toList());
        List<RowDetails> result = new ArrayList<>();

        if (!stagingList.isEmpty()) {
            stagingList.sort(Comparator.comparing(RowDetails::getSlowPercentage, Comparator.reverseOrder())
                    .thenComparing(RowDetails::getVolume, Comparator.reverseOrder())
                    .thenComparing(RowDetails::getTransaction_name));

            result = (stagingList.size() <= request.getTopNCount()) ?
                    stagingList : stagingList.subList(0, request.getTopNCount());
        }

        return result;
    }
}