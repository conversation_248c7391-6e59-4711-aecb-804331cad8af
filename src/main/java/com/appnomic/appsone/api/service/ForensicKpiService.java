package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.businesslogic.ForensicKpiBL;
import com.appnomic.appsone.api.businesslogic.ForensicKpiTopNSqlBL;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.*;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.*;

public class ForensicKpiService {

    private static final Logger logger = LoggerFactory.getLogger(ForensicKpiService.class);
    public GenericResponse<LinkedHashMap<String, LinkedHashMap<String, List<Long>>>> getKpiTimeSeries(Request request, Response response){
        GenericResponse<LinkedHashMap<String, LinkedHashMap<String, List<Long>>>> responseObject = new GenericResponse<>();
        long startAPI = System.currentTimeMillis();
        try {
            RequestObject requestObject = new RequestObject(request);
            ForensicKpiBL bl = new ForensicKpiBL();

            UtilityBean<Object> clientValidation = bl.clientValidation(requestObject);
            UtilityBean<Object> serverValidation = bl.serverValidation(clientValidation);
            responseObject.setData(bl.processData(serverValidation));

            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);
            logger.info("Kpi TimeSeries successfully fetched.");
        }
        catch (DataProcessingException de) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            logger.error("Error occurred while processing data for getting Forensic Kpi TimeSeries", de);
            responseObject.setMessage(de.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
        } catch (ServerException se) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            logger.error("Error occurred while server validation for getting Forensic Kpi TimeSeries.", se);
            responseObject.setMessage(se.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
        } catch (ClientException ce) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            logger.error("Error occurred while client validation  for getting Forensic Kpi TimeSeries", ce);
            responseObject.setMessage(ce.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
        } catch (Exception e) {
            logger.error("Error occurred while fetching forensic kpis data.",e);
            responseObject.setMessage(Constants.MESSAGE_INTERNAL_ERROR);
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
        }
        finally {
            logger.info("Time taken to fetch kpi TimeSeries: {} ms.", (System.currentTimeMillis() - startAPI));
        }
        return responseObject;
    }

    public GenericResponse<ForensicKpiDataResponse> getForensicKpiTopNSQL(Request request, Response response) {
        GenericResponse<ForensicKpiDataResponse> responseObject = new GenericResponse<>();
        long startAPI = System.currentTimeMillis();
        try {
            RequestObject requestObject = new RequestObject(request);
            ForensicKpiTopNSqlBL bl = new ForensicKpiTopNSqlBL();
            UtilityBean<Object> clientValidation = bl.clientValidation(requestObject);
            UtilityBean<Object> serverValidation = bl.serverValidation(clientValidation);
            responseObject.setData(bl.processData(serverValidation));
            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);
            logger.info("Kpi TimeSeries successfully fetched.");
        }
        catch (DataProcessingException de) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            logger.error("Error occurred while processing data for getting Forensic Kpi TimeSeries", de);
            responseObject.setMessage(de.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
        } catch (ServerException se) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            logger.error("Error occurred while server validation for getting Forensic Kpi TimeSeries.", se);
            responseObject.setMessage(se.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
        } catch (ClientException ce) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            logger.error("Error occurred while client validation  for getting Forensic Kpi TimeSeries", ce);
            responseObject.setMessage(ce.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
        } catch (Exception e) {
            logger.error("Error occurred while fetching forensic kpis data.",e);
            responseObject.setMessage(Constants.MESSAGE_INTERNAL_ERROR);
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
        }
        finally {
            logger.info("Time taken to fetch kpi TimeSeries: {} ms.", (System.currentTimeMillis() - startAPI));
        }
        return responseObject;
    }
}
