package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.businesslogic.ApplicationSDMBL;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.AppsoneException;
import com.appnomic.appsone.api.pojo.*;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import lombok.extern.slf4j.Slf4j;
import spark.Request;

import java.util.List;

@Slf4j
public class ApplicationSDMService {
    private ApplicationSDMService() {
        //Dummy constructor to hide the implicit one.
    }

    public static TopologyDetailsResponse getApplicationTopologyDetails(Request request) {
        log.trace("{} getApplicationTopologyDetails. PARAMS: {}", Constants.INVOKED_METHOD, request);
        long st = System.currentTimeMillis();

        TopologyDetailsResponse topologyDetailsResponse = new TopologyDetailsResponse();
        List<TopologyDetailsResponse.TopologyDetails> topologyDetails;

        try {
            RequestObject requestObject = new RequestObject(request);
            ApplicationSDMBL applicationSDMBL = new ApplicationSDMBL();

            UtilityBean<ApplicationSDMRequestBean> utilityBean = applicationSDMBL.clientValidation(requestObject);
            UtilityBean<ApplicationSDMRequestBean> req = applicationSDMBL.serverValidation(utilityBean);
            topologyDetails = applicationSDMBL.processData(req);

            topologyDetailsResponse.setData(topologyDetails);
            topologyDetailsResponse.setResponseStatus(StatusResponse.SUCCESS.name());
            topologyDetailsResponse.setResponseMessage("");
        } catch (AppsoneException e) {
            log.error("Request Exception occurred while getting topology details.", e);
            topologyDetailsResponse.setData(null);
            topologyDetailsResponse.setResponseStatus(StatusResponse.FAILURE.name());
            topologyDetailsResponse.setResponseMessage(e.getMessage());
        } catch (Exception e) {
            log.error("Error occurred while getting topology details.", e);
            topologyDetailsResponse.setData(null);
            topologyDetailsResponse.setResponseStatus(StatusResponse.FAILURE.name());
            topologyDetailsResponse.setResponseMessage(e.getMessage());
        } finally {
            log.debug("Total time taken to load application topology: {}", System.currentTimeMillis() - st);
        }

        return topologyDetailsResponse;
    }

}
