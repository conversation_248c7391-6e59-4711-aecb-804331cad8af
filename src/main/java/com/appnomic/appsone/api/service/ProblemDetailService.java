package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.beans.tfp.TFPTopNEvents;
import com.appnomic.appsone.api.businesslogic.AnomalyEventsBL;
import com.appnomic.appsone.api.businesslogic.TFPTopNEventsBL;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.AnomalyEventDetail;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.AnomalyIncidentRequest;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.List;
import java.util.Map;

public class ProblemDetailService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ProblemDetailService.class);

    public GenericResponse<Map<String, Map<Long, List<AnomalyEventDetail>>>> getProblemDetail(Request request, Response response) {
        LOGGER.trace("{} getProblemDetail().", Constants.INVOKED_METHOD);
        GenericResponse<Map<String, Map<Long, List<AnomalyEventDetail>>>> genericResponse = new GenericResponse<>();
        try {
            RequestObject requestObject = new RequestObject(request);
            AnomalyEventsBL bl = new AnomalyEventsBL();

            UtilityBean<AnomalyIncidentRequest> utilityBean = bl.clientValidation(requestObject);
            UtilityBean<AnomalyIncidentRequest> serverValBean = bl.serverValidation(utilityBean);
            genericResponse.setData(bl.processData(serverValBean));

            genericResponse.setResponseStatus(StatusResponse.SUCCESS.name());
            genericResponse.setMessage(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (DataProcessingException de) {
            LOGGER.error("Error occurred while processing data for get problem details.", de);
            CommonUtils.populateErrorResponse(genericResponse, response, de.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ServerException se) {
            LOGGER.error("Error occurred while server validation for get problem details.", se);
            CommonUtils.populateErrorResponse(genericResponse, response, se.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ClientException ce) {
            LOGGER.error("Error occurred while client validation for get problem details.", ce);
            CommonUtils.populateErrorResponse(genericResponse, response, ce.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (Exception e) {
            LOGGER.error("Exception for get problem details.", e);
            CommonUtils.populateErrorResponse(genericResponse, response, e.getMessage(), 500);
        }
        return genericResponse;
    }

    public GenericResponse<List<TFPTopNEvents>> getTopNEvents(Request request, Response response) {
        LOGGER.trace("{} getProblemDetail().", Constants.INVOKED_METHOD);
        GenericResponse<List<TFPTopNEvents>> genericResponse = new GenericResponse<>();
        try {
            RequestObject requestObject = new RequestObject(request);
            TFPTopNEventsBL bl = new TFPTopNEventsBL();

            UtilityBean<Integer> clientValidation = bl.clientValidation(requestObject);
            UtilityBean<Integer> serverValidation = bl.serverValidation(clientValidation);
            genericResponse.setData(bl.processData(serverValidation));

            genericResponse.setResponseStatus(StatusResponse.SUCCESS.name());
            genericResponse.setMessage(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (DataProcessingException de) {
            LOGGER.error("Error occurred while processing data for get top n events.", de);
            CommonUtils.populateErrorResponse(genericResponse, response, de.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ServerException se) {
            LOGGER.error("Error occurred while server validation for get top n events.", se);
            CommonUtils.populateErrorResponse(genericResponse, response, se.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ClientException ce) {
            LOGGER.error("Error occurred while client validation for get top n events.", ce);
            CommonUtils.populateErrorResponse(genericResponse, response, ce.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (Exception e) {
            LOGGER.error("Exception for get top n events.", e);
            CommonUtils.populateErrorResponse(genericResponse, response, e.getMessage(), 500);
        }
        return genericResponse;
    }
}
