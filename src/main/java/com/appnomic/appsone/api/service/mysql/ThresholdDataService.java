package com.appnomic.appsone.api.service.mysql;

import com.appnomic.appsone.api.beans.*;
import com.appnomic.appsone.api.dao.ThresholdDataDao;
import com.appnomic.appsone.api.manager.MySQLConnectionManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * <AUTHOR> : 6/3/19
 */
public class ThresholdDataService {
    private static final Logger logger = LoggerFactory.getLogger(ThresholdDataService.class);

    public static List<ViewCoverageWinProfDetailsBean> getCoverageWindowsProfiles() {
        ThresholdDataDao thresholdDataDao = MySQLConnectionManager.getInstance().open(ThresholdDataDao.class);
        try {
            return thresholdDataDao.getCoverageWindowsProfiles();
        } catch (Exception e) {
            logger.error("Exception while adding application threshold -" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(thresholdDataDao);
        }
        return null;
    }

}
