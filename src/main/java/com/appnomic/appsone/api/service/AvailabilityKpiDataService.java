package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.businesslogic.AvailabilityKpiDataBL;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.KpiDataPoints;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.AvailabilityKpiDataRequest;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

/**
 * <AUTHOR> on 09/05/22
 */
@Slf4j
public class AvailabilityKpiDataService {

    public GenericResponse<KpiDataPoints> getAvailabilityKpiDataList(Request request, Response response) {
        log.trace("{} getAvailabilityKpiDataList. PARAMS: {}", Constants.INVOKED_METHOD, request);
        long st = System.currentTimeMillis();
        GenericResponse<KpiDataPoints> genericResponse = new GenericResponse<>();

        try {
            AvailabilityKpiDataBL bl = new AvailabilityKpiDataBL();
            RequestObject requestObject = new RequestObject(request);

            UtilityBean<AvailabilityKpiDataRequest> clientData = bl.clientValidation(requestObject);
            UtilityBean<AvailabilityKpiDataRequest> serverData = bl.serverValidation(clientData);
            KpiDataPoints kpiDataPoints = bl.processData(serverData);

            genericResponse.setData(kpiDataPoints);
            genericResponse.setResponseStatus(StatusResponse.SUCCESS.name());
            genericResponse.setMessage(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (DataProcessingException de) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while processing data for get availability kpi data points list.", de);
            CommonUtils.populateErrorResponse(genericResponse, response, de.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ServerException se) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while server validation for get availability kpi data points list.", se);
            CommonUtils.populateErrorResponse(genericResponse, response, se.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ClientException ce) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while client validation for get availability kpi data points list.", ce);
            CommonUtils.populateErrorResponse(genericResponse, response, ce.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Exception for get availability kpi data points list.", e);
            CommonUtils.populateErrorResponse(genericResponse, response, e.getMessage(), 500);
        } finally {
            log.debug("Total time taken to load availability kpi data points list: {}", System.currentTimeMillis() - st);
        }
        return genericResponse;
    }

}
