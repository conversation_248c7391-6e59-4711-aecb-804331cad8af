package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.businesslogic.GetInstancesByServiceIdBL;
import com.appnomic.appsone.api.businesslogic.InstanceDataBL;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.IdPojo;
import com.appnomic.appsone.api.pojo.InstanceMaintenanceData;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.List;

public class InstanceDataService {
    private static final Logger LOGGER = LoggerFactory.getLogger(InstanceDataService.class);

    public GenericResponse getInstances(Request request, Response response) {
        GenericResponse<List<InstanceMaintenanceData>> responseObject = new GenericResponse<>();
        try {

            InstanceDataBL bl = new InstanceDataBL();
            RequestObject requestObject = new RequestObject(request);
            UtilityBean<Object> utilityBean = bl.clientValidation(requestObject);
            UtilityBean<Object> utilityBean1 = bl.serverValidation(utilityBean);

            responseObject.setData(bl.processData(utilityBean1));
            responseObject.setResponseStatus(StatusResponse.SUCCESS.toString());
            responseObject.setMessage(StatusResponse.SUCCESS.toString());

        } catch (ServerException se) {

            LOGGER.error("Error occurred while server validation for inbound services.", se);
            responseObject.setMessage(se.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);

        } catch (ClientException ce) {

            LOGGER.error("Error occurred while client validation for inbound services.", ce);
            responseObject.setMessage(ce.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);

        } catch (Exception e) {

            LOGGER.error("Error occurred while getting inbound services.", e);
            responseObject.setMessage(UIMessages.ERROR_INTERNAL + e.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);

        }
        return responseObject;
    }

    public GenericResponse<List<IdPojo>> getInstancesByServiceId(Request request, Response response) {
        GenericResponse<List<IdPojo>> responseObject = new GenericResponse<>();
        try {

            GetInstancesByServiceIdBL getInstancesByServiceIdBL = new GetInstancesByServiceIdBL();
            RequestObject requestObject = new RequestObject(request);

            UtilityBean<String> utilityBean = getInstancesByServiceIdBL.clientValidation(requestObject);
            UtilityBean<String> bean = getInstancesByServiceIdBL.serverValidation(utilityBean);
            List<IdPojo> data = getInstancesByServiceIdBL.processData(bean);

            responseObject.setData(data);
            responseObject.setResponseStatus(StatusResponse.SUCCESS.toString());
            responseObject.setMessage(StatusResponse.SUCCESS.toString());

        } catch (Exception e) {

            LOGGER.error("Error occurred while getting instance by service.", e);
            responseObject.setMessage(UIMessages.ERROR_INTERNAL + e.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
        }
        return responseObject;
    }
}
