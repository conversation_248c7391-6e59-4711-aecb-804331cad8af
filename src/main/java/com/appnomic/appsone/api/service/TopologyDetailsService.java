package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.dao.opensearch.MaintenanceWindowSearchRepo;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.AgentRepo;
import com.appnomic.appsone.api.dao.redis.ApplicationRepo;
import com.appnomic.appsone.api.dao.redis.ServiceRepo;
import com.appnomic.appsone.api.pojo.ComponentType;
import com.appnomic.appsone.api.pojo.ConnectionDetails;
import com.appnomic.appsone.api.pojo.TopologyDetailsResponse;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.api.util.DateTimeUtil;
import com.heal.configuration.pojos.*;
import lombok.extern.slf4j.Slf4j;
import spark.Request;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class TopologyDetailsService {
    private static final String TITLE_SPLITTER = ConfProperties.getString(Constants.ICON_TITLE_SPLITTER, Constants.ICON_TITLE_SPLITTER_DEFAULT);

    private TopologyDetailsService() {
        //Dummy constructor to hide the implicit one.
    }

    /**
     * @param request - Incoming request from the UI
     * @return topologyDetailsResponse
     */
    public static TopologyDetailsResponse getTopologyDetails(Request request) {

        TopologyDetailsResponse topologyDetailsResponse = new TopologyDetailsResponse();
        AccountRepo accountRepo = new AccountRepo();
        TopologyDetailsResponse.TopologyDetails topologyDetails = new TopologyDetailsResponse.TopologyDetails();
        List<TopologyDetailsResponse.TopologyDetails> topologyDetailsList = new ArrayList<>();

        try {
            String accountIdString = request.params(Constants.REQUEST_PARAM_IDENTIFIER);
            String applicationIdString = request.queryParams("applicationId");
            com.heal.configuration.pojos.Account account = accountRepo.getAccount(accountIdString);

            // Is given account id is valid
            if (account == null) {
                log.error("Invalid account id provided");
                topologyDetailsResponse.setData(null);
                topologyDetailsResponse.setResponseStatus(StatusResponse.FAILURE.name());
                topologyDetailsResponse.setResponseMessage(UIMessages.ERROR_INVALID_ACCOUNT_ID);
                return topologyDetailsResponse;
            }

            String userId = CommonUtils.getUserId(request);

            List<TopologyDetailsResponse.Nodes> nodesList;
            List<TopologyDetailsResponse.Edges> edgesList;
            Timestamp date = new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());

            ApplicationRepo applicationRepo = new ApplicationRepo();
            List<Application> accountApplicationList = applicationRepo.getAllApplicationDetails(account.getIdentifier());
            Map<String, BasicAgentEntity> agentMap = new AgentRepo().getAllAgents(account.getIdentifier())
                    .parallelStream().collect(Collectors.toMap(BasicEntity::getIdentifier, c -> c));
            Set<String> accessibleApplications = applicationRepo.getAccessibleApplicationsByUserId(userId, account.getIdentifier())
                    .parallelStream().map(BasicEntity::getIdentifier).collect(Collectors.toSet());



            if (applicationIdString == null) {
                Set<Service> serviceSet = new ServiceRepo().getAllServices(account.getIdentifier())
                        .parallelStream()
                        .map(c -> HealUICache.INSTANCE.getServiceDetails(account.getIdentifier(), c.getIdentifier()))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());

                Map<String, Boolean> servicesMaintenanceMap = new MaintenanceWindowSearchRepo().isServiceUnderMaintenance(account.getIdentifier(),
                        date.getTime(), serviceSet.stream().map(BasicEntity::getIdentifier).collect(Collectors.toSet()));

                nodesList = getNodeList(account.getIdentifier(), serviceSet, accountApplicationList, agentMap, accessibleApplications, servicesMaintenanceMap);
                edgesList = getEdgeList(account.getIdentifier(), serviceSet);
            } else {
                int applicationId = Integer.parseInt(applicationIdString);

                Set<Service> serviceSet = new ApplicationRepo().getServicesByAppId(account.getIdentifier(), applicationId)
                        .parallelStream()
                        .map(c -> HealUICache.INSTANCE.getServiceDetails(account.getIdentifier(), c.getIdentifier()))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());

                Map<String, Boolean> servicesMaintenanceMap = new MaintenanceWindowSearchRepo().isServiceUnderMaintenance(account.getIdentifier(),
                        date.getTime(), serviceSet.stream().map(BasicEntity::getIdentifier).collect(Collectors.toSet()));

                nodesList = getNodeList(account.getIdentifier(), serviceSet, accountApplicationList, agentMap, accessibleApplications, servicesMaintenanceMap);
                edgesList = getEdgeList(account.getIdentifier(), serviceSet);
            }

            topologyDetails.setNodes(nodesList);
            topologyDetails.setEdges(edgesList);
            topologyDetailsList.add(topologyDetails);
            topologyDetailsResponse.setData(topologyDetailsList);
            topologyDetailsResponse.setResponseStatus(Constants.MESSAGE_SUCCESS);
            topologyDetailsResponse.setResponseMessage("");

        } catch (Exception e) {
            log.error("Error occurred while getting topology details.", e);
            topologyDetailsResponse.setData(null);
            topologyDetailsResponse.setResponseStatus(Constants.MESSAGE_FAILURE);
            topologyDetailsResponse.setResponseMessage(e.getMessage());
        }

        return topologyDetailsResponse;
    }

    /**
     * @return nodesList
     */
    public static List<TopologyDetailsResponse.Nodes> getNodeList(String accountIdentifier, Set<Service> serviceSet,
                                                                  List<Application> allApplications, Map<String, BasicAgentEntity> agentMap,
                                                                  Set<String> allAccessibleApplications, Map<String, Boolean> servicesMaintenanceMap) {

        List<TopologyDetailsResponse.Nodes> nodesList = new ArrayList<>();
        try {
            long startEnriching = System.currentTimeMillis();
            long[] startArray = new long[1];

            nodesList = serviceSet.parallelStream()
                    .map(service -> {
                        List<BasicEntity> appsMappedToService = HealUICache.INSTANCE.getServiceApplicationList(accountIdentifier, service.getIdentifier());

                        boolean isAccessible = appsMappedToService.stream().anyMatch(a -> allAccessibleApplications.contains(a.getIdentifier()));

                        startArray[0] = System.currentTimeMillis();
                        TopologyDetailsResponse.Nodes temp = getNode(service, isAccessible,
                                servicesMaintenanceMap, appsMappedToService, allApplications, agentMap);
                        log.debug("Time taken for fetching node for service: {} is {} ms.", service.getName(), (System.currentTimeMillis() - startArray[0]));
                        return temp;
                    })
                    .collect(Collectors.toList());
            log.debug("Time taken to enrich {} SDM nodes is {} ms.", nodesList.size(), (System.currentTimeMillis() - startEnriching));
        } catch (Exception e) {
            log.error("Error occurred while getting nodes for account: {}", accountIdentifier, e);
        }

        return nodesList;
    }

    public static TopologyDetailsResponse.Nodes getNode(Service service, boolean isAccessible,
                                                        Map<String, Boolean> servicesMaintenanceMap, List<BasicEntity> appsMappedToService,
                                                        List<Application> accountApplicationList, Map<String, BasicAgentEntity> agentMap) {
        TopologyDetailsResponse.Nodes serviceNode = new TopologyDetailsResponse.Nodes();

        serviceNode.setId(String.valueOf(service.getId()));
        serviceNode.setName(service.getName());
        serviceNode.setIdentifier(service.getIdentifier());
        serviceNode.setType(ComponentType.unknown.name());
        serviceNode.setUserAccessible(isAccessible);

        List<String> applicationIdentifiers = appsMappedToService
                .parallelStream()
                .map(BasicEntity::getIdentifier)
                .collect(Collectors.toList());

        List<Application> applicationList = accountApplicationList
                .parallelStream()
                .filter(application -> applicationIdentifiers.contains(application.getIdentifier()))
                .peek(app -> {
                    if (app.getTags().parallelStream().anyMatch(tag -> tag.getValue().equalsIgnoreCase(Constants.MICROSERVICE))) {
                        app.setIsMicroService(1);
                    }
                })
                .map(a -> Application.builder()
                        .id(a.getId())
                        .name(a.getName())
                        .isMicroService(a.getIsMicroService())
                        .build())
                .collect(Collectors.toList());

        serviceNode.setApplications(applicationList);

        long start = System.currentTimeMillis();

        serviceNode.setMaintenance(servicesMaintenanceMap.getOrDefault(service.getIdentifier(), false));
        log.debug("Time taken for maintenance window details is {} ms.", (System.currentTimeMillis() - start));

        if (service.getTags() == null) {
            log.debug("Tags is null for service [{}]", service.getIdentifier());
            return serviceNode;
        }

        //layer details
        start = System.currentTimeMillis();
        Optional<String> layerDetails = service.getTags()
                .parallelStream()
                .filter(c -> c.getType().equalsIgnoreCase(Constants.LAYER_TAG))
                .map(com.heal.configuration.pojos.Tags::getValue)
                .findAny();

        /*The field in tag may contain information about the title to be shown in the icon in UI, if that is the case
         then there will be title as well as type in tag value which will be separated by a splitter , below we handle
         that scenario*/
        if (layerDetails.isPresent()) {
            String[] splitType = layerDetails.get().toLowerCase().split(TITLE_SPLITTER);
            if (splitType.length == 2) {
                serviceNode.setType(splitType[0]);
                serviceNode.setTitle(splitType[1]);
            } else {
                serviceNode.setType(layerDetails.get().toLowerCase());
            }
        }
        log.debug("Time taken for layer details details is {} ms.", (System.currentTimeMillis() - start));

        //agent details
        start = System.currentTimeMillis();
        BasicAgentEntity agent = service.getTags()
                .parallelStream()
                .filter(c -> c.getType().equalsIgnoreCase(Constants.AGENT_TABLE))
                .map(c -> agentMap.get(c.getValue()))
                .filter(Objects::nonNull)
                .filter(a -> a.getType().equalsIgnoreCase(Constants.JIM_AGENT_TYPE))
                .findFirst().orElse(null);

        if (agent == null) {
            log.trace("Jim Agent is not mapped to Service : {}", service.getName());
            serviceNode.addToMetaData("jimEnabled", 0);
        } else {
            log.trace("Jim Agent : {} is mapped to Service : {}", agent.getName(), service.getName());
            serviceNode.addToMetaData("jimEnabled", 1);
        }
        log.debug("Time taken for JIM details is {} ms.", (System.currentTimeMillis() - start));

        //entry point details
        start = System.currentTimeMillis();
        serviceNode.setEntryPointNode(service.getTags().parallelStream()
                .anyMatch(c -> c.getKey().equalsIgnoreCase(Constants.DEFAULT_ENTRY_POINT)
                        && c.getType().equalsIgnoreCase(Constants.ENTRY_POINT)));
        log.debug("Time taken for entry point details is {} ms.", (System.currentTimeMillis() - start));

        //kubernetes check
        start = System.currentTimeMillis();
        boolean isKubernetesEnabled = service.getTags().parallelStream()
                .anyMatch(c -> c.getValue().equalsIgnoreCase(Constants.KUBERNETES)
                        && c.getType().equalsIgnoreCase(Constants.SERVICE_TYPE_TAG));
        if (isKubernetesEnabled) {
            serviceNode.addToMetaData("isKubernetes", 1);
        } else {
            serviceNode.addToMetaData("isKubernetes", 0);
        }
        log.debug("Time taken for Kubernetes details is {} ms.", (System.currentTimeMillis() - start));

        return serviceNode;
    }

    /**
     * @return edgesList - List to edges available in TopologyDetailsResponse
     */

    public static List<TopologyDetailsResponse.Edges> getEdgeList(String accountIdentifier, Set<Service> serviceSet) {

        List<TopologyDetailsResponse.Edges> edgesList = new ArrayList<>();
        try {
            List<ConnectionDetails> connectionDetailsList = serviceSet.parallelStream()
                    .map(c -> HealUICache.INSTANCE.getServiceConnectionDetails(accountIdentifier, c.getIdentifier())
                            .parallelStream()
                            .filter(Objects::nonNull)
                            .map(d -> ConnectionDetails.builder()
                                    .sourceId(c.getId())
                                    .destinationId(d.getId())
                                    .build())
                            .collect(Collectors.toList()))
                    .flatMap(Collection::stream)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            connectionDetailsList.forEach(connectionDetails -> {
                TopologyDetailsResponse.Edges edges = new TopologyDetailsResponse.Edges();

                // checking for services only
                Optional<Service> controller = serviceSet.stream()
                        .filter(t -> t.getId() == connectionDetails.getSourceId() || t.getId() == connectionDetails.getDestinationId())
                        .findAny();

                // extra validation so that duplicate edges are not created
                if (controller.isPresent()
                        && (edgesList.stream().noneMatch(it -> (it.getSource().equals(String.valueOf(connectionDetails.getSourceId()))
                        && it.getTarget().equals(String.valueOf(connectionDetails.getDestinationId())))))) {
                    edges.setSource(String.valueOf(connectionDetails.getSourceId()));
                    edges.setTarget(String.valueOf(connectionDetails.getDestinationId()));
                    edgesList.add(edges);
                }
            });
        } catch (Exception e) {
            log.error("Error occurred while getting Edges for account: {}", accountIdentifier, e);
        }
        return edgesList;
    }
}
