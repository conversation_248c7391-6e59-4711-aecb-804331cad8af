package com.appnomic.appsone.api.service.mysql;

import com.appnomic.appsone.api.dao.UserAttributesDao;
import com.appnomic.appsone.api.manager.MySQLConnectionManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class UserAttributesDataService {
    private static final Logger logger = LoggerFactory.getLogger(UserAttributesDataService.class);
    private UserAttributesDataService() {
        //do nothing.
    }

    public static int getId(String userId) {
        UserAttributesDao userAttributesDao = MySQLConnectionManager.getInstance().open(UserAttributesDao.class);
        try {
            return userAttributesDao.getUserId(userId);
        } catch (Exception e) {
            logger.error("Error retrieving user id: {}", userId, e);
        } finally {
            MySQLConnectionManager.getInstance().close(userAttributesDao);
        }
        return -1;
    }
}
