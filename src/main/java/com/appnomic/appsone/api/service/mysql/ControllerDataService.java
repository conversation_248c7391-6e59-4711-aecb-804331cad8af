package com.appnomic.appsone.api.service.mysql;

import com.appnomic.appsone.api.beans.ControllerBean;
import com.appnomic.appsone.api.beans.ViewApplicationServiceMappingBean;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.dao.ControllerDao;
import com.appnomic.appsone.api.manager.MySQLConnectionManager;
import com.appnomic.appsone.api.pojo.Controller;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

public class ControllerDataService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ControllerDataService.class);


    public List<ViewApplicationServiceMappingBean> getApplicationServiceByAccountId(int accountId) {
        ControllerDao controllerDao = MySQLConnectionManager.getInstance().open(ControllerDao.class);
        try {
            return controllerDao.getApplicationServicesByAccount(accountId);
        } catch (Exception e) {
            LOGGER.error("Exception occurred while fetching applications and services. accountId: {}",
                    accountId, e);
        } finally {
            MySQLConnectionManager.getInstance().close(controllerDao);
        }
        return Collections.emptyList();
    }

    public List<ViewApplicationServiceMappingBean> getServicesForApplicationWithAccID(int accountId) {
        ControllerDao controllerDao = MySQLConnectionManager.getInstance().open(ControllerDao.class);
        try {
            return controllerDao.getServicesForApplicationWithAccID(accountId);
        } catch (Exception e) {
            LOGGER.error("Exception occurred while fetching getApplicationServiceMapping with account id. Details: {}", e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(controllerDao);
        }
        return new ArrayList<>();
    }

    public static List<Controller> getControllerList(Integer accountId) {
        ControllerDao controllerDao =
                MySQLConnectionManager.getInstance().open(ControllerDao.class);
        try {
            return controllerDao.getControllerList(accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while getting controller details for account id:{}", accountId, e);
        } finally {
            MySQLConnectionManager.getInstance().close(controllerDao);
        }
        return Collections.emptyList();
    }
}
