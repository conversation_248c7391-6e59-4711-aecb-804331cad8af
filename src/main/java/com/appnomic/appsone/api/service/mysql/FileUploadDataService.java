package com.appnomic.appsone.api.service.mysql;

import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.dao.FileUploadDataDao;
import com.appnomic.appsone.api.dao.mysql.entity.InstallationAttributeBean;
import com.appnomic.appsone.api.manager.MySQLConnectionManager;
import com.appnomic.appsone.api.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import spark.Response;

import java.util.List;

@Slf4j
public class FileUploadDataService {
    public static GenericResponse<List<InstallationAttributeBean>> getInstallationAttributes(Response response) {
        GenericResponse<List<InstallationAttributeBean>> responseObject;
        FileUploadDataDao dao = MySQLConnectionManager.getInstance().open(FileUploadDataDao.class);
        try {
            List<InstallationAttributeBean> installAttrs = dao.getInstallationAttributes();
            responseObject = CommonUtils.getGenericResponse(response,
                    StatusResponse.SUCCESS.name(), 200, "Success", null, false);
            responseObject.setData(installAttrs);
        } catch (Exception e) {
            log.error("Error occurred while fetching installation attribute.", e);
            responseObject = CommonUtils.getGenericResponse(response,
                    StatusResponse.FAILURE.name(), 400, e.getMessage(), e, true);
        }
        return responseObject;
    }
}
