package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.beans.UserTimezoneRequestData;
import com.appnomic.appsone.api.businesslogic.*;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.*;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.List;

/**
 * <AUTHOR> : 08/04/2019
 */
public class TimezoneService {
    private Logger logger = LoggerFactory.getLogger(TimezoneService.class);

    public GenericResponse getAllTimezones(Response response) {
        GenericResponse<List<TimezonePojo>> genericResponse = new GenericResponse<>();
        try {
            TimezoneBL bl = new TimezoneBL();
            genericResponse.setData(bl.processData(null));
            genericResponse.setResponseStatus(StatusResponse.SUCCESS.name());
            genericResponse.setMessage(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (DataProcessingException de) {
            logger.error("Error occurred while processing data for get all timezone.", de);
            CommonUtils.populateErrorResponse(genericResponse, response, de.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (Exception e) {
            logger.error("Error occurred while for get all timezone.", e);
            CommonUtils.populateErrorResponse(genericResponse, response, e.getMessage(), 500);
        }
        return genericResponse;
    }

    public GenericResponse<String> setUserPreferredTimezone(Request request, Response response) {
        GenericResponse<String> genericResponse = new GenericResponse<>();
        try {
            RequestObject requestObject = new RequestObject(request);
            SetUserTimezoneBL bl = new SetUserTimezoneBL();
            UtilityBean<UserTimezoneRequestData> utilityBean = bl.clientValidation(requestObject);
            UserTimezoneRequestData userTimezoneRequestData = bl.serverValidation(utilityBean);
            genericResponse.setData(bl.processData(userTimezoneRequestData));
            genericResponse.setResponseStatus(StatusResponse.SUCCESS.name());
            genericResponse.setMessage(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (DataProcessingException de) {
            logger.error("Error occurred while processing data for set user timezone.", de);
            CommonUtils.populateErrorResponse(genericResponse, response, de.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ServerException se) {
            logger.error("Error occurred while server validation for set user timezone.", se);
            CommonUtils.populateErrorResponse(genericResponse, response, se.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ClientException ce) {
            logger.error("Error occurred while client validation for set user timezone.", ce);
            CommonUtils.populateErrorResponse(genericResponse, response, ce.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (Exception e) {
            logger.error("Exception occurred for set user timezone.", e);
            CommonUtils.populateErrorResponse(genericResponse, response, e.getMessage(), 500);
        }
        return genericResponse;
    }

    public GenericResponse getUserPreferredTimezone(Request request, Response response) {
        GenericResponse<UserTimezonePojo> genericResponse = new GenericResponse<>();
        try {
            RequestObject requestObject = new RequestObject(request);
            GetUserDetailsBL bl = new GetUserDetailsBL();
            UtilityBean<String> utilityBean = bl.clientValidation(requestObject);
            UserTimezoneRequestData userTimezoneRequestData = bl.serverValidation(utilityBean);
            genericResponse.setData(bl.processData(userTimezoneRequestData));
            genericResponse.setResponseStatus(StatusResponse.SUCCESS.name());
            genericResponse.setMessage(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (DataProcessingException de) {
            logger.error("Error occurred while processing data for get user timezone.", de);
            CommonUtils.populateErrorResponse(genericResponse, response, de.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ServerException se) {
            logger.error("Error occurred while server validation for get user timezone.", se);
            CommonUtils.populateErrorResponse(genericResponse, response, se.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ClientException ce) {
            logger.error("Error occurred while client validation for get user timezone.", ce);
            CommonUtils.populateErrorResponse(genericResponse, response, ce.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (Exception e) {
            logger.error("Exception occurred for get user timezone.", e);
            CommonUtils.populateErrorResponse(genericResponse, response, e.getMessage(), 500);
        }
        return genericResponse;
    }

    public GenericResponse getServiceTimezones(Request request, Response response) {
        GenericResponse<List<ServiceTimezonePojo>> genericResponse = new GenericResponse<>();
        try {
            RequestObject requestObject = new RequestObject(request);
            TimezoneByServiceBL bl = new TimezoneByServiceBL();
            UtilityBean<Object> utilityBean =  bl.clientValidation(requestObject);
            UtilityBean serverValBean = bl.serverValidation(utilityBean);
            genericResponse.setData(bl.processData(serverValBean));
            genericResponse.setResponseStatus(StatusResponse.SUCCESS.name());
            genericResponse.setMessage(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (DataProcessingException de) {
            logger.error("Error occurred while processing data for get service timezone.", de);
            CommonUtils.populateErrorResponse(genericResponse, response, de.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ServerException se) {
            logger.error("Error occurred while server validation for get service timezone.", se);
            CommonUtils.populateErrorResponse(genericResponse, response, se.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ClientException ce) {
            logger.error("Error occurred while client validation for get service timezone.", ce);
            CommonUtils.populateErrorResponse(genericResponse, response, ce.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (Exception e) {
            logger.error("Exception for get service timezone.", e);
            CommonUtils.populateErrorResponse(genericResponse, response, e.getMessage(), 500);
        }
        return genericResponse;
    }

    public GenericResponse getSignalTimezones(Request request, Response response) {
        GenericResponse<List<SignalTimezonePojo>> genericResponse = new GenericResponse<>();
        try {
            RequestObject requestObject = new RequestObject(request);
            TimezoneBySignalBL bl = new TimezoneBySignalBL();
            UtilityBean<SignalData> clientValBean = bl.clientValidation(requestObject);
            UtilityBean<SignalData> serverValBean = bl.serverValidation(clientValBean);
            genericResponse.setData(bl.processData(serverValBean));
            genericResponse.setResponseStatus(StatusResponse.SUCCESS.name());
            genericResponse.setMessage(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (DataProcessingException de) {
            logger.error("Error occurred while processing data for get signal timezone.", de);
            CommonUtils.populateErrorResponse(genericResponse, response, de.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ServerException se) {
            logger.error("Error occurred while server validation for get signal timezone.", se);
            CommonUtils.populateErrorResponse(genericResponse, response, se.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ClientException ce) {
            logger.error("Error occurred while client validation for get signal timezone.", ce);
            CommonUtils.populateErrorResponse(genericResponse, response, ce.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (Exception e) {
            logger.error("Exception for get signal timezone.", e);
            CommonUtils.populateErrorResponse(genericResponse, response, e.getMessage(), 500);
        }
        return genericResponse;
    }
}
