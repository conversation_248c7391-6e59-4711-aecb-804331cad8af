package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.beans.tfp.TFPInboundOutboundRequestData;
import com.appnomic.appsone.api.businesslogic.TransactionFlowPathInboundOutboundBL;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.pojo.tfp.TFPAnomalyTransactionDetails;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

import java.util.List;

@Slf4j
public class TransactionFlowPathInboundOutboundService {

    public GenericResponse<List<TFPAnomalyTransactionDetails>> getOutboundTransactions(Request request, Response response) {
        log.trace("{} getServiceAnomalyTransactions().", Constants.INVOKED_METHOD);
        GenericResponse<List<TFPAnomalyTransactionDetails>> responseObject = new GenericResponse<>();
        try {
            RequestObject requestObject = new RequestObject(request);
            TransactionFlowPathInboundOutboundBL bl = new TransactionFlowPathInboundOutboundBL();

            UtilityBean<TFPInboundOutboundRequestData> parsedData = bl.clientValidation(requestObject);
            UtilityBean<TFPInboundOutboundRequestData> validatedData = bl.serverValidation(parsedData);
            responseObject.setData(bl.processData(validatedData));

            responseObject.setMessage(UIMessages.SUCCESS);
            responseObject.setResponseStatus(StatusResponse.SUCCESS.toString());

        } catch (DataProcessingException de) {

            log.error("Error occurred while processing data for anomaly transaction services.", de);
            responseObject.setMessage(de.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);

        } catch (ServerException se) {

            log.error("Error occurred while server validation for anomaly transaction services.", se);
            responseObject.setMessage(se.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);

        } catch (ClientException ce) {

            log.error("Error occurred while client validation for anomaly transaction services.", ce);
            responseObject.setMessage(ce.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);

        } catch (Exception e) {

            log.error("Error occurred while getting anomaly transaction services.", e);
            responseObject.setMessage(UIMessages.ERROR_INTERNAL + e.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);

        }
        return responseObject;
    }
}
