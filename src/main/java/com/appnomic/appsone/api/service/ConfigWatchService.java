package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.businesslogic.ComponentInstanceBL;
import com.appnomic.appsone.api.businesslogic.ConfigWatchCompareBL;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.ComponentInstancesResponse;
import com.appnomic.appsone.api.pojo.ConfigWatchCompareRequest;
import com.appnomic.appsone.api.pojo.InstanceWiseKpis;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.List;
import java.util.Set;

public class ConfigWatchService {
    private final Logger logger = LoggerFactory.getLogger(ConfigWatchService.class);

    public GenericResponse<Set<ComponentInstancesResponse>> getComponentInstances(Request request, Response response) {
        GenericResponse<Set<ComponentInstancesResponse>> genericResponse = new GenericResponse<>();
        try {
            RequestObject requestObject = new RequestObject(request);
            ComponentInstanceBL bl = new ComponentInstanceBL();
            UtilityBean<Integer> clientValBean = bl.clientValidation(requestObject);
            UtilityBean<Integer> serverValBean = bl.serverValidation(clientValBean);
            genericResponse.setData(bl.processData(serverValBean));
            genericResponse.setResponseStatus(StatusResponse.SUCCESS.name());
            genericResponse.setMessage(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (DataProcessingException de) {
            logger.error("Error occurred while processing data for get component instance.", de);
            CommonUtils.populateErrorResponse(genericResponse, response, de.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ServerException se) {
            logger.error("Error occurred while server validation for get component instance.", se);
            CommonUtils.populateErrorResponse(genericResponse, response, se.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ClientException ce) {
            logger.error("Error occurred while client validation for get component instance.", ce);
            CommonUtils.populateErrorResponse(genericResponse, response, ce.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (Exception e) {
            logger.error("Exception for get component instance.", e);
            CommonUtils.populateErrorResponse(genericResponse, response, e.getMessage(), 500);
        }
        return genericResponse;
    }

    public GenericResponse<List<InstanceWiseKpis>> getConfigCompare(Request request, Response response) {
        GenericResponse<List<InstanceWiseKpis>> genericResponse = new GenericResponse<>();
        try {
            RequestObject requestObject = new RequestObject(request);
            ConfigWatchCompareBL bl = new ConfigWatchCompareBL();
            UtilityBean<ConfigWatchCompareRequest> clientValBean = bl.clientValidation(requestObject);
            UtilityBean<ConfigWatchCompareRequest> serverValBean = bl.serverValidation(clientValBean);
            genericResponse.setData(bl.processData(serverValBean));
            genericResponse.setResponseStatus(StatusResponse.SUCCESS.name());
            genericResponse.setMessage(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (DataProcessingException de) {
            logger.error("Error occurred while processing data for get compare across instance.", de);
            CommonUtils.populateErrorResponse(genericResponse, response, de.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ServerException se) {
            logger.error("Error occurred while server validation for get compare across instance.", se);
            CommonUtils.populateErrorResponse(genericResponse, response, se.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ClientException ce) {
            logger.error("Error occurred while client validation for get compare across instance.", ce);
            CommonUtils.populateErrorResponse(genericResponse, response, ce.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (Exception e) {
            logger.error("Exception for get compare across instance.", e);
            CommonUtils.populateErrorResponse(genericResponse, response, e.getMessage(), 500);
        }
        return genericResponse;
    }
}
