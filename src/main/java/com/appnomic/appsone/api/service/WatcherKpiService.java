package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.dao.opensearch.ConfigWatcherRepo;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.InstanceRepo;
import com.appnomic.appsone.api.dao.redis.KpiRepo;
import com.appnomic.appsone.api.pojo.ConfigWatchData;
import com.appnomic.appsone.api.pojo.FileOperation;
import com.appnomic.appsone.api.pojo.WatcherKpiDetailResponseObject;
import com.appnomic.appsone.api.pojo.WatcherResponseData;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.api.util.DateTimeUtil;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.CompInstClusterDetails;
import com.heal.configuration.pojos.CompInstKpiEntity;
import com.heal.configuration.pojos.opensearch.CollatedKpi;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> : 08/03/2019
 */
public class WatcherKpiService {
    private static final Logger logger = LoggerFactory.getLogger(WatcherKpiService.class);
    private static final String TIMEZONE_OFF_SET = ConfProperties.getString(Constants.TIMEZONE_OFFSET, Constants.TIMEZONE_OFFSET_DEFAULT);
    private static final String KEY_VALUE_SEPARATOR_DATA = ConfProperties.getString(Constants.CONFIG_WATCH_KEY_VALUE_SPLITTER,
            Constants.CONFIG_WATCH_KEY_VALUE_SPLITTER_DEFAULT);

    public WatcherKpiDetailResponseObject getWatcherKpiData(Request request, Response response) {
        logger.trace(Constants.INVOKED_METHOD + "getWatcherKpiData");
        WatcherKpiDetailResponseObject responseObject = new WatcherKpiDetailResponseObject();
        try {
            AccountRepo accountRepo = new AccountRepo();
            KpiRepo kpiRepo = new KpiRepo();
            String accountIdString = request.params(":identifier");
            String instanceIdString = request.params(":instanceId");
            String kpiIdString = request.params(":kpiId");
            String fromTimeString = request.queryParams("fromTime");
            String toTimeString = request.queryParams("toTime");
            String fileName = request.queryParams("fileName");
            com.heal.configuration.pojos.Account account = accountRepo.getAccount(accountIdString);

            if (account == null) {
                logger.error("Invalid input parameter/s provided.");
                responseObject.setResponse_message("Parameter validation failed");
                responseObject.setResponse_status(StatusResponse.FAILURE.toString());
                return responseObject;
            }

            if (!validateInput(String.valueOf(account.getId()), instanceIdString, kpiIdString, fromTimeString, toTimeString)) {
                logger.error("Input parameter validation failed.");
                responseObject.setResponse_message("Parameter validation failed");
                responseObject.setResponse_status(StatusResponse.FAILURE.toString());
                return responseObject;
            }

            InstanceRepo instanceRepo = new InstanceRepo();
            int instanceId = Integer.parseInt(instanceIdString);
            int kpiId = Integer.parseInt(kpiIdString);
            long fromTime = Long.parseLong(fromTimeString);
            long toTime = Long.parseLong(toTimeString);

            CompInstClusterDetails componentInstanceBean = instanceRepo.getInstanceDetailsWithId(account.getIdentifier(), instanceId);

            if (componentInstanceBean == null) {
                logger.error("Unable to fetch instance details for the instance id: {}", instanceId);
                responseObject.setResponse_message("Internal server error, check logs");
                responseObject.setResponse_status(StatusResponse.FAILURE.toString());
                return responseObject;
            }

            CompInstKpiEntity instKpi = kpiRepo.getKpiDetailByAccInstKpiId(account.getIdentifier(), componentInstanceBean.getIdentifier(), kpiId);
            if (instKpi == null) {
                logger.error("Unable to fetch group id/ type name/ instance details for the instance id: {},  kpi id: {}"
                        , instanceId, kpiId);
                responseObject.setResponse_message("Internal server error, check logs");
                responseObject.setResponse_status(StatusResponse.FAILURE.toString());
                return responseObject;
            }

            //Fetch filename for the kpi
            String fileNameDB = getFileName(instKpi, fileName.trim());
            if (fileNameDB.isEmpty()) {
                logger.error("Invalid filename received for the kpi id: {} , type name: {}", kpiId, instKpi.getType());
                responseObject.setResponse_message("No Data mapped");
                responseObject.setResponse_status(StatusResponse.SUCCESS.toString());
                return responseObject;
            }
            List<CollatedKpi> configWatcherDetailsList;
            ConfigWatcherRepo configWatcherRepo = new ConfigWatcherRepo();
            long startTime = System.currentTimeMillis();
            configWatcherDetailsList = configWatcherRepo.getConfigWatcherDetails(accountIdString, componentInstanceBean.getIdentifier(), kpiId, fileNameDB.replace(" ", ""), fromTime, toTime);
            long endTime = System.currentTimeMillis();
            logger.debug("Total time taken while fetching the ConfigWatcherDetails from openSearch for  {}s",(endTime-startTime)/1000);
            if (configWatcherDetailsList.isEmpty()) {
                logger.info("Data does not exists, account_id:{}, instance_id:{}, kpi_id:{}, from:{}, to:{}.",
                        account.getIdentifier(), componentInstanceBean.getIdentifier(), kpiIdString, fromTime, toTime);
                responseObject.setResponse_message("Data does not exists for given query.");
                responseObject.setResponse_status(StatusResponse.FAILURE.toString());
                return responseObject;
            }

            List<ConfigWatchData> resultData = new ArrayList<>();
            for (CollatedKpi configWatchModel : configWatcherDetailsList) {
                resultData.addAll(processOSData(configWatchModel, instKpi.getType()));
            }
            return getPreparedResponseObject(responseObject, resultData, instKpi.getType());

        } catch (Exception e) {
            logger.error("Error occurred while fetching watcher kpi data:", e);
            responseObject.setResponse_message("Internal server error, check logs");
            responseObject.setResponse_status(StatusResponse.FAILURE.toString());
        }
        return responseObject;
    }

    private List<ConfigWatchData> processOSData(CollatedKpi configWatchModel, String type) {
        List<ConfigWatchData> configWatchDataList = null;
        try {

            String fileName = configWatchModel.getGroupAttribute();
            configWatchDataList = new ArrayList<>();
            if (type != null && (type.trim().equalsIgnoreCase(Constants.CONFIGWATCH_IDENTIFIER) || type.trim().equalsIgnoreCase(Constants.FILEWATCH_IDENTIFIER))) {
                Map<String, String> data = configWatchModel.getWatcherValue();
                if (data != null && !data.isEmpty()) {
                    configWatchDataList.addAll(parseConfigWatchFileWatchDataOS(data, configWatchModel.getTimeInGMT(), fileName));
                }
            }
            return configWatchDataList;
        } catch (Exception e) {
            logger.warn("Error occurred while processing OpenSearch data for config/file watch information {}", e.getMessage());
            logger.debug("trace: ", e);
        }
        return configWatchDataList;
    }

    private List<ConfigWatchData> parseConfigWatchFileWatchDataOS(Map<String, String> keyValuePair, long epochTime, String fileName) {
        List<ConfigWatchData> configWatchDataList = new ArrayList<>();

        keyValuePair.forEach((key, value) -> {
            String[] singleData = value.split(KEY_VALUE_SEPARATOR_DATA, 5);
            ConfigWatchData configWatchData = new ConfigWatchData();
            configWatchData.setTime(singleData.length > 4 ? DateTimeUtil.getGMTToEpochTime(singleData[4]) : epochTime);
            configWatchData.setKey(key);
            configWatchData.setFileName(fileName);
            configWatchData.setValue(singleData[1].trim());
            switch (singleData[0].trim()) {
                case "Added":
                    configWatchData.setOperation(FileOperation.ADDED);
                    break;
                case "Deleted":
                    configWatchData.setOperation(FileOperation.DELETED);
                    configWatchData.setValue(singleData[2].trim());
                    break;
                case "Modified":
                    configWatchData.setOperation(FileOperation.MODIFIED);
                    configWatchData.setValue(((StringUtils.isEmpty(singleData[2])) ? "''" : singleData[2].trim()) + " to " + ((StringUtils.isEmpty(singleData[1])) ? "''" : singleData[1].trim()));
                    break;
            }
            configWatchDataList.add(configWatchData);
        });
        return configWatchDataList;
    }

    private WatcherKpiDetailResponseObject getPreparedResponseObject(WatcherKpiDetailResponseObject responseObject,
                                                                     List<ConfigWatchData> data, String type) {
        try {
            if (type.equalsIgnoreCase(Constants.CONFIGWATCH_IDENTIFIER)) {
                Map<String, List<ConfigWatchData>> result = data.stream()
                        .collect(Collectors.groupingBy(ConfigWatchData::getKey));

                result.forEach((k, v) -> {
                    //Sorting the list based on time as per Vikas' request, sorting the map is done in UI
                    v.sort(Comparator.comparing(ConfigWatchData::getTime).reversed());
                    v.forEach(it -> {
                        if (responseObject.getData().get(k) == null) {
                            WatcherResponseData w = new WatcherResponseData();
                            responseObject.getData().put(k, w);
                        }
                        responseObject.getData().get(k).getDate().add(it.getTime());
                        responseObject.getData().get(k).getOperation().add(it.getOperation().toString());
                        responseObject.getData().get(k).getValue().add(it.getValue());
                    });
                });
            } else if (type.equalsIgnoreCase(Constants.FILEWATCH_IDENTIFIER)) {
                Map<String, List<ConfigWatchData>> result = data.stream()
                        .collect(Collectors.groupingBy(ConfigWatchData::getFileName));

                result.forEach((k, v) -> {
                    //Sorting the list based on time as per Vikas' request, sorting the map is done in UI
                    v.sort(Comparator.comparing(ConfigWatchData::getTime).reversed());
                    v.forEach(it -> {
                        if (responseObject.getData().get(k) == null) {
                            WatcherResponseData w = new WatcherResponseData();
                            responseObject.getData().put(k, w);
                        }
                        responseObject.getData().get(k).getDate().add(it.getTime());
                        responseObject.getData().get(k).getOperation().add(it.getOperation().toString());
                        responseObject.getData().get(k).getValue().add(it.getValue());
                    });
                });
            }
            responseObject.setResponse_message("Success");
            responseObject.setResponse_status(StatusResponse.SUCCESS.toString());
        } catch (Exception e) {
            logger.error("Error occurred while processing config watch data for OpenSearch", e);
            responseObject.setResponse_message("Internal server error");
            responseObject.setResponse_status(StatusResponse.FAILURE.toString());
        }
        return responseObject;
    }

    public String getFileName(CompInstKpiEntity instKpi, String fileName) {
        if ((instKpi.getType().trim().equalsIgnoreCase(Constants.CONFIGWATCH_IDENTIFIER) && instKpi.getDiscovery() == 0) ||
                instKpi.getType().trim().equalsIgnoreCase(Constants.FILEWATCH_IDENTIFIER)) {
            if (instKpi.getAttributeValues() != null && instKpi.getAttributeValues().containsKey(fileName)) {
                return fileName;
            } else if (instKpi.getInactiveAttributeValues() != null && instKpi.getInactiveAttributeValues().containsKey(fileName)) {
                return fileName;
            }
        } else if (instKpi.getType().trim().equalsIgnoreCase(Constants.CONFIGWATCH_IDENTIFIER) && instKpi.getDiscovery() == 1) {
            return fileName.isEmpty() ? instKpi.getGroupIdentifier() : fileName ;
        }
        return "";
    }

    private boolean validateInput(String accIdStr, String instIdStr, String kpiIdStr, String fromTimeStr, String toTimeStr) {
        int accId;
        int instId;
        int kpiId;
        long fromTime;
        long toTime;
        AccountRepo accountRepo = new AccountRepo();
        InstanceRepo instanceRepo = new InstanceRepo();
        KpiRepo kpiRepo = new KpiRepo();

        try {
            accId = Integer.parseInt(accIdStr);
            instId = Integer.parseInt(instIdStr);
            kpiId = Integer.parseInt(kpiIdStr);
            fromTime = Long.parseLong(fromTimeStr);
            toTime = Long.parseLong(toTimeStr);
        } catch (NumberFormatException n) {
            logger.error("Invalid numerical input provided, details: {}", n.getMessage());
            logger.debug("trace: ", n);
            return false;
        }

        Account account = accountRepo.getAccounts().stream().filter(a -> a.getId() == accId).findAny().orElse(null);
        if (account == null) {
            logger.error("Invalid account id provided.");
            return false;
        }

        CompInstClusterDetails compInstClusterDetails = instanceRepo.getInstanceDetailsWithId(account.getIdentifier(), instId);
        if (compInstClusterDetails == null) {
            logger.error("Invalid instance id provided: {}", instId);
            return false;
        }

        CompInstKpiEntity instKpi = kpiRepo.getKpiDetailByAccInstKpiId(account.getIdentifier(), compInstClusterDetails.getIdentifier(), kpiId);
        if (instKpi == null) {
            logger.error("Unable to kpi details for for the instance id: {},  kpi id: {}", instId, kpiId);
            return false;
        }

        if (fromTime <= 0 && toTime <= 0 && fromTime >= toTime) {
            logger.error("Invalid from/to time provided for this request");
            return false;
        }
        return true;
    }
}
