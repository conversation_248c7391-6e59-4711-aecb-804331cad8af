package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.businesslogic.TimeRangesBL;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.common.UIMessages;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.TimeRangesPojo;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

/**
 * <AUTHOR>
 */
@Slf4j
public class TimeRangesService {
    public GenericResponse<TimeRangesPojo> getApplicableTimeRanges(Request request, Response response) {

        log.trace("{} getApplicableTimeRanges().", Constants.INVOKED_METHOD);
        GenericResponse<TimeRangesPojo> responseObject = new GenericResponse<>();
        try {
            RequestObject requestObject = new RequestObject(request);
            TimeRangesBL bl = new TimeRangesBL();

            UtilityBean<String> utilityBean = bl.clientValidation(requestObject);
            UtilityBean<String> accountIdentifier = bl.serverValidation(utilityBean);
            responseObject.setData(bl.processData(accountIdentifier));

            responseObject.setMessage(UIMessages.SUCCESS);
            responseObject.setResponseStatus(StatusResponse.SUCCESS.toString());

        } catch (DataProcessingException de) {

            log.error("Error occurred while processing data for get time range details services.", de);
            responseObject.setMessage(de.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);

        } catch (ServerException se) {

            log.error("Error occurred while server validation for get time range details services.", se);
            responseObject.setMessage(se.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);

        } catch (ClientException ce) {

            log.error("Error occurred while client validation for get time range details services.", ce);
            responseObject.setMessage(ce.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);

        } catch (Exception e) {

            log.error("Error occurred while getting time range details services.", e);
            responseObject.setMessage(UIMessages.ERROR_INTERNAL + e.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            response.status(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);

        }
        return responseObject;
    }

}
