package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.businesslogic.JimTraceBL;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.JimTraceRequest;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import com.google.gson.JsonElement;
import com.google.gson.JsonParser;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

@Slf4j
public class JimTraceService {
    public JsonElement takeJaegerTraceForTransaction(Request request, Response response) {
        RequestObject requestObject = new RequestObject(request);
        GenericResponse<JsonElement> responseObject = new GenericResponse<>();
        try {
            JimTraceBL jimTraceBL = new JimTraceBL();
            UtilityBean<JimTraceRequest> jaegerTraceRecordClientValidation = jimTraceBL.clientValidation(requestObject);
            JimTraceRequest jaegerTraceRecordServerValidation = jimTraceBL.serverValidation(jaegerTraceRecordClientValidation);

            return jimTraceBL.processData(jaegerTraceRecordServerValidation);
        } catch (ClientException | ServerException | DataProcessingException e) {
            log.error("Request validation failed. Details: ", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    e.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (Exception e) {
            log.error("Error occurred while taking the jaeger trace transaction information.", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    "Error occurred while taking the jaeger trace transaction information.", Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
        }

        return JsonParser.parseString(responseObject.toString());
    }
}
