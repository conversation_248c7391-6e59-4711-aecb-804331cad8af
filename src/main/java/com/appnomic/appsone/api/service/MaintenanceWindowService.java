package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.beans.UtilityBean;
import com.appnomic.appsone.api.beans.MaintenanceDetailsBean;
import com.appnomic.appsone.api.businesslogic.MaintenanceWindowBL;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.List;

public class MaintenanceWindowService {
    private static final Logger logger = LoggerFactory.getLogger(MaintenanceWindowService.class);

    public GenericResponse<List<MaintenanceDetailsBean>> getMaintenanceDetails(Request request, Response response){
        logger.trace("{} getProblemDetail().", Constants.INVOKED_METHOD);
        GenericResponse<List<MaintenanceDetailsBean>> responseObject = new GenericResponse<>();
        MaintenanceWindowBL maintenanceWindowBL = new MaintenanceWindowBL();
        try {
            UtilityBean<Object> utilityBean = maintenanceWindowBL.clientValidations(request);
            maintenanceWindowBL.serverValidations(utilityBean);
            List<MaintenanceDetailsBean> maintenanceWindowPojoList = maintenanceWindowBL.getMaintenanceWindowDetailsPerService(utilityBean);

            responseObject.setData(maintenanceWindowPojoList);
            responseObject.setMessage("Success");
            responseObject.setResponseStatus(StatusResponse.SUCCESS.toString());
            response.status(Constants.SUCCESS_STATUS_CODE);
        }
        catch (Exception e){
            logger.error("Error occurred while fetching Maintenance Details details. ", e);
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            responseObject.setMessage("Internal error, check logs");
            response.status(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
        }
        return responseObject;
    }

}
