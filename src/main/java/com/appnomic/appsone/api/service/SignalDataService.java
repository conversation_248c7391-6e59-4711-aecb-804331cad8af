package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.businesslogic.SignalBL;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.SignalData;
import com.appnomic.appsone.api.pojo.request.SignalDataRequest;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

import java.util.Set;

@Slf4j
public class SignalDataService {
    public GenericResponse<Set<SignalData>> getSignalList(Request request, Response response) {
        log.trace(Constants.INVOKED_METHOD + " getSignalList");
        GenericResponse<Set<SignalData>> genericResponse = new GenericResponse<>();
        try {
            SignalBL bl = new SignalBL();
            RequestObject requestObject = new RequestObject(request);

            UtilityBean<SignalDataRequest> clientData = bl.clientValidation(requestObject);
            UtilityBean<SignalDataRequest> serverData = bl.serverValidation(clientData);
            genericResponse.setData(bl.processData(serverData));

            genericResponse.setResponseStatus(StatusResponse.SUCCESS.name());
            genericResponse.setMessage(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (DataProcessingException de) {
            log.error("Error occurred while processing data for get signal list.", de);
            CommonUtils.populateErrorResponse(genericResponse, response, de.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ServerException se) {
            log.error("Error occurred while server validation for get signal list.", se);
            CommonUtils.populateErrorResponse(genericResponse, response, se.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ClientException ce) {
            log.error("Error occurred while client validation for get signal list.", ce);
            CommonUtils.populateErrorResponse(genericResponse, response, ce.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (Exception e) {
            log.error("Exception for get signal list.", e);
            CommonUtils.populateErrorResponse(genericResponse, response, e.getMessage(), 500);
        }
        return genericResponse;
    }
}
