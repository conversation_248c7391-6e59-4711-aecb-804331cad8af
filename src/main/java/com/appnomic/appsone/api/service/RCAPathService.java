package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.businesslogic.RcaPathBL;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.*;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.*;

/**
 * <AUTHOR> :17/04/2019
 */
public class RCAPathService {
    private static final Logger logger = LoggerFactory.getLogger(RCAPathService.class);
    public GenericResponse<RCAPathResponseObject.ResponseData> getRCAPathDetails(Request request, Response response) {
        logger.trace("{} getApplicationHealthStatus. PARAMS: {}", Constants.INVOKED_METHOD, request);
        long st = System.currentTimeMillis();
        GenericResponse<RCAPathResponseObject.ResponseData> genericResponse = new GenericResponse<>();
        try {
            RcaPathBL bl = new RcaPathBL();
            RequestObject requestObject = new RequestObject(request);
            UtilityBean<RCAPathRequestPojo> clientData = bl.clientValidation(requestObject);
            UtilityBean<RCAPathRequestPojo> serverData = bl.serverValidation(clientData);
            genericResponse.setData(bl.processData(serverData));
            genericResponse.setResponseStatus(StatusResponse.SUCCESS.name());
            genericResponse.setMessage(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);
        }catch (DataProcessingException de) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            logger.error("Error occurred while processing data for get signal list.", de);
            CommonUtils.populateErrorResponse(genericResponse, response, de.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ServerException se) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            logger.error("Error occurred while server validation for get signal list.", se);
            CommonUtils.populateErrorResponse(genericResponse, response, se.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ClientException ce) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            logger.error("Error occurred while client validation for get signal list.", ce);
            CommonUtils.populateErrorResponse(genericResponse, response, ce.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            logger.error("Exception for get signal list.", e);
            CommonUtils.populateErrorResponse(genericResponse, response, e.getMessage(), 500);
        }
        finally {
            logger.debug("Total time taken to load application health summary: {}", System.currentTimeMillis() - st);
        }
        return genericResponse;
    }

    /**
     * This method is useful to merge all the generated RCA path into a single RCA path, this is used in case of
     * warnings where there does not exists multiple RCA paths but a single graph with all generated RCA paths as a
     * single graph
     *
     * @param rcaPaths
     * @return
     */
    protected static List<TopologyDetailsResponse.TopologyDetails> combineRCAPaths(List<TopologyDetailsResponse.TopologyDetails> rcaPaths) {
        TopologyDetailsResponse.TopologyDetails result = null;

        for(TopologyDetailsResponse.TopologyDetails rcaPath: rcaPaths) {
            if( result == null ) {
                result = rcaPath;
                continue;
            }

            /**
             * while merging the nodes/edges we give priority for nodes/edges that have meta data in them , in this
             * case the meta data is that whether given nodes/edges is path of RCA path
             */

            for( TopologyDetailsResponse.Nodes node: rcaPath.getNodes() ) {
                if( result.getNodes().stream().noneMatch(it -> it.equals(node)) ) {
                    result.getNodes().add(node);
                }

                TopologyDetailsResponse.Nodes curNode = result.getNodes()
                        .stream()
                        .filter(it -> it.equals(node))
                        .findAny()
                        .orElse(null);

                if( curNode!= null && ! curNode.isRCAPathNode() ) {
                    curNode.setStartNode(node.isStartNode());
                    curNode.setRCAPathNode(node.isRCAPathNode());
                }

            }

            for(TopologyDetailsResponse.Edges edge: rcaPath.getEdges() ) {
                if(result.getEdges().stream().noneMatch(it -> it.equals(edge))) {
                    result.getEdges().add(edge);
                }

                TopologyDetailsResponse.Edges curEdge = result.getEdges()
                        .stream()
                        .filter( it -> it.equals(edge))
                        .findAny()
                        .orElse(null);
                if( curEdge != null && curEdge.getData().isEmpty() ) curEdge.setData(edge.getData());
            }

            if(result.getImpactedServiceName().stream()
                    .noneMatch(it -> it.equalsIgnoreCase(rcaPath.getImpactedServiceName().get(0)))) {

                result.getImpactedServiceName().add(rcaPath.getImpactedServiceName().get(0));
            }
        }

        List<TopologyDetailsResponse.TopologyDetails> temp = new ArrayList<>();
        temp.add(result);
        return temp;
    }
}
