package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.beans.CategoryDetailBean;
import com.appnomic.appsone.api.beans.UserAccountIdentifiersBean;
import com.appnomic.appsone.api.businesslogic.NotificationBL;
import com.appnomic.appsone.api.cache.UserDetailsCache;
import com.appnomic.appsone.api.common.*;
import com.appnomic.appsone.api.custom.exceptions.AppsoneException;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.MasterRepo;
import com.appnomic.appsone.api.pojo.NotificationsPojo;
import com.appnomic.appsone.api.pojo.PreferencesPojo;
import com.appnomic.appsone.api.pojo.UserAccessDetails;
import com.appnomic.appsone.api.service.mysql.ComponentDataService;
import com.appnomic.appsone.api.service.mysql.MasterDataService;
import com.appnomic.appsone.api.beans.MasterComponentBean;
import com.appnomic.appsone.api.beans.MasterComponentTypeBean;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.AECSBouncyCastleUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.heal.configuration.enums.NotificationPreferencesType;
import com.heal.configuration.pojos.Account;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.io.IOException;
import java.text.MessageFormat;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

public class NotificationService {
    private static final Logger LOGGER = LoggerFactory.getLogger(NotificationService.class);

    private NotificationService() {
        //Dummy constructor to hide the implicit one
    }

    public static GenericResponse getNotifications(Request request, Response response) {
        try {
            return validateAndGetNotificationSubTypes(request, response);
        } catch (Exception e) {
            LOGGER.error(UIMessages.NOTIFICATION_TYPE_ERROR, e);
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.INTERNAL_SERVER_ERROR_STATUS_CODE, UIMessages.NOTIFICATION_DATA_ERROR, null, true);
        }
    }

    private static GenericResponse validateAndGetNotificationSubTypes(Request request, Response response) {
        if (null == request) {
            LOGGER.error("Validation failure. Request or request body cannot be NULL or empty.");
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
                    "Request or request body cannot be NULL or empty.", null, true);
        }

        String userIdentifierFromAuthToken;
        try {
            userIdentifierFromAuthToken = CommonUtils.getUserId(request);
        } catch (Exception e) {
            LOGGER.error("Invalid user identifier. Reason: {}", e.getMessage(), e);
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
                    "Invalid user identifier.", null, true);
        }

        String accountIdString = request.params(Constants.ACCOUNT_IDENTIFIER);
        Account account = new AccountRepo().getAccount(accountIdString);
        if (null == account) {
            LOGGER.error("Invalid account identifier [{}]", accountIdString);
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
                    UIMessages.INVALID_ACCOUNT_MESSAGE, null, true);
        }

        String userIdentifierFromQuery = request.params(Constants.REQUEST_PARAM_USER_ID);
        if (userIdentifierFromQuery == null || userIdentifierFromQuery.trim().isEmpty()) {
            LOGGER.error(UIMessages.USER_ERROR);
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
                    UIMessages.USER_ERROR, null, true);
        }

        try {
            NotificationsPojo data = new NotificationBL().processRequestAndGetNotificationTypeList(account, userIdentifierFromQuery, userIdentifierFromAuthToken);

            GenericResponse<String> responseObject = CommonUtils.getGenericResponse(response, StatusResponse.SUCCESS.name(),
                    Constants.SUCCESS_STATUS_CODE, "Notifications fetched successfully", null, false);

            String notificationsPojoToString = CommonUtils.getObjectMapper().writeValueAsString(data);
            String encryptedData = new AECSBouncyCastleUtil().encrypt(notificationsPojoToString);

            responseObject.setData(encryptedData);

            return responseObject;
        } catch (AppsoneException | JsonProcessingException | InvalidCipherTextException e) {
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, e.getMessage(), null, true);
        }
    }

    public static GenericResponse saveNotifications(Request request, Response response) {
        try {
            return validateRequestAndSaveNotifications(request, response);
        } catch (Exception e) {
            LOGGER.error("Unexpected exception encountered while adding tag. Reason: {}", e.getMessage(), e);
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.INTERNAL_SERVER_ERROR_STATUS_CODE, e.getMessage(), e, true);
        }
    }

    private static GenericResponse validateRequestAndSaveNotifications(Request request, Response response) throws AppsoneException, InvalidCipherTextException {
        if (null == request || null == request.body() || request.body().trim().isEmpty()) {
            LOGGER.error("Validation failure. Request or request body cannot be NULL or empty.");
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
                    "Request or request body cannot be NULL or empty.", null, true);
        }

        String accountIdString = request.params(Constants.ACCOUNT_IDENTIFIER);
        com.heal.configuration.pojos.Account account = new AccountRepo().getAccount(accountIdString);
        if (account == null) {
            LOGGER.error("Invalid account id: {}", accountIdString);
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
                    UIMessages.INVALID_ACCOUNT_MESSAGE, null, true);
        }

        String userId;
        try {
            userId = CommonUtils.getUserId(request);
        } catch (Exception e) {
            LOGGER.error("Invalid user identifier. Reason: {}", e.getMessage(), e);
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
                    "Invalid user identifier.", null, true);
        }

        String applicableUserId = request.params(Constants.REQUEST_PARAM_USER_ID);
        if (applicableUserId == null || applicableUserId.trim().isEmpty()) {
            LOGGER.error(UIMessages.USER_ERROR);
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
                    UIMessages.USER_ERROR, null, true);
        }

        UserAccessDetails accessDetails = null;
        try {
            accessDetails = UserDetailsCache.getInstance().userApplications.get(new UserAccountIdentifiersBean(applicableUserId, account.getIdentifier()));
        } catch (ExecutionException e) {
            LOGGER.error("Exception while fetching user access details. Reason: {}", e.getMessage(), e);
        }

        if (accessDetails == null) {
            LOGGER.error("user does not have access to given account please contact admin");
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
                    UIMessages.NOTIFICATION_ACCOUNT_ERROR, null, true);
        }
        String decryptedString = new AECSBouncyCastleUtil().decrypt(request.body());
        PreferencesPojo addingNotificationPojo;
        try {
            addingNotificationPojo = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(decryptedString, PreferencesPojo.class);
        } catch (IOException e) {
            LOGGER.error("Exception encountered while retrieving add notifications. Reason: {}", e.getMessage(), e);
            throw new AppsoneException(UIMessages.INVALID_REQUEST_BODY);
        }

        if (addingNotificationPojo == null) {
            LOGGER.error("notifications are invalid.");
            throw new AppsoneException("notifications are unavailable.");
        }

        try {
            return validateAndSaveNotifications(response, account, applicableUserId, userId, addingNotificationPojo);
        } catch (Exception e) {
            LOGGER.error("Error while converting request object into tag object. Reason: {}", e.getMessage(), e);
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
                    e.getMessage(), null, true);
        }
    }

    private static GenericResponse validateAndSaveNotifications(Response response, Account account, String applicableUser,
                                                                String userId, PreferencesPojo addingNotificationPojo) {

        if (addingNotificationPojo.getNotificationChoice() == null || addingNotificationPojo.getNotificationChoice().getType() == null) {
            throw new AppsoneException(MessageFormat.format("NotificationPreferences- {0} is not found in DB.",
                    addingNotificationPojo.getNotificationChoice()));
        }

        com.heal.configuration.pojos.ViewTypes notificationPrefType = new MasterRepo().getTypes().stream()
                .filter(v -> v.getTypeName().equalsIgnoreCase(Constants.MST_TYPE_NOTIFICATION_PREFERENCES))
                .filter(v -> v.getSubTypeName().equalsIgnoreCase(addingNotificationPojo.getNotificationChoice().getType()))
                .findAny().orElse(null);

        if (notificationPrefType == null) {
            throw new AppsoneException(MessageFormat.format("NotificationPreferences- {0}:{1} is not found in DB.",
                    Constants.MST_TYPE_NOTIFICATION_PREFERENCES, addingNotificationPojo.getNotificationChoice().getType()));
        }

        if (notificationPrefType.getSubTypeId() == NotificationPreferencesType.ALL.getId()) {
            addingNotificationPojo.getNotificationChoice().setComponent(null);
            addingNotificationPojo.getNotificationChoice().setCategories(null);
        } else if (notificationPrefType.getSubTypeId() == NotificationPreferencesType.COMPONENT.getId()) {
            if (null != addingNotificationPojo.getNotificationChoice().getComponent() &&
                    !ComponentSelection.contains(addingNotificationPojo.getNotificationChoice().getComponent())) {
                throw new AppsoneException(MessageFormat.format("Invalid component {0}", addingNotificationPojo.getNotificationChoice().getComponent()));
            } else {
                addingNotificationPojo.getNotificationChoice().setCategories(null);
            }
        } else if (notificationPrefType.getSubTypeId() == NotificationPreferencesType.COMPONENT_TYPE.getId()) {
            Set<Integer> componentTypeIds =
                    new ComponentDataService().getAllComponentTypes(account.getId()).stream().map(MasterComponentTypeBean::getId).collect(Collectors.toSet());
            if (!componentTypeIds.containsAll(addingNotificationPojo.getNotificationChoice().getCategories())) {
                throw new AppsoneException(MessageFormat.format("Invalid component type id id(s) componentTypeIds, " +
                        "Request: {0}", addingNotificationPojo.getNotificationChoice().getComponent()));
            } else {
                addingNotificationPojo.getNotificationChoice().setComponent(null);
            }
        } else if (notificationPrefType.getSubTypeId() == NotificationPreferencesType.COMPONENT_NAME.getId()) {
            Set<Integer> componentIds =
                    new ComponentDataService().getAllComponents(account.getId()).stream().map(MasterComponentBean::getId).collect(Collectors.toSet());
            if (!componentIds.containsAll(addingNotificationPojo.getNotificationChoice().getCategories())) {
                throw new AppsoneException(MessageFormat.format("Invalid component id id(s) component Ids, Request: " +
                        "{0}", addingNotificationPojo.getNotificationChoice().getComponent()));
            } else {
                addingNotificationPojo.getNotificationChoice().setComponent(null);
            }
        } else {
            Set<Integer> categoriesIds = MasterDataService.getCategoryDetails(account.getId())
                    .parallelStream()
                    .map(CategoryDetailBean::getCategoryId)
                    .collect(Collectors.toSet());
            if (!categoriesIds.containsAll(addingNotificationPojo.getNotificationChoice().getCategories())) {
                throw new AppsoneException(MessageFormat.format("Invalid category id(s) categoriesIds: {0}, Request: {1}",
                        categoriesIds, addingNotificationPojo.getNotificationChoice().getComponent()));
            } else {
                addingNotificationPojo.getNotificationChoice().setComponent(null);
            }
        }

        try {
            new NotificationBL().createNotifications(addingNotificationPojo, applicableUser, userId, account);

        } catch (AppsoneException e) {
            LOGGER.error("Error while updating request object into notification object. Reason: {}", e.getMessage(), e);
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
                    e.getMessage(), null, true);
        }

        GenericResponse<String> genericResponse = CommonUtils.getGenericResponse(response,
                StatusResponse.SUCCESS.name(),
                Constants.SUCCESS_STATUS_CODE, "Notification Preferences saved Successfully", null, false);

        genericResponse.setData("User Notification");

        return genericResponse;
    }
}
