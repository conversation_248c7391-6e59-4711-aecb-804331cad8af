package com.appnomic.appsone.api.service.mysql;

import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.dao.TransactionDao;
import com.appnomic.appsone.api.dao.mysql.entity.TransactionAuditConfigurationBean;
import com.appnomic.appsone.api.manager.MySQLConnectionManager;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * <AUTHOR> Parekaden : 24/1/19
 */
public class TransactionDataService {
    private static final org.slf4j.Logger logger = LoggerFactory.getLogger(TransactionDataService.class);

    public static int getTxnCountForService(int accountId, String tagKey, String objectRefTable, int tagId) {
        TransactionDao transactionDaoAtomicConn = MySQLConnectionManager.getInstance().open(TransactionDao.class);
        try {
            long start = System.currentTimeMillis();
            int count = transactionDaoAtomicConn.getTxnCountPerService(accountId, tagId, objectRefTable, tagKey);
            logger.debug("Time taken to fetch transaction details for ac: {}, service: {} is {} ms.", accountId,
                    tagKey, (System.currentTimeMillis() - start));
            return count;

        } catch (Exception e) {
            logger.error("Error occurred while getting txn for the account: {}, Service: {}.", accountId, tagKey, e);
        } finally {
            MySQLConnectionManager.getInstance().close(transactionDaoAtomicConn);
        }
        return 0;
    }
}
