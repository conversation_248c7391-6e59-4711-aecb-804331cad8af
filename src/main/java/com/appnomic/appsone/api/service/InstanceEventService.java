package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.businesslogic.InstanceEventDetailsBL;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.CompInstClusterDetails;
import com.appnomic.appsone.api.pojo.InstanceEvents;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.List;

public class InstanceEventService {
    private static final Logger LOGGER = LoggerFactory.getLogger(InstanceEventService.class);
    private static final String GET_ERR_MSG = "Error while fetching instance event";
    public GenericResponse<List<InstanceEvents>> getInstanceEventsDetails(Request request, Response response) {
        LOGGER.trace("{} getInstanceEventsDetails().", Constants.INVOKED_METHOD);
        GenericResponse<List<InstanceEvents>> responseObject = new GenericResponse<>();
        try {
            InstanceEventDetailsBL instanceEventDetailsBL = new InstanceEventDetailsBL();
            RequestObject requestObject = new RequestObject(request);
            UtilityBean<CompInstClusterDetails> compInstClusterDetailsUtilityBean = instanceEventDetailsBL.clientValidation(requestObject);
            instanceEventDetailsBL.serverValidation(compInstClusterDetailsUtilityBean);

            response.status(Constants.SUCCESS_STATUS_CODE);
            responseObject.setData(instanceEventDetailsBL.processData(compInstClusterDetailsUtilityBean));
            responseObject.setMessage(Constants.SUCCESS_MESSAGE);
            responseObject.setResponseStatus(StatusResponse.SUCCESS.toString());

        } catch (DataProcessingException de) {

            LOGGER.error("Error occurred while processing data for instance categories.", de);
            CommonUtils.populateErrorResponse(responseObject, response, GET_ERR_MSG,
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;

        } catch (ServerException se) {

            LOGGER.error("Error occurred while server validation for instance events data.", se);
            CommonUtils.populateErrorResponse(responseObject, response, GET_ERR_MSG,
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;

        } catch (ClientException ce) {

            LOGGER.error("Error occurred while client validation for instance events data.", ce);
            CommonUtils.populateErrorResponse(responseObject, response, GET_ERR_MSG,
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;

        } catch (Exception e) {

            LOGGER.error("Error occurred while getting for instance events data.", e);
            CommonUtils.populateErrorResponse(responseObject, response, GET_ERR_MSG,
                    Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;

        }
        return responseObject;
    }
}
