package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.cache.MasterCache;
import com.appnomic.appsone.api.pojo.Account;
import com.appnomic.appsone.api.pojo.KeyCloakUserDetails;
import com.appnomic.appsone.api.pojo.UserAccessDetails;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.keycloak.KeyCloakSessionValidator;
import com.appnomic.appsone.model.JWTData;
import com.appnomic.appsone.util.KeyCloakConnectionSpec;
import com.appnomic.appsone.util.KeyCloakUtility;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class KeyCloakAuthService {

    private static final Logger log = LoggerFactory.getLogger(KeyCloakAuthService.class);

    private static String KEYCLOAK_ACCOUNT_IDENTIFIER = ConfProperties.getString(Constants.USER_ACCOUNT_ACCESS_IDENTIFIER,
            Constants.USER_ACCOUNT_ACCESS_IDENTIFIER_DEFAULT);
    private static KeyCloakSessionValidator keyCloakSessionValidator = null;
    private static KeyCloakUtility keyCloakUtility = null;
    private static ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();

    public static void init() {
        try {

            String keycloakIp = ConfProperties.getString(Constants.KEYCLOAK_IP);
            String keycloakPort = ConfProperties.getString(Constants.KEYCLOAK_PORT);
            String username = ConfProperties.getString(Constants.KEYCLOAK_USER);
            String encryptedPassword = ConfProperties.getString(Constants.KEYCLOAK_AUTH);

            if (keycloakIp == null || keycloakPort == null || username == null || encryptedPassword == null) {
                log.error("Missing keycloak specific configuration in conf file.");
                System.exit(-1);
            }

            String decryptedPwd = CommonUtils.getDecryptedData(encryptedPassword);

            log.debug("Inside Session Validator method");
            KeyCloakConnectionSpec keyCloakConnectionSpec = new KeyCloakConnectionSpec()
                    .setKeyCloakIP(keycloakIp)
                    .setKeyCloakPort(keycloakPort)
                    .setKeyCloakUsername(username)
                    .setKeyCloakPassword(decryptedPwd)
                    .createKeyCloakConnectionSpec();

            log.debug("KeycloakSpec : " + keyCloakConnectionSpec.toString());
            keyCloakSessionValidator = new KeyCloakSessionValidator(keyCloakConnectionSpec);
            keyCloakUtility = new KeyCloakUtility(keyCloakConnectionSpec);
        } catch (Exception e) {
            log.error("Error occurred while getting key cloak connection spec.", e);
        }
    }

    public static boolean isValidKey(String appToken) {

        try {
            if(keyCloakSessionValidator == null) {
                init();
            }
            boolean status = keyCloakSessionValidator.validateJwsToken(appToken);
            log.trace("Status of the token is [{}]" , status);
            return status;

        } catch (Exception e) {
            log.error("Invalid token. Reason: {}" , e.getMessage(), e);
            return false;
        }
    }

    public static JWTData extractUserDetails(String appToken) throws Exception {
        if (keyCloakUtility == null)
            init();

        if(keyCloakUtility == null) {
            throw new Exception("Unable to initialize the KeyCloak server. Kindly look into the heal-ui-service logs.");
        }
        log.trace("Invoked method: extractUserDetails");
        JWTData jwtData = keyCloakUtility.extractUsername(appToken);

        if(jwtData == null) {
            throw new Exception("Unable to get the username from token. Kindly look into the heal-ui-service logs.");
        }

        return jwtData;
    }

    public static KeyCloakUserDetails getKeyCloakUserDetails(String userData)  {
        KeyCloakUserDetails keyCloakUserDetails = null;
        try {
            keyCloakUserDetails = objectMapper.readValue(userData, KeyCloakUserDetails.class);
        } catch (IOException e) {
            log.error("Error occurred while reading user details from keycloak service"+e.getMessage(), e);
            log.debug("trace: ", e);
        }

        return keyCloakUserDetails;
    }

    public static KeyCloakUserDetails getKeycloakUserDataFromId(String userIdentifier)   {
        KeyCloakUserDetails userData=null;
        if (keyCloakUtility == null) init();
        try {
            String userDetailsString = keyCloakUtility.fetchUserDetails(userIdentifier);
            userData = getKeyCloakUserDetails(userDetailsString);
        }   catch (Exception e) {
            log.error("Error occurred while fetching username from keycloak: ",e);
        }
        return userData;
    }
}
