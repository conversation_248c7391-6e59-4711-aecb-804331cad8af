package com.appnomic.appsone.api.service.mysql;

import com.appnomic.appsone.api.beans.RecurringDetails;
import com.appnomic.appsone.api.beans.ServiceMaintenanceMapping;
import com.appnomic.appsone.api.dao.MaintenanceWindowDao;
import com.appnomic.appsone.api.dao.MasterDataDao;
import com.appnomic.appsone.api.manager.MySQLConnectionManager;
import com.appnomic.appsone.api.pojo.MaintenanceDetails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class MaintenanceWindowService {

    private static final Logger logger = LoggerFactory.getLogger(MaintenanceWindowService.class);

    public static List<ServiceMaintenanceMapping> getMaintenanceWindowsByServiceId(int serviceId) {
        MaintenanceWindowDao maintenanceWindowDao = MySQLConnectionManager.getInstance().open(MaintenanceWindowDao.class);
        try {
            return maintenanceWindowDao.getMaintenanceWindowsByServiceId(serviceId);
        } catch (Exception e) {
            logger.error("Exception while retrieving getMaintenanceWindowsByServiceId", e);
        } finally {
            MySQLConnectionManager.getInstance().close(maintenanceWindowDao);
        }
        return null;
    }

    public static List<MaintenanceDetails> getMaintenanceWindowDetailsList(List<Integer> maintenanceIds, Timestamp startTime, Timestamp endTime) {
        MasterDataDao inMasterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        List<MaintenanceDetails> maintenanceDetails = new ArrayList<>();
        try {
            maintenanceIds.forEach(maintenanceId -> maintenanceDetails.add(inMasterDataDao.getMaintenanceWindowDetails(maintenanceId, startTime, endTime)));
        } catch (Exception e) {
            logger.error("Exception while retrieving the getMaintenanceWindowDetailsList method. MaintenanceIds: {}, startTime: {}, endTime: {}.", maintenanceIds, startTime, endTime, e);
        } finally {
            MySQLConnectionManager.getInstance().close(inMasterDataDao);
        }
        return maintenanceDetails.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static List<RecurringDetails> getRecurringDetailsList(List<Integer> maintenanceIds) {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        List<RecurringDetails> recurringDetails = new ArrayList<>();
        try {
            maintenanceIds.forEach(maintenanceId -> recurringDetails.add(masterDataDao.getRecurringDetails(maintenanceId)));
        } catch (Exception e) {
            logger.error("Exception while retrieving the getMaintenanceWindowDetailsList method. maintenanceIds: {}", maintenanceIds, e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return recurringDetails.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }
}
