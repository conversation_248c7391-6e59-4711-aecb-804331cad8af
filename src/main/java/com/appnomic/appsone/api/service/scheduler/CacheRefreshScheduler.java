package com.appnomic.appsone.api.service.scheduler;

import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.util.ConfProperties;
import com.google.common.util.concurrent.AbstractScheduledService;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class CacheRefreshScheduler extends AbstractScheduledService {

    @Override
    protected void runOneIteration() {
        HealUICache.INSTANCE.refreshCache();
    }

    @Override
    protected AbstractScheduledService.Scheduler scheduler() {
        long schedulerPeriodInMS = ConfProperties.getInt(Constants.CACHE_REFRESH_SCHEDULER_PROPERTY_NAME, Constants.CACHE_REFRESH_SCHEDULER_PROPERTY_NAME_DEFAULT_VALUE);
        return AbstractScheduledService.Scheduler.newFixedRateSchedule(1000L, schedulerPeriodInMS, TimeUnit.MILLISECONDS);
    }
}
