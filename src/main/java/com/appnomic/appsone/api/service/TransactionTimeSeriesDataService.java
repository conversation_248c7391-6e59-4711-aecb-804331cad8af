package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.opensearch.CollatedTransactionsSearchRepo;
import com.appnomic.appsone.api.dao.opensearch.RawTransactionSearchRepo;
import com.appnomic.appsone.api.dao.redis.ComponentRepo;
import com.appnomic.appsone.api.dao.redis.TimeRangeDetailsDao;
import com.appnomic.appsone.api.pojo.*;
import com.appnomic.appsone.api.pojo.request.TransactionTimeSeriesDataRequest;
import com.appnomic.appsone.api.util.DateTimeUtil;
import com.appnomic.appsone.api.util.NewTimeIntervalGenerationUtility;
import com.appnomic.appsone.api.util.ValidationUtils;
import com.appnomic.appsone.opeasearchquery.results.TabularResults;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.*;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
public class TransactionTimeSeriesDataService {

    public GenericResponse getTransactionTimeSeriesData(Request request, Response response) {
        log.trace("{} getTransactionTimeSeriesData()", Constants.INVOKED_METHOD);
        long startAPI = System.currentTimeMillis();
        GenericResponse<UIData> responseObject = new GenericResponse<>();
        try {
            TransactionTimeSeriesDataRequest requestData = getProcessedRequest(request, response);
            long startValidationTime = System.currentTimeMillis();
            GenericResponse tempResponse = requestData.validateAndPopulate();
            log.debug("Time taken for validation: {} ms.", (System.currentTimeMillis() - startValidationTime));
            if (tempResponse != null) return tempResponse;

            String accountIdentifier = requestData.getAccount().getIdentifier();

            TimeRangeDetailsDao timeRangeDetailsDao = new TimeRangeDetailsDao();
            List<TimeRangeDetails> timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(accountIdentifier);
            if (timeRangeDetailsList == null) {
                log.error("Exception occurred while fetching time range details for account {}", accountIdentifier);
                throw new ServerException("Exception occurred while fetching time range details for account " + accountIdentifier);
            }

            if (timeRangeDetailsList.isEmpty()) {
                log.trace("No time range details found for account {}. Searching for Global account.", accountIdentifier);
                timeRangeDetailsList = timeRangeDetailsDao.getTimeRangeDetails(Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
            }
            if (timeRangeDetailsList == null) {
                log.error("Exception occurred while fetching time range details for account {}", Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
                throw new ServerException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
            }

            if (timeRangeDetailsList.isEmpty()) {
                log.error("No time range details found");
                throw new ServerException("Exception occurred while fetching time range details for account " + Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT);
            }
            TimeRangeDetails timeRangeDetails = ValidationUtils.getRollupPojo(requestData.getFromTime(), requestData.getToTime(), timeRangeDetailsList);
            if (timeRangeDetails == null) {
                log.error("Time range details are invalid.");
                throw new ServerException("Time range details are invalid.");
            }
            requestData.setTimeRangeDetails(timeRangeDetails);
            requestData.setTimeRangeDetailsList(timeRangeDetailsList);

            responseObject.setData(fetchTransactionTimeSeriesData(requestData));
            responseObject.setMessage(Constants.MESSAGE_SUCCESS);
            responseObject.setResponseStatus(StatusResponse.SUCCESS.toString());
            response.status(Constants.SUCCESS_STATUS_CODE);

        } catch (Exception e) {
            responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
            responseObject.setMessage(Constants.MESSAGE_INTERNAL_ERROR);
            response.status(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            log.error("Error occurred while fetching time series data for transaction.", e);
        }
        log.debug("Time taken for API is {} ms.", (System.currentTimeMillis() - startAPI));
        return responseObject;
    }

    protected TransactionTimeSeriesDataRequest getProcessedRequest(Request request, Response response) {
        return new TransactionTimeSeriesDataRequest(request, response);
    }

    public UIData fetchTransactionTimeSeriesData(TransactionTimeSeriesDataRequest requestData) {
        log.trace("{} getTransactionTimeSeriesData(), with PARAM: {}", Constants.INVOKED_METHOD, requestData);

        if (DateTimeUtil.inRange(requestData.getFromTime())) {
            return processRawTxnData(requestData);
        }
        Account account = requestData.getAccount();
        BasicEntity service = requestData.getServiceDetails();
        KPINameDetails kpiNameDetails = requestData.getKpiNameDetails();
        long fromTime = requestData.getFromTime();
        long toTime = requestData.getToTime();
        KpiData kpiData = new KpiData();

        NewTimeIntervalGenerationUtility t = new NewTimeIntervalGenerationUtility();
        t.setTimezoneOffset(account.getIdentifier());
        t.processTimeRange(fromTime, toTime);
        t.addEndTimeInOSTime();

        String timezoneId = t.getTimeZoneId();
        List<Long> times = t.getOSTimes();
        AggregationLevel aggregationLevel = t.getNewTimeRangeDefinition().getAggregationLevel();
        int aggregationValue = aggregationLevel.getAggregrationValue();

        UIData uiData = new UIData();
        uiData.setAggregationLevel(aggregationValue);
        uiData.setDateFormat(aggregationLevel.getDataTimePattern());
        uiData.setTime(t.getDisplayTimes());

        long totalDataPoints = ((toTime - fromTime) / 60000);

        BasicKpiEntity basicKpiEntity = new ComponentRepo().getComponentKpis(Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT, Constants.TRANSACTION_IDENTIFIER_DEFAULT)
                .parallelStream().filter(c -> c.getStatus() == 1 && c.getName().equalsIgnoreCase(requestData.getKpiNameString()))
                .findAny().orElse(null);
        if (basicKpiEntity == null) {
            return uiData;
        }

        CollatedTransactionsSearchRepo collatedTransactionsSearchRepo = new CollatedTransactionsSearchRepo();
        Map<Long, Map<String, Double>> txnKpiValuesMap = collatedTransactionsSearchRepo.getTransactionCollatedDataByServiceForSingleKpi(account.getIdentifier(),
                service.getIdentifier(), basicKpiEntity.getIdentifier(), ClusterOperationEnum.SUM,
                aggregationValue, requestData.getResponseTypeString(),
                fromTime, toTime, requestData.getTimeRangeDetails(), requestData.getTimeRangeDetailsList(),
                times, timezoneId);

        times.remove(times.size() - 1);

        if (txnKpiValuesMap.isEmpty()) {
            kpiData.setValue(Collections.emptyList());
        } else {
            List<Double> values = new ArrayList<>();

            times.forEach(c -> {
                if (txnKpiValuesMap.containsKey(c)) {
                    values.add(txnKpiValuesMap.get(c).getOrDefault(basicKpiEntity.getIdentifier(), 0D));
                } else values.add(0D);
            });

            kpiData.setValue(values);
            long sum = values.stream().filter(Objects::nonNull).mapToLong(Double::longValue).sum();
            long tpm = (sum == 0 || totalDataPoints == 0) ? 0L : sum / totalDataPoints;
            log.debug("TPM is {}.", tpm);
            kpiData.setTransactionPerMinute(tpm);
        }

        kpiData.setKpi_name(kpiNameDetails.getDisplayName());
        kpiData.setUnit("Count");


        List<KpiData> chartDataList = new ArrayList<>();
        chartDataList.add(kpiData);
        uiData.setChart_data(chartDataList);

        return uiData;
    }

    private UIData processRawTxnData(TransactionTimeSeriesDataRequest requestData) {

        Account account = requestData.getAccount();
        BasicEntity service = requestData.getServiceDetails();
        KPINameDetails kpiNameDetails = requestData.getKpiNameDetails();
        long fromTime = requestData.getFromTime();
        long toTime = requestData.getToTime();
        KpiData kpiData = new KpiData();

        NewTimeIntervalGenerationUtility t = new NewTimeIntervalGenerationUtility();
        t.setTimezoneOffset(account.getIdentifier());
        t.processTimeRange(fromTime, toTime);

        String timezoneId = t.getTimeZoneId();

        AggregationLevel aggregationLevel = t.getNewTimeRangeDefinition().getAggregationLevel();
        int aggregationValue = aggregationLevel.getAggregrationValue();

        UIData uiData = new UIData();
        uiData.setAggregationLevel(aggregationValue);
        uiData.setDateFormat(aggregationLevel.getDataTimePattern());
        uiData.setTime(t.getDisplayTimes());

        List<Long> times = t.getOSTimes();
        times.remove(times.size() - 1);

        BasicKpiEntity basicKpiEntity = new ComponentRepo().getComponentKpis(Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT, Constants.TRANSACTION_IDENTIFIER_DEFAULT)
                .parallelStream().filter(c -> c.getStatus() == 1 && c.getName().equalsIgnoreCase(requestData.getKpiNameString()))
                .findAny().orElse(null);
        if (basicKpiEntity == null) {
            return uiData;
        }

        RawTransactionSearchRepo rawTransactionSearchRepo = new RawTransactionSearchRepo();
        TabularResults tabularResults = rawTransactionSearchRepo.getTransactionTSDataClusterLevel(account.getIdentifier(),
                service.getIdentifier(), null, null, requestData.getResponseTypeString(), aggregationValue,
                fromTime, toTime, timezoneId);

        if (tabularResults == null || tabularResults.getRowResults() == null || tabularResults.getRowResults().isEmpty()) {
            return uiData;
        }

        List<AttributeKpiValuePojo> attributeKpiValuePojoList = extractDataFromRawTabularResult(tabularResults);

        Map<String, List<Double>> attributeKpiValueMap = getTotalVolumeRawKpiValue(attributeKpiValuePojoList, requestData.getKpiNameString(), times);

        long totalDataPoints = ((toTime - fromTime) / 60000);

        if (attributeKpiValueMap.isEmpty()) {
            kpiData.setValue(Collections.emptyList());
        } else {
            List<Double> values = attributeKpiValueMap.get(requestData.getKpiNameString());
            kpiData.setValue(values);
            long sum = values.stream().filter(Objects::nonNull).mapToLong(Double::longValue).sum();
            long tpm = (sum == 0 || totalDataPoints == 0) ? 0L : sum / totalDataPoints;
            log.debug("TPM is {}.", tpm);
            kpiData.setTransactionPerMinute(tpm);
        }

        kpiData.setKpi_name(kpiNameDetails.getDisplayName());
        kpiData.setUnit("Count");

        List<KpiData> chartDataList = new ArrayList<>();
        chartDataList.add(kpiData);

        uiData.setChart_data(chartDataList);

        return uiData;

    }

    private Map<String, List<Double>> getTotalVolumeRawKpiValue(List<AttributeKpiValuePojo> attributeKpiValuePojoList, String kpiNameString, List<Long> originalTimes) {

        Map<String, List<Double>> attributeKpiValueMap = new LinkedHashMap<>();

        List<Long> times = new ArrayList<>(originalTimes);
        if (times.get(1) - times.get(0) != times.get(2) - times.get(1)) {
            long firstTimestamp = times.get(1) - (times.get(2) - times.get(1));
            times.remove(0);
            times.add(0, firstTimestamp);
        }

        List<Double> resultList = new ArrayList<>();

        times.forEach(t -> {
            AtomicReference<Double> val = new AtomicReference<>(0D);
            attributeKpiValuePojoList.forEach(c ->
                    val.set(Double.parseDouble(c.getValuesMap().getOrDefault(t, "0")) + val.get()));
            resultList.add(val.get());
        });

        attributeKpiValueMap.put(kpiNameString, resultList);
        return attributeKpiValueMap;

    }

    private List<AttributeKpiValuePojo> extractDataFromRawTabularResult(TabularResults tabularResults) {
        List<AttributeKpiValuePojo> attributeKpiValuePojoList = new ArrayList<>();

        for (TabularResults.ResultRow resultRow : tabularResults.getRowResults()) {
            long time = resultRow.getTimestamp().getTime();
            Map<Long, String> timeValueMap = new LinkedHashMap<>();
            AttributeKpiValuePojo attributeKpiValuePojo = new AttributeKpiValuePojo();
            for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {
                if (resultRowColumn.getColumnName().equalsIgnoreCase("responseTimes.responseStatusTag")) {
                    attributeKpiValuePojo.setAttributeName(resultRowColumn.getColumnValue());
                }
            }
            timeValueMap.put(time, String.valueOf(resultRow.getCountValue()));
            attributeKpiValuePojo.setValuesMap(timeValueMap);
            attributeKpiValuePojoList.add(attributeKpiValuePojo);
        }
        return attributeKpiValuePojoList;

    }
}
