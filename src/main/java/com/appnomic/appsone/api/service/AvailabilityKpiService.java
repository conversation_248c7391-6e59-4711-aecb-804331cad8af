package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.businesslogic.AvailabilityKpiBL;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.AvailabilityKpi;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.AvailabilityKpiRequest;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

import java.util.List;

/**
 * <AUTHOR> on 09/05/22
 */
@Slf4j
public class AvailabilityKpiService {

    public GenericResponse<List<AvailabilityKpi>> getAvailabilityKpiList(Request request, Response response) {
        log.trace("{} getAvailabilityKpiList. PARAMS: {}", Constants.INVOKED_METHOD, request);
        long st = System.currentTimeMillis();
        GenericResponse<List<AvailabilityKpi>> genericResponse = new GenericResponse<>();

        try {
            AvailabilityKpiBL bl = new AvailabilityKpiBL();
            RequestObject requestObject = new RequestObject(request);

            UtilityBean<AvailabilityKpiRequest> clientData = bl.clientValidation(requestObject);
            UtilityBean<AvailabilityKpiRequest> serverData = bl.serverValidation(clientData);
            List<AvailabilityKpi> availabilityKpiList = bl.processData(serverData);

            genericResponse.setData(availabilityKpiList);
            genericResponse.setResponseStatus(StatusResponse.SUCCESS.name());
            genericResponse.setMessage(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (DataProcessingException de) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while processing data for get availability kpi list.", de);
            CommonUtils.populateErrorResponse(genericResponse, response, de.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ServerException se) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while server validation for get availability kpi list.", se);
            CommonUtils.populateErrorResponse(genericResponse, response, se.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (ClientException ce) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Error occurred while client validation for get availability kpi list.", ce);
            CommonUtils.populateErrorResponse(genericResponse, response, ce.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (Exception e) {
            HealUICache.INSTANCE.updateHealUIErrors(1);
            log.error("Exception for get availability kpi list.", e);
            CommonUtils.populateErrorResponse(genericResponse, response, e.getMessage(), 500);
        } finally {
            log.debug("Total time taken to load availability kpi list: {}", System.currentTimeMillis() - st);
        }
        return genericResponse;
    }

}
