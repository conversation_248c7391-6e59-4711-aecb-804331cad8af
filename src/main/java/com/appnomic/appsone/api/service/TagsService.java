package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.beans.*;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.cache.MasterCache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.dao.TagsDao;
import com.appnomic.appsone.api.dao.redis.MasterRepo;
import com.appnomic.appsone.api.pojo.TagMappingDetails;
import com.appnomic.appsone.api.pojo.TagPreDefinedData;
import com.appnomic.appsone.api.pojo.Tags;
import com.appnomic.appsone.api.service.mysql.TagsDataService;
import com.appnomic.appsone.api.util.DateTimeUtil;
import com.appnomic.appsone.api.util.StringUtils;
import com.heal.configuration.pojos.TagDetails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * <AUTHOR> : 23/1/19
 */
public class TagsService {
    private static final Logger logger = LoggerFactory.getLogger(TagsService.class);

    /**
     * Create tags mapping and tags if a new tag is found
     */
    public Map<String, String> addTags(List<Tags> tags, int accountId, int objectId, String objectName, String userId, TagsDao tagsDao) throws Exception {
        Map<String, String> message = new HashMap<>();
        List<TagMappingDetails> tagMappingDetailsList = new ArrayList<>();
        for (Tags tag : tags) {

            //TODO: Validate the tag name and identifier fields.
            String tagName = tag.getName().trim();
            String tagKey = tag.getIdentifier().trim();
            String tagValue = tag.getValue();
            if (tagValue == null)
                tagValue = tagKey;
            else
                tagValue = tagValue.trim();

            /**
             * Check the tag name in master table
             */
            TagDetails tagDetailsBean = new MasterRepo().getTagDetails().stream()
                    .filter(t -> t.getName().equalsIgnoreCase(tagName))
                    .findAny()
                    .orElse(null);
            if (tagDetailsBean == null) {
                String log = "Tag detail is not found for given tag name-" + tagName + " and account id-" + accountId;
                logger.warn(log);
                message.put("tagName-index-" + tag.toString(), log);
                throw new Exception(log);
            }

            /**
             * If reference table is null or empty then only need to insert data inot tab mapping table
             */
            String refTable = tagDetailsBean.getRefTable();
            if (refTable == null || refTable.equalsIgnoreCase("null") || refTable.length() < 1) {
                TagMappingDetails tagMappingDetails = createTagMappingDetailsObj(tagDetailsBean.getId(), objectId, objectName, tagKey, tagValue, userId, accountId);
                if (tagMappingDetails != null) tagMappingDetailsList.add(tagMappingDetails);
                continue;
            }

            /**
             * Get id from reference table for a given identifier
             */
            TagPreDefinedData preDefinedData = null;
            if (tagDetailsBean.isPredefined()) {
                preDefinedData = TagsDataService.getTagData(refTable, tagKey,
                        tagDetailsBean.getRefSelectColumnName(), tagDetailsBean.getRefWhereColumnName(), accountId);

                if(preDefinedData != null) {
                    logger.debug("TagPreDefinedData:"+preDefinedData.toString());
                    tagKey = preDefinedData.getTagKey();
                    tagValue = preDefinedData.getTagValue();
/*
                    if(tagDetailsBean.getName().equals(Constants.TIME_ZONE_TAG)) {
                        tagValue = MasterCache.getInstance().getTimeZoneOffset(preDefinedData) +"";
                    }*/
                }
            }

            if (tag.getSubTypeName() != null && tag.getSubTypeName().trim().length() > 1) {

                /**
                 * If object id is not fount in reference table then we have to create tha tag for a given identifier
                 */
                if (preDefinedData == null && tagDetailsBean.isPredefined()) {

                    com.heal.configuration.pojos.ViewTypes masterSubTypeBean = new MasterRepo().getTypes().stream()
                            .filter(v -> v.getTypeName().equalsIgnoreCase(Constants.CONTROLLER_TYPE_NAME_DEFAULT))
                            .filter(v -> v.getSubTypeName().equalsIgnoreCase(tag.getSubTypeName().trim()))
                            .findAny().orElse(null);

                    if (masterSubTypeBean == null) {
                        String log = "unable to fetch subType information for provided tag value: " + tag.getSubTypeName();
                        message.put("sub-type-not-found", log);
                        throw new Exception(log);
                    }

                    int subTypeId = masterSubTypeBean.getSubTypeId();

                    logger.debug("No value is found for given tag name-" + tagName + ", key-"+tagKey+", value-" + tagValue);
                    int tagIdentifierId = createNewTag(accountId, tagKey, tagValue, userId, subTypeId, false, tagsDao);
                    if (tagIdentifierId < 1) {
                        String log = "Failed to create tag entry for : " + tag.getName() + ", for account id: " + accountId;
                        logger.error(log);
                        message.put("tag-not-found", "create tag failed for tag: " + tagName);
                        throw new Exception(log);
                    } else {
                        logger.info("Tag created with id: " + tagIdentifierId);
                        tagKey = tagIdentifierId+"";
                    }
                }
            }

            /**
             * Add the tag bean object into list for bulk update
             */
            TagMappingDetails tagMappingDetails = createTagMappingDetailsObj(tagDetailsBean.getId(), objectId, objectName, tagKey, tagValue, userId, accountId);
            if (tagMappingDetails != null) tagMappingDetailsList.add(tagMappingDetails);
            String layer = tag.getLayer();
            /**
             * If layer is available then add into tag mapping table
             */
            if (!StringUtils.isEmpty(layer)) {
                tagMappingDetails = addLayer(tagKey, userId, accountId, layer);
                if (tagMappingDetails != null) tagMappingDetailsList.add(tagMappingDetails);
            }
        }

        if (!tagMappingDetailsList.isEmpty()) {
            TagsDataService.addTagMappingDetails(tagMappingDetailsList, tagsDao);
        }
        return message;
    }


    int createNewTag(int accountId, String identifier, String aliasName, String userId, int controllerTypeId, boolean isAPartOfDBTransaction, TagsDao tagsDao) {
        logger.trace(Constants.INVOKED_METHOD + "createNewTag");
        int tagId = -1;
        try {
            ControllerBean controllerBean = new ControllerBean();
            controllerBean.setName(identifier);
            controllerBean.setIdentifier(aliasName);
            controllerBean.setAccountId(accountId);
            controllerBean.setUserDetailsId(userId);
            controllerBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
            controllerBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
            controllerBean.setControllerTypeId(controllerTypeId);
            if (isAPartOfDBTransaction) tagId = TagsDataService.createNewTag(controllerBean, tagsDao);
            else tagId = TagsDataService.createNewTag(controllerBean);
            if (tagId > 0) {
                logger.info("Tag: " + identifier + " created successfully for account id: " + accountId);
            }
        } catch (Exception e) {
            logger.error("Error occurred while adding new tag", e);
        }
        return tagId;
    }

    private TagMappingDetails createTagMappingDetailsObj(int tagId, int objId, String objectName, String tagKey, String tagValue, String userId, int accountId) {
        int tagMappingId = TagsDataService.getTagMappingId(tagId, objId, objectName, tagKey, tagValue, accountId);
        if (tagMappingId != 0) {
            logger.warn("Data is already available in tag_mapping table for tagId-" + tagId + ",objId" + objId + ",objectName-" + objectName + ",tagKey-" + tagKey + ",tagValue-" + tagValue + ",accountId-" + accountId);
            return null;
        }
        TagMappingDetails tagMappingDetails = new TagMappingDetails();
        tagMappingDetails.setTagId(tagId);
        tagMappingDetails.setObjectId(objId);
        tagMappingDetails.setObjectRefTable(objectName);
        tagMappingDetails.setTagKey(tagKey);
        tagMappingDetails.setTagValue(tagValue);
        String date = DateTimeUtil.getTimeInGMT(System.currentTimeMillis());
        tagMappingDetails.setCreatedTime(date);
        tagMappingDetails.setUpdatedTime(date);
        tagMappingDetails.setUserDetailsId(userId);
        tagMappingDetails.setAccountId(accountId);
        return tagMappingDetails;
    }

    private TagMappingDetails addLayer(String tagKey, String userId, int accountId, String layerName) {
        TagDetails tagDetailsBean = new MasterRepo().getTagDetails().stream()
                .filter(t -> t.getName().equalsIgnoreCase(Constants.LAYER_TAG))
                .findAny()
                .orElse(null);
        if (tagDetailsBean == null) {
            String log = "Tag detail is not found for given tag name-" + Constants.LAYER_TAG + " and account id-" + accountId;
            logger.warn(log);
            return null;
        }
        return createTagMappingDetailsObj(tagDetailsBean.getId(), Integer.parseInt(tagKey), "controller", Constants.LAYER_DEFAULT, layerName, userId, accountId);
    }
}
