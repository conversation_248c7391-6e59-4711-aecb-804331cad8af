package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.beans.AccessDetailsBean;
import com.appnomic.appsone.api.beans.UserAccessBean;
import com.appnomic.appsone.api.beans.UserAccountIdentifiersBean;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.cache.MasterCache;
import com.appnomic.appsone.api.cache.UserDetailsCache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.ApplicationRepo;
import com.appnomic.appsone.api.dao.redis.ServiceRepo;
import com.appnomic.appsone.api.pojo.Account;
import com.appnomic.appsone.api.pojo.AccountResponse;
import com.appnomic.appsone.api.pojo.ApplicationDetail;
import com.appnomic.appsone.api.pojo.UserAccessDetails;
import com.appnomic.appsone.api.service.mysql.ApplicationMigrationService;
import com.appnomic.appsone.api.service.mysql.UserAccessDataService;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.DateTimeUtil;
import com.appnomic.appsone.model.JWTData;
import com.google.gson.reflect.TypeToken;
import com.heal.configuration.entities.ApplicationMigrationDateRangesBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.lang.reflect.Type;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

public class AccountService {

    private static final Logger logger = LoggerFactory.getLogger(AccountService.class);

    public static AccountResponse getAccountList(String appToken) {
        AccountResponse response = new AccountResponse();

        try {
            JWTData jwtData = KeyCloakAuthService.extractUserDetails(appToken);

            UserAccessBean accessDetails = UserAccessDataService.getUserAccessDetails(jwtData.getSub().trim());

            Type userBeanType = new TypeToken<AccessDetailsBean>() {
            }.getType();

            AccessDetailsBean bean = CommonUtils.jsonToObject(accessDetails.getAccessDetailsJson(), userBeanType);

            List<Account> accessibleAccounts = new ArrayList<>();

            if (bean != null && bean.getAccounts() != null) {
                accessibleAccounts = MasterCache.getInstance().getAccounts();
                if (!bean.getAccounts().contains("*")) {
                    accessibleAccounts = accessibleAccounts.parallelStream().filter(acc -> bean.getAccounts().contains(acc.getIdentifier())).collect(Collectors.toList());
                }
            }
            response.setData(accessibleAccounts);
            response.setResponse_message("");
            response.setResponse_status("SUCCESS");
        } catch (Exception e) {
            logger.error("Error occurred while getting all accounts.", e);
            response.setResponse_message(e.getMessage());
            response.setResponse_status("FAILURE");
        }
        return response;
    }

    public GenericResponse<List<ApplicationDetail>> getApplicationList(Request request, Response response) {
        GenericResponse<List<ApplicationDetail>> responseObject = new GenericResponse<>();
        AccountRepo accountRepo = new AccountRepo();
        try {

            String accountIdentifier = request.params("identifier");
            com.heal.configuration.pojos.Account account = accountRepo.getAccount(accountIdentifier);

            if (account == null) {
                response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
                responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
                responseObject.setMessage("Invalid account identifier provided.");
                return responseObject;
            }

            String userId = CommonUtils.getUserId(request);

            UserAccessDetails accessDetails;
            try {
                accessDetails = UserDetailsCache.getInstance().userApplications
                        .get(new UserAccountIdentifiersBean(userId, account.getIdentifier()));
            } catch (ExecutionException e) {
                logger.error("Exception while fetching user access details. Reason: {}", e.getMessage(), e);

                response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
                responseObject.setResponseStatus(StatusResponse.FAILURE.toString());
                responseObject.setMessage("Error in fetching user access details");
                return responseObject;
            }

            boolean timeRangesRequired = Boolean.parseBoolean(request.queryParamOrDefault("isReportTimeRangesRequired", "false"));
            List<ApplicationDetail> data = getAccessibleApplication(accessDetails, account, timeRangesRequired);
            //Added as per acceptance criteria for sorted list
            data.sort(Comparator.comparing(ApplicationDetail::isHasTransactionConfigured, Comparator.reverseOrder())
                    .thenComparing(ApplicationDetail::getName));

            responseObject.setResponseStatus(StatusResponse.SUCCESS.toString());
            responseObject.setMessage("Success");
            responseObject.setData(data);
            response.status(Constants.SUCCESS_STATUS_CODE);

        } catch (Exception e) {
            logger.error("Error occurred while fetching accessible application list for user.", e);
        }
        return responseObject;
    }

    private List<ApplicationDetail> getAccessibleApplication(UserAccessDetails user, com.heal.configuration.pojos.Account account, boolean timeRangesRequired) {
        List<ApplicationDetail> result = new ArrayList<>();
        try {
            List<String> userApps = user.getApplicationIdentifiers();
            ServiceRepo serviceRepo = new ServiceRepo();

            if (userApps.isEmpty()) {
                logger.error("There is no application map with user.");
                return Collections.emptyList();
            }

            List<com.heal.configuration.pojos.Application> applicationList = new ApplicationRepo().getAllApplicationDetails(account.getIdentifier());

            Map<Integer, Map<String, Long>> appTimeRangesMap = new HashMap<>();
            if (timeRangesRequired) {
                int timezoneOffset = account.getTags().stream()
                        .filter(t -> t.getType().equalsIgnoreCase("Timezone"))
                        .map((timezoneTag -> Integer.parseInt(timezoneTag.getValue())))
                        .findAny().orElse(********);
                String accountTimeZone = ZoneId.ofOffset("GMT", ZoneOffset.ofTotalSeconds(timezoneOffset / 1000)).getId();

                List<ApplicationMigrationDateRangesBean> applicationMigrationDateRangesBeanList = new ApplicationMigrationService().getApplicationMigrationDateRanges();
                applicationMigrationDateRangesBeanList.forEach(c -> {
                    Map<String, Long> timeRangesMap = new HashMap<>();

                    long st = DateTimeUtil.getGMTToLongTimeInRequiredTimeZone(c.getNonRollupIndexStartTime(), accountTimeZone);
                    if (st != 0L) {
                        timeRangesMap.put("nonRolledUpStartTime", st);
                    }

                    long et = DateTimeUtil.getGMTToLongTimeInRequiredTimeZone(c.getNonRollupIndexEndTime(), accountTimeZone);
                    if (st != 0L && et != 0L) {
                        timeRangesMap.put("nonRolledUpEndTime", et + Constants.DAY - 1);
                    }

                    //Rolled UP Start Time
                    st = DateTimeUtil.getGMTToLongTimeInRequiredTimeZone(c.getRollupIndexStartTime(), accountTimeZone);
                    if (st != 0L) {
                        timeRangesMap.put("rolledUpStartTime", st);
                    }

                    et = DateTimeUtil.getGMTToLongTimeInRequiredTimeZone(c.getRollupIndexEndTime(), accountTimeZone);
                    if (st != 0L && et != 0L) {
                        timeRangesMap.put("rolledUpEndTime", et + Constants.DAY - 1);
                    }

                    if (!timeRangesMap.isEmpty()) {
                        appTimeRangesMap.put(c.getApplicationId(), timeRangesMap);
                    }

                });
            }

            result = applicationList.stream()
                    .filter(it -> userApps.contains(it.getIdentifier()))
                    .map(a -> {
                        boolean isTxnConfigured = HealUICache.INSTANCE.getApplicationServiceList(account.getIdentifier(), a.getIdentifier())
                                .stream()
                                .anyMatch(s -> serviceRepo.getTransactionsByServiceIdentifier(account.getIdentifier(), s.getIdentifier())
                                        .parallelStream().anyMatch(t -> t.getStatus() == 1));
                        return ApplicationDetail.builder()
                                .id(a.getId())
                                .identifier(a.getIdentifier())
                                .name(a.getName())
                                .hasTransactionConfigured(isTxnConfigured)
                                .reportTimeRanges(appTimeRangesMap.getOrDefault(a.getId(), null))
                                .build();
                    }).collect(Collectors.toList());

        } catch (Exception e) {
            logger.error("Error occurred while processing accessible list for user: {}", user);
        }
        return result;
    }
}
