package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.beans.UserAttributesBean;
import com.appnomic.appsone.api.businesslogic.UserAccessibleActionBL;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.AppsoneException;
import com.appnomic.appsone.api.custom.exceptions.RequestException;
import com.appnomic.appsone.api.pojo.UserAccessibleActions;
import com.appnomic.appsone.api.util.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

public class UserAccessibleActionService {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserAccessibleActionService.class);
    private static final String ERROR_MESSAGE = "user-access-info REST CALL failed. Reason: {}";

    public GenericResponse<UserAccessibleActions> getUserAccessibilityDetails(Request request, Response response) {
        try {
            return validateAndFetchUserAccessibleDetails(request, response);
        } catch (Exception e) {
            LOGGER.error("Encountered exception while processing user-access-info REST call. Reason: {}", e.getMessage(), e);

            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(), Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
                    "Internal Server Error. Kindly contact the administrator.", null, true);
        }
    }

    private GenericResponse<UserAccessibleActions> validateAndFetchUserAccessibleDetails(Request request, Response response) {
        UserAccessibleActionBL userAccessibleAction = new UserAccessibleActionBL();

        String userId;
        try {
            userId = userAccessibleAction.clientValidation(request);
        } catch (RequestException | AppsoneException e) {
            LOGGER.error(ERROR_MESSAGE, e.getMessage(), e);

            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
                    "Request validation failure. Refer to application logs for more details.", null, true);
        }

        UserAttributesBean userAttributesBean;
        try {
            userAttributesBean = userAccessibleAction.serverValidation(userId);
        } catch (AppsoneException e) {
            LOGGER.error(ERROR_MESSAGE, e.getMessage(), e);

            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
                    "User details unavailable.", null, true);
        }

        UserAccessibleActions userActionsDetails;
        try {
            userActionsDetails = userAccessibleAction.getUserAccessibleActions(userAttributesBean);
        } catch (AppsoneException e) {
            LOGGER.error(ERROR_MESSAGE, e.getMessage(), e);

            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
                    "Error while fetching user access details. Refer to application logs for more details.", null, true);
        }

        GenericResponse<UserAccessibleActions> genericResponse = CommonUtils.getGenericResponse(response, StatusResponse.SUCCESS.name(),
                Constants.SUCCESS_STATUS_CODE, "User access information successfully fetched.", null, false);
        genericResponse.setData(userActionsDetails);

        return genericResponse;
    }
}
