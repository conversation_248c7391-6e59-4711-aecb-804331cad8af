package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.businesslogic.ApplicationWiseSignalsBL;
import com.appnomic.appsone.api.businesslogic.SignalBL;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.common.*;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.custom.exceptions.UserException;
import com.appnomic.appsone.api.dao.opensearch.SignalSearchRepo;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.ApplicationRepo;
import com.appnomic.appsone.api.dao.redis.MasterRepo;
import com.appnomic.appsone.api.dao.redis.ServiceRepo;
import com.appnomic.appsone.api.pojo.Controller;
import com.appnomic.appsone.api.pojo.SignalData;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.model.JWTData;
import com.heal.configuration.pojos.*;
import com.heal.configuration.pojos.opensearch.SignalDetails;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
public class ApplicationWiseSignalsService {
    private static int SIGNAL_CLOSE_WINDOW_TIME = Integer.parseInt(ConfProperties.getString(Constants.SIGNAL_CLOSE_WINDOW_TIME, Constants.SIGNAL_CLOSE_WINDOW_DEFAULT_TIME));

    public GenericResponse<Set<SignalData>> getApplicationWiseSignals(Request request, Response response) {
        log.trace("{} getApplicationHealthStatus. PARAMS: {}", Constants.INVOKED_METHOD, request);
        GenericResponse<Set<SignalData>> responseObject;

        try {
            ApplicationWiseSignalsBL.clientValidation(request);

            String identifier = request.params(Constants.REQUEST_PARAM_IDENTIFIER);
            List<String> appIds = new ArrayList<>();
            if (request.params().get(Constants.REQUEST_PARAM_APPLICATION_SMALLID).equalsIgnoreCase("all")) {
                appIds = Arrays.stream(request.queryParams(Constants.REQUEST_QUERY_PARAM_APPLICATION_ID).toLowerCase().split(",")).collect(Collectors.toList());
            } else {
                appIds.add(request.params(Constants.REQUEST_PARAM_APPLICATION_SMALLID));
            }
            String fromTimeString = request.queryParams(Constants.REQUEST_PARAM_FROM_TIME);
            String toTimeString = request.queryParams(Constants.REQUEST_PARAM_TO_TIME);
            String status = request.queryParams(Constants.STATUS_TAG);
            Long fromTime = (fromTimeString == null) ? null : Long.parseLong(fromTimeString);
            Long toTime = (toTimeString == null) ? null : Long.parseLong(toTimeString);

            try {
                List<InstallationAttributes> installationAttributesList = new MasterRepo().getInstallationAttributes();
                if (!installationAttributesList.isEmpty()) {
                    installationAttributesList.parallelStream()
                            .filter(attrs -> attrs.getName().equals(Constants.SIGNAL_CLOSE_WINDOW_TIME))
                            .findAny()
                            .ifPresent(installationAttributeBean -> SIGNAL_CLOSE_WINDOW_TIME = Integer.parseInt(installationAttributeBean.getValue()));
                }
            } catch (Exception ex) {
                log.error("Exception encountered while getting Signal Window Closing Time based on attributes. Reason: {}", ex.getMessage(), ex);
            }

            if (null != fromTime && null != toTime) {
                long signalWindowTime = toTime - TimeUnit.MINUTES.toMillis(SIGNAL_CLOSE_WINDOW_TIME);
                if (fromTime > signalWindowTime) {
                    fromTime = signalWindowTime;
                }
            }

            if (status != null) {
                status = status.toUpperCase();
            }

            String authKey = request.headers("Authorization");
            JWTData jwtData = KeyCloakAuthService.extractUserDetails(authKey);
            String userId = jwtData.getSub();
            if (userId == null) {
                throw new ServerException(Constants.MESSAGE_INVALID_AUTH_TOKEN);
            }

            Account account = new AccountRepo().getAccount(identifier);
            if (account == null) {
                log.error("Invalid account identifier: {}.", identifier);
                throw new ServerException(UIMessages.ERROR_INVALID_ACCOUNT_ID);
            }

            Set<SignalData> result = new HashSet<>();

            ApplicationRepo applicationRepo = new ApplicationRepo();
            Set<String> accessibleApplications = applicationRepo.getAccessibleApplicationsByUserId(userId, account.getIdentifier()).
                    stream().map(BasicEntity::getIdentifier).collect(Collectors.toSet());
            List<Application> accessibleApps = applicationRepo.getAllApplicationDetails(account.getIdentifier())
                    .stream()
                    .filter(a -> accessibleApplications.contains(a.getIdentifier())).collect(Collectors.toList());

            Map<String, Controller> serviceMap = new ServiceRepo().getAllServices(account.getIdentifier())
                    .parallelStream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(BasicEntity::getIdentifier, c -> Controller.builder()
                            .appId(String.valueOf(c.getId()))
                            .name(c.getName())
                            .identifier(c.getIdentifier())
                            .build()));


            List<ViewTypes> typeDetailsList = new MasterRepo().getTypes();
            Set<SignalDetails> signalIds = new SignalSearchRepo().getAllSignals(account.getIdentifier(), Collections.emptySet(), Collections.emptySet(), fromTime, toTime)
                    .parallelStream().filter(Objects::nonNull).collect(Collectors.toSet());
            log.debug("Signal Ids fetched from OS: {}", signalIds.size());

            String finalStatus = status;
            appIds.forEach(appId -> {
                Application application;
                try {
                    application = accessibleApps.stream()
                            .filter(app -> app.getId() == Integer.parseInt(appId))
                            .findFirst()
                            .orElseThrow(() -> new ServerException("Invalid application id: " + appId));
                } catch (ServerException e) {
                    throw new RuntimeException(e);
                }

                UtilityBean<?> utilityBean = UtilityBean.builder()
                        .accountIdString(identifier)
                        .account(account)
                        .application(application)
                        .build();

                if (!signalIds.isEmpty()) {
                    result.addAll(getOpenApplicationSignals(signalIds, utilityBean, finalStatus, serviceMap, typeDetailsList));
                }
            });

            responseObject = new GenericResponse<>();
            responseObject.setData(result);
            responseObject.setResponseStatus(StatusResponse.SUCCESS.toString());
            responseObject.setMessage(Constants.MESSAGE_SUCCESS);
            response.status(Constants.SUCCESS_STATUS_CODE);

        } catch (UserException e) {
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.toString(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, e.getSimpleMessage(),
                    null, true);
        } catch (Exception e) {
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.toString(),
                    Constants.INTERNAL_SERVER_ERROR_STATUS_CODE, Constants.MESSAGE_INTERNAL_ERROR, e, true);
        }
        return responseObject;
    }

    private Set<SignalData> getOpenApplicationSignals(Set<SignalDetails> signalRow, UtilityBean<?> utilityBean, String status,
                                                      Map<String, Controller> controllerMap, List<ViewTypes> typeDetailsList) {
        log.trace("{} getOpenProblem()", Constants.INVOKED_METHOD);

        if (signalRow == null) {
            return new HashSet<>();
        }

        return signalRow.stream().map(signal -> {
                    try {
                        Set<SignalData> signalDataSet = new HashSet<>();
                        if (!signal.getSignalType().equalsIgnoreCase(SignalType.INFO.name())) {
                            if (status == null || signal.getCurrentStatus().equalsIgnoreCase(status)) {

                                SignalData signalData = getSignalData(signal, utilityBean.getAccount(), controllerMap, typeDetailsList);

                                Set<String> affectedServices = signal.getServiceIds();
                                if (SignalType.BATCH_JOB.name().equalsIgnoreCase(signal.getSignalType())) {

                                    List<Controller> controllers = affectedServices.stream()
                                            .map(identifier -> controllerMap.getOrDefault(identifier, null))
                                            .filter(Objects::nonNull)
                                            .collect(Collectors.toList());

                                    signalData.setAffectedServices(new HashSet<>());

                                    //In case of batch job, there are no applications, the services are considered as applications
                                    signalData.addApplications(controllers);
                                    signalDataSet.add(signalData);
                                } else {
                                    signalData.setAffectedServices(affectedServices);

                                    List<Controller> applicationsAffected = affectedServices.parallelStream()
                                            .map(c -> HealUICache.INSTANCE.getServiceApplicationList(utilityBean.getAccountIdString(), c))
                                            .filter(Objects::nonNull)
                                            .flatMap(Collection::parallelStream)
                                            .map(c -> Controller.builder()
                                                    .appId(String.valueOf(c.getId()))
                                                    .name(c.getName())
                                                    .identifier(c.getIdentifier())
                                                    .build())
                                            .distinct()
                                            .collect(Collectors.toList());

                                    for (Controller c : applicationsAffected) {
                                        if (String.valueOf(utilityBean.getApplication().getId()).equals(c.getAppId())) {
                                            signalData.addApplications(applicationsAffected);
                                            signalDataSet.add(signalData);
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                        return signalDataSet;
                    } catch (Exception e) {
                        log.error("Exception while getting signalData for signal Id {}. ", signal.getSignalId(), e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .flatMap(Collection::parallelStream)
                .collect(Collectors.toSet());
    }

    protected SignalData getSignalData(SignalDetails signal, Account account, Map<String, Controller> controllerMap, List<ViewTypes> typeDetailsList) {
        SignalData signalData = null;
        if (signal != null) {
            ViewTypes typeDetails = typeDetailsList.stream().filter((type) -> type.getSubTypeId() == signal.getSeverityId()).findAny().orElse(null);
            //If No severity type found, then Severity Name is marked as "NA here" instead of Severe or Default
            String severityName = typeDetails == null ? "NA" : typeDetails.getSubTypeName();
            String time = signal.getMetadata().get("end_time");
            signalData = new SignalData();
            signalData.setId(signal.getSignalId());
            signalData.setType(SignalType.valueOf(signal.getSignalType()).getDisplayName());
            signalData.setCurrentStatus(SignalStatus.valueOf(signal.getCurrentStatus()).getReturnType());
            signalData.setSeverity(severityName);
            signalData.setStartTimeMilli(signal.getStartedTime());
            signalData.setUpdatedTimeMilli((time == null) ? 0L : Long.parseLong(signal.getMetadata().get("end_time")));
            signalData.setDescription(SignalBL.getProblemDescription(signal, account, controllerMap, severityName));
            Set<String> anomalyIdSet = signal.getAnomalies();
            long anomalyCount = (anomalyIdSet == null) ? 0 : anomalyIdSet.size();
            log.debug("Number of anomaly events in signal: {}, is {}.", signalData.getId(), anomalyCount);
            signalData.setEventCount(anomalyCount);
            Long txnAnomalyTime = signal.getTxnAnomalyTime();
            Long kpiAnomalyTime = signal.getUpdatedTime();
            if (txnAnomalyTime != null || kpiAnomalyTime != null) {
                if (txnAnomalyTime == null) {
                    signalData.setLastEventTime(kpiAnomalyTime);
                } else if (kpiAnomalyTime == null) {
                    signalData.setLastEventTime(txnAnomalyTime);
                } else {
                    signalData.setLastEventTime((txnAnomalyTime >= kpiAnomalyTime) ? txnAnomalyTime : kpiAnomalyTime);
                }
            }
            signalData.setEntryServices(signal.getEntryServiceId());
        }
        return signalData;
    }
}
