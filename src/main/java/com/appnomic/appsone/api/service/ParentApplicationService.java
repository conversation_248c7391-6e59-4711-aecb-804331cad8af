package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.businesslogic.ParentApplicationServiceBL;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import com.heal.configuration.pojos.Application;
import spark.Request;
import spark.Response;

import java.util.List;

public class ParentApplicationService {
    public GenericResponse<List<Application>> getApplicationListMappedtoParentApplication(Request request, Response response) {
        List<Application> data;
        ParentApplicationServiceBL bl = new ParentApplicationServiceBL();
        try {
            RequestObject requestObject = new RequestObject(request);
            UtilityBean<Object> clientValidation = bl.clientValidation(requestObject);
            UtilityBean<Object> serverValidation = bl.serverValidation(clientValidation);
            data = bl.processData(serverValidation);
        } catch (ClientException | ServerException | DataProcessingException e) {
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
                    e.getMessage(), null, true);
        }
        GenericResponse<List<Application>> genericResponse = CommonUtils.getGenericResponse(response, StatusResponse.SUCCESS.name(),
                Constants.SUCCESS_STATUS_CODE, "Application Detail(s)", null, false);
        genericResponse.setData(data);
        return genericResponse;
    }
}
