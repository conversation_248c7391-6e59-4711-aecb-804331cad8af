package com.appnomic.appsone.api.service.mysql;

import com.appnomic.appsone.api.dao.SolutionRecommendationDao;
import com.appnomic.appsone.api.manager.MySQLConnectionManager;
import com.appnomic.appsone.api.beans.SolutionRecommendationFeedbackBean;
import com.appnomic.appsone.api.pojo.SolutionIsUsefulFeedbackPojo;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.sqlobject.Bind;

import java.util.List;

@Slf4j
public class SolutionRecommendationDataService {

    public int createFeedbackEntry(SolutionRecommendationFeedbackBean feedbackPojo) {
        SolutionRecommendationDao solutionRecommendationDao = MySQLConnectionManager.getInstance().open(SolutionRecommendationDao.class);
        try {
            return solutionRecommendationDao.createFeedbackEntry(feedbackPojo);
        } catch (Exception e)   {
            log.error("Error occurred while creating new feedback entry: {}", feedbackPojo, e);
        } finally {
            MySQLConnectionManager.getInstance().close(solutionRecommendationDao);
        }
        return 0;
    }

    public SolutionRecommendationFeedbackBean getUserFeedback(String signalId, String feedbackUserId) {
        SolutionRecommendationDao solutionRecommendationDao = MySQLConnectionManager.getInstance().open(SolutionRecommendationDao.class);
        try {
            return solutionRecommendationDao.getUserFeedback(signalId, feedbackUserId);
        } catch (Exception e) {
            log.error("Error occurred while creating new feedback entry for signal id: {}", signalId, e);
        } finally {
            MySQLConnectionManager.getInstance().close(solutionRecommendationDao);
        }
        return null;
    }

    public int createFeedbackMapping(SolutionRecommendationFeedbackBean feedback) {
        SolutionRecommendationDao solutionRecommendationDao = MySQLConnectionManager.getInstance().open(SolutionRecommendationDao.class);
        try {
            return solutionRecommendationDao.createFeedbackMapping(feedback);
        } catch (Exception e) {
            log.error("Error occurred while creating new feedback entry: {}", feedback, e);
        } finally {
            MySQLConnectionManager.getInstance().close(solutionRecommendationDao);
        }
        return 0;
    }

    public int updateSolutionFeedbackComment(String comments, String feedbackUserId, String signalId, String updatedTime) {
        SolutionRecommendationDao solutionRecommendationDao = MySQLConnectionManager.getInstance().open(SolutionRecommendationDao.class);
        try {
            return solutionRecommendationDao.updateFeedbackComment(comments, feedbackUserId, signalId, updatedTime);
        } catch (Exception e) {
            log.error("Error occurred while creating new feedback entry for signal id: {}", signalId, e);
        } finally {
            MySQLConnectionManager.getInstance().close(solutionRecommendationDao);
        }
        return 0;
    }

    public int updateSolutionFeedbackIsUseful(int isUseful, int feedbackId, String updatedTime, int solutionId, int clusterId) {
        SolutionRecommendationDao solutionRecommendationDao = MySQLConnectionManager.getInstance().open(SolutionRecommendationDao.class);
        try {
            return solutionRecommendationDao.updateFeedbackIsUseful(isUseful, feedbackId, updatedTime, solutionId, clusterId);
        } catch (Exception e) {
            log.error("Error occurred while creating new feedback entry for feedback id: {}", feedbackId, e);
        } finally {
            MySQLConnectionManager.getInstance().close(solutionRecommendationDao);
        }
        return 0;
    }

    public int upsertSolutionFeedbackIsUseful(int isUseful, int feedbackId, String updatedTime, int solutionId, int clusterId, int signalStatus, String createdTime) {
        SolutionRecommendationDao solutionRecommendationDao = MySQLConnectionManager.getInstance().open(SolutionRecommendationDao.class);
        try {
            return solutionRecommendationDao.upsertFeedbackIsUseful(isUseful, feedbackId, updatedTime, solutionId, clusterId, signalStatus, createdTime);
        } catch (Exception e) {
            log.error("Error occurred while creating new feedback entry for feedback id: {}", feedbackId, e);
        } finally {
            MySQLConnectionManager.getInstance().close(solutionRecommendationDao);
        }
        return 0;
    }

    public List<SolutionIsUsefulFeedbackPojo> getFeedbackIsUseful(int feedbackId) {
        SolutionRecommendationDao solutionRecommendationDao = MySQLConnectionManager.getInstance().open(SolutionRecommendationDao.class);
        try {
            return solutionRecommendationDao.getFeedbackIsUseful(feedbackId);
        } catch (Exception e) {
            log.error("Error occurred while creating new feedback entry for feedback id: {}", feedbackId, e);
        } finally {
            MySQLConnectionManager.getInstance().close(solutionRecommendationDao);
        }
        return null;
    }

    public SolutionIsUsefulFeedbackPojo getFeedbackIsUsefulBySolution(int feedbackId, int solutionId, int clusterId) {
        SolutionRecommendationDao solutionRecommendationDao = MySQLConnectionManager.getInstance().open(SolutionRecommendationDao.class);
        try {
            return solutionRecommendationDao.getFeedbackIsUsefulBySolution(feedbackId, solutionId, clusterId);
        } catch (Exception e) {
            log.error("Error occurred while creating new feedback entry for feedback id: {}", feedbackId, e);
        } finally {
            MySQLConnectionManager.getInstance().close(solutionRecommendationDao);
        }
        return null;
    }
}
