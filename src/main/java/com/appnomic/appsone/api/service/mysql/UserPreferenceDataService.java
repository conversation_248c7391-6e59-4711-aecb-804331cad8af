package com.appnomic.appsone.api.service.mysql;

import com.appnomic.appsone.api.custom.exceptions.AppsoneException;
import com.appnomic.appsone.api.dao.UserPreferenceDao;
import com.appnomic.appsone.api.manager.MySQLConnectionManager;
import com.appnomic.appsone.api.pojo.TagMappingDetails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.List;

public class UserPreferenceDataService {
    private static final Logger logger = LoggerFactory.getLogger(UserPreferenceDataService.class);

    private UserPreferenceDataService() {
        // do nothing.
    }

    public static List<TagMappingDetails> getUserPreferences(TagMappingDetails userPreferenceBean) {
        UserPreferenceDao userPreferenceDao = MySQLConnectionManager.getInstance().open(UserPreferenceDao.class);
        try {
            return userPreferenceDao.getUserPreferences(userPreferenceBean);
        } catch (Exception e) {
            logger.error("Error retrieving user preferences" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(userPreferenceDao);
        }
        return Collections.emptyList();
    }

    public static int addUserPreferences(TagMappingDetails userPreferenceBean) throws AppsoneException {
        UserPreferenceDao userPreferenceDao = MySQLConnectionManager.getInstance().open(UserPreferenceDao.class);
        try {
            return userPreferenceDao.addUserPreferences(userPreferenceBean);
        } catch (Exception e) {
            throw new AppsoneException(e, "Error while inserting user preferences into database.");
        } finally {
            MySQLConnectionManager.getInstance().close(userPreferenceDao);
        }
    }

    public static void updateUserPreferences(TagMappingDetails userPreferenceBean) {
        UserPreferenceDao userPreferenceDao = MySQLConnectionManager.getInstance().open(UserPreferenceDao.class);
        try {
            userPreferenceDao.updateUserPreferences(userPreferenceBean);
        } catch (Exception e) {
            logger.error("Error updating user preferences" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(userPreferenceDao);
        }
    }

    public static void deleteUserPreferences(TagMappingDetails userPreferenceBean) {
        UserPreferenceDao userPreferenceDao = MySQLConnectionManager.getInstance().open(UserPreferenceDao.class);
        try {
            userPreferenceDao.deleteUserPreferences(userPreferenceBean);
        } catch (Exception e) {
            logger.error("Error deleting user preferences" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(userPreferenceDao);
        }
    }

    public static TagMappingDetails getUserPreference(TagMappingDetails userPreferenceBean) {
        UserPreferenceDao userPreferenceDao = MySQLConnectionManager.getInstance().open(UserPreferenceDao.class);
        try {
            return userPreferenceDao.getUserPreference(userPreferenceBean);
        } catch (Exception e) {
            logger.error("Error retrieving user preference" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(userPreferenceDao);
        }
        return null;
    }
}
