package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.beans.*;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.cache.MasterCache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.pojo.*;
import com.appnomic.appsone.api.service.mysql.MasterDataService;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.ConfProperties;
import com.appnomic.appsone.api.util.ValidationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Response;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.appnomic.appsone.api.common.Constants.*;
import static java.util.stream.Collectors.groupingBy;

public class ConfigurationDataService {

    private static final Logger log = LoggerFactory.getLogger(ConfigurationDataService.class);
    public static GenericResponse<List<DateComponent>> getDateTimeDropdownList(Response response){

        GenericResponse<List<DateComponent>> responseObject = new GenericResponse<>();

        try {
            List<DateComponent> list = MasterDataService.getDateComponentList();
            responseObject = CommonUtils.getGenericResponse(response,
                    StatusResponse.SUCCESS.name(), 200, "Success", null, false);
            responseObject.setData(list);
        } catch (Exception e) {
            log.error("Error occurred while fetching date component list", e);
            responseObject = CommonUtils.getGenericResponse(response,
                    StatusResponse.FAILURE.name(), 400, e.getMessage(), e, true);
        }
        return responseObject;
    }

    public static GenericResponse<List<MasterFeaturesBean>> getMasterFeatures(Response response){

        GenericResponse<List<MasterFeaturesBean>> responseObject = null;

        try {
            List<MasterFeaturesBean> list = MasterDataService.getMasterFeatures();
            responseObject = CommonUtils.getGenericResponse(response,
                    StatusResponse.SUCCESS.name(), 200, "Success", null, false);
            responseObject.setData(list);
        } catch (Exception e) {
            log.error("Error occurred while fetching mst features", e);
            responseObject = CommonUtils.getGenericResponse(response,
                    StatusResponse.FAILURE.name(), 400, e.getMessage(), e, true);
        }
        return responseObject;
    }

    public static KeycloakSettingsResponse getKeyCloakSettings() {
        KeycloakSettingsResponse response = new KeycloakSettingsResponse();
        response.setResponseMessage("Success");
        response.setResponseStatus(StatusResponse.SUCCESS.toString());
        response.setData(ConfProperties.getKeycloakSSOConfigurations());
        return response;
    }

}
