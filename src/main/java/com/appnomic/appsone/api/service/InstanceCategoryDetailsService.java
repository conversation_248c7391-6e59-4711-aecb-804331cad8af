package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.businesslogic.InstanceCategoryDetailsBL;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.common.GenericResponse;
import com.appnomic.appsone.api.common.StatusResponse;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.CategoryEvents;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

import java.util.List;
import java.util.Map;

@Slf4j
public class InstanceCategoryDetailsService {

    public GenericResponse<List<CategoryEvents>> getInstanceCategoryDetails(Request request, Response response) {
        log.trace("{} getInstanceCategoryDetails().", Constants.INVOKED_METHOD);
        GenericResponse<List<CategoryEvents>> responseObject = new GenericResponse<>();
        try {
            InstanceCategoryDetailsBL instanceCategoryDetailsBL = new InstanceCategoryDetailsBL();
            RequestObject requestObject = new RequestObject(request);

            UtilityBean<Map<String, Integer>> mapUtilityBean = instanceCategoryDetailsBL.clientValidation(requestObject);
            instanceCategoryDetailsBL.serverValidation(mapUtilityBean);
            responseObject.setData(instanceCategoryDetailsBL.processData(mapUtilityBean));

            response.status(Constants.SUCCESS_STATUS_CODE);
            responseObject.setMessage(Constants.SUCCESS_MESSAGE);
            responseObject.setResponseStatus(StatusResponse.SUCCESS.toString());

        } catch (DataProcessingException de) {

            log.error("Error occurred while processing data for instance categories.", de);
            CommonUtils.populateErrorResponse(responseObject, response, de.getMessage(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (ServerException se) {

            log.error("Error occurred while server validation for instance categories data.", se);
            CommonUtils.populateErrorResponse(responseObject, response, se.getMessage(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (ClientException ce) {

            log.error("Error occurred while client validation for instance categories data.", ce);
            CommonUtils.populateErrorResponse(responseObject, response, ce.getMessage(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;

        } catch (Exception e) {

            log.error("Error occurred while getting for instance categories data.", e);
            CommonUtils.populateErrorResponse(responseObject, response, e.getMessage(),
                    Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }
        return responseObject;
    }
}
