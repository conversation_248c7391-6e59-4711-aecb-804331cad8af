package com.appnomic.appsone.api;

import com.appnomic.appsone.api.health.ApplicationInfo;
import com.appnomic.appsone.api.manager.MySQLConnectionManager;
import com.appnomic.appsone.api.manager.RedisConnectionManager;
import com.appnomic.appsone.api.rest.Routes;
import com.appnomic.appsone.api.service.scheduler.CacheRefreshScheduler;
import com.appnomic.appsone.api.service.scheduler.HealthMetricsScheduler;
import com.appnomic.appsone.api.util.CommonUtils;
import com.google.common.collect.ImmutableSet;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.common.util.concurrent.Service;
import com.google.common.util.concurrent.ServiceManager;
import lombok.NonNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

public class Main {
    private static final Logger log = LoggerFactory.getLogger(Main.class);

    public static void main(String[] args) {

        log.debug("Inside main() method.");
        if (args != null && args.length > 0 && args[0].equals("haveToRunTestCases")) {
            MySQLConnectionManager.getInstance().setHaveToRunTestCases(true);
        }

        MySQLConnectionManager.getInstance().getHandle();
        RedisConnectionManager.INSTANCE.getRedisClient();

        Routes routes = new Routes();
        routes.init();

        ApplicationInfo applicationInfo = new ApplicationInfo();
        CommonUtils.registerMBean("ApplicationInfo", "HealDashboard", applicationInfo);

        start();
    }

    private static void start() {

        Set<Service> services = ImmutableSet.of(new HealthMetricsScheduler(), new CacheRefreshScheduler());
        ServiceManager manager = new ServiceManager(services);

        manager.addListener(new ServiceManager.Listener() {
            @Override
            public void healthy() {
                log.info("start-up complete....");
            }

            @Override
            public void stopped() {
                log.info("shutdown complete....");
            }

            @Override
            public void failure(@NonNull Service service) {
                // some other action.  For now, we will just exit.
                log.error("{} failed to initialize", service);
                System.exit(1);
            }
        }, MoreExecutors.directExecutor());

        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            // Give the services 15 seconds to stop to ensure that we are responsive to shutdown requests.
            try {
                log.info("shutdown signal received... Waiting to stop all services");
                manager.stopAsync().awaitStopped(15, TimeUnit.SECONDS);
            } catch (TimeoutException timeout) {
                log.info("could not stop the service gracefully....");
            }
        }));

        manager.startAsync();
        log.debug("End main() method.");
    }

}
