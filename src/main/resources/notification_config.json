[{"type": "xptFlowConversion", "threshold": 50, "alertProfileId": 1, "emailEnabled": true, "emailToAddress": "<EMAIL>", "emailCcAddress": "<EMAIL>", "emailBccAddress": "<EMAIL>", "subject": "Flow Conversion Rate", "body": "Flow conversion rate for account:${account}, flow:${flow} is :${data}. ${TimeStamp}", "smsEnabled": false, "mobileNumbers": "**********", "message": "Flow conversion rate:${data},account:${account},flow:${flow}. ${TimeStamp}."}, {"type": "xptStepConversion", "threshold": 50, "alertProfileId": 1, "emailEnabled": true, "emailToAddress": "<EMAIL>", "emailCcAddress": "<EMAIL>", "emailBccAddress": "<EMAIL>", "subject": "Step Conversion Rate", "body": "Step conversion rate for account:${account}, flow:${flow}, step:${step} is :${data}. ${TimeStamp}", "smsEnabled": false, "mobileNumbers": "**********", "message": "Step conversion rate:${data},account:${account},flow:${flow}. ${TimeStamp}."}, {"type": "xptBizError", "threshold": 50, "alertProfileId": 1, "emailEnabled": true, "emailToAddress": "<EMAIL>", "emailCcAddress": "<EMAIL>", "emailBccAddress": "<EMAIL>", "subject": "<PERSON><PERSON>", "body": "Biz Error for account:${account}, flow:${flow} is :${data}. ${TimeStamp}", "smsEnabled": false, "mobileNumbers": "**********", "message": "Biz Error:${data},account:${account},flow:${flow}. ${TimeStamp}."}]