#=====================================
# KEYCLOAK CONFIG
#KeyCloak parameters, these are used for session management
#=====================================
keycloak.ip=**************
keycloak.port=9998
keycloak.user=appsoneadmin
keycloak.pwd=QXBwc29uZUAxMjM=

#=====================================
# OPENSEARCH CONFIG
#=====================================
opensearch.connection.io.reactor.size=2

#======================================
# Mysql Database Configuration
#=======================================
mysql.server.connection.url=******************************************************************************************************************************************************
mysql.database.username=dbadmin
mysql.database.password=cm9vdEAxMjM=
mysql.database.pool.size=50
mysql.database.pool.size.max=100


timezone.offset=3000

#=======================================
# Cache Service Configuration
#=======================================
# Cache refresh interval
http.handler.max.threads = 100

#=======================================
# Signal Close Interval
#=======================================
signal.close.window.time=15
show.application.sdm.topology=signals
signal.close.window.time.check.enabled=true

#======================================
# Signal description Configuration
#=======================================
signal.problem.description=Transactions at <entry_service_name> have been affected.
signal.warning.description=Event(s) in <root_cause_service_list> root cause service(s) may impact transaction performance.
#signal.info.description=

#======================================
# Signal description Configuration
#=======================================
opensearch.index.extension=_ext
forensic.lookahead.interval.minutes=1
forensic.lookback.interval.minutes=5
forensic.level=category

instance.health.min=15

keystore.file.path=/home/<USER>/heal_repo/appsone-api/src/main/resources/appnomic-keystore.jks
truststore.file.path=
static.files.path=/public
keystore.password=serverpw
truststore.password=serverpw

admin.user.identifier=7640123a-fbde-4fe5-9812-581cd1e3a9c1
#=======================================
# Redis configuration
#=======================================
redis.hosts=**************:7001,**************:7002,**************:7003,**************:7004,**************:7005,**************:7006
redis.ssl.enabled=true
redis.username=
redis.password=cmVkaXNAMTIz
redis.cluster.mode= true
offset.from.gmt=0

health.metrics.scheduler.milliseconds=60000
https.port=11301
html.encoder.enable=1
service.uiservice.txn.check.raw.index=3
top.anomaly.count.from.opensearch=10
cache.maximum.size=50000
cache.timeout=30
cache.timeout.maintenance.data=50
cache.refresh.scheduler.milliseconds=600000

#======================================
# Reports
#======================================
report.data.service.url=https://**************:11491/triggerReport
resend.mail.service.url=https://**************:11491/sendMail
report.ui.service.output.path.container=/tmp/reports

#=======================================
#JEAGER JIM TRACE CONFIG
#=======================================
jaeger.ip=**************
jaeger.port=16686