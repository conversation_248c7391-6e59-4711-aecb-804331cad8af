package manager;

import com.appnomic.appsone.api.cache.MasterCache;
import com.appnomic.appsone.api.dao.MasterDataDao;
import com.appnomic.appsone.api.manager.MySQLConnectionManager;
import com.appnomic.appsone.api.pojo.Account;
import com.appnomic.appsone.api.pojo.AllKpiList;
import com.appnomic.appsone.api.service.mysql.MasterDataService;
import com.appnomic.appsone.api.testpojo.TestClass;
import com.appnomic.appsone.api.util.SQLReader;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.skife.jdbi.v2.Handle;
import org.skife.jdbi.v2.Script;
import org.skife.jdbi.v2.sqlobject.SqlQuery;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.Arrays;
import java.util.List;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class MySQLConnectionManagerTest {

    @Test
    public void testMysqlConnection()   {
        /*Handle handle = MySQLConnectionManager.getInstance().getHandle().open();
        assert (handle != null);
        String version = handle.createQuery("select name from mst_component where Id=1")
                .map(StringMapper.FIRST).first();
        System.out.println("DB version: "+version);*/
    }

    /*@Test
    public void testH2Connection()  {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true);
        Handle handle = MySQLConnectionManager.getInstance().getHandle().open();
        try {
            handle.execute("create table if not exists test(id INT AUTO_INCREMENT, name VARCHAR(255) NOT NULL) ENGINE=INNODB;");

            List<String> names = Arrays.asList("Appnomic","Appsone","DS");

            names.forEach( name -> {
                handle.execute("insert into test(name) values (?);",name);
            });

            *//*TestDao testDao = handle.attach(TestDao.class);
            StringBuilder script = new StringBuilder();
            script.append(SQLReader.initalizeDB());
            script.append(SQLReader.initalizeMasterData());
            script.append(SQLReader.loadTestData());
            script.append(SQLReader.createViews());
            script.append(SQLReader.dummyTest());
            handle.createScript(script.toString()).execute();

            List<TestClass> t = testDao.listUsers();
            t.forEach(System.out::println);
            Assertions.assertEquals(t.size(),1);*//*



        }   finally {
            handle.close();
            MySQLConnectionManager.getInstance().setHaveToRunTestCases(false);
        }
    }*/

    public interface TestDao    {
        //@SqlQuery("SELECT * from test")
        @SqlQuery("SELECT  id, name from account")
        @RegisterMapperFactory(BeanMapperFactory.class)
        public List<TestClass> listUsers();
    }
}
