package com.appnomic.appsone.api.service.benchmark;

import com.appnomic.appsone.api.pojo.request.TransactionPerformanceDataRequest;
import com.appnomic.appsone.api.service.TransactionPerformanceDataService;
import lombok.Data;
import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.runner.Runner;
import org.openjdk.jmh.runner.RunnerException;
import org.openjdk.jmh.runner.options.Options;
import org.openjdk.jmh.runner.options.OptionsBuilder;
import spark.Response;

import java.util.concurrent.TimeUnit;

@State(Scope.Benchmark)
public class TransactionPerformanceBenchmark {

    TransactionPerformanceDataService dataService;
    TransactionPerformanceDataRequest dataRequest;
    Response response;

    @Setup
    public void setup() {
        dataService = new TransactionPerformanceDataService();
        dataRequest = new TransactionPerformanceDataRequest();
        response = new DummyResponse();
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    @OutputTimeUnit(TimeUnit.MILLISECONDS)
    public void testAPI() {
        dataRequest.setAccountIdentifierString("d681ef13-d690-4917-jkhg-6c79b-1");
        dataRequest.setServiceIdString("2");
        dataRequest.setTransactionIdString("0");
        dataRequest.setTransactionResponseTypeString("DC");
        dataRequest.setFromTimeString("*************");
        dataRequest.setToTimeString("*************");
        dataRequest.setRequestTypeString("services");
        dataRequest.setTagIdString("0");
//        dataRequest.setResponse(response);
//        dataRequest.validateAndPopulate();
//        dataService.fetchTransactionPerformanceData(dataRequest);
    }

    public static void main(String[] args) throws RunnerException {
        Options opt = new OptionsBuilder()
                .include(TransactionPerformanceBenchmark.class.getSimpleName())
                .forks(1)
                .warmupIterations(10)
                .measurementIterations(10)
                .build();

        new Runner(opt).run();
    }

    @Data
    class DummyResponse extends Response {
        public void status(int s){
            //do nothing
        }
    }
}
