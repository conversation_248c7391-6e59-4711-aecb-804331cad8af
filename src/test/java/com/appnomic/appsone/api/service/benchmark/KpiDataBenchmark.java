package com.appnomic.appsone.api.service.benchmark;

import com.appnomic.appsone.api.pojo.request.KpiDataRequest;
import com.appnomic.appsone.api.service.KpiDataService;
import lombok.Data;
import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.runner.Runner;
import org.openjdk.jmh.runner.RunnerException;
import org.openjdk.jmh.runner.options.Options;
import org.openjdk.jmh.runner.options.OptionsBuilder;
import spark.Response;

import java.util.concurrent.TimeUnit;

/**
 * These benchmark will only work when the conf.properties points to a working setup
 */
@State(Scope.Benchmark)
public class KpiDataBenchmark {

    KpiDataService kpiDataService;
    KpiDataRequest kpiDataRequest;
    Response response;

    @Setup
    public void setup() {
        kpiDataService = new KpiDataService();
        kpiDataRequest = new KpiDataRequest();
        response = new DummyResponse();
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    @OutputTimeUnit(TimeUnit.SECONDS)
    public void testKpiData() {
        kpiDataRequest.setAccountId("d681ef13-d690-4917-jkhg-6c79b-1");
        kpiDataRequest.setCompInstanceId("6");
        kpiDataRequest.setKpiId("10");
        kpiDataRequest.setGroupId("2");
        kpiDataRequest.setFromTime("*************");
        //kpiDataRequest.setFromTime("*************");
        kpiDataRequest.setToTime("*************");
        kpiDataRequest.setType("");
//        kpiDataService.getKpiData(kpiDataRequest);
    }

    public static void main(String[] args) throws RunnerException {
        Options opt = new OptionsBuilder()
                .include(KpiDataBenchmark.class.getSimpleName())
                .forks(1)
                .warmupIterations(20)
                .measurementIterations(20)
                .build();

        new Runner(opt).run();
        System.exit(0);
    }


    @Data
    class DummyResponse extends Response {
        public void status(int s){
            //do nothing
        }
    }
}
