package com.appnomic.appsone.api.service.benchmark;

import com.appnomic.appsone.api.pojo.request.TransactionDetailDataRequest;
import com.appnomic.appsone.api.service.TransactionDetailDataService;
import lombok.Data;
import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.runner.Runner;
import org.openjdk.jmh.runner.RunnerException;
import org.openjdk.jmh.runner.options.Options;
import org.openjdk.jmh.runner.options.OptionsBuilder;
import spark.Response;

import java.util.concurrent.TimeUnit;

@State(Scope.Benchmark)
public class TransactionListBenchmark {

    TransactionDetailDataService dataService;
    TransactionDetailDataRequest dataRequest;
    Response response;

    @Setup
    public void setup() {
        System.out.println("Here");
        dataService = new TransactionDetailDataService();
        dataRequest = new TransactionDetailDataRequest();
        response = new DummyResponse();
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    @OutputTimeUnit(TimeUnit.MILLISECONDS)
    public void testTransactionListAPI() {
        dataRequest.setAccountIdString("d681ef13-d690-4917-jkhg-6c79b-1");
        dataRequest.setServiceIdString("2");
        dataRequest.setInstanceIdString("2");
        dataRequest.setResponseType("DC");
        dataRequest.setTopNCountString("5");
        dataRequest.setFromTimeString("*************");
        dataRequest.setToTimeString("*************");
        dataRequest.setAggregationType("services");
        dataRequest.setTagIdString("0");
        dataRequest.setMode("all");
        dataRequest.setResponse(response);
        dataRequest.validateAndPopulate();
        //dataService.getTransactionDetails(dataRequest);
    }

    public static void main(String[] args) throws RunnerException {
        Options opt = new OptionsBuilder()
                .include(TransactionListBenchmark.class.getSimpleName())
                .forks(1)
                .warmupIterations(10)
                .measurementIterations(10)
                .build();

        new Runner(opt).run();
    }

    @Data
    class DummyResponse extends Response {
        public void status(int s){
            //do nothing
        }
    }
}
