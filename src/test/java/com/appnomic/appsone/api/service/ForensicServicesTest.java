package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.pojo.KpiAnomalyData;
import com.heal.configuration.pojos.opensearch.ForensicPojo;
import org.junit.Test;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;

import java.util.*;

import static org.junit.Assert.assertEquals;

/**
 * <AUTHOR> - 12-12-2023
 */
public class ForensicServicesTest {

    @Test
    public void getExtraFieldForForensicHeaderTest(){
        ForensicPojo forensicPojo = new ForensicPojo();
        Map<String, String> forensicPojoMap = new HashMap<>();
        forensicPojoMap.put("TriggerSource","ComponentAgent");
        forensicPojoMap.put("violationLevel","service");
        forensicPojoMap.put("ThresholdType","SOR");
        forensicPojoMap.put("persistence","1");
        forensicPojoMap.put("suppression","2");
        forensicPojoMap.put("KPIValue","12.0");
        forensicPojoMap.put("Operation","greater than");
        forensicPojoMap.put("Lower","20");
        forensicPojoMap.put("Upper",null);
        forensicPojoMap.put("CommandExecutionTime", "62457");
        forensicPojo.setMetadata(forensicPojoMap);
        LinkedHashMap<String, String> extraFieldForForensicHeader =
                ForensicServices.getExtraFieldForForensicHeader(forensicPojo, "1");
        assert extraFieldForForensicHeader.size() == 5;
    }

    @Test
    public void getExtraFieldForForensicHeaderBasedOnOperationTest(){
        ForensicPojo forensicPojo = new ForensicPojo();
        Map<String, String> forensicPojoMap = new HashMap<>();
        forensicPojoMap.put("TriggerSource","ComponentAgent");
        forensicPojoMap.put("violationLevel","service");
        forensicPojoMap.put("ThresholdType","NOR");
        forensicPojoMap.put("persistence","1");
        forensicPojoMap.put("suppression","2");
        forensicPojoMap.put("KPIValue","12.0");
        forensicPojoMap.put("Operation","not between");
        forensicPojoMap.put("Lower","20");
        forensicPojoMap.put("Upper", "30");
        forensicPojoMap.put("CommandExecutionTime", "299005");
        forensicPojo.setMetadata(forensicPojoMap);
        LinkedHashMap<String, String> extraFieldForForensicHeader =
                ForensicServices.getExtraFieldForForensicHeader(forensicPojo, "1");
        assert extraFieldForForensicHeader.size() == 5;
    }

    @Test
    public void getExtraFieldForForensicHeaderSortingTestTest(){
        LinkedHashMap<String, String> forensicValMap = new LinkedHashMap<>();
        forensicValMap.put("Component Instance", "Instance 1");
        forensicValMap.put("Component Name", "Weblogic");
        forensicValMap.put("Component Type", "App");
        forensicValMap.put("Category", "Cpu");
        forensicValMap.put("Host Address", "**************");

        ForensicPojo forensicPojo = new ForensicPojo();
        Map<String, String> forensicPojoMap = new HashMap<>();
        forensicPojoMap.put("TriggerSource","ComponentAgent");
        forensicPojoMap.put("violationLevel","service");
        forensicPojoMap.put("ThresholdType","NOR");
        forensicPojoMap.put("persistence","1");
        forensicPojoMap.put("suppression","2");
        forensicPojoMap.put("KPIValue","12.0");
        forensicPojoMap.put("Operation","not between");
        forensicPojoMap.put("Lower","20");
        forensicPojoMap.put("Upper", "30");
        forensicPojoMap.put("CommandExecutionTime", "239004");
        forensicPojo.setMetadata(forensicPojoMap);
        LinkedHashMap<String, String> extraFieldForForensicHeader =
                ForensicServices.getExtraFieldForForensicHeader(forensicPojo, "1");
        forensicValMap.putAll(extraFieldForForensicHeader);

        assert extraFieldForForensicHeader.size() == 5;
        assert forensicValMap.size() == 10;
        for (Map.Entry<String, String> entry : forensicValMap.entrySet()) {
            String keyAtIndex0 = entry.getKey();
            String valueAtIndex0 = entry.getValue();
            assertEquals(keyAtIndex0, "Component Instance");
            assertEquals(valueAtIndex0, "Instance 1");
            break;
        }
    }

    @Test
    public void getExtraFieldForForensicHeaderWithoutPersistenceTest(){
        LinkedHashMap<String, String> forensicValMap = new LinkedHashMap<>();
        forensicValMap.put("Component Instance", "Instancename 1");
        forensicValMap.put("Component Name", "Weblogic");
        forensicValMap.put("Component Type", "App");
        forensicValMap.put("Category", "Cpu");
        forensicValMap.put("Host Address", "**************");

        ForensicPojo forensicPojo = new ForensicPojo();
        Map<String, String> forensicPojoMap = new HashMap<>();
        forensicPojoMap.put("TriggerSource","ComponentAgent");
        forensicPojoMap.put("violationLevel","service");
        forensicPojoMap.put("ThresholdType","NOR");
        forensicPojoMap.put("suppression","2");
        forensicPojoMap.put("KPIValue","12.0");
        forensicPojoMap.put("Operation","not between");
        forensicPojoMap.put("Lower","20");
        forensicPojoMap.put("Upper", "30");
        forensicPojoMap.put("CommandExecutionTime", "118980");
        forensicPojo.setMetadata(forensicPojoMap);
        LinkedHashMap<String, String> extraFieldForForensicHeader =
                ForensicServices.getExtraFieldForForensicHeader(forensicPojo, "1");

        forensicValMap.putAll(extraFieldForForensicHeader);
        assert extraFieldForForensicHeader.size() == 5;
        assert forensicValMap.size() == 10;
        assert forensicValMap.containsKey("Violation Config");
        assert !forensicValMap.get("Violation Config").contains("Persistence");
    }

    @Test
    public void getExtraFieldForForensicHeaderNoViolationTest(){
        LinkedHashMap<String, String> forensicValMap = new LinkedHashMap<>();
        forensicValMap.put("Component Instance", "Instancename 1");
        forensicValMap.put("Component Name", "Weblogic");
        forensicValMap.put("Component Type", "App");
        forensicValMap.put("Category", "Cpu");
        forensicValMap.put("Host Address", "**************");

        ForensicPojo forensicPojo = new ForensicPojo();
        Map<String, String> forensicPojoMap = new HashMap<>();
        forensicPojoMap.put("TriggerSource","ComponentAgent");
        forensicPojoMap.put("violationLevel","service");
        forensicPojoMap.put("ThresholdType","NOR");
        forensicPojoMap.put("KPIValue","12.0");
        forensicPojoMap.put("Operation","not between");
        forensicPojoMap.put("Lower","20");
        forensicPojoMap.put("Upper", "30");
        forensicPojoMap.put("CommandExecutionTime", "299005");
        forensicPojo.setMetadata(forensicPojoMap);
        LinkedHashMap<String, String> extraFieldForForensicHeader =
                ForensicServices.getExtraFieldForForensicHeader(forensicPojo, "1");

        forensicValMap.putAll(extraFieldForForensicHeader);
        assert extraFieldForForensicHeader.size() == 4;
        assert forensicValMap.size() == 9;
        assert !forensicValMap.containsKey("Violation Config");
    }

    @Test
    public void getExtraFieldForForensicHeader_NoForensicExecutionTime_Inheader(){
        LinkedHashMap<String, String> forensicValMap = new LinkedHashMap<>();
        forensicValMap.put("Component Instance", "Instancename 1");
        forensicValMap.put("Component Name", "Weblogic");
        forensicValMap.put("Component Type", "App");
        forensicValMap.put("Category", "Cpu");
        forensicValMap.put("Host Address", "**************");

        ForensicPojo forensicPojo = new ForensicPojo();
        Map<String, String> forensicPojoMap = new HashMap<>();
        forensicPojoMap.put("TriggerSource","ComponentAgent");
        forensicPojoMap.put("violationLevel","service");
        forensicPojoMap.put("ThresholdType","NOR");
        forensicPojoMap.put("KPIValue","12.0");
        forensicPojoMap.put("Operation","not between");
        forensicPojoMap.put("Lower","20");
        forensicPojoMap.put("Upper", "30");
        forensicPojoMap.put("CommandExecutionTime", "299005");
        forensicPojo.setMetadata(forensicPojoMap);
        LinkedHashMap<String, String> extraFieldForForensicHeader =
                ForensicServices.getExtraFieldForForensicHeader(forensicPojo, "0");

        forensicValMap.putAll(extraFieldForForensicHeader);
        assert extraFieldForForensicHeader.size() == 3;
        assert forensicValMap.size() == 8;
        assert !forensicValMap.containsKey("Violation Config");
    }

    @Test
    public void mapKpiAnomalyToForensic() {
        List<KpiAnomalyData> kpiAnomalyDataList = new ArrayList<>();
        List<ForensicPojo> forensicPojoList = new ArrayList<>();

        KpiAnomalyData kpiAnomalyData = new KpiAnomalyData();
        kpiAnomalyData.setTime(1703658720000L);
        kpiAnomalyData.setValue("3.0");
        kpiAnomalyData.setMaxThreshold(0.0);
        kpiAnomalyData.setMinThreshold(1.0);
        kpiAnomalyData.setForensicAvailable(false);
        kpiAnomalyData.setForensicTime(null);
        kpiAnomalyData.setInstanceId(6314);
        kpiAnomalyData.setInstanceName("Host_Inst_JIF_232");
        kpiAnomalyData.setOperationType("greater than");

        kpiAnomalyDataList.add(kpiAnomalyData);

        ForensicPojo forensicPojo = new ForensicPojo();
        forensicPojo.setForensicId("FetchNetwork");
        forensicPojo.setExitCode(0);
        forensicPojo.setCommandStartTime(*************L);
        forensicPojo.setCommandOutput("H4sIAAAAAAAAAKVT0W6jMBB85ytW6mtDjCFOsHRSU0J1qImJAmnVp4gSV6VqAIF9av/+TAQJpEV3xy1CwM7uzNheWHTgFFIuShGJqxt1zdfrwGfujrmPS4+5x9yCl3GR5CLJUgpOlAtZcBCvvGmEvTzk15CkIHhxKCF7Ue/PmUz3EKk7k+L40csfiKgQECaVFwOP8HSEETavARFqmhSj3kZXsQ9p++CxFByUrpAlhUDGMS/L3voNVytOnWyvhPpZ7/nnQ/QuuS9FLgWF3sK6QAu2q9V880Q1CMJ56MI34fhbFmqjOi7RJj/SPHarKhc7VzHdLr3gp7uoCowT4Cz9wN09zr3w2IlOQPDEdoHLwpqyC2ych8UXIPRWLaYWcOexVl4B/jb8zhY+A11brY6urQvgbKsFdG21gK4tpP34l9Du1x6En7k6+dYiNBHnzSGg5mHYWDfITDcsHZuYYlwXnPLE1lWeIGTbVb69KYQY03FZvu4pFFkmbnJRwt+ImNOJZXVELH02owae2gZcihhkMkHjl+SdP3P10x6jEiF/EJkgQshXEcsy68a2CLbQzBy/Rb+i85wO3vLzeAymaAbpvwiqgRtMcBrMwQzNBGu/AbtYp3yvBQAA");
        forensicPojo.setInstanceId("instanceId");
        forensicPojo.setCategoryId("Network Utilization");
        forensicPojo.setForensicTriggerTime(*************L);

        Map<String, String> forensicPojoMap = new HashMap<>();
        forensicPojoMap.put("CategoryId","Network Utilization");
        forensicPojoMap.put("AccountId","JIF");
        forensicPojoMap.put("ActionTriggerTime","*************");
        forensicPojoMap.put("SshPort", "22");
        forensicPojoMap.put("starttime", "*************");
        forensicPojoMap.put("TriggerSource","ComponentAgent");
        forensicPojoMap.put("violationLevel","service");
        forensicPojoMap.put("ThresholdType","NOR");
        forensicPojoMap.put("persistence","1");
        forensicPojoMap.put("suppression","2");
        forensicPojoMap.put("KPIValue","12.0");
        forensicPojoMap.put("Operation","not between");
        forensicPojoMap.put("Lower","20");
        forensicPojoMap.put("Upper", "30");
        forensicPojoMap.put("CommandExecutionTime", "299005");


        ForensicPojo forensicPojo1 = new ForensicPojo();
        forensicPojo1.setForensicId("FetchNetwork");
        forensicPojo1.setExitCode(0);
        forensicPojo1.setCommandStartTime(*************L);
        forensicPojo1.setCommandOutput("H4sIAAAAAAAAAKVT0W6jMBB85ytW6mtDjCFOsHRSU0J1qImJAmnVp4gSV6VqAIF9av/+TAQJpEV3xy1CwM7uzNheWHTgFFIuShGJqxt1zdfrwGfujrmPS4+5x9yCl3GR5CLJUgpOlAtZcBCvvGmEvTzk15CkIHhxKCF7Ue/PmUz3EKk7k+L40csfiKgQECaVFwOP8HSEETavARFqmhSj3kZXsQ9p++CxFByUrpAlhUDGMS/L3voNVytOnWyvhPpZ7/nnQ/QuuS9FLgWF3sK6QAu2q9V880Q1CMJ56MI34fhbFmqjOi7RJj/SPHarKhc7VzHdLr3gp7uoCowT4Cz9wN09zr3w2IlOQPDEdoHLwpqyC2ych8UXIPRWLaYWcOexVl4B/jb8zhY+A11brY6urQvgbKsFdG21gK4tpP34l9Du1x6En7k6+dYiNBHnzSGg5mHYWDfITDcsHZuYYlwXnPLE1lWeIGTbVb69KYQY03FZvu4pFFkmbnJRwt+ImNOJZXVELH02owae2gZcihhkMkHjl+SdP3P10x6jEiF/EJkgQshXEcsy68a2CLbQzBy/Rb+i85wO3vLzeAymaAbpvwiqgRtMcBrMwQzNBGu/AbtYp3yvBQAA");
        forensicPojo1.setInstanceId("instanceId");
        forensicPojo1.setCategoryId("Network Utilization");
        forensicPojo1.setForensicTriggerTime(*************L);

        Map<String, String> forensicPojoMap1 = new HashMap<>();
        forensicPojoMap1.put("CategoryId","Network Utilization");
        forensicPojoMap1.put("AccountId","JIF");
        forensicPojoMap1.put("ActionTriggerTime","*************");
        forensicPojoMap1.put("SshPort", "22");
        forensicPojoMap1.put("starttime", "*************");
        forensicPojoMap1.put("TriggerSource","ComponentAgent");
        forensicPojoMap1.put("violationLevel","service");
        forensicPojoMap1.put("ThresholdType","NOR");
        forensicPojoMap1.put("persistence","1");
        forensicPojoMap1.put("suppression","2");
        forensicPojoMap1.put("KPIValue","12.0");
        forensicPojoMap1.put("Operation","not between");
        forensicPojoMap1.put("Lower","20");
        forensicPojoMap1.put("Upper", "30");
        forensicPojoMap1.put("CommandExecutionTime", "299005");

        forensicPojoList.add(forensicPojo1);
        forensicPojoList.add(forensicPojo);

        long forensicLookahead = 120000l;
        ForensicServices.mapKpiAnomalyToForensic(kpiAnomalyDataList, forensicPojoList, forensicLookahead);
        assert kpiAnomalyDataList.size() == 1;
        assert kpiAnomalyDataList.get(0).getForensicTime() == *************l;
        assert kpiAnomalyDataList.get(0).isForensicAvailable();
    }

    @Test
    public void mapKpiAnomalyToForensic_NoForensicFound() {
        List<KpiAnomalyData> kpiAnomalyDataList = new ArrayList<>();
        List<ForensicPojo> forensicPojoList = new ArrayList<>();

        KpiAnomalyData kpiAnomalyData = new KpiAnomalyData();
        kpiAnomalyData.setTime(1703658720000L);
        kpiAnomalyData.setValue("3.0");
        kpiAnomalyData.setMaxThreshold(0.0);
        kpiAnomalyData.setMinThreshold(1.0);
        kpiAnomalyData.setForensicAvailable(false);
        kpiAnomalyData.setForensicTime(null);
        kpiAnomalyData.setInstanceId(6314);
        kpiAnomalyData.setInstanceName("Host_Inst_JIF_232");
        kpiAnomalyData.setOperationType("greater than");

        kpiAnomalyDataList.add(kpiAnomalyData);

        ForensicPojo forensicPojo = new ForensicPojo();
        forensicPojo.setForensicId("FetchNetwork");
        forensicPojo.setExitCode(0);
        forensicPojo.setCommandStartTime(*************L);
        forensicPojo.setCommandOutput("H4sIAAAAAAAAAKVT0W6jMBB85ytW6mtDjCFOsHRSU0J1qImJAmnVp4gSV6VqAIF9av/+TAQJpEV3xy1CwM7uzNheWHTgFFIuShGJqxt1zdfrwGfujrmPS4+5x9yCl3GR5CLJUgpOlAtZcBCvvGmEvTzk15CkIHhxKCF7Ue/PmUz3EKk7k+L40csfiKgQECaVFwOP8HSEETavARFqmhSj3kZXsQ9p++CxFByUrpAlhUDGMS/L3voNVytOnWyvhPpZ7/nnQ/QuuS9FLgWF3sK6QAu2q9V880Q1CMJ56MI34fhbFmqjOi7RJj/SPHarKhc7VzHdLr3gp7uoCowT4Cz9wN09zr3w2IlOQPDEdoHLwpqyC2ych8UXIPRWLaYWcOexVl4B/jb8zhY+A11brY6urQvgbKsFdG21gK4tpP34l9Du1x6En7k6+dYiNBHnzSGg5mHYWDfITDcsHZuYYlwXnPLE1lWeIGTbVb69KYQY03FZvu4pFFkmbnJRwt+ImNOJZXVELH02owae2gZcihhkMkHjl+SdP3P10x6jEiF/EJkgQshXEcsy68a2CLbQzBy/Rb+i85wO3vLzeAymaAbpvwiqgRtMcBrMwQzNBGu/AbtYp3yvBQAA");
        forensicPojo.setInstanceId("instanceId");
        forensicPojo.setCategoryId("Network Utilization");
        forensicPojo.setForensicTriggerTime(*************L);

        Map<String, String> forensicPojoMap = new HashMap<>();
        forensicPojoMap.put("CategoryId","Network Utilization");
        forensicPojoMap.put("AccountId","JIF");
        forensicPojoMap.put("ActionTriggerTime","*************");
        forensicPojoMap.put("SshPort", "22");
        forensicPojoMap.put("starttime", "*************");
        forensicPojoMap.put("TriggerSource","ComponentAgent");
        forensicPojoMap.put("violationLevel","service");
        forensicPojoMap.put("ThresholdType","NOR");
        forensicPojoMap.put("persistence","1");
        forensicPojoMap.put("suppression","2");
        forensicPojoMap.put("KPIValue","12.0");
        forensicPojoMap.put("Operation","not between");
        forensicPojoMap.put("Lower","20");
        forensicPojoMap.put("Upper", "30");
        forensicPojoMap.put("CommandExecutionTime", "299005");


        ForensicPojo forensicPojo1 = new ForensicPojo();
        forensicPojo1.setForensicId("FetchNetwork");
        forensicPojo1.setExitCode(0);
        forensicPojo1.setCommandStartTime(*************L);
        forensicPojo1.setCommandOutput("H4sIAAAAAAAAAKVT0W6jMBB85ytW6mtDjCFOsHRSU0J1qImJAmnVp4gSV6VqAIF9av/+TAQJpEV3xy1CwM7uzNheWHTgFFIuShGJqxt1zdfrwGfujrmPS4+5x9yCl3GR5CLJUgpOlAtZcBCvvGmEvTzk15CkIHhxKCF7Ue/PmUz3EKk7k+L40csfiKgQECaVFwOP8HSEETavARFqmhSj3kZXsQ9p++CxFByUrpAlhUDGMS/L3voNVytOnWyvhPpZ7/nnQ/QuuS9FLgWF3sK6QAu2q9V880Q1CMJ56MI34fhbFmqjOi7RJj/SPHarKhc7VzHdLr3gp7uoCowT4Cz9wN09zr3w2IlOQPDEdoHLwpqyC2ych8UXIPRWLaYWcOexVl4B/jb8zhY+A11brY6urQvgbKsFdG21gK4tpP34l9Du1x6En7k6+dYiNBHnzSGg5mHYWDfITDcsHZuYYlwXnPLE1lWeIGTbVb69KYQY03FZvu4pFFkmbnJRwt+ImNOJZXVELH02owae2gZcihhkMkHjl+SdP3P10x6jEiF/EJkgQshXEcsy68a2CLbQzBy/Rb+i85wO3vLzeAymaAbpvwiqgRtMcBrMwQzNBGu/AbtYp3yvBQAA");
        forensicPojo1.setInstanceId("instanceId");
        forensicPojo1.setCategoryId("Network Utilization");
        forensicPojo1.setForensicTriggerTime(*************L);

        Map<String, String> forensicPojoMap1 = new HashMap<>();
        forensicPojoMap1.put("CategoryId","Network Utilization");
        forensicPojoMap1.put("AccountId","JIF");
        forensicPojoMap1.put("ActionTriggerTime","*************");
        forensicPojoMap1.put("SshPort", "22");
        forensicPojoMap1.put("starttime", "*************");
        forensicPojoMap1.put("TriggerSource","ComponentAgent");
        forensicPojoMap1.put("violationLevel","service");
        forensicPojoMap1.put("ThresholdType","NOR");
        forensicPojoMap1.put("persistence","1");
        forensicPojoMap1.put("suppression","2");
        forensicPojoMap1.put("KPIValue","12.0");
        forensicPojoMap1.put("Operation","not between");
        forensicPojoMap1.put("Lower","20");
        forensicPojoMap1.put("Upper", "30");
        forensicPojoMap1.put("CommandExecutionTime", "299005");

        forensicPojoList.add(forensicPojo1);
        forensicPojoList.add(forensicPojo);

        long forensicLookahead = 60000l;
        ForensicServices.mapKpiAnomalyToForensic(kpiAnomalyDataList, forensicPojoList, forensicLookahead);
        assert kpiAnomalyDataList.size() == 1;
        assert kpiAnomalyDataList.get(0).getForensicTime() == null;
        assert !kpiAnomalyDataList.get(0).isForensicAvailable();
    }
}