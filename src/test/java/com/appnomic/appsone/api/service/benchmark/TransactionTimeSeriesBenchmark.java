package com.appnomic.appsone.api.service.benchmark;

import com.appnomic.appsone.api.pojo.request.TransactionTimeSeriesDataRequest;
import com.appnomic.appsone.api.service.TransactionTimeSeriesDataService;
import lombok.Data;
import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.runner.Runner;
import org.openjdk.jmh.runner.RunnerException;
import org.openjdk.jmh.runner.options.Options;
import org.openjdk.jmh.runner.options.OptionsBuilder;
import spark.Response;

import java.util.concurrent.TimeUnit;

@State(Scope.Benchmark)
public class TransactionTimeSeriesBenchmark {

    TransactionTimeSeriesDataService dataService;
    TransactionTimeSeriesDataRequest dataRequest;
    Response response;

    @Setup
    public void setup() {
        dataService = new TransactionTimeSeriesDataService();
        dataRequest = new TransactionTimeSeriesDataRequest();
        response = new DummyResponse();
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    @OutputTimeUnit(TimeUnit.MILLISECONDS)
    public void testTransactionListAPI() {
        dataRequest.setAccountIdString("d681ef13-d690-4917-jkhg-6c79b-1");
        dataRequest.setServiceIdString("2");
        dataRequest.setResponseTypeString("DC");
        dataRequest.setFromTimeString("*************");
        dataRequest.setToTimeString("*************");
        dataRequest.setKpiNameString("volume");
        dataRequest.setTagIdString("0");
        dataRequest.setRequestTypeString("services");
        dataRequest.setResponse(response);
        dataRequest.validateAndPopulate();
        dataService.fetchTransactionTimeSeriesData(dataRequest);
    }

    public static void main(String[] args) throws RunnerException {
        Options opt = new OptionsBuilder()
                .include(TransactionTimeSeriesBenchmark.class.getSimpleName())
                .forks(1)
                .warmupIterations(10)
                .measurementIterations(10)
                .build();

        new Runner(opt).run();
    }


    @Data
    class DummyResponse extends Response {
        public void status(int s){
            //do nothing
        }
    }
}
