package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.pojo.ServiceDependencyGraph;
import com.appnomic.appsone.api.pojo.TopologyDetailsResponse;
import com.appnomic.appsone.api.util.RCAPathGenerator;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class RCAPathServiceTest extends RCAPathGenerator{

    @Test
    public void testIsSubPath() {
        int[] input = {4,3,6,8,7,9,0};
        int[] validate = {0,3,5,2,5,4,3,6,8,7,9,0,7,8,3,2,9,8,6};
        List<TopologyDetailsResponse.Nodes> inputList = new ArrayList<>();
        List<TopologyDetailsResponse.Nodes> validatorList = new ArrayList<>();
        Arrays.stream(input).forEach( it -> {
            TopologyDetailsResponse.Nodes node = new TopologyDetailsResponse.Nodes(Integer.toString(it));
            inputList.add(node);
        });
        Arrays.stream(validate).forEach( it -> {
            TopologyDetailsResponse.Nodes node = new TopologyDetailsResponse.Nodes(Integer.toString(it));
            validatorList.add(node);
        });

        RCAPathGenerator rcaPathGenerator = new RCAPathGenerator();
        boolean result = rcaPathGenerator.isSubPath(inputList,validatorList);
        assertEquals(true,result);
    }

    @Test
    public void testIsSubPathFalse() {
        int[] input = {4,3,6,8,7,9,0};
        int[] validate = {0,3,5,2,5,4,3,8,7,9,0,7,8,3,2,9,8,6};
        List<TopologyDetailsResponse.Nodes> inputList = new ArrayList<>();
        List<TopologyDetailsResponse.Nodes> validatorList = new ArrayList<>();
        Arrays.stream(input).forEach( it -> {
            TopologyDetailsResponse.Nodes node = new TopologyDetailsResponse.Nodes(Integer.toString(it));
            inputList.add(node);
        });
        Arrays.stream(validate).forEach( it -> {
            TopologyDetailsResponse.Nodes node = new TopologyDetailsResponse.Nodes(Integer.toString(it));
            validatorList.add(node);
        });

        RCAPathGenerator rcaPathGenerator = new RCAPathGenerator();
        boolean result = rcaPathGenerator.isSubPath(inputList,validatorList);
        assertEquals(false,result);
    }

    @Test
    public void testAddRCAPath()    {
        int[][] RCAPath = {{1,2,3},{1,3,6},{4,5,6}};
        int [] newPath = {1,3,6,4};
        List<List<TopologyDetailsResponse.Nodes>> RCAPathData = new ArrayList<>();
        List<TopologyDetailsResponse.Nodes> newPathData = new ArrayList<>();

        //Populate RCAPathData
        Arrays.stream(RCAPath).forEach( RCAPathListData -> {
            List<TopologyDetailsResponse.Nodes> list = new ArrayList<>();
            Arrays.stream(RCAPathListData).forEach( it -> {
                TopologyDetailsResponse.Nodes node = new TopologyDetailsResponse.Nodes(Integer.toString(it));
                list.add(node);
            });
            RCAPathData.add(list);
        });

        //Populate newPathData
        Arrays.stream(newPath).forEach( it -> {
            TopologyDetailsResponse.Nodes node = new TopologyDetailsResponse.Nodes(Integer.toString(it));
            newPathData.add(node);
        });

        RCAPathGenerator rcaPathGenerator = new RCAPathGenerator();
        List<List<TopologyDetailsResponse.Nodes>> result = rcaPathGenerator.addRCAPath(newPathData,RCAPathData);
        int resultSize = result.size();
        assertEquals(3,resultSize);

    }

    @Test
    public void testServiceGraphCreation()  {
        ServiceDependencyGraph graph = new ServiceDependencyGraph();
        graph.addEdge("2","1");
        graph.addEdge("1","3");
        graph.addEdge("1","4");
        graph.addEdge("2","5");
        graph.addEdge("3","4");
        graph.addEdge("3","6");
        graph.addEdge("4","2");
        graph.addEdge("4","5");
        graph.addEdge("6","7");
        graph.addEdge("7","5");

        RCAPathGenerator path = new RCAPathGenerator(graph);
        List<List<TopologyDetailsResponse.Nodes>> result = path.getAllRCAPath(new TopologyDetailsResponse.Nodes("1"),
                new TopologyDetailsResponse.Nodes("5"), new ArrayList<>());
        assertEquals(7,result.size());
    }

    @Test
    public void testget1DegreeNodesAndEdges()   {
        ServiceDependencyGraph graph = new ServiceDependencyGraph();
        graph.addEdge("2","1");
        graph.addEdge("1","3");
        graph.addEdge("1","4");
        graph.addEdge("2","5");
        graph.addEdge("3","4");
        graph.addEdge("3","6");
        graph.addEdge("4","2");
        graph.addEdge("4","5");
        graph.addEdge("6","7");
        graph.addEdge("7","5");

        RCAPathGenerator path = new RCAPathGenerator(graph);
        List<List<TopologyDetailsResponse.Nodes>> rcaPath = path.getAllRCAPath(new TopologyDetailsResponse.Nodes("1"),
                new TopologyDetailsResponse.Nodes("5"), new ArrayList<>());

        Map<String,Object> result = path.get1DegreeNodesAndEdges(graph,rcaPath.get(0));
        assertEquals(2,result.size());
    }

    @Test
    public void testMergeRCAPathsFunctionality() {
        List<TopologyDetailsResponse.TopologyDetails> result = new ArrayList<>();
        List<TopologyDetailsResponse.Nodes> nodeHolder = new ArrayList<>();

        for(int i=0; i<5; i++) {
            TopologyDetailsResponse.Nodes temp = new TopologyDetailsResponse.Nodes();
            temp.setId(Integer.toString(i));
            nodeHolder.add(temp);
        }

        TopologyDetailsResponse.TopologyDetails path1 = new TopologyDetailsResponse.TopologyDetails();
        TopologyDetailsResponse.TopologyDetails path2 = new TopologyDetailsResponse.TopologyDetails();
        path1.setNodes(new ArrayList<>());
        path2.setNodes(new ArrayList<>());
        path1.setEdges(new ArrayList<>());
        path2.setEdges(new ArrayList<>());
        path1.getNodes().add(nodeHolder.get(0));
        path1.getNodes().add(nodeHolder.get(2));
        path1.getNodes().add(nodeHolder.get(4));
        path2.getNodes().add(nodeHolder.get(1));
        path2.getNodes().add(nodeHolder.get(3));
        path1.getImpactedServiceName().add("test1");
        path2.getImpactedServiceName().add("test2");

        result.add(path1);
        result.add(path2);

        assertEquals(2,result.size());
        result = RCAPathService.combineRCAPaths(result);
        assertEquals(1, result.size());
        assertEquals(5, result.get(0).getNodes().size());
    }

    @Test
    public void testMergeRCAPathsWithSameNodesFunctionality() {
        List<TopologyDetailsResponse.TopologyDetails> result = new ArrayList<>();
        List<TopologyDetailsResponse.Nodes> nodeHolder = new ArrayList<>();

        for(int i=0; i<5; i++) {
            TopologyDetailsResponse.Nodes temp = new TopologyDetailsResponse.Nodes();
            temp.setId(Integer.toString(i));
            nodeHolder.add(temp);
        }

        TopologyDetailsResponse.TopologyDetails path1 = new TopologyDetailsResponse.TopologyDetails();
        TopologyDetailsResponse.TopologyDetails path2 = new TopologyDetailsResponse.TopologyDetails();
        path1.setNodes(new ArrayList<>());
        path2.setNodes(new ArrayList<>());
        path1.setEdges(new ArrayList<>());
        path2.setEdges(new ArrayList<>());

        path1.getNodes().add(nodeHolder.get(0));
        path1.getNodes().add(nodeHolder.get(1));
        path1.getNodes().add(nodeHolder.get(2));
        path1.getNodes().add(nodeHolder.get(3));
        path1.getNodes().add(nodeHolder.get(4));

        path2.getNodes().add(nodeHolder.get(0));
        path2.getNodes().add(nodeHolder.get(1));
        path2.getNodes().add(nodeHolder.get(2));
        path2.getNodes().add(nodeHolder.get(3));
        path2.getNodes().add(nodeHolder.get(4));

        path1.getImpactedServiceName().add("test1");
        path2.getImpactedServiceName().add("test2");

        result.add(path1);
        result.add(path2);

        assertEquals(2,result.size());
        result = RCAPathService.combineRCAPaths(result);
        assertEquals(1, result.size());
        assertEquals(5, result.get(0).getNodes().size());
    }

    @Test
    public void testMergeRCAPathsWithEdgesFunctionality() {
        List<TopologyDetailsResponse.TopologyDetails> result = new ArrayList<>();
        List<TopologyDetailsResponse.Edges> edgeHolder = new ArrayList<>();

        for(int i=0; i<5; i++) {
            TopologyDetailsResponse.Edges tempEdge = new TopologyDetailsResponse.Edges();
            tempEdge.setSource(Integer.toString(i));
            tempEdge.setTarget(Integer.toString(i+1));
            edgeHolder.add(tempEdge);
        }

        TopologyDetailsResponse.TopologyDetails path1 = new TopologyDetailsResponse.TopologyDetails();
        TopologyDetailsResponse.TopologyDetails path2 = new TopologyDetailsResponse.TopologyDetails();
        path1.setNodes(new ArrayList<>());
        path2.setNodes(new ArrayList<>());
        path1.setEdges(new ArrayList<>());
        path2.setEdges(new ArrayList<>());

        path1.getEdges().add(edgeHolder.get(0));
        path2.getEdges().add(edgeHolder.get(1));
        path1.getEdges().add(edgeHolder.get(2));
        path2.getEdges().add(edgeHolder.get(3));
        path1.getEdges().add(edgeHolder.get(4));

        path1.getImpactedServiceName().add("test1");
        path2.getImpactedServiceName().add("test2");

        result.add(path1);
        result.add(path2);

        assertEquals(2,result.size());
        result = RCAPathService.combineRCAPaths(result);
        assertEquals(1, result.size());
        assertEquals(5, result.get(0).getEdges().size());
    }

    @Test
    public void testMergeRCAPathsWithSameEdgesFunctionality() {
        List<TopologyDetailsResponse.TopologyDetails> result = new ArrayList<>();
        List<TopologyDetailsResponse.Edges> edgeHolder = new ArrayList<>();

        for(int i=0; i<5; i++) {
            TopologyDetailsResponse.Edges tempEdge = new TopologyDetailsResponse.Edges();
            tempEdge.setSource(Integer.toString(i));
            tempEdge.setTarget(Integer.toString(i+1));
            edgeHolder.add(tempEdge);
        }

        TopologyDetailsResponse.TopologyDetails path1 = new TopologyDetailsResponse.TopologyDetails();
        TopologyDetailsResponse.TopologyDetails path2 = new TopologyDetailsResponse.TopologyDetails();
        path1.setNodes(new ArrayList<>());
        path2.setNodes(new ArrayList<>());
        path1.setEdges(new ArrayList<>());
        path2.setEdges(new ArrayList<>());

        path1.getEdges().add(edgeHolder.get(0));
        path1.getEdges().add(edgeHolder.get(1));
        path1.getEdges().add(edgeHolder.get(2));
        path1.getEdges().add(edgeHolder.get(3));
        path1.getEdges().add(edgeHolder.get(4));

        path2.getEdges().add(edgeHolder.get(0));
        path2.getEdges().add(edgeHolder.get(1));
        path2.getEdges().add(edgeHolder.get(2));
        path2.getEdges().add(edgeHolder.get(3));
        path2.getEdges().add(edgeHolder.get(4));

        path1.getImpactedServiceName().add("test1");
        path2.getImpactedServiceName().add("test2");

        result.add(path1);
        result.add(path2);

        assertEquals(2,result.size());
        result = RCAPathService.combineRCAPaths(result);
        assertEquals(1, result.size());
        assertEquals(5, result.get(0).getEdges().size());
    }

    /*@Test
    public void testgetRCAPathEdges()   {
        ServiceDependencyGraph graph = new ServiceDependencyGraph();
        graph.addEdge("2","1");
        graph.addEdge("1","3");
        graph.addEdge("1","4");
        graph.addEdge("2","5");
        graph.addEdge("3","4");
        graph.addEdge("3","6");
        graph.addEdge("4","2");
        graph.addEdge("4","5");
        graph.addEdge("6","7");
        graph.addEdge("7","5");
        graph.addEdge("8","8");

        RCAPathGenerator path = new RCAPathGenerator(graph);
        List<List<TopologyDetailsResponse.Nodes>> rcaPath = path.getAllRCAPath(new TopologyDetailsResponse.Nodes("8"),
                new TopologyDetailsResponse.Nodes("8"), new ArrayList<>());
        *//*List<List<TopologyDetailsResponse.Nodes>> rcaPath = path.getAllRCAPath(new TopologyDetailsResponse.Nodes("1"),
                new TopologyDetailsResponse.Nodes("5"), new ArrayList<>());*//*

        List<TopologyDetailsResponse.Edges> edges = path.getRCAPathEdges(graph,rcaPath.get(0));
        assertEquals(rcaPath.get(0).size()-1,edges.size());
    }*/

}