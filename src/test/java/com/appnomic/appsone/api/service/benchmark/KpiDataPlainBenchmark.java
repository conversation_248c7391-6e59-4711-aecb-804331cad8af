package com.appnomic.appsone.api.service.benchmark;

import com.appnomic.appsone.api.pojo.request.KpiDataRequest;
import com.appnomic.appsone.api.service.KpiDataService;

import java.util.ArrayList;
import java.util.List;

/**
 * These benchmark will only work when the conf.properties points to a working setup
 */
public class KpiDataPlainBenchmark {

    KpiDataService kpiDataService;
    KpiDataRequest kpiDataRequest;

    public KpiDataPlainBenchmark() {
        kpiDataService = new KpiDataService();
        kpiDataRequest = new KpiDataRequest();
    }

    public void testKpiDataHourly() {
        kpiDataRequest.setAccountId("d681ef13-d690-4917-jkhg-6c79b-1");
        kpiDataRequest.setCompInstanceId("6");
        kpiDataRequest.setKpiId("10");
        kpiDataRequest.setGroupId("2");
        kpiDataRequest.setFromTime("*************");
        kpiDataRequest.setToTime("*************");
        kpiDataRequest.setType("");
//        kpiDataService.getKpiData(kpiDataRequest);
    }

    public void testKpiDataMonthly() {
        kpiDataRequest.setAccountId("d681ef13-d690-4917-jkhg-6c79b-1");
        kpiDataRequest.setCompInstanceId("6");
        kpiDataRequest.setKpiId("10");
        kpiDataRequest.setGroupId("2");
        kpiDataRequest.setFromTime("*************");
        kpiDataRequest.setToTime("*************");
        kpiDataRequest.setType("");
//        kpiDataService.getKpiData(kpiDataRequest);
    }

    public static void main(String[] args) {
        long start;
        List<Long> timeTaken = new ArrayList<>();
        KpiDataPlainBenchmark kpiDataPlainBenchmark = new KpiDataPlainBenchmark();
        int warmupIterations = 20;
        int measurementIterations = 20;

        for(int i=0; i<(warmupIterations+measurementIterations); i++) {
            start = System.currentTimeMillis();
            kpiDataPlainBenchmark.testKpiDataHourly();
            if(i > (warmupIterations-1)) timeTaken.add((System.currentTimeMillis() - start));
        }

        System.out.println("Stats after "+warmupIterations+" warmup runs.");
        for(int i=1; i<= timeTaken.size(); i++) {
            System.out.println("Run{ "+i+" } = "+timeTaken.get(i-1)+" millis.");
        }
        System.out.println("Total: "+timeTaken.stream().mapToDouble(it->it).sum());
        System.out.println("Best: "+timeTaken.stream().mapToDouble(it->it).min().orElseGet(null));
        System.out.println("Worst: "+timeTaken.stream().mapToDouble(it->it).max().orElseGet(null));
        System.out.println("Average: "+timeTaken.stream().mapToDouble(it->it).average().orElseGet(null));
        System.exit(0);
    }
}
