/* sagalap created on 12/05/22 inside the package - com.appnomic.appsone.api.service */
package com.appnomic.appsone.api.service;

import com.appnomic.appsone.api.pojo.applicationhealth.ApplicationHealthDetail;
import com.appnomic.appsone.api.pojo.applicationhealth.ApplicationHealthStatus;
import org.junit.jupiter.api.Test;

import java.util.*;

/**
 * <AUTHOR>
 */
public class ApplicationHealthServiceTest {

    @Test
    public void sortApplicationHealthData(){
        ApplicationHealthService healthService = new ApplicationHealthService();
        List<ApplicationHealthDetail> appHealthData = new ArrayList<>();

        ApplicationHealthDetail status = ApplicationHealthDetail.builder()
                .id(21)
                .identifier("9862df2d-8c22-4f72-8a1c-2a1deb337360")
                .name("AppsoneHealthApplication")
                .problem(Arrays.asList(ApplicationHealthStatus.builder().name("Severe").count(35).priority(1).build(), ApplicationHealthStatus.builder().name("Default").count(2).priority(0).build()))
                .warning(Arrays.asList(ApplicationHealthStatus.builder().name("Severe").count(3).priority(1).build(), ApplicationHealthStatus.builder().name("Default").count(1).priority(0).build()))
                .batch(Arrays.asList(ApplicationHealthStatus.builder().name("Severe").count(0).priority(1).build(), ApplicationHealthStatus.builder().name("Default").count(0).priority(0).build()))
                .build();

        System.out.println(status.getSevereProblemCount());
        System.out.println(status.getDefaultProblemCount());

        System.out.println(status.getSevereWarningCount());
        System.out.println(status.getDefaultWarningCount());

        appHealthData.add(ApplicationHealthDetail.builder()
                .id(21)
                .identifier("9862df2d-8c22-4f72-8a1c-2a1deb337360")
                .name("AppsoneHealthApplication")
                .problem(Arrays.asList(ApplicationHealthStatus.builder().name("Severe").count(35).priority(1).build(), ApplicationHealthStatus.builder().name("Default").count(1).priority(0).build()))
                .warning(Arrays.asList(ApplicationHealthStatus.builder().name("Severe").count(3).priority(1).build(), ApplicationHealthStatus.builder().name("Default").count(0).priority(0).build()))
                .batch(Arrays.asList(ApplicationHealthStatus.builder().name("Severe").count(0).priority(1).build(), ApplicationHealthStatus.builder().name("Default").count(0).priority(0).build()))
                .build());

        appHealthData.add(ApplicationHealthDetail.builder()
                .id(1)
                .identifier("Heal_Health_App")
                .name("Heal Health Application")
                .problem(Arrays.asList(ApplicationHealthStatus.builder().name("Severe").count(40).priority(1).build(), ApplicationHealthStatus.builder().name("Default").count(0).priority(0).build()))
                .warning(Arrays.asList(ApplicationHealthStatus.builder().name("Severe").count(0).priority(1).build(), ApplicationHealthStatus.builder().name("Default").count(0).priority(0).build()))
                .batch(Arrays.asList(ApplicationHealthStatus.builder().name("Severe").count(0).priority(1).build(), ApplicationHealthStatus.builder().name("Default").count(0).priority(0).build()))
                .maintenanceWindowStatus(true)
                .build());

        appHealthData.add(ApplicationHealthDetail.builder()
                .id(23)
                .identifier("7aa29cb9-981b-4de9-b52f-9fb0807ec476")
                .name("Oracle-DB-Application")
                .problem(Arrays.asList(ApplicationHealthStatus.builder().name("Severe").count(0).priority(1).build(), ApplicationHealthStatus.builder().name("Default").count(45).priority(0).build()))
                .warning(Arrays.asList(ApplicationHealthStatus.builder().name("Severe").count(0).priority(1).build(), ApplicationHealthStatus.builder().name("Default").count(5).priority(0).build()))
                .batch(Arrays.asList(ApplicationHealthStatus.builder().name("Severe").count(0).priority(1).build(), ApplicationHealthStatus.builder().name("Default").count(0).priority(0).build()))
                .build());

        appHealthData.add(ApplicationHealthDetail.builder()
                .id(2)
                .identifier("Heal_Health_App2")
                .name("Heal Health Application2")
                .problem(Arrays.asList(ApplicationHealthStatus.builder().name("Severe").count(70).priority(1).build(), ApplicationHealthStatus.builder().name("Default").count(0).priority(0).build()))
                .warning(Arrays.asList(ApplicationHealthStatus.builder().name("Severe").count(0).priority(1).build(), ApplicationHealthStatus.builder().name("Default").count(0).priority(0).build()))
                .batch(Arrays.asList(ApplicationHealthStatus.builder().name("Severe").count(0).priority(1).build(), ApplicationHealthStatus.builder().name("Default").count(0).priority(0).build()))
                .maintenanceWindowStatus(true)
                .build());

        appHealthData.add(ApplicationHealthDetail.builder()
                .id(25)
                .identifier("8c7ce83f-1209-404c-9b08-83365cf0a7c4")
                .name("appsone_user")
                .problem(Arrays.asList(ApplicationHealthStatus.builder().name("Severe").count(0).priority(1).build(), ApplicationHealthStatus.builder().name("Default").count(0).priority(0).build()))
                .warning(Arrays.asList(ApplicationHealthStatus.builder().name("Severe").count(0).priority(2).build(), ApplicationHealthStatus.builder().name("Default").count(15).priority(0).build()))
                .batch(Arrays.asList(ApplicationHealthStatus.builder().name("Severe").count(0).priority(1).build(), ApplicationHealthStatus.builder().name("Default").count(0).priority(0).build()))
                .build());

        appHealthData.add(ApplicationHealthDetail.builder()
                .id(26)
                .identifier("4296bc23-58dc-4972-b641-274c47ed96d1")
                .name("helloworld")
                .problem(Arrays.asList(ApplicationHealthStatus.builder().name("Severe").count(5).priority(1).build(), ApplicationHealthStatus.builder().name("Default").count(0).priority(0).build()))
                .warning(Arrays.asList(ApplicationHealthStatus.builder().name("Severe").count(5).priority(2).build(), ApplicationHealthStatus.builder().name("Default").count(1).priority(0).build()))
                .batch(Arrays.asList(ApplicationHealthStatus.builder().name("Severe").count(0).priority(1).build(), ApplicationHealthStatus.builder().name("Default").count(0).priority(0).build()))
                .build());

        appHealthData = healthService.sortApplicationHealthData(appHealthData);
        appHealthData.forEach(a -> System.out.println(a.getName()+"::"+a.getId()+", SevereProblem:"+a.getSevereProblemCount()+", DefaultProblem:"+a.getDefaultProblemCount()+", SevereWarning:"+a.getSevereWarningCount()+", DefaultWarning:"+a.getDefaultWarningCount()));
        System.out.println(appHealthData);
    }
}
