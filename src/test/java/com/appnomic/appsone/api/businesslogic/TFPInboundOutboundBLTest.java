package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.beans.tfp.TFPInboundOutboundRequestData;
import com.appnomic.appsone.api.beans.tfp.TFPRequestData;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.pojo.tfp.TFPAnomalyTransactionDetails;
import org.junit.Test;

import java.util.List;

public class TFPInboundOutboundBLTest {
    @Test
    public void testOutbounds() {
        try {

            // https://*************:8443/appsone-ui-service/v2.0/ui/accounts/qa-d681ef13-d690-4917-jkhg-6c79b-1/applications/14/outbounds?fromTime=*************&toTime=*************
            TransactionFlowPathOutboundBL bl = new TransactionFlowPathOutboundBL();
            UtilityBean<TFPRequestData> utilityBean = UtilityBean.<TFPRequestData>builder()
                    .accountIdString("qa-d681ef13-d690-4917-jkhg-6c79b-1")
                    .applicationIdString("1")
                    .applicationId(1)
                    .fromTimeString("*************")
                    .toTimeString("*************")
                    .fromTime(Long.parseLong("*************"))
                    .toTime(Long.parseLong("*************"))
                    .authToken("eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.aNuI6cSyNKVaFf1fs8KdRjuJk1T2rcA4DJJUjnIffEhXYI3chk9k7qvOi4H6mhACKYDpKu6h7IRAKKo6x3dDiYWlwO4RzWsIz0ZHpxj-d8cjZ3tl8067kmcoi-E9SrLa_9YNXM_H8ODxYc5IETJnf3O7GheKEgUDKoU3WmArxnpPkXmkPe8t5Ak0t97hSzUML2B-OokNSn6nmGOFfUA4WTFJ8b0weRptW5W--_spCGK5XgSfWSAD37IiW35z7kIm077zgPNi1ipfFTvFQ2RdE_zNOcv6IoRRxD8UX29D7ny2NP5oCd0OlntnRPNYxuPm0cbAxVSBCDfFMMKJwQcq-A")
                    .build();
            UtilityBean<TFPRequestData> tfpRequestData = bl.serverValidation(utilityBean);
            System.out.print(bl.processData(tfpRequestData));
        } catch (Exception ex) {
            System.out.println(ex);
        }
    }

    @Test
    public void testOutboundsDetails() {


        // https://*************:8443/appsone-ui-service/v2.0/ui/accounts/qa-d681ef13-d690-4917-jkhg-6c79b-1/applications/14/outbounds?fromTime=*************&toTime=*************

        // Historical - *************&toTime=*************
        // *************&toTime=*************
        try {
            TransactionFlowPathInboundOutboundBL bl = new TransactionFlowPathInboundOutboundBL();
            UtilityBean<TFPInboundOutboundRequestData> utilityBean = UtilityBean.<TFPInboundOutboundRequestData>builder()
                    .accountIdString("qa-d681ef13-d690-4917-jkhg-6c79b-1")
                    .applicationIdString("14")
                    .applicationId(14)
                    .fromTimeString("*************")
                    .toTimeString("*************")
                    .srcServiceIdString("4,2,7") // 16,18 for Enet, 4,2,7 for NB
                    .dstServiceIdString("12")
                    .authToken("eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ZKHsI5ZJhzwuTGQm8oW7Np29D3WLMijza8oLvpkQ3rjIZlmfSl50LG7-ac0UdoOaJY1GdanrYejaJbPfcTciCS_Zbuzm121GMdkTPfRdjtxRI-HsFcC_HMErSz13exHyNxM7fKpet8v5g0-dTe078v6Tr92IJh20GFUn3aQlHTPJLSQUILnLzq5sgjMC_dQYFNyoDLTr8AQsdsdAJStVvcWFhsOmTyHYiSl5IHael7xSRuTYF5u6BOUwD0mJuG-Hz2pQqxedj-tIY6gB1AIQdLmuLvPkzNyQb3-oArjB0rhuiiDU1mbfyBjfJH_rgyUOtxobk_TCirmZNTuZ4cZI6w")
                    .build();
            utilityBean.setFromTime(Long.parseLong(utilityBean.getFromTimeString()));
            utilityBean.setToTime(Long.parseLong(utilityBean.getToTimeString()));
            UtilityBean<TFPInboundOutboundRequestData> tfpRequestData = bl.serverValidation(utilityBean);
            List<TFPAnomalyTransactionDetails> dtls = bl.processData(tfpRequestData);
            dtls.forEach(x -> System.out.println("Txn: " + x.getTransactionName() + " | Anomaly: " + x.getIsAnomaly() + " | Fail count: " + x.getFailVolume()
                    + " | Slow count: " + x.getSlowVolume() + " | Overall: " + x.getVolume()));
        } catch (Exception ex) {
            System.out.println(ex);
        }
    }


}

