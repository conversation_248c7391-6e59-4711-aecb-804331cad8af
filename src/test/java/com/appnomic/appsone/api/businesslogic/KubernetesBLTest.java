package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.beans.KubernetesBean;
import com.appnomic.appsone.api.cache.HealUICache;
import com.appnomic.appsone.api.cache.MasterCache;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.AppsoneException;
import com.appnomic.appsone.api.pojo.AllAccountDetails;
import com.appnomic.appsone.api.pojo.CompInstClusterDetails;
import com.appnomic.appsone.api.util.CommonUtils;
import com.appnomic.appsone.api.util.KubernetesUtil;
import org.junit.jupiter.api.Test;

import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertTrue;

public class KubernetesBLTest {


    private List<CompInstClusterDetails> getNodeList(AllAccountDetails allAccountDetails, Set<Integer> nodeInstanceIds) {
        List<CompInstClusterDetails> nodeList = allAccountDetails.getCompInstanceDetailsList()
                .stream()
                .filter(it -> (it.getComponentTypeName().equalsIgnoreCase(Constants.HOST)))
                .filter(it -> (nodeInstanceIds.contains(it.getInstanceId())))
                .collect(Collectors.toList());
        return nodeList;
    }

    private List<CompInstClusterDetails> getInstanceList(AllAccountDetails allAccountDetails, KubernetesBean kubernetesBean) {
        List<CompInstClusterDetails> instanceList = CommonUtils.getInstancesOfService(allAccountDetails, kubernetesBean.getServiceId(), true, false);
        return instanceList;
    }

    private Set<Integer> getNodeInstanceIds( List<CompInstClusterDetails> instanceList) {
        Set<Integer> nodeInstanceIds = instanceList
                .stream()
                .filter(it -> (it.getComponentTypeName().equalsIgnoreCase(Constants.POD)))
                .collect(Collectors.mapping(CompInstClusterDetails::getParentInstanceId, Collectors.toSet()));
        return nodeInstanceIds;
    }

    private List<CompInstClusterDetails> getPodList( List<CompInstClusterDetails> instanceList) {
        List<CompInstClusterDetails> podList = instanceList
                .stream()
                .filter(it -> (it.getComponentTypeName().equalsIgnoreCase(Constants.POD)))
                .collect(Collectors.toList());
        return podList;
    }

    private CompInstClusterDetails getPodInstance( List<CompInstClusterDetails> instanceList, KubernetesBean kubernetesBean) {
        CompInstClusterDetails podInstance = instanceList
                .stream()
                .filter(it -> (it.getInstanceId() == kubernetesBean.getPodId()))
                .findAny().orElse(null);
        return podInstance;
    }

    private  List<CompInstClusterDetails> getContainerList( List<CompInstClusterDetails> instanceList, KubernetesBean kubernetesBean) {
        List<CompInstClusterDetails> containerList = instanceList
                .stream()
                .filter(it -> (it.getParentInstanceId() == kubernetesBean.getPodId()))
                .collect(Collectors.toList());
        return containerList;
    }


}
