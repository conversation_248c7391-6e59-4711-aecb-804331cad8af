package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.beans.tfp.TFPRequestData;
import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import org.junit.Test;
import spark.Request;

public class TransactionFlowPathOutboundBLTest {
    @Test
    public void testOutbounds() {
        try {

            // https://192.168.14.88:8443/appsone-ui-service/v2.0/ui/accounts/qa-d681ef13-d690-4917-jkhg-6c79b-1/applications/14/outbounds?fromTime=*************&toTime=*************
            TransactionFlowPathOutboundBL bl = new TransactionFlowPathOutboundBL();
            UtilityBean<TFPRequestData> utilityBean = UtilityBean.<TFPRequestData>builder()
                    .accountIdString("qa-d681ef13-d690-4917-jkhg-6c79b-1")
                    .applicationIdString("14")
                    .applicationId(14)
                    .fromTimeString("*************")
                    .toTimeString("*************")
                    .authToken("eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Hyn6mttxA5oBDEgeDq8VXtM-sLQ29NrYZYoYZLJUcam2cjGfVF63OZPCRNdEx7a0o3HgrFDNnOsaQfIohyswZmFOQZ1n6gDEe2rzR2qRke-XpiMjdY4juDQEc6zmQDopUStj2Dx5SQ4UMDZocEGYc_dMX9-Z6W9Y_0ezxmq6VIDdS_JkNP5fXYVe9xS_VMKQHl3cJq8KKqKMkQvS1r2qDyR1CN9SfsU62jQzWlQIgI5pa71obF3tL1x9d0LH1IVas75d9_uw2TdzoWea7rQNjqnx73hLNKCaOeSRlhH6Uz98m8VH0sc66xf7wU9QR2G3EJH17hsfAga4JDlVft2X_g")
                    .build();
            UtilityBean<TFPRequestData> tfpRequestData = bl.serverValidation(utilityBean);
            System.out.print(bl.processData(tfpRequestData));
        } catch (Exception ex) {
            System.out.println(ex);
        }
    }
}
