package com.appnomic.appsone.api.businesslogic;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.pojo.JimTraceRequest;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.google.gson.JsonElement;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.MockitoAnnotations.initMocks;

@Slf4j
public class JimTraceBLTest {
    @InjectMocks
    JimTraceBL jimTraceBL;

    @BeforeEach
    void setUp() {
        initMocks(this);
    }

    @Test
    void clientValidationWhenRequestObjectIsNull() {
        assertThrows(ClientException.class, () -> jimTraceBL.clientValidation(null));
    }

    @Test
    void clientValidationWhenTokenIsEmpty() {
        RequestObject requestObject = new RequestObject(null);

        Map<String, String> cookies = new HashMap<>();
        cookies.put("token", "");
        requestObject.setCookies(cookies);

        assertThrows(ClientException.class, () -> jimTraceBL.clientValidation(requestObject));
    }

    @Test
    void clientValidationIfAccountEmpty() {
        RequestObject requestObject = new RequestObject(null);
        Map<String, String> authorizationToken = new HashMap<>();
        authorizationToken.put(Constants.AUTHORIZATION_HEADER, "authorization token");
        requestObject.setHeaders(authorizationToken);
        assertThrows(ClientException.class, () -> jimTraceBL.clientValidation(requestObject));
    }

    @Test
    void clientValidationWhenServiceEmpty() {
        RequestObject requestObject = new RequestObject(null);
        Map<String, String> authorizationToken = new HashMap<>();
        authorizationToken.put(Constants.AUTHORIZATION_HEADER, "authorization token");
        requestObject.setHeaders(authorizationToken);

        Map<String, String> params = new HashMap<>();
        params.put(":identifier", "identifier");
        requestObject.setParams(params);
        assertThrows(ClientException.class, () -> jimTraceBL.clientValidation(requestObject));
    }

    @Test
    void clientValidationWhenTransactionEmpty() {
        RequestObject requestObject = new RequestObject(null);

        Map<String, String> authorizationToken = new HashMap<>();
        authorizationToken.put(Constants.AUTHORIZATION_HEADER, "authorization token");
        requestObject.setHeaders(authorizationToken);

        Map<String, String> params = new HashMap<>();
        params.put(":identifier", "identifier");
        requestObject.setParams(params);

        Map<String, String[]> queryParam = new HashMap<>();
        queryParam.put("service", new String[]{"serviceId"});
        requestObject.setQueryParams(queryParam);
        assertThrows(ClientException.class, () -> jimTraceBL.clientValidation(requestObject));
    }

    @Test
    void clientValidationWhenStartIsEmpty() {
        RequestObject requestObject = new RequestObject(null);

        Map<String, String> authorizationToken = new HashMap<>();
        authorizationToken.put(Constants.AUTHORIZATION_HEADER, "authorization token");
        requestObject.setHeaders(authorizationToken);

        Map<String, String> params = new HashMap<>();
        params.put(":identifier", "identifier");

        Map<String, String[]> queryParams = new HashMap<>();
        queryParams.put("service", new String[]{"serviceId"});
        queryParams.put("transaction", new String[]{"serviceId"});
        queryParams.put("start", new String[]{""});

        requestObject.setParams(params);
        requestObject.setHeaders(authorizationToken);
        requestObject.setQueryParams(queryParams);

        assertThrows(ClientException.class, () -> jimTraceBL.clientValidation(requestObject));
    }

    @Test
    void clientValidationWhenToTimeIsEmpty() {
        RequestObject requestObject = new RequestObject(null);

        Map<String, String> authorizationToken = new HashMap<>();
        authorizationToken.put(Constants.AUTHORIZATION_HEADER, "authorization token");
        requestObject.setHeaders(authorizationToken);

        Map<String, String> params = new HashMap<>();
        params.put(":identifier", "identifier");

        Map<String, String[]> queryParams = new HashMap<>();
        queryParams.put("service", new String[]{"serviceId"});
        queryParams.put("transaction", new String[]{"serviceId"});
        queryParams.put("start", new String[]{"fromTime"});
        queryParams.put("end", new String[]{""});

        requestObject.setParams(params);
        requestObject.setQueryParams(queryParams);

        assertThrows(ClientException.class, () -> jimTraceBL.clientValidation(requestObject));
    }

    @Test
    void clientValidationWhenToTimeIsNotLongValue() {
        RequestObject requestObject = new RequestObject(null);

        Map<String, String> authorizationToken = new HashMap<>();
        authorizationToken.put(Constants.AUTHORIZATION_HEADER, "authorization token");

        requestObject.setHeaders(authorizationToken);

        Map<String, String> params = new HashMap<>();
        params.put(":identifier", "identifier");

        Map<String, String[]> queryParams = new HashMap<>();
        queryParams.put("service", new String[]{"serviceId"});
        queryParams.put("transaction", new String[]{"serviceId"});
        queryParams.put("start", new String[]{"13245664"});
        queryParams.put("end", new String[]{"endTime"});

        requestObject.setParams(params);
        requestObject.setQueryParams(queryParams);

        assertThrows(ClientException.class, () -> jimTraceBL.clientValidation(requestObject));
    }

    @Test
    void clientValidationWhenFromTimeIsGreaterThanToTime() {

        RequestObject requestObject = new RequestObject(null);
        Map<String, String> authorizationToken = new HashMap<>();
        authorizationToken.put(Constants.AUTHORIZATION_HEADER, "authorization token");

        requestObject.setHeaders(authorizationToken);

        Map<String, String> params = new HashMap<>();
        params.put(":identifier", "identifier");

        Map<String, String[]> queryParams = new HashMap<>();
        queryParams.put("service", new String[]{"serviceId"});
        queryParams.put("transaction", new String[]{"serviceId"});
        queryParams.put("start", new String[]{"1923456780"});
        queryParams.put("end", new String[]{"1234567890"});

        requestObject.setParams(params);
        requestObject.setQueryParams(queryParams);

        assertThrows(ClientException.class, () -> jimTraceBL.clientValidation(requestObject));
    }

    @Test
    void serverValidationWhenInvalidAuthToken() {
        UtilityBean<JimTraceRequest> utilityBean = UtilityBean.<JimTraceRequest>builder()
                .authToken("Authorization")
                .build();

        assertThrows(ServerException.class, () -> jimTraceBL.serverValidation(utilityBean));
    }

    @Test
    void serverValidationWhenInvalidAccount() {
        UtilityBean<JimTraceRequest> utilityBean = UtilityBean.<JimTraceRequest>builder()
                .authToken("eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJybTNrekphSjlJUDFJUFZOT2d2ZmxRQWVud1g2d2MwZTRxQkZOQTdRNzNFIn0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bp9UNqngJ4mtE6Ln4Deovnfe9_oa4tuf6TcWM7J5H4PZcsf3FfRjSbPRz37MFzYgOWpgm7U94yPLYAfaL7Gs3cYbr6PYLyWtigB8X7T9hBCT3ZGEU70v7mFmCR4WGtWpjWnOKalYEPYJgPl_9o1Es09lCwcutl5ws-v7kiiVtPGQftgQG0ByCA6M8CvU6ruNQqQ9QswH6R3ibbd5pY7fOEQkFysgzNOAfObZnSaPPyrcQEABApJNOzcwL2IN8P-oNrfqs_Gk4ejjuE8JB9bhqe9QV_IGzCS58FhlOeAtjf8lCmttz31ItgaCPKWjJNoBjCvccpYoedaBqnyRCxP_fQ")
                .accountIdString("identifier")
                .build();
        assertThrows(ServerException.class, () -> jimTraceBL.serverValidation(utilityBean));
    }

    @Test
    void serverValidationWhenInvalidService() {
        UtilityBean<JimTraceRequest> utilityBean = UtilityBean.<JimTraceRequest>builder()
                .authToken("eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJybTNrekphSjlJUDFJUFZOT2d2ZmxRQWVud1g2d2MwZTRxQkZOQTdRNzNFIn0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bp9UNqngJ4mtE6Ln4Deovnfe9_oa4tuf6TcWM7J5H4PZcsf3FfRjSbPRz37MFzYgOWpgm7U94yPLYAfaL7Gs3cYbr6PYLyWtigB8X7T9hBCT3ZGEU70v7mFmCR4WGtWpjWnOKalYEPYJgPl_9o1Es09lCwcutl5ws-v7kiiVtPGQftgQG0ByCA6M8CvU6ruNQqQ9QswH6R3ibbd5pY7fOEQkFysgzNOAfObZnSaPPyrcQEABApJNOzcwL2IN8P-oNrfqs_Gk4ejjuE8JB9bhqe9QV_IGzCS58FhlOeAtjf8lCmttz31ItgaCPKWjJNoBjCvccpYoedaBqnyRCxP_fQ")
                .accountIdString("heal_health")
                .serviceId(1)
                .build();
        assertThrows(ServerException.class, () -> jimTraceBL.serverValidation(utilityBean));
    }

    @Test
    void serverValidationWhenInvalidTransaction() {
        UtilityBean<JimTraceRequest> utilityBean = UtilityBean.<JimTraceRequest>builder()
                .authToken("eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJybTNrekphSjlJUDFJUFZOT2d2ZmxRQWVud1g2d2MwZTRxQkZOQTdRNzNFIn0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bp9UNqngJ4mtE6Ln4Deovnfe9_oa4tuf6TcWM7J5H4PZcsf3FfRjSbPRz37MFzYgOWpgm7U94yPLYAfaL7Gs3cYbr6PYLyWtigB8X7T9hBCT3ZGEU70v7mFmCR4WGtWpjWnOKalYEPYJgPl_9o1Es09lCwcutl5ws-v7kiiVtPGQftgQG0ByCA6M8CvU6ruNQqQ9QswH6R3ibbd5pY7fOEQkFysgzNOAfObZnSaPPyrcQEABApJNOzcwL2IN8P-oNrfqs_Gk4ejjuE8JB9bhqe9QV_IGzCS58FhlOeAtjf8lCmttz31ItgaCPKWjJNoBjCvccpYoedaBqnyRCxP_fQ")
                .accountIdString("heal_health")
                .serviceId(2)
                .transactionId(1)
                .build();
        assertThrows(ServerException.class, () -> jimTraceBL.serverValidation(utilityBean));
    }

    @Test
    void processDataValidationForJaegerApiCall() throws DataProcessingException {
        JimTraceRequest jimTraceRequest = JimTraceRequest.builder()
                .end(1676521009000L)
                .limit(20)
                .start(1676348209000L)
                .lookBack("custom")
                .maxDuration("")
                .minDuration("")
                .serviceIdentifier("tomcat-app-service")
                .transactionUrlPattern("/index.jsp")
                .build();
        JsonElement jsonElement = jimTraceBL.processData(jimTraceRequest);
        boolean response = !jsonElement.isJsonNull();
        assertTrue(response);
    }
}
