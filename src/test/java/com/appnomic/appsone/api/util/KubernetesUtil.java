package com.appnomic.appsone.api.util;

import com.appnomic.appsone.api.beans.KubernetesBean;
import com.heal.configuration.pojos.Account;

public class KubernetesUtil {

    private static int serviceId = 2;
    private static int accountId = 2;
    private static int podId = 17;

    public static KubernetesBean getKubernetesBean(String type) {
        KubernetesBean kubernetesBean = null;
        com.heal.configuration.pojos.Account account = new Account();
        account.setId(accountId);

        if (type.equals("nodeList") || type.equals("podList")) {
            kubernetesBean = KubernetesBean.builder().account(account).serviceId(serviceId).build();
        }

        if (type.equals("containerList")) {
            kubernetesBean = KubernetesBean.builder().account(account).serviceId(serviceId).podId(podId).build();
        }

        if (kubernetesBean == null) {
            kubernetesBean = KubernetesBean.builder().account(account).serviceId(serviceId).build();
        }

        return kubernetesBean;
    }
}
