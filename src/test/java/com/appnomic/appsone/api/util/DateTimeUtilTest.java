package com.appnomic.appsone.api.util;

import com.appnomic.appsone.api.pojo.AggregationLevel;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class DateTimeUtilTest {

    @Test
    void getCustomGeneratedTimeSeries() {
        AggregationLevel aggregationLevel = AggregationLevel.SIXHOURLY;
        List<Long> result = DateTimeUtil.getCustomGeneratedTimeSeries(1558656000000l,1558742400000l,aggregationLevel);
        assertEquals(true,result.get(0).equals(1558656000000l));
        assertEquals(true,result.get(1).equals(1558677600000l));
        assertEquals(true,result.get(2).equals(1558699200000l));
        assertEquals(true,result.get(3).equals(1558720800000l));
        assertEquals(true,result.get(4).equals(1558742400000l));
        assertEquals(5,result.size());
    }

    @Test
    void getAggregationLevelWIP()   {
        AggregationLevel aggregationLevel = DateTimeUtil.getAggregationLevelWIP(1559538000000l,1559538900000l);
        assertEquals(aggregationLevel, AggregationLevel.MINUTELY_HALFHOUR);

        aggregationLevel = DateTimeUtil.getAggregationLevelWIP(1559538000000l,1559539800000l);
        assertEquals(aggregationLevel, AggregationLevel.MINUTELY_HALFHOUR);

        aggregationLevel = DateTimeUtil.getAggregationLevelWIP(1559538000000l,1559541600000l);
        assertEquals(aggregationLevel, AggregationLevel.MINUTELY_FULLHOUR);

        aggregationLevel = DateTimeUtil.getAggregationLevelWIP(1559538000000l,1559548800000l);
        assertEquals(aggregationLevel, AggregationLevel.FIFTEENMINUTELY_FOURHOUR);

        aggregationLevel = DateTimeUtil.getAggregationLevelWIP(1559538000000l,1559581200000l);
        assertEquals(aggregationLevel, AggregationLevel.THIRTYMINUTELY_TWELVEHOUR);

        aggregationLevel = DateTimeUtil.getAggregationLevelWIP(1559538000000l,1559624400000l);
        assertEquals(aggregationLevel, AggregationLevel.HOURLY_TWENTYHOUR);

        aggregationLevel = DateTimeUtil.getAggregationLevelWIP(1559538000000l,1560056400000l);
        assertEquals(aggregationLevel, AggregationLevel.DAILY_WEEK);

        aggregationLevel = DateTimeUtil.getAggregationLevelWIP(1559538000000l,1562130000000l);
        assertEquals(aggregationLevel, AggregationLevel.DAILY_MONTH);

        aggregationLevel = DateTimeUtil.getAggregationLevelWIP(1559538000000l,1567486800000l);
        assertEquals(aggregationLevel, AggregationLevel.MONTHLY);

        aggregationLevel = DateTimeUtil.getAggregationLevelWIP(1559538000000l,1599109200000l);
        assertEquals(aggregationLevel, AggregationLevel.YEARLY);
    }
}