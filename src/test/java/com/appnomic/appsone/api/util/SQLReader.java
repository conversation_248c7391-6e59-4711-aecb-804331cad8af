package com.appnomic.appsone.api.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;

/**
 * <AUTHOR> : 30/07/2019
 */
public class SQLReader {
    private static final Logger logger = LoggerFactory.getLogger(SQLReader.class);

    private static String getFileContents(String filename)   {
        StringBuilder temp = new StringBuilder();
        try {
            if(filename != null && filename.length() > 0)   {
                BufferedReader br = new BufferedReader(new FileReader(filename));
                int intC;
                //To prevent DOS attack, we are processing one character at a time instead of entire line
                while ((intC = br.read()) != -1) {
                    char c = (char) intC;
                    if(c != '\n' && c != '\r') {
                        temp.append(c);
                    }
                }
            }
        }   catch (Exception e) {
            logger.error("Error occurred while reading file: {}",filename,e);
        }
        return temp.toString();
    }

    public static String initalizeDB()  {
        return getFileContents("./src/test/resources/create.sql");
    }

    public static String initalizeMasterData()  {
        return getFileContents("./src/test/resources/MasterData.sql");
    }

    public static String loadTestData() {
        return getFileContents("./src/test/resources/TestData.sql");
    }

    public static String createViews() {
        return getFileContents("./src/test/resources/Views.sql");
    }

    public static String dummyTest() {
        return getFileContents("./src/test/resources/dummy.sql");
    }

    public static String checkStatus(int[] status)  {
        StringBuilder result = new StringBuilder();
        for(int i=0,n=status.length;i<n;i++)    {
            if(status[i] != 1)
                result.append(i+",");
        }
        return result.toString();
    }

    public static void main(String[] args)  {
        System.out.println(initalizeMasterData());
    }
}
