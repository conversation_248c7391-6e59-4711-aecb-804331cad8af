package com.appnomic.appsone.api.util;

import com.appnomic.appsone.api.pojo.AggregationOpertion;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class CommonUtilsTest {

    @Test
    void aggregator() {
        List<String> input = new ArrayList<>();
        List<String> inputDouble = new ArrayList<>();
        for(int i=0;i<100;i++)  {
            input.add(Integer.toString(10));
            inputDouble.add(Double.toString(i));
        }
        String result = CommonUtils.aggregator(input, AggregationOpertion.SUM);
        assertEquals(result,"1000.0");

        result = CommonUtils.aggregator(input,AggregationOpertion.AVG);
        assertEquals(result,"10.0");

        result = CommonUtils.aggregator(input,AggregationOpertion.MAX);
        assertEquals(result,"10.0");

        result = CommonUtils.aggregator(input,AggregationOpertion.MIN);
        assertEquals(result,"10.0");

        result = CommonUtils.aggregator(inputDouble,AggregationOpertion.SUM);
        assertEquals(result,"4950.0");

        result = CommonUtils.aggregator(inputDouble,AggregationOpertion.AVG);
        assertEquals(result,"49.5");

        result = CommonUtils.aggregator(inputDouble,AggregationOpertion.MAX);
        assertEquals(result,"0.0");

        result = CommonUtils.aggregator(inputDouble,AggregationOpertion.MIN);
        assertEquals(result,"99.0");

    }
}