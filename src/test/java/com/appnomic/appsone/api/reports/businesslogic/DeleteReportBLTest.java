package com.appnomic.appsone.api.reports.businesslogic;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.manager.OpenSearchConnectionManager;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.reports.dao.opensearch.ReportsSearchRepo;
import com.appnomic.appsone.api.reports.pojo.EmailFilePojo;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.heal.configuration.pojos.opensearch.TriggeredReportDetails;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.opensearch.action.admin.indices.delete.DeleteIndexRequest;

import org.opensearch.action.index.IndexRequest;

import org.opensearch.client.RequestOptions;
import org.opensearch.client.RestHighLevelClient;
import org.opensearch.common.xcontent.XContentType;

import java.io.IOException;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.initMocks;

@Slf4j
class DeleteReportBLTest {
    RestHighLevelClient client = OpenSearchConnectionManager.INSTANCE.getElasticClient("heal_health", Constants.INDEX_PREFIX_REPORTS);
    @Mock
    ReportsSearchRepo reportsSearchRepo;

    @InjectMocks
    DeleteReportBL deleteReportBL;

    @BeforeEach
    void setUp() throws JsonProcessingException {
        initMocks(this);
        Map<String, String> args = new HashMap<>();
        args.put("application_name", "testApplicationName");
        args.put("kpi_names", "TestKpiNames");

        Set<String> email = new HashSet<>();
        email.add("<EMAIL>");

        TriggeredReportDetails triggeredReportDetails1 = TriggeredReportDetails.builder()
                .reportName("testReportName")
                .reportId(1234)
                .triggeredUserDetailsId("testTriggeredUserDetailsId")
                .triggeredTime(1672055491602L)
                .arguments(args)
                .completedTime(1672055847247L)
                .outputFileName("testOutput\\.FileName")
                .emailAddress(email)
                .status("Completed")
                .triggeredUserDetailsId("7640123a-fbde-4fe5-9812-581cd1e3a9c1")
                .build();

        String indexName = Constants.INDEX_PREFIX_REPORTS + "_" + "heal_health" + "_" + "2022.w52";

        ObjectMapper objectMapper = new ObjectMapper();

        IndexRequest indexRequest = new IndexRequest()
                .index(indexName)
                .source(objectMapper.writeValueAsString(triggeredReportDetails1), XContentType.JSON)
                .id(indexName);

        long st = System.currentTimeMillis();
        try {
            if (client == null) {
                return;
            }

            client.index(indexRequest, RequestOptions.DEFAULT);


        } catch (Exception e) {
            log.error("Exception while pushing data to push into OpenSearch. ", e);
        } finally {
            log.debug("Pushed index request {} to OpenSearch, time taken {} ms.", indexRequest, (System.currentTimeMillis() - st));
        }
    }

    @AfterEach
    void tearDown() {
        String indexName = Constants.INDEX_PREFIX_REPORTS + "_" + "heal_health" + "_" + "2022.w52";
        IndexRequest indexRequest = new IndexRequest()
                .index(indexName)
                .id(indexName);
        long st = System.currentTimeMillis();
        try {

            if (client == null) {
                return;
            }

            DeleteIndexRequest request = new DeleteIndexRequest(indexName);

            client.indices().delete(request, RequestOptions.DEFAULT);


        } catch (Exception e) {
            log.error("Exception while pushing data to push into OpenSearch. ", e);
        } finally {
            log.debug("deleted index request {} to OpenSearch, time taken {} ms.", indexRequest, (System.currentTimeMillis() - st));
        }
    }

    @After
    public void closeConnection() throws IOException {
        client.close();
    }

    @Test
    void clientValidationWhenRequestObjectIsNull() {
        DeleteReportBL deleteReportBL = new DeleteReportBL();

        assertThrows(ClientException.class, () -> deleteReportBL.clientValidation(null));
    }

    @Test
    void clientValidationWhenAuthKeyIsEmpty() {
        DeleteReportBL deleteReportBL = new DeleteReportBL();

        RequestObject requestObject = new RequestObject(null);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "");
        requestObject.setHeaders(headers);

        assertThrows(ClientException.class, () -> deleteReportBL.clientValidation(requestObject));
    }

    @Test
    void clientValidationWhenIdentifierIsEmpty() {
        DeleteReportBL deleteReportBL = new DeleteReportBL();

        RequestObject requestObject = new RequestObject(null);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Authorization");
        requestObject.setHeaders(headers);

        Map<String, String> params = new HashMap<>();
        params.put(":identifier", "");
        requestObject.setParams(params);

        assertThrows(ClientException.class, () -> deleteReportBL.clientValidation(requestObject));
    }

    @Test
    void clientValidationWhenFromTimeIsEmpty() {
        DeleteReportBL deleteReportBL = new DeleteReportBL();

        RequestObject requestObject = new RequestObject(null);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Authorization");
        requestObject.setHeaders(headers);

        Map<String, String> params = new HashMap<>();
        params.put(":identifier", "identifier");
        requestObject.setParams(params);

        Map<String, String[]> queryParams = new HashMap<>();
        queryParams.put("fromTime", new String[]{""});
        requestObject.setQueryParams(queryParams);

        assertThrows(ClientException.class, () -> deleteReportBL.clientValidation(requestObject));
    }

    @Test
    void clientValidationWhenToTimeIsEmpty() {
        DeleteReportBL deleteReportBL = new DeleteReportBL();

        RequestObject requestObject = new RequestObject(null);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Authorization");
        requestObject.setHeaders(headers);

        Map<String, String> params = new HashMap<>();
        params.put(":identifier", "identifier");
        requestObject.setParams(params);

        Map<String, String[]> queryParams = new HashMap<>();
        queryParams.put("fromTime", new String[]{"fromTime"});
        queryParams.put("toTime", new String[]{""});
        requestObject.setQueryParams(queryParams);

        assertThrows(ClientException.class, () -> deleteReportBL.clientValidation(requestObject));
    }

    @Test
    void clientValidationWhenFromTimeIsNotLongValue() {
        DeleteReportBL deleteReportBL = new DeleteReportBL();

        RequestObject requestObject = new RequestObject(null);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Authorization");
        requestObject.setHeaders(headers);

        Map<String, String> params = new HashMap<>();
        params.put(":identifier", "identifier");
        requestObject.setParams(params);

        Map<String, String[]> queryParams = new HashMap<>();
        queryParams.put("fromTime", new String[]{"fromTime"});
        queryParams.put("toTime", new String[]{"toTime"});
        requestObject.setQueryParams(queryParams);

        assertThrows(ClientException.class, () -> deleteReportBL.clientValidation(requestObject));
    }

    @Test
    void clientValidationWhenToTimeIsNotLongValue() {
        DeleteReportBL deleteReportBL = new DeleteReportBL();

        RequestObject requestObject = new RequestObject(null);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Authorization");
        requestObject.setHeaders(headers);

        Map<String, String> params = new HashMap<>();
        params.put(":identifier", "identifier");
        requestObject.setParams(params);

        Map<String, String[]> queryParams = new HashMap<>();
        queryParams.put("fromTime", new String[]{"1671795193402"});
        queryParams.put("toTime", new String[]{"toTime"});
        requestObject.setQueryParams(queryParams);

        assertThrows(ClientException.class, () -> deleteReportBL.clientValidation(requestObject));
    }

    @Test
    void clientValidationWhenToTimeIsLessThanFromTime() {
        DeleteReportBL deleteReportBL = new DeleteReportBL();

        RequestObject requestObject = new RequestObject(null);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Authorization");
        requestObject.setHeaders(headers);

        Map<String, String> params = new HashMap<>();
        params.put(":identifier", "identifier");
        requestObject.setParams(params);

        Map<String, String[]> queryParams = new HashMap<>();
        queryParams.put("fromTime", new String[]{"1671795193402"});
        queryParams.put("toTime", new String[]{"1671795072846"});
        requestObject.setQueryParams(queryParams);

        assertThrows(ClientException.class, () -> deleteReportBL.clientValidation(requestObject));
    }

    @Test
    void clientValidationWhenInvalidBody() {
        DeleteReportBL deleteReportBL = new DeleteReportBL();
        long fromTime = System.currentTimeMillis();
        RequestObject requestObject = new RequestObject(null);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Authorization");
        requestObject.setHeaders(headers);

        Map<String, String> params = new HashMap<>();
        params.put(":identifier", "identifier");
        requestObject.setParams(params);

        Map<String, String[]> queryParams = new HashMap<>();
        long toTime = System.currentTimeMillis();
        queryParams.put("fromTime", new String[]{String.valueOf(fromTime)});
        queryParams.put("toTime", new String[]{String.valueOf(toTime)});
        requestObject.setQueryParams(queryParams);

        requestObject.setBody("body");

        assertThrows(ClientException.class, () -> deleteReportBL.clientValidation(requestObject));
    }

    @Test
    void clientValidationWhenSuccess() throws ClientException {
        DeleteReportBL deleteReportBL = new DeleteReportBL();
        long fromTime = System.currentTimeMillis();
        RequestObject requestObject = new RequestObject(null);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Authorization");
        requestObject.setHeaders(headers);

        Map<String, String> params = new HashMap<>();
        params.put(":identifier", "identifier");
        requestObject.setParams(params);

        Map<String, String[]> queryParams = new HashMap<>();
        long toTime = System.currentTimeMillis();
        queryParams.put("fromTime", new String[]{String.valueOf(fromTime)});
        queryParams.put("toTime", new String[]{String.valueOf(toTime)});
        requestObject.setQueryParams(queryParams);

        List<String> emailIds = new ArrayList<>();
        emailIds.add("<EMAIL>");

        List<String> fileNames = new ArrayList<>();
        fileNames.add("testFileName");

        EmailFilePojo emailFilePojo = EmailFilePojo.builder()
                .fileNames(fileNames)
                .emailIds(emailIds)
                .build();

        Gson gson = new Gson();
        String jsonData = gson.toJson(emailFilePojo);
        requestObject.setBody(jsonData);

        UtilityBean<List<String>> expected = UtilityBean.<List<String>>builder()
                .authToken("Authorization")
                .accountIdString("identifier")
                .requestPayloadObject(emailFilePojo.getFileNames())
                .build();

        UtilityBean<List<String>> actual = deleteReportBL.clientValidation(requestObject);

        assertEquals(expected, actual);
    }

    @Test
    void serverValidationWhenInvalidAuthToken() {
        DeleteReportBL deleteReportBL = new DeleteReportBL();

        UtilityBean<List<String>> utilityBean = UtilityBean.<List<String>>builder()
                .authToken("Authorization")
                .build();

        assertThrows(ServerException.class, () -> deleteReportBL.serverValidation(utilityBean));
    }

    @Test
    void serverValidationWhenInvalidIdentifier() {
        DeleteReportBL deleteReportBL = new DeleteReportBL();

        UtilityBean<List<String>> utilityBean = UtilityBean.<List<String>>builder()
                .authToken("eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJybTNrekphSjlJUDFJUFZOT2d2ZmxRQWVud1g2d2MwZTRxQkZOQTdRNzNFIn0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bp9UNqngJ4mtE6Ln4Deovnfe9_oa4tuf6TcWM7J5H4PZcsf3FfRjSbPRz37MFzYgOWpgm7U94yPLYAfaL7Gs3cYbr6PYLyWtigB8X7T9hBCT3ZGEU70v7mFmCR4WGtWpjWnOKalYEPYJgPl_9o1Es09lCwcutl5ws-v7kiiVtPGQftgQG0ByCA6M8CvU6ruNQqQ9QswH6R3ibbd5pY7fOEQkFysgzNOAfObZnSaPPyrcQEABApJNOzcwL2IN8P-oNrfqs_Gk4ejjuE8JB9bhqe9QV_IGzCS58FhlOeAtjf8lCmttz31ItgaCPKWjJNoBjCvccpYoedaBqnyRCxP_fQ")
                .accountIdString("identifier")
                .build();

        assertThrows(ServerException.class, () -> deleteReportBL.serverValidation(utilityBean));
    }

    @Test
    void serverValidationWhenTriggeredReportDetailsSizeIsZero() {
        DeleteReportBL deleteReportBL = new DeleteReportBL();

        UtilityBean<List<String>> utilityBean = UtilityBean.<List<String>>builder()
                .authToken("eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJybTNrekphSjlJUDFJUFZOT2d2ZmxRQWVud1g2d2MwZTRxQkZOQTdRNzNFIn0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bp9UNqngJ4mtE6Ln4Deovnfe9_oa4tuf6TcWM7J5H4PZcsf3FfRjSbPRz37MFzYgOWpgm7U94yPLYAfaL7Gs3cYbr6PYLyWtigB8X7T9hBCT3ZGEU70v7mFmCR4WGtWpjWnOKalYEPYJgPl_9o1Es09lCwcutl5ws-v7kiiVtPGQftgQG0ByCA6M8CvU6ruNQqQ9QswH6R3ibbd5pY7fOEQkFysgzNOAfObZnSaPPyrcQEABApJNOzcwL2IN8P-oNrfqs_Gk4ejjuE8JB9bhqe9QV_IGzCS58FhlOeAtjf8lCmttz31ItgaCPKWjJNoBjCvccpYoedaBqnyRCxP_fQ")
                .accountIdString("heal_health")
                .build();

        assertThrows(ServerException.class, () -> deleteReportBL.serverValidation(utilityBean));
    }

    @Test
    void serverValidationWhenSuccess() throws ClientException, ServerException {
        DeleteReportBL deleteReportBL = new DeleteReportBL();

        List<String> fileNames = new ArrayList<>();
        fileNames.add("testOutput\\.FileName");

        UtilityBean<List<String>> utilityBean = UtilityBean.<List<String>>builder()
                .authToken("eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJybTNrekphSjlJUDFJUFZOT2d2ZmxRQWVud1g2d2MwZTRxQkZOQTdRNzNFIn0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bp9UNqngJ4mtE6Ln4Deovnfe9_oa4tuf6TcWM7J5H4PZcsf3FfRjSbPRz37MFzYgOWpgm7U94yPLYAfaL7Gs3cYbr6PYLyWtigB8X7T9hBCT3ZGEU70v7mFmCR4WGtWpjWnOKalYEPYJgPl_9o1Es09lCwcutl5ws-v7kiiVtPGQftgQG0ByCA6M8CvU6ruNQqQ9QswH6R3ibbd5pY7fOEQkFysgzNOAfObZnSaPPyrcQEABApJNOzcwL2IN8P-oNrfqs_Gk4ejjuE8JB9bhqe9QV_IGzCS58FhlOeAtjf8lCmttz31ItgaCPKWjJNoBjCvccpYoedaBqnyRCxP_fQ")
                .accountIdString("heal_health")
                .requestPayloadObject(fileNames)
                .build();
        preServerValidation(deleteReportBL);


        List<TriggeredReportDetails> expected = new ArrayList<>();

        Set<String> email = new HashSet<>();
        email.add("<EMAIL>");
        Map<String, String> args = new HashMap<>();
        args.put("application_name", "testApplicationName");
        args.put("kpi_names", "TestKpiNames");

        TriggeredReportDetails triggeredReportDetails = TriggeredReportDetails.builder()
                .reportId(1234)
                .reportName("testReportName")
                .outputFileName("testOutput\\.FileName")
                .triggeredUserDetailsId("7640123a-fbde-4fe5-9812-581cd1e3a9c1")
                .triggeredTime(1672055491602L)
                .completedTime(1672055847247L)
                .status("Completed")
                .emailAddress(email)
                .arguments(args)
                .build();
        expected.add(triggeredReportDetails);

        List<TriggeredReportDetails> actual = deleteReportBL.serverValidation(utilityBean);

        assertEquals(expected, actual);
    }

    public void preServerValidation(DeleteReportBL deleteReportBL) throws ClientException {
        long fromTime = 1672055491602L;
        long toTime = 1672055847247L;

        RequestObject requestObject = new RequestObject(null);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Authorization");
        requestObject.setHeaders(headers);

        Map<String, String> params = new HashMap<>();
        params.put(":identifier", "identifier");
        requestObject.setParams(params);

        Map<String, String[]> queryParams = new HashMap<>();
        queryParams.put("fromTime", new String[]{String.valueOf(fromTime)});
        queryParams.put("toTime", new String[]{String.valueOf(toTime)});
        requestObject.setQueryParams(queryParams);

        List<String> emailIds = new ArrayList<>();
        emailIds.add("<EMAIL>");

        List<String> fileNames = new ArrayList<>();
        fileNames.add("testFileName");

        EmailFilePojo emailFilePojo = EmailFilePojo.builder()
                .fileNames(fileNames)
                .emailIds(emailIds)
                .build();

        Gson gson = new Gson();
        String jsonData = gson.toJson(emailFilePojo);
        requestObject.setBody(jsonData);

        deleteReportBL.clientValidation(requestObject);
    }

    @Test
    void processDataWhenNoFileFoundOfSameNameInOS() throws ServerException, ClientException {
        DeleteReportBL deleteReportBL = new DeleteReportBL();
        List<TriggeredReportDetails> triggeredReportDetailsList = new ArrayList<>();

        Set<String> email = new HashSet<>();
        email.add("<EMAIL>");
        Map<String, String> args = new HashMap<>();
        args.put("application_name", "testApplicationName");
        args.put("kpi_names", "TestKpiNames");

        TriggeredReportDetails triggeredReportDetails = TriggeredReportDetails.builder()
                .reportId(1234)
                .reportName("testReportName")
                .outputFileName("testOutput\\.FileName")
                .triggeredUserDetailsId("7640123a-fbde-4fe5-9812-581cd1e3a9c1")
                .triggeredTime(1672055491602L)
                .completedTime(1672055847247L)
                .status("Completed")
                .emailAddress(email)
                .arguments(args)
                .build();
        triggeredReportDetailsList.add(triggeredReportDetails);
        preProcessData(deleteReportBL);

        assertThrows(DataProcessingException.class, () -> deleteReportBL.processData(triggeredReportDetailsList));
    }


    void processDataWhenSuccess() throws ServerException, ClientException, DataProcessingException {

        List<TriggeredReportDetails> triggeredReportDetailsList = new ArrayList<>();

        Set<String> email = new HashSet<>();
        email.add("<EMAIL>");
        Map<String, String> args = new HashMap<>();
        args.put("application_name", "testApplicationName");
        args.put("kpi_names", "TestKpiNames");

        TriggeredReportDetails triggeredReportDetails = TriggeredReportDetails.builder()
                .reportId(1234)
                .reportName("testReportName")
                .outputFileName("testOutput\\.FileName")
                .triggeredUserDetailsId("7640123a-fbde-4fe5-9812-581cd1e3a9c1")
                .triggeredTime(1672055491602L)
                .completedTime(1672055847247L)
                .status("Completed")
                .emailAddress(email)
                .arguments(args)
                .build();
        triggeredReportDetailsList.add(triggeredReportDetails);
        preProcessData(deleteReportBL);

        when(reportsSearchRepo.deleteTriggeredReportWithGeneratedFileName("heal_health",
                triggeredReportDetails.getOutputFileName(), 1672055491602L, 1672055847247L)).thenReturn(true);
        assertTrue(reportsSearchRepo.deleteTriggeredReportWithGeneratedFileName("heal_health",
                triggeredReportDetails.getOutputFileName(), 1672055491602L, 1672055847247L));

        String actual = deleteReportBL.processData(triggeredReportDetailsList);
        String expected = "Files deleted successfully.";

        assertEquals(expected, actual);

    }

    public void preProcessData(DeleteReportBL deleteReportBL) throws ServerException, ClientException {

        List<String> fileNames = new ArrayList<>();
        fileNames.add("testOutput\\.FileName");

        UtilityBean<List<String>> utilityBean = UtilityBean.<List<String>>builder()
                .authToken("eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJybTNrekphSjlJUDFJUFZOT2d2ZmxRQWVud1g2d2MwZTRxQkZOQTdRNzNFIn0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bp9UNqngJ4mtE6Ln4Deovnfe9_oa4tuf6TcWM7J5H4PZcsf3FfRjSbPRz37MFzYgOWpgm7U94yPLYAfaL7Gs3cYbr6PYLyWtigB8X7T9hBCT3ZGEU70v7mFmCR4WGtWpjWnOKalYEPYJgPl_9o1Es09lCwcutl5ws-v7kiiVtPGQftgQG0ByCA6M8CvU6ruNQqQ9QswH6R3ibbd5pY7fOEQkFysgzNOAfObZnSaPPyrcQEABApJNOzcwL2IN8P-oNrfqs_Gk4ejjuE8JB9bhqe9QV_IGzCS58FhlOeAtjf8lCmttz31ItgaCPKWjJNoBjCvccpYoedaBqnyRCxP_fQ")
                .accountIdString("heal_health")
                .requestPayloadObject(fileNames)
                .build();
        preServerValidation(deleteReportBL);

        deleteReportBL.serverValidation(utilityBean);
    }

}