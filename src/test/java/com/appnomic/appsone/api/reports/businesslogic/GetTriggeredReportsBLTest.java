package com.appnomic.appsone.api.reports.businesslogic;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.manager.OpenSearchConnectionManager;
import com.appnomic.appsone.api.pojo.RequestObject;

import com.appnomic.appsone.api.pojo.request.UtilityBean;

import com.appnomic.appsone.api.reports.pojo.TriggeredReports;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.Tags;
import com.heal.configuration.pojos.opensearch.TriggeredReportDetails;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.opensearch.action.admin.indices.delete.DeleteIndexRequest;
import org.opensearch.action.index.IndexRequest;
import org.opensearch.client.RequestOptions;
import org.opensearch.client.RestHighLevelClient;
import org.opensearch.common.xcontent.XContentType;

import java.io.IOException;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.MockitoAnnotations.initMocks;

@Slf4j
class GetTriggeredReportsBLTest {
    RestHighLevelClient client = OpenSearchConnectionManager.INSTANCE.getElasticClient("test_identifier", Constants.INDEX_PREFIX_REPORTS);
    @InjectMocks
    GetTriggeredReportsBL getTriggeredReportsBL;

    @BeforeEach
    public void start() throws JsonProcessingException {
        initMocks(this);
        Map<String, String> args = new HashMap<>();
        args.put("application_name", "testApplicationName");
        args.put("kpi_names", "TestKpiNames");

        Set<String> email = new HashSet<>();
        email.add("<EMAIL>");

        TriggeredReportDetails triggeredReportDetails1 = TriggeredReportDetails.builder()
                .reportName("testReportName")
                .reportId(1234)
                .triggeredUserDetailsId("testTriggeredUserDetailsId")
                .triggeredTime(1672055491602L)
                .status("testStatus")
                .arguments(args)
                .completedTime(1672055847247L)
                .outputFileName("testOutput\\.FileName")
                .emailAddress(email)
                .build();

        String indexName = Constants.INDEX_PREFIX_REPORTS + "_" + "test_identifier" + "_" + "2022.w52";

        ObjectMapper objectMapper = new ObjectMapper();

        IndexRequest indexRequest = new IndexRequest()
                .index(indexName)
                .source(objectMapper.writeValueAsString(triggeredReportDetails1), XContentType.JSON)
                .id(indexName);

        long st = System.currentTimeMillis();
        try {
            if (client == null) {
                return;
            }

            client.index(indexRequest, RequestOptions.DEFAULT);


        } catch (Exception e) {
            log.error("Exception while pushing data to push into OpenSearch. ", e);
        } finally {
            log.debug("Pushed index request {} to OpenSearch, time taken {} ms.", indexRequest, (System.currentTimeMillis() - st));
        }
    }

    @AfterEach
    public void end() {
        String indexName = Constants.INDEX_PREFIX_REPORTS + "_" + "test_identifier" + "_" + "2022.w52";

        IndexRequest indexRequest = new IndexRequest()
                .index(indexName)
                .id(indexName);
        long st = System.currentTimeMillis();
        try {

            if (client == null) {
                return;
            }

            DeleteIndexRequest request = new DeleteIndexRequest(indexName);

            client.indices().delete(request, RequestOptions.DEFAULT);


        } catch (Exception e) {
            log.error("Exception while pushing data to push into OpenSearch. ", e);
        } finally {
            log.debug("deleted index request {} to OpenSearch, time taken {} ms.", indexRequest, (System.currentTimeMillis() - st));
        }
    }

    @Before
    public void setUp() {

    }

    @After
    public void cleanUp() throws IOException {
        client.close();
    }

    @Test
    void clientValidationWhenRequestObjectIsNUll() {
        assertThrows(ClientException.class, () -> getTriggeredReportsBL.clientValidation(null));
    }

    @Test
    void clientValidationWhenAuthTokenIsEmpty() {

        RequestObject requestObject = new RequestObject(null);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "");
        requestObject.setHeaders(headers);

        assertThrows(ClientException.class, () -> getTriggeredReportsBL.clientValidation(requestObject));
    }

    @Test
    void clientValidationWhenIdentifierIsNull() {
        RequestObject requestObject = new RequestObject(null);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Authorization");
        requestObject.setHeaders(headers);

        Map<String, String> params = new HashMap<>();
        params.put(":identifier", "");
        requestObject.setParams(params);

        assertThrows(ClientException.class, () -> getTriggeredReportsBL.clientValidation(requestObject));
    }

    @Test
    void clientValidationWhenFromTimeIsEmpty() {

        RequestObject requestObject = new RequestObject(null);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Authorization");
        requestObject.setHeaders(headers);

        Map<String, String> params = new HashMap<>();
        params.put(":identifier", "identifier");
        requestObject.setParams(params);

        Map<String, String[]> queryParams = new HashMap<>();
        queryParams.put("fromTime", new String[1]);
        requestObject.setQueryParams(queryParams);

        assertThrows(ClientException.class, () -> getTriggeredReportsBL.clientValidation(requestObject));
    }

    @Test
    void clientValidationWhenToTimeIsEmpty() {

        RequestObject requestObject = new RequestObject(null);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Authorization");
        requestObject.setHeaders(headers);

        Map<String, String> params = new HashMap<>();
        params.put(":identifier", "identifier");
        requestObject.setParams(params);

        Map<String, String[]> queryParams = new HashMap<>();
        queryParams.put("fromTime", new String[]{"fromTime"});
        queryParams.put("toTime", new String[1]);
        requestObject.setQueryParams(queryParams);

        assertThrows(ClientException.class, () -> getTriggeredReportsBL.clientValidation(requestObject));
    }

    @Test
    void clientValidationWhenToTimeOrFromTimeIsInvalid() {

        RequestObject requestObject = new RequestObject(null);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Authorization");
        requestObject.setHeaders(headers);

        Map<String, String> params = new HashMap<>();
        params.put(":identifier", "identifier");
        requestObject.setParams(params);

        Map<String, String[]> queryParams = new HashMap<>();
        queryParams.put("fromTime", new String[]{"fromTime"});
        queryParams.put("toTime", new String[]{"toTime"});
        requestObject.setQueryParams(queryParams);

        assertThrows(ClientException.class, () -> getTriggeredReportsBL.clientValidation(requestObject));
    }

    @Test
    void clientValidationWhenFromTimeIsGreaterThanToTime() {

        RequestObject requestObject = new RequestObject(null);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Authorization");
        requestObject.setHeaders(headers);

        Map<String, String> params = new HashMap<>();
        params.put(":identifier", "identifier");
        requestObject.setParams(params);

        Map<String, String[]> queryParams = new HashMap<>();
        queryParams.put("fromTime", new String[]{"1671795193402"});
        queryParams.put("toTime", new String[]{"1671795072846"});
        requestObject.setQueryParams(queryParams);

        assertThrows(ClientException.class, () -> getTriggeredReportsBL.clientValidation(requestObject));
    }

    @Test
    void clientValidationWhenSuccess() throws ClientException {

        long fromTime = System.currentTimeMillis();

        RequestObject requestObject = new RequestObject(null);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Authorization");
        requestObject.setHeaders(headers);

        Map<String, String> params = new HashMap<>();
        params.put(":identifier", "identifier");
        requestObject.setParams(params);

        long toTime = System.currentTimeMillis();

        Map<String, String[]> queryParams = new HashMap<>();
        queryParams.put("fromTime", new String[]{String.valueOf(fromTime)});
        queryParams.put("toTime", new String[]{String.valueOf(toTime)});
        requestObject.setQueryParams(queryParams);

        UtilityBean<String> expected = UtilityBean.<String>builder()
                .authToken("Authorization")
                .requestPayloadObject("identifier")
                .fromTime(fromTime)
                .toTime(toTime)
                .build();

        UtilityBean<String> actual = getTriggeredReportsBL.clientValidation(requestObject);

        assertEquals(expected, actual);
    }

    @Test
    void serverValidationWhenUserIdIsNull() {
        long fromTime = System.currentTimeMillis();
        long toTime = System.currentTimeMillis();
        UtilityBean<String> utilityBean = UtilityBean.<String>builder()
                .authToken("Authorization")
                .requestPayloadObject("identifier")
                .fromTime(fromTime)
                .toTime(toTime)
                .build();

        assertThrows(ServerException.class, () -> getTriggeredReportsBL.serverValidation(utilityBean));
    }

    @Test
    void serverValidationWhenAccountIsNull() {

        long fromTime = System.currentTimeMillis();
        long toTime = System.currentTimeMillis();
        UtilityBean<String> utilityBean = UtilityBean.<String>builder()
                .authToken("eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJybTNrekphSjlJUDFJUFZOT2d2ZmxRQWVud1g2d2MwZTRxQkZOQTdRNzNFIn0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bp9UNqngJ4mtE6Ln4Deovnfe9_oa4tuf6TcWM7J5H4PZcsf3FfRjSbPRz37MFzYgOWpgm7U94yPLYAfaL7Gs3cYbr6PYLyWtigB8X7T9hBCT3ZGEU70v7mFmCR4WGtWpjWnOKalYEPYJgPl_9o1Es09lCwcutl5ws-v7kiiVtPGQftgQG0ByCA6M8CvU6ruNQqQ9QswH6R3ibbd5pY7fOEQkFysgzNOAfObZnSaPPyrcQEABApJNOzcwL2IN8P-oNrfqs_Gk4ejjuE8JB9bhqe9QV_IGzCS58FhlOeAtjf8lCmttz31ItgaCPKWjJNoBjCvccpYoedaBqnyRCxP_fQ")
                .requestPayloadObject("identifier")
                .fromTime(fromTime)
                .toTime(toTime)
                .build();

        assertThrows(ServerException.class, () -> getTriggeredReportsBL.serverValidation(utilityBean));
    }

    @Test
    void serverValidationWhenSuccess() throws ServerException {

        long fromTime = System.currentTimeMillis();
        long toTime = System.currentTimeMillis();

        UtilityBean<String> utilityBean = UtilityBean.<String>builder()
                .authToken("eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJybTNrekphSjlJUDFJUFZOT2d2ZmxRQWVud1g2d2MwZTRxQkZOQTdRNzNFIn0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bp9UNqngJ4mtE6Ln4Deovnfe9_oa4tuf6TcWM7J5H4PZcsf3FfRjSbPRz37MFzYgOWpgm7U94yPLYAfaL7Gs3cYbr6PYLyWtigB8X7T9hBCT3ZGEU70v7mFmCR4WGtWpjWnOKalYEPYJgPl_9o1Es09lCwcutl5ws-v7kiiVtPGQftgQG0ByCA6M8CvU6ruNQqQ9QswH6R3ibbd5pY7fOEQkFysgzNOAfObZnSaPPyrcQEABApJNOzcwL2IN8P-oNrfqs_Gk4ejjuE8JB9bhqe9QV_IGzCS58FhlOeAtjf8lCmttz31ItgaCPKWjJNoBjCvccpYoedaBqnyRCxP_fQ")
                .requestPayloadObject("heal_health")
                .fromTime(fromTime)
                .toTime(toTime)
                .build();

        Account account = new Account();
        account.setPrivateKey("MIIBCQIBADAQBgcqhkjOPQIBBgUrgQQAJwSB8TCB7gIBAQRIAAWoEJHwA9hOkSIOEjRfjhLg+JM5F43Hy8TfQ0YXUIyV/3dMS+JnNf+mBS4RfTsfEb1i2uL6hn7cXU491iuLt54f1BLshQjfoAcGBSuBBAAnoYGVA4GSAAQBPxDDWJdKBdAjgQoC62jaS9YGt4ZIQNjswquEtORgAScGqobCarv5D6UTQuxae/GY4EFR+jXulYFNG96+TF6hNe2oSV/5K6AFzg4uGgoCwXD7PJS1E48Peh85aP7fNiKJaqbjw7LlmkFEAPZlhLBddQVxkj8zBU1aRDKodyviw8v4TCLjhqNRPnm1mIq9qSM=");
        account.setPublicKey("MIGnMBAGByqGSM49AgEGBSuBBAAnA4GSAAQBPxDDWJdKBdAjgQoC62jaS9YGt4ZIQNjswquEtORgAScGqobCarv5D6UTQuxae/GY4EFR+jXulYFNG96+TF6hNe2oSV/5K6AFzg4uGgoCwXD7PJS1E48Peh85aP7fNiKJaqbjw7LlmkFEAPZlhLBddQVxkj8zBU1aRDKodyviw8v4TCLjhqNRPnm1mIq9qSM=");
        account.setTimezone("(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi");

        List<Tags> tagsList = new ArrayList<>();
        Tags tags = new Tags();
        tags.setKey("49");
        tags.setType("Timezone");
        tags.setValue("********");
        tagsList.add(tags);
        account.setTags(tagsList);

        account.setId(2);
        account.setStatus(1);
        account.setCreatedTime("2021-12-15 00:00:00");
        account.setUpdatedTime("2021-12-15 00:00:00");
        account.setName("HealHealth");
        account.setIdentifier("heal_health");
        account.setLastModifiedBy("7640123a-fbde-4fe5-9812-581cd1e3a9c1");

        UtilityBean<String> expected = UtilityBean.<String>builder()
                .authToken("eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJybTNrekphSjlJUDFJUFZOT2d2ZmxRQWVud1g2d2MwZTRxQkZOQTdRNzNFIn0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bp9UNqngJ4mtE6Ln4Deovnfe9_oa4tuf6TcWM7J5H4PZcsf3FfRjSbPRz37MFzYgOWpgm7U94yPLYAfaL7Gs3cYbr6PYLyWtigB8X7T9hBCT3ZGEU70v7mFmCR4WGtWpjWnOKalYEPYJgPl_9o1Es09lCwcutl5ws-v7kiiVtPGQftgQG0ByCA6M8CvU6ruNQqQ9QswH6R3ibbd5pY7fOEQkFysgzNOAfObZnSaPPyrcQEABApJNOzcwL2IN8P-oNrfqs_Gk4ejjuE8JB9bhqe9QV_IGzCS58FhlOeAtjf8lCmttz31ItgaCPKWjJNoBjCvccpYoedaBqnyRCxP_fQ")
                .requestPayloadObject("heal_health")
                .fromTime(fromTime)
                .toTime(toTime)
                .account(account)
                .build();

        UtilityBean<String> actual = getTriggeredReportsBL.serverValidation(utilityBean);

        assertEquals(expected, actual);
    }

    @Test
    void processDataWhenReportDetailsSizeIsZero() throws DataProcessingException {

        Account account = new Account();
        account.setName("HealHealth");
        account.setIdentifier("identifier");


        UtilityBean<String> utilityBean = UtilityBean.<String>builder()
                .account(account)
                .toTime(1671795193402L)
                .fromTime(1671795072846L)
                .build();

        List<TriggeredReports> expected = new ArrayList<>();
        List<TriggeredReports> actual = getTriggeredReportsBL.processData(utilityBean);

        assertEquals(expected, actual);

    }

    @Test
    void processDataWhenSuccess() throws ServerException, DataProcessingException {
        GetTriggeredReportsBL getTriggeredReportsBL = new GetTriggeredReportsBL();

        long fromTime = 1672055491602L;
        long toTime = 1672055847247L;

        UtilityBean<String> utilityBean1 = UtilityBean.<String>builder()
                .authToken("eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJybTNrekphSjlJUDFJUFZOT2d2ZmxRQWVud1g2d2MwZTRxQkZOQTdRNzNFIn0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bp9UNqngJ4mtE6Ln4Deovnfe9_oa4tuf6TcWM7J5H4PZcsf3FfRjSbPRz37MFzYgOWpgm7U94yPLYAfaL7Gs3cYbr6PYLyWtigB8X7T9hBCT3ZGEU70v7mFmCR4WGtWpjWnOKalYEPYJgPl_9o1Es09lCwcutl5ws-v7kiiVtPGQftgQG0ByCA6M8CvU6ruNQqQ9QswH6R3ibbd5pY7fOEQkFysgzNOAfObZnSaPPyrcQEABApJNOzcwL2IN8P-oNrfqs_Gk4ejjuE8JB9bhqe9QV_IGzCS58FhlOeAtjf8lCmttz31ItgaCPKWjJNoBjCvccpYoedaBqnyRCxP_fQ")
                .requestPayloadObject("heal_health")
                .fromTime(fromTime)
                .toTime(toTime)
                .build();

        getTriggeredReportsBL.serverValidation(utilityBean1);


        Account account = new Account();
        account.setName("HealHealth");
        account.setIdentifier("test_identifier");
        UtilityBean<String> utilityBean = UtilityBean.<String>builder()
                .account(account)
                .toTime(toTime)
                .fromTime(fromTime)
                .build();

        List<TriggeredReports> expected = new ArrayList<>();
        TriggeredReports triggeredReports = TriggeredReports.builder()
                .reportId(1234)
                .reportName("testReportName")
                .fileName("testOutput\\.FileName")
                .userTriggered("testTriggeredUserDetailsId")
                .triggerTime(1672055491602L)
                .completedTime(1672055847247L)
                .status("testStatus")
                .emailAddress("<EMAIL>")
                .emailStatus("Configured")
                .shouldDownload(false)
                .applicationName("testApplicationName")
                .whyDownloadFailed("File is deleted or moved or not created yet")
                .kpiNames("TestKpiNames")
                .fileFormat("FileName")
                .build();
        expected.add(triggeredReports);
        List<TriggeredReports> actual = getTriggeredReportsBL.processData(utilityBean);

        assertEquals(expected, actual);
    }

}