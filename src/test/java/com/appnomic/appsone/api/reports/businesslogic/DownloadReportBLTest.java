package com.appnomic.appsone.api.reports.businesslogic;

import com.appnomic.appsone.api.common.Constants;
import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.manager.OpenSearchConnectionManager;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.opensearch.TriggeredReportDetails;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.opensearch.action.admin.indices.delete.DeleteIndexRequest;
import org.opensearch.action.index.IndexRequest;
import org.opensearch.client.RequestOptions;
import org.opensearch.client.RestHighLevelClient;
import org.opensearch.common.xcontent.XContentType;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.MockitoAnnotations.initMocks;

@Slf4j
class DownloadReportBLTest {

    @InjectMocks
    DownloadReportBL downloadReportBL;

    public void pushToOS(IndexRequest indexRequest) {
        RestHighLevelClient client = OpenSearchConnectionManager.INSTANCE.getElasticClient("DevTestAccount", Constants.INDEX_PREFIX_REPORTS);
        long st = System.currentTimeMillis();
        try {
            if (client == null) {
                return;
            }

            client.index(indexRequest, RequestOptions.DEFAULT);

        } catch (Exception e) {
            log.error("Exception while pushing data to push into OpenSearch. ", e);
        } finally {
            log.debug("Pushed index request {} to OpenSearch, time taken {} ms.", indexRequest, (System.currentTimeMillis() - st));
        }
    }

    @BeforeEach
    void setUp() {
        initMocks(this);
    }

    @AfterEach
    void tearDown() {
    }

    @Test
    void clientValidationWhenRequestObjectIsNull() {
        assertThrows(ClientException.class, () -> downloadReportBL.clientValidation(null));
    }

    @Test
    void clientValidationWhenTokenIsEmpty() {
        RequestObject requestObject = new RequestObject(null);

        Map<String, String> cookies = new HashMap<>();
        cookies.put("token", "");
        requestObject.setCookies(cookies);

        assertThrows(ClientException.class, () -> downloadReportBL.clientValidation(requestObject));
    }

    @Test
    void clientValidationWhenIdentifierIsEmpty() {
        RequestObject requestObject = new RequestObject(null);

        Map<String, String> cookies = new HashMap<>();
        cookies.put("token", "token");

        Map<String, String> params = new HashMap<>();
        params.put(":identifier", "");

        requestObject.setParams(params);
        requestObject.setCookies(cookies);

        assertThrows(ClientException.class, () -> downloadReportBL.clientValidation(requestObject));
    }

    @Test
    void clientValidationWhenFileNameIsEmpty() {
        RequestObject requestObject = new RequestObject(null);

        Map<String, String> cookies = new HashMap<>();
        cookies.put("token", "token");

        Map<String, String> params = new HashMap<>();
        params.put(":identifier", "identifier");
        params.put(":fileName", "");

        requestObject.setParams(params);
        requestObject.setCookies(cookies);

        assertThrows(ClientException.class, () -> downloadReportBL.clientValidation(requestObject));
    }

    @Test
    void clientValidationWhenFromTimeIsEmpty() {
        RequestObject requestObject = new RequestObject(null);

        Map<String, String> cookies = new HashMap<>();
        cookies.put("token", "token");

        Map<String, String> params = new HashMap<>();
        params.put(":identifier", "identifier");
        params.put(":fileName", "fileName");

        Map<String, String[]> queryParams = new HashMap<>();
        queryParams.put("fromTime", new String[]{""});

        requestObject.setParams(params);
        requestObject.setCookies(cookies);
        requestObject.setQueryParams(queryParams);

        assertThrows(ClientException.class, () -> downloadReportBL.clientValidation(requestObject));
    }

    @Test
    void clientValidationWhenToTimeIsEmpty() {
        RequestObject requestObject = new RequestObject(null);

        Map<String, String> cookies = new HashMap<>();
        cookies.put("token", "token");

        Map<String, String> params = new HashMap<>();
        params.put(":identifier", "identifier");
        params.put(":fileName", "fileName");

        Map<String, String[]> queryParams = new HashMap<>();
        queryParams.put("fromTime", new String[]{"fromTime"});
        queryParams.put("toTime", new String[]{""});

        requestObject.setParams(params);
        requestObject.setCookies(cookies);
        requestObject.setQueryParams(queryParams);

        assertThrows(ClientException.class, () -> downloadReportBL.clientValidation(requestObject));
    }

    @Test
    void clientValidationWhenToTimeIsNotLongValue() {
        RequestObject requestObject = new RequestObject(null);

        Map<String, String> cookies = new HashMap<>();
        cookies.put("token", "token");

        Map<String, String> params = new HashMap<>();
        params.put(":identifier", "identifier");
        params.put(":fileName", "fileName");

        Map<String, String[]> queryParams = new HashMap<>();
        queryParams.put("fromTime", new String[]{"*********0"});
        queryParams.put("toTime", new String[]{"toTime"});

        requestObject.setParams(params);
        requestObject.setCookies(cookies);
        requestObject.setQueryParams(queryParams);

        assertThrows(ClientException.class, () -> downloadReportBL.clientValidation(requestObject));
    }

    @Test
    void clientValidationWhenFromTimeIsGreaterThanToTime() {
        RequestObject requestObject = new RequestObject(null);

        Map<String, String> cookies = new HashMap<>();
        cookies.put("token", "token");

        Map<String, String> params = new HashMap<>();
        params.put(":identifier", "identifier");
        params.put(":fileName", "fileName");

        Map<String, String[]> queryParams = new HashMap<>();
        queryParams.put("fromTime", new String[]{"**********"});
        queryParams.put("toTime", new String[]{"*********0"});

        requestObject.setParams(params);
        requestObject.setCookies(cookies);
        requestObject.setQueryParams(queryParams);

        assertThrows(ClientException.class, () -> downloadReportBL.clientValidation(requestObject));
    }

    @Test
    void clientValidationWhenSuccess() throws ClientException {
        RequestObject requestObject = new RequestObject(null);

        Map<String, String> cookies = new HashMap<>();
        cookies.put("token", "token");

        Map<String, String> params = new HashMap<>();
        params.put(":identifier", "identifier");
        params.put(":fileName", "fileName");

        Map<String, String[]> queryParams = new HashMap<>();
        queryParams.put("fromTime", new String[]{"*********"});
        queryParams.put("toTime", new String[]{"**********"});

        requestObject.setParams(params);
        requestObject.setCookies(cookies);
        requestObject.setQueryParams(queryParams);

        UtilityBean<String> expected = UtilityBean.<String>builder()
                .requestPayloadObject("fileName")
                .authToken("token")
                .accountIdString("identifier")
                .fromTime(*********L)
                .toTime(**********L)
                .build();

        UtilityBean<String> actual = downloadReportBL.clientValidation(requestObject);

        assertEquals(expected, actual);
    }

    @Test
    void serverValidationUserIdIsNull() {
        UtilityBean<String> utilityBean = UtilityBean.<String>builder()
                .authToken("authToken")
                .build();

        assertThrows(ServerException.class, () -> downloadReportBL.serverValidation(utilityBean));
    }

    @Test
    void serverValidationAccountISNull() {

        UtilityBean<String> utilityBean = UtilityBean.<String>builder()
                .authToken("eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJybTNrekphSjlJUDFJUFZOT2d2ZmxRQWVud1g2d2MwZTRxQkZOQTdRNzNFIn0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bp9UNqngJ4mtE6Ln4Deovnfe9_oa4tuf6TcWM7J5H4PZcsf3FfRjSbPRz37MFzYgOWpgm7U94yPLYAfaL7Gs3cYbr6PYLyWtigB8X7T9hBCT3ZGEU70v7mFmCR4WGtWpjWnOKalYEPYJgPl_9o1Es09lCwcutl5ws-v7kiiVtPGQftgQG0ByCA6M8CvU6ruNQqQ9QswH6R3ibbd5pY7fOEQkFysgzNOAfObZnSaPPyrcQEABApJNOzcwL2IN8P-oNrfqs_Gk4ejjuE8JB9bhqe9QV_IGzCS58FhlOeAtjf8lCmttz31ItgaCPKWjJNoBjCvccpYoedaBqnyRCxP_fQ")
                .accountIdString("identifier")
                .build();

        assertThrows(ServerException.class, () -> downloadReportBL.serverValidation(utilityBean));
    }

    @Test
    void serverValidationWhenTriggeredDetailsIsNull() {

        UtilityBean<String> utilityBean = UtilityBean.<String>builder()
                .authToken("eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJybTNrekphSjlJUDFJUFZOT2d2ZmxRQWVud1g2d2MwZTRxQkZOQTdRNzNFIn0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bp9UNqngJ4mtE6Ln4Deovnfe9_oa4tuf6TcWM7J5H4PZcsf3FfRjSbPRz37MFzYgOWpgm7U94yPLYAfaL7Gs3cYbr6PYLyWtigB8X7T9hBCT3ZGEU70v7mFmCR4WGtWpjWnOKalYEPYJgPl_9o1Es09lCwcutl5ws-v7kiiVtPGQftgQG0ByCA6M8CvU6ruNQqQ9QswH6R3ibbd5pY7fOEQkFysgzNOAfObZnSaPPyrcQEABApJNOzcwL2IN8P-oNrfqs_Gk4ejjuE8JB9bhqe9QV_IGzCS58FhlOeAtjf8lCmttz31ItgaCPKWjJNoBjCvccpYoedaBqnyRCxP_fQ")
                .accountIdString("heal_health")
                .toTime(**********L)
                .fromTime(*********0L)
                .build();

        assertThrows(ServerException.class, () -> downloadReportBL.serverValidation(utilityBean));
    }


    @Test
    void serverValidationWhenSuccess() throws ServerException, JsonProcessingException {
        Map<String, String> args = new HashMap<>();
        args.put("application_name", "DevTestApplication");
        args.put("kpi_names", "TestKpiNames");

        Set<String> email = new HashSet<>();
        email.add("<EMAIL>");

        TriggeredReportDetails triggeredReportDetails = TriggeredReportDetails.builder()
                .reportName("testReportName")
                .reportId(1234)
                .triggeredUserDetailsId("7640123a-fbde-4fe5-9812-581cd1e3a9c1")
                .triggeredTime(1672055491602L)
                .status("Completed")
                .arguments(args)
                .completedTime(1672055847247L)
                .outputFileName("test.txt")
                .emailAddress(email)
                .build();

        String indexName = Constants.INDEX_PREFIX_REPORTS + "_" + "devtestaccount" + "_" + "2022.w52";

        ObjectMapper objectMapper = new ObjectMapper();

        pushToOS(new IndexRequest()
                .index(indexName)
                .source(objectMapper.writeValueAsString(triggeredReportDetails), XContentType.JSON)
                .id(indexName));

        UtilityBean<String> utilityBean = UtilityBean.<String>builder()
                .authToken("eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJybTNrekphSjlJUDFJUFZOT2d2ZmxRQWVud1g2d2MwZTRxQkZOQTdRNzNFIn0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bp9UNqngJ4mtE6Ln4Deovnfe9_oa4tuf6TcWM7J5H4PZcsf3FfRjSbPRz37MFzYgOWpgm7U94yPLYAfaL7Gs3cYbr6PYLyWtigB8X7T9hBCT3ZGEU70v7mFmCR4WGtWpjWnOKalYEPYJgPl_9o1Es09lCwcutl5ws-v7kiiVtPGQftgQG0ByCA6M8CvU6ruNQqQ9QswH6R3ibbd5pY7fOEQkFysgzNOAfObZnSaPPyrcQEABApJNOzcwL2IN8P-oNrfqs_Gk4ejjuE8JB9bhqe9QV_IGzCS58FhlOeAtjf8lCmttz31ItgaCPKWjJNoBjCvccpYoedaBqnyRCxP_fQ")
                .accountIdString("DevTestAccount")
                .toTime(1672055847247L)
                .fromTime(1672055491602L)
                .requestPayloadObject("test.txt")
                .build();

        TriggeredReportDetails expected = TriggeredReportDetails.builder()
                .reportName("testReportName")
                .reportId(1234)
                .triggeredUserDetailsId("7640123a-fbde-4fe5-9812-581cd1e3a9c1")
                .triggeredTime(1672055491602L)
                .status("Completed")
                .arguments(args)
                .completedTime(1672055847247L)
                .emailAddress(email)
                .outputFileName("test.txt")
                .build();

        TriggeredReportDetails actual = downloadReportBL.serverValidation(utilityBean);

        assertEquals(expected, actual);

        RestHighLevelClient client = OpenSearchConnectionManager.INSTANCE.getElasticClient("DevTestAccount", Constants.INDEX_PREFIX_REPORTS);

        TriggeredReportDetails triggeredReportDetails1 = TriggeredReportDetails.builder()
                .reportName("testReportName")
                .reportId(1234)
                .triggeredUserDetailsId("7640123a-fbde-4fe5-9812-581cd1e3a9c1")
                .triggeredTime(1672055491602L)
                .status("Completed")
                .arguments(args)
                .completedTime(1672055847247L)
                .emailAddress(email)
                .build();

        ObjectMapper objectMapper1 = new ObjectMapper();

        IndexRequest indexRequest = new IndexRequest()
                .index(indexName)
                .source(objectMapper1.writeValueAsString(triggeredReportDetails1), XContentType.JSON)
                .id(indexName);
        long st = System.currentTimeMillis();
        try {

            if (client == null) {
                return;
            }

            DeleteIndexRequest request = new DeleteIndexRequest(indexName);

            client.indices().delete(request, RequestOptions.DEFAULT);

            client.close();

        } catch (Exception e) {
            log.error("Exception while pushing data to push into OpenSearch. ", e);
        } finally {
            log.debug("deleted index request {} to OpenSearch, time taken {} ms.", indexRequest, (System.currentTimeMillis() - st));
        }

    }

    @Test
    void processDataWhenSuccess() throws DataProcessingException {
        Map<String, String> args = new HashMap<>();
        args.put("application_name", "DevTestApplication");
        args.put("kpi_names", "TestKpiNames");

        Set<String> email = new HashSet<>();
        email.add("<EMAIL>");
        TriggeredReportDetails triggeredReportDetails = TriggeredReportDetails.builder()
                .reportName("testReportName")
                .reportId(1234)
                .triggeredUserDetailsId("7640123a-fbde-4fe5-9812-581cd1e3a9c1")
                .triggeredTime(1672055491602L)
                .status("Completed")
                .arguments(args)
                .completedTime(1672055847247L)
                .emailAddress(email)
                .outputFileName("test.txt")
                .build();
        String[] expected = new String[]{"test.txt", "/tmp/reports\\test.txt"};
        String[] actual = downloadReportBL.processData(triggeredReportDetails);

        assertEquals(expected[0], actual[0]);
    }
}