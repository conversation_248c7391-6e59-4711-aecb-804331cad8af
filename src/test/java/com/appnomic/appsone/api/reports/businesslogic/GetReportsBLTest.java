package com.appnomic.appsone.api.reports.businesslogic;

import com.appnomic.appsone.api.custom.exceptions.ClientException;
import com.appnomic.appsone.api.custom.exceptions.DataProcessingException;
import com.appnomic.appsone.api.custom.exceptions.ServerException;
import com.appnomic.appsone.api.dao.redis.AccountRepo;
import com.appnomic.appsone.api.dao.redis.ReportRepo;
import com.appnomic.appsone.api.pojo.RequestObject;
import com.appnomic.appsone.api.pojo.request.UtilityBean;
import com.appnomic.appsone.api.reports.pojo.ConfiguredReport;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.ReportDetails;
import org.junit.jupiter.api.Test;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class GetReportsBLTest {

    @Test
    void clientValidationWhenRequestObjectIsNUll() {
        GetReportsBL getReportsBL = new GetReportsBL();

        assertThrows(ClientException.class, () -> getReportsBL.clientValidation(null));
    }

    @Test
    void clientValidationWhenAuthTokenIsEmpty(){
        GetReportsBL getReportsBL = new GetReportsBL();

        RequestObject requestObject = new RequestObject(null);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "");
        requestObject.setHeaders(headers);

        assertThrows(ClientException.class, () -> getReportsBL.clientValidation(requestObject));
    }

    @Test
    void clientValidationWhenIdentifierIsNull(){
        GetReportsBL getReportsBL = new GetReportsBL();

        RequestObject requestObject = new RequestObject(null);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Authorization");
        requestObject.setHeaders(headers);

        Map<String, String> params = new HashMap<>();
        params.put(":identifier", "");
        requestObject.setParams(params);

        assertThrows(ClientException.class, () -> getReportsBL.clientValidation(requestObject));
    }

    @Test
    void clientValidationWhenSuccess() throws ClientException {
        GetReportsBL getReportsBL = new GetReportsBL();

        RequestObject requestObject = new RequestObject(null);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Authorization");
        requestObject.setHeaders(headers);

        Map<String, String> params = new HashMap<>();
        params.put(":identifier", "identifier");
        requestObject.setParams(params);

        UtilityBean<Object> expected = UtilityBean.builder()
                .authToken("Authorization")
                .accountIdString("identifier")
                .build();

        UtilityBean<Object> actual = getReportsBL.clientValidation(requestObject);

        assertEquals(expected,actual);
    }

    @Test
    void serverValidationWhenUserIdIsNull() {
        GetReportsBL getReportsBL = new GetReportsBL();

        UtilityBean<Object> utilityBean = UtilityBean.builder().build();
        utilityBean.setAuthToken("Authorization");

        assertThrows(ServerException.class, () -> getReportsBL.serverValidation(utilityBean));
    }

    @Test
    void serverValidationWhenAccountIsNull() {
        GetReportsBL getReportsBL = new GetReportsBL();

        UtilityBean<Object> utilityBean = UtilityBean.builder().build();
        utilityBean.setAuthToken("eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJybTNrekphSjlJUDFJUFZOT2d2ZmxRQWVud1g2d2MwZTRxQkZOQTdRNzNFIn0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bp9UNqngJ4mtE6Ln4Deovnfe9_oa4tuf6TcWM7J5H4PZcsf3FfRjSbPRz37MFzYgOWpgm7U94yPLYAfaL7Gs3cYbr6PYLyWtigB8X7T9hBCT3ZGEU70v7mFmCR4WGtWpjWnOKalYEPYJgPl_9o1Es09lCwcutl5ws-v7kiiVtPGQftgQG0ByCA6M8CvU6ruNQqQ9QswH6R3ibbd5pY7fOEQkFysgzNOAfObZnSaPPyrcQEABApJNOzcwL2IN8P-oNrfqs_Gk4ejjuE8JB9bhqe9QV_IGzCS58FhlOeAtjf8lCmttz31ItgaCPKWjJNoBjCvccpYoedaBqnyRCxP_fQ");
        utilityBean.setAccountIdString("identifier");

        assertThrows(ServerException.class, () -> getReportsBL.serverValidation(utilityBean));
    }

    @Test
    void severValidationWhenSuccess() throws ServerException {
        GetReportsBL getReportsBL = new GetReportsBL();

        UtilityBean<Object> utilityBean = UtilityBean.builder().build();
        utilityBean.setAuthToken("eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJybTNrekphSjlJUDFJUFZOT2d2ZmxRQWVud1g2d2MwZTRxQkZOQTdRNzNFIn0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bp9UNqngJ4mtE6Ln4Deovnfe9_oa4tuf6TcWM7J5H4PZcsf3FfRjSbPRz37MFzYgOWpgm7U94yPLYAfaL7Gs3cYbr6PYLyWtigB8X7T9hBCT3ZGEU70v7mFmCR4WGtWpjWnOKalYEPYJgPl_9o1Es09lCwcutl5ws-v7kiiVtPGQftgQG0ByCA6M8CvU6ruNQqQ9QswH6R3ibbd5pY7fOEQkFysgzNOAfObZnSaPPyrcQEABApJNOzcwL2IN8P-oNrfqs_Gk4ejjuE8JB9bhqe9QV_IGzCS58FhlOeAtjf8lCmttz31ItgaCPKWjJNoBjCvccpYoedaBqnyRCxP_fQ");
        utilityBean.setAccountIdString("heal_health");

        AccountRepo accountRepo = mock(AccountRepo.class);
        when(accountRepo.getAccount(any())).thenReturn(new Account());
        utilityBean.setAccount(new Account());

        assertEquals(utilityBean, getReportsBL.serverValidation(utilityBean));
    }

    @Test
    void processDataReportDetailsSizeIsZero() throws DataProcessingException {
        GetReportsBL getReportsBL = new GetReportsBL();

        UtilityBean<Object> configData = UtilityBean.builder().build();
        Account account = new Account();
        account.setIdentifier("identifier");
        configData.setAccount(account);

        List<ReportDetails> reportDetails = new ArrayList<>();

        ReportRepo reportRepo = mock(ReportRepo.class);
        when(reportRepo.getReportDetails(any())).thenReturn(reportDetails);
        assertEquals(reportDetails, reportRepo.getReportDetails(any()));

        List<ConfiguredReport> expected = new ArrayList<>();
        List<ConfiguredReport> actual = getReportsBL.processData(configData);

        assertEquals(expected, actual);
    }

    @Test
    void processDataReportWhenSuccess() throws DataProcessingException {
        GetReportsBL getReportsBL = new GetReportsBL();

        UtilityBean<Object> configData = UtilityBean.builder().build();
        Account account = new Account();
        account.setIdentifier("heal_health");
        configData.setAccount(account);

        ConfiguredReport configuredReport = new ConfiguredReport();
        configuredReport.setId(1);
        configuredReport.setName("Heal Host Utilization Daily");
        configuredReport.setIdentifier("Heal_Host_Utilization_Daily");
        List<String> parameters = new ArrayList<>();
        parameters.add("Application");
        parameters.add("daterange=31");
        parameters.add("fileFormat=PDF|CSV|XLS|HTML");
        configuredReport.setParameters(parameters);

        List<ConfiguredReport> expected = new ArrayList<>();
        expected.add(configuredReport);

        List<ConfiguredReport> actual = getReportsBL.processData(configData);

        assertEquals(expected.get(0), actual.get(0));
    }
}