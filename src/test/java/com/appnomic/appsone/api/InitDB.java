package com.appnomic.appsone.api;

import org.junit.Test;
import spark.Request;

class RequestStub extends Request   {
    private String testBody;
    public void setBody(String body)   {
        this.testBody = body;
    }

    public String body()    {
        return this.testBody;
    }
}

public class InitDB {
    private boolean status;

    @Test
    public void testConnection()    {
        /*status = MySQLConnectionManager.getInstance().setHaveToRunTestCases(true);
        assert(status);*/
    }

    @Test
    public void testTransactionAdd() throws Exception {
        /*status = MySQLConnectionManager.getInstance().setHaveToRunTestCases(true);
        MasterCache.getInstance().loadMasterDataForCache();
        TransactionService transactionService = new TransactionService();
        if(status)  {
            String jsonString = "[{\"txnGrp\":\"ACCESS_LOG\",\"txnType\":\"HTTP\",\"tags\":[{\"name\":\"Controller\",\"value\":\"BranchTransactions_INT\",\"subTypeName\":\"Application\"}],\"txnName\":\"ACCOUNT_ID\",\"txnIdentifier\":null,\"description\":\"ACCOUNT_ID\",\"isAuditEnabled\":false,\"isAutoConfigured\":false,\"isRawEnabled\":false,\"txnThresholds\":[],\"subTransactions\":[{\"httpTxnConfig\":{\"type\":\"GET\",\"urlPattern\":\"/finbranch/arjspmorph/INFENG/search_accountId.jsp\",\"queryParam\":null,\"headerPattern\":null,\"bodyPattern\":null},\"tcpTxnConfig\":null}],\"bizValueExtractorList\":[],\"txnAuditDetails\":[]}]";
            RequestStub request = new RequestStub();
            request.setBody(jsonString);
            transactionService.addTransaction(request,2,"test");
        }*/
    }
}
