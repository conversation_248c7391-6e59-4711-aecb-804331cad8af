
create table test(id INT AUTO_INCREMENT, name VA<PERSON>HAR(255) NOT NULL) ENGINE=INNODB;
insert into test(name) values ('Appnomic');
insert into test(name) values ('Appsone');


CREATE TABLE IF NOT EXISTS account (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `status` TINYINT NOT NULL,
  `private_key` TEXT NULL,
  `public_key` TEXT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `identifier` VARCHAR(128) NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;

CREATE UNIQUE INDEX `identifier_UNIQUE` ON `account` (`identifier` ASC);

INSERT INTO account (id,name,created_time,updated_time,status,user_details_id,private_key,public_key,identifier) VALUES (1,'Global','2019-07-26 00:00:00','2019-07-26 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1','MIIBCQIBADAQBgcqhkjOPQIBBgUrgQQAJwSB8TCB7gIBAQRIAAWoEJHwA9hOkSIOEjRfjhLg+JM5F43Hy8TfQ0YXUIyV/3dMS+JnNf+mBS4RfTsfEb1i2uL6hn7cXU491iuLt54f1BLshQjfoAcGBSuBBAAnoYGVA4GSAAQBPxDDWJdKBdAjgQoC62jaS9YGt4ZIQNjswquEtORgAScGqobCarv5D6UTQuxae/GY4EFR+jXulYFNG96+TF6hNe2oSV/5K6AFzg4uGgoCwXD7PJS1E48Peh85aP7fNiKJaqbjw7LlmkFEAPZlhLBddQVxkj8zBU1aRDKodyviw8v4TCLjhqNRPnm1mIq9qSM=','MIGnMBAGByqGSM49AgEGBSuBBAAnA4GSAAQBPxDDWJdKBdAjgQoC62jaS9YGt4ZIQNjswquEtORgAScGqobCarv5D6UTQuxae/GY4EFR+jXulYFNG96+TF6hNe2oSV/5K6AFzg4uGgoCwXD7PJS1E48Peh85aP7fNiKJaqbjw7LlmkFEAPZlhLBddQVxkj8zBU1aRDKodyviw8v4TCLjhqNRPnm1mIq9qSM=','e573f852-5057-11e9-8fd2-b37b61e52317');


DROP TABLE IF EXISTS `mst_timezone` ;

CREATE TABLE IF NOT EXISTS `mst_timezone` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `time_zone_id` VARCHAR(128) NOT NULL,
  `timeoffset` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;

CREATE INDEX `mst_timezone_account1_idx` ON `mst_timezone` (`account_id` ASC);
