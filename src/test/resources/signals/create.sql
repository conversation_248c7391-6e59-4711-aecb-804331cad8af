-- -----------------------------------------------------
-- Schema appsone
-- -----------------------------------------------------
-- CREATE SCHEMA IF NOT EXISTS `appsone` DEFAULT CHARACTER SET utf8 ;
drop SCHEMA IF EXISTS `appsone` ;
create SCHEMA `appsone`;
USE `appsone` ;

-- -----------------------------------------------------
-- DROP TABLES
-- -----------------------------------------------------
drop view if exists view_types;
drop table IF EXISTS `appsone`.`account` ;
drop table IF EXISTS `appsone`.`mst_roles` ;
drop table IF EXISTS `appsone`.`mst_access_profiles` ;
drop table IF EXISTS `appsone`.`user_attributes` ;
drop table IF EXISTS `appsone`.`user_access_details` ;
drop table IF EXISTS `appsone`.`mst_type`;
drop table IF EXISTS `appsone`.`mst_sub_type`;
drop table IF EXISTS `appsone`.`tag_details` ;
drop table IF EXISTS `appsone`.`tag_mapping` ;
drop table IF EXISTS `appsone`.`controller` ;

-- -----------------------------------------------------
-- Table `appsone`.``account`.`
-- -----------------------------------------------------

create TABLE `appsone`.`account` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(128) NOT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `status` tinyint NOT NULL,
  `private_key` text,
  `public_key` text,
  `user_details_id` varchar(256) NOT NULL,
  `identifier` varchar(128) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `identifier_UNIQUE` (`identifier`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.``mst_roles`.`
-- -----------------------------------------------------

create TABLE `appsone`.`mst_roles` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(32) NOT NULL,
  `description` VARCHAR(256) NULL,
  `status` TINYINT NOT NULL,
  `ui_visible` TINYINT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `name_UNIQUE` (`name` ASC));

-- -----------------------------------------------------
-- Table `appsone`.``mst_access_profiles`.`
-- -----------------------------------------------------

create TABLE `appsone`.`mst_access_profiles` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(64) NOT NULL,
  `mst_role_id` INT NOT NULL,
  `account_id` INT NOT NULL,
  `is_custom` TINYINT NOT NULL,
  `status` TINYINT NOT NULL,
  `ui_visible` TINYINT NOT NULL DEFAULT 1,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  INDEX `fk_mst_access_profiles_1_idx` (`mst_role_id` ASC),
  CONSTRAINT `fk_mst_access_profiles_1`
    FOREIGN KEY (`mst_role_id`)
    REFERENCES `mst_roles` (`id`)
    ON delete NO ACTION
    ON update NO ACTION);

-- -----------------------------------------------------
-- Table `appsone`.``user_attributes`.`
-- -----------------------------------------------------

create TABLE `appsone`.`user_attributes` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `user_identifier` VARCHAR(256) NOT NULL,
  `contact_number` VARCHAR(64) NULL,
  `email_address` VARCHAR(256) NULL,
  `username` VARCHAR(256) NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `status` tinyint NOT NULL,
  `is_timezone_mychoice` tinyint NOT NULL DEFAULT 0,
  `mst_access_profile_id` INT NOT NULL,
  `mst_role_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  INDEX `fk_user_attributes_1_idx` (`mst_access_profile_id` ASC),
  CONSTRAINT `fk_user_attributes_1`
    FOREIGN KEY (`mst_access_profile_id`)
    REFERENCES `mst_access_profiles` (`id`)
    ON delete NO ACTION
    ON update NO ACTION);

-- -----------------------------------------------------
-- Table `appsone`.``user_access_details`.`
-- -----------------------------------------------------

create TABLE `appsone`.`user_access_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `user_identifier` VARCHAR(256) NOT NULL,
  `access_details` CLOB NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  PRIMARY KEY (`id`));

-- -----------------------------------------------------
-- Table `appsone`.``mst_type`.`
-- -----------------------------------------------------

create TABLE `appsone`.`mst_type` (
  `id` int NOT NULL AUTO_INCREMENT,
  `type` varchar(45) NOT NULL,
  `description` varchar(128) DEFAULT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `account_id` int NOT NULL,
  `status` tinyint NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `mst_type_account1_idx` (`account_id`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.``mst_sub_type`.`
-- -----------------------------------------------------

create TABLE `appsone`.`mst_sub_type` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(45) NOT NULL,
  `mst_type_id` int NOT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `account_id` int NOT NULL,
  `description` varchar(256) NOT NULL,
  `is_custom` tinyint NOT NULL DEFAULT '0',
  `status` tinyint NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `fk_mst_sub_type_mst_type_idx` (`mst_type_id`),
  KEY `mst_sub_type_account1_idx` (`account_id`),
  CONSTRAINT `fk_mst_sub_type_mst_type` FOREIGN KEY (`mst_type_id`) REFERENCES `mst_type` (`id`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.``view_types`.`
-- -----------------------------------------------------

create view appsone.view_types as
select t.type, t.id typeid, st.name, st.id subtypeid
from mst_type t, mst_sub_type st
where t.id = st.mst_type_id;

-- -----------------------------------------------------
-- Table `appsone`.`tag_details`
-- -----------------------------------------------------

create TABLE `appsone`.`tag_details` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(128) NOT NULL,
  `tag_type_id` int NOT NULL,
  `is_predefined` tinyint NOT NULL,
  `ref_table` varchar(64) DEFAULT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `account_id` int NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `ref_where_column_name` varchar(128) DEFAULT NULL,
  `ref_select_column_name` varchar(126) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_tag_details_tag_type_id_idx` (`tag_type_id`),
  KEY `fk_tag_details_account_id_idx` (`account_id`),
  CONSTRAINT `fk_tag_details_account_id` FOREIGN KEY (`account_id`) REFERENCES `account` (`id`),
  CONSTRAINT `fk_tag_details_tag_type_id` FOREIGN KEY (`tag_type_id`) REFERENCES `mst_sub_type` (`id`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`tag_mapping`
-- -----------------------------------------------------

create TABLE `appsone`.`tag_mapping` (
  `id` int NOT NULL AUTO_INCREMENT,
  `tag_id` int NOT NULL,
  `object_id` int NOT NULL,
  `object_ref_table` varchar(256) NOT NULL,
  `tag_key` varchar(256) NOT NULL,
  `tag_value` varchar(256) DEFAULT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `account_id` int NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_tag_mapping_account_id_idx` (`account_id`),
  KEY `fk_tag_mapping_tag_id_idx` (`tag_id`),
  CONSTRAINT `fk_tag_mapping_account_id` FOREIGN KEY (`account_id`) REFERENCES `account` (`id`),
  CONSTRAINT `fk_tag_mapping_tag_id` FOREIGN KEY (`tag_id`) REFERENCES `tag_details` (`id`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`controller`
-- -----------------------------------------------------

create TABLE `appsone`.`controller` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(128) NOT NULL,
  `identifier` varchar(128) NOT NULL,
  `account_id` int NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `controller_type_id` int NOT NULL,
  `monitor_enabled` tinyint NOT NULL DEFAULT '1',
  `status` tinyint NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `fk_application_account_id_idx` (`account_id`),
  CONSTRAINT `fk_application_account_id` FOREIGN KEY (`account_id`) REFERENCES `account` (`id`)
) ENGINE=InnoDB;