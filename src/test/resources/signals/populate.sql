INSERT INTO `account` (`id`,`name`,`created_time`,`updated_time`,`status`,`private_key`,`public_key`,`user_details_id`,`identifier`) VALUES (1,'Global','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'MIIBCQIBADAQBgcqhkjOPQIBBgUrgQQAJwSB8TCB7gIBAQRIAAWoEJHwA9hOkSIOEjRfjhLg+JM5F43Hy8TfQ0YXUIyV/3dMS+JnNf+mBS4RfTsfEb1i2uL6hn7cXU491iuLt54f1BLshQjfoAcGBSuBBAAnoYGVA4GSAAQBPxDDWJdKBdAjgQoC62jaS9YGt4ZIQNjswquEtORgAScGqobCarv5D6UTQuxae/GY4EFR+jXulYFNG96+TF6hNe2oSV/5K6AFzg4uGgoCwXD7PJS1E48Peh85aP7fNiKJaqbjw7LlmkFEAPZlhLBddQVxkj8zBU1aRDKodyviw8v4TCLjhqNRPnm1mIq9qSM=','MIGnMBAGByqGSM49AgEGBSuBBAAnA4GSAAQBPxDDWJdKBdAjgQoC62jaS9YGt4ZIQNjswquEtORgAScGqobCarv5D6UTQuxae/GY4EFR+jXulYFNG96+TF6hNe2oSV/5K6AFzg4uGgoCwXD7PJS1E48Peh85aP7fNiKJaqbjw7LlmkFEAPZlhLBddQVxkj8zBU1aRDKodyviw8v4TCLjhqNRPnm1mIq9qSM=','7640123a-fbde-4fe5-9812-581cd1e3a9c1','e573f852-5057-11e9-8fd2-b37b61e52317');
INSERT INTO `account` (`id`,`name`,`created_time`,`updated_time`,`status`,`private_key`,`public_key`,`user_details_id`,`identifier`) VALUES (2,'INDIA','2020-07-09 11:23:36','2020-07-09 11:23:36',1,'MIIBCQIBADAQBgcqhkjOPQIBBgUrgQQAJwSB8TCB7gIBAQRIAy3YO3JTGhESIDySsQVYDcSaa3UDIi9zqwJflFgAYKSrJSXh/tfbnnLwO9QT7vmuIsmaD2e4z8HtAAeRCn9EuigUUlHIlD71oAcGBSuBBAAnoYGVA4GSAAQD4/FrpQMwRqyqqcuahSXi/8EcJcBoIQ8LMvPi4cknK8d94ICHEdAic3TQ+mLEZrlPkeIFim/McLp3PpVaEvqffjCyp2/VXLsEFgydF0FbeWvxZtGOzL41HtyCXNQhNMuVC0O3sMpKSVZ1A35UQgcakuDQpM9TKEP81ZeEcTqejszE5aGF86ccIhJApryF+44=','MIGnMBAGByqGSM49AgEGBSuBBAAnA4GSAAQD4/FrpQMwRqyqqcuahSXi/8EcJcBoIQ8LMvPi4cknK8d94ICHEdAic3TQ+mLEZrlPkeIFim/McLp3PpVaEvqffjCyp2/VXLsEFgydF0FbeWvxZtGOzL41HtyCXNQhNMuVC0O3sMpKSVZ1A35UQgcakuDQpM9TKEP81ZeEcTqejszE5aGF86ccIhJApryF+44=','7640123a-fbde-4fe5-9812-581cd1e3a9c1','d681ef13-d690-4917-jkhg-6c79b-1');

INSERT INTO mst_roles (id, name, description, status, ui_visible, created_time, updated_time, user_details_id) VALUES (1, 'Super Admin', 'One who initially installs Heal, has complete access to all accounts per installation.', 1, 0, '2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_roles (id, name, description, status, ui_visible, created_time, updated_time, user_details_id) VALUES (2, 'Admin', 'One or more individual, having access to one or multiple Big features/application (read/write/edit access).', 1, 1, '2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_roles (id, name, description, status, ui_visible, created_time, updated_time, user_details_id) VALUES (3, 'User Manager', 'One or more individual, having access to only Admin_Users section in Control Center.', 1, 1, '2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_roles (id, name, description, status, ui_visible, created_time, updated_time, user_details_id) VALUES (4, 'User', 'One or more individual, who has access to Heal dashboard only. Access will be a combination of Big Feature and Application level.', 1, 1, '2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');

INSERT INTO mst_access_profiles(id, name, mst_role_id, account_id, is_custom, status, ui_visible, created_time, updated_time, user_details_id) VALUES (1, 'Super Admin', 1, 1, 0, 1, 0,'2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profiles(id, name, mst_role_id, account_id, is_custom, status, ui_visible, created_time, updated_time, user_details_id) VALUES (2, 'Heal Admin', 2, 1, 0, 1, 1,'2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profiles(id, name, mst_role_id, account_id, is_custom, status, ui_visible, created_time, updated_time, user_details_id) VALUES (3, 'Application Owner', 2, 1, 0, 1, 1,'2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profiles(id, name, mst_role_id, account_id, is_custom, status, ui_visible, created_time, updated_time, user_details_id) VALUES (4, 'User Manager', 3, 1, 0, 1, 1,'2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profiles(id, name, mst_role_id, account_id, is_custom, status, created_time, updated_time, user_details_id) VALUES (5, 'Application User', 4, 1, 0, 1,'2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');

INSERT INTO user_attributes (id, user_identifier, username, status, mst_access_profile_id, mst_role_id, created_time, updated_time, user_details_id) VALUES (1, '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 'appsoneadmin', 1, 1, 1,'2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO `user_attributes` (`id`, `user_identifier`, `contact_number`, `email_address`, `username`, `user_details_id`, `created_time`, `updated_time`, `status`, `is_timezone_mychoice`, `mst_access_profile_id`, `mst_role_id`) VALUES ('47', '11f40006-ccb6-4fab-8fc9-c296f317d175', '**********', '<EMAIL>', 'healadmin', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-08-01 17:44:35', '2020-08-03 15:13:10', '1', '0', '2', '2');
INSERT INTO `user_attributes` (`id`, `user_identifier`, `contact_number`, `email_address`, `username`, `user_details_id`, `created_time`, `updated_time`, `status`, `is_timezone_mychoice`, `mst_access_profile_id`, `mst_role_id`) VALUES ('48', 'f67bca59-7a8d-4d20-bc8b-c972fe007915', '**********', '<EMAIL>', 'appowner', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-08-01 17:45:43', '2020-08-03 15:17:00', '1', '0', '3', '2');
INSERT INTO `user_attributes` (`id`, `user_identifier`, `contact_number`, `email_address`, `username`, `user_details_id`, `created_time`, `updated_time`, `status`, `is_timezone_mychoice`, `mst_access_profile_id`, `mst_role_id`) VALUES ('49', '9feea7db-c6eb-4071-828a-ee9f40016dff', '**********', '<EMAIL>', 'usermanager', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-08-01 17:47:04', '2020-08-01 17:47:04', '1', '0', '4', '3');
INSERT INTO `user_attributes` (`id`, `user_identifier`, `contact_number`, `email_address`, `username`, `user_details_id`, `created_time`, `updated_time`, `status`, `is_timezone_mychoice`, `mst_access_profile_id`, `mst_role_id`) VALUES ('50', 'e8c4c018-4eb4-43da-b5b0-dc8409bacbe1', '**********', '<EMAIL>', 'appuser', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-08-01 17:47:38', '2020-08-01 17:47:38', '1', '0', '5', '4');
INSERT INTO `user_attributes` (`id`, `user_identifier`, `contact_number`, `email_address`, `username`, `user_details_id`, `created_time`, `updated_time`, `status`, `is_timezone_mychoice`, `mst_access_profile_id`, `mst_role_id`) VALUES ('55', 'f635c526-d84c-476d-bd7e-dec7182565ce', '**********', '<EMAIL>', 'Testing', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-08-03 18:32:28', '2020-08-04 11:46:33', '1', '0', '3', '2');
INSERT INTO `user_attributes` (`id`, `user_identifier`, `contact_number`, `email_address`, `username`, `user_details_id`, `created_time`, `updated_time`, `status`, `is_timezone_mychoice`, `mst_access_profile_id`, `mst_role_id`) VALUES ('56', '46ff60d2-f072-416a-8631-6069fa1fb6c3', '**********', '<EMAIL>', 'Test', '9feea7db-c6eb-4071-828a-ee9f40016dff', '2020-08-04 13:34:54', '2020-08-04 13:36:22', '0', '0', '4', '3');

INSERT INTO user_access_details (id, user_identifier, access_details,created_time,updated_time,user_details_id) VALUES (1, '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '{"accounts":["*"], "accountMapping": {}}','2020-07-05 00:00:00','2020-07-05 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO `user_access_details` (`id`, `user_identifier`, `access_details`, `user_details_id`, `created_time`, `updated_time`) VALUES ('46', '11f40006-ccb6-4fab-8fc9-c296f317d175', '{"accounts": ["d681ef13-d690-4917-jkhg-6c79b-1", "j681ef13-d690-4917-jkhg-6c79b-1"], "accountMapping": {"d681ef13-d690-4917-jkhg-6c79b-1": {"applications": ["*"]}, "j681ef13-d690-4917-jkhg-6c79b-1": {"applications": ["*"]}}}', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-08-01 12:14:34', '2020-08-03 09:43:10');
INSERT INTO `user_access_details` (`id`, `user_identifier`, `access_details`, `user_details_id`, `created_time`, `updated_time`) VALUES ('47', 'f67bca59-7a8d-4d20-bc8b-c972fe007915', '{"accounts": ["d681ef13-d690-4917-jkhg-6c79b-1"], "accountMapping": {"d681ef13-d690-4917-jkhg-6c79b-1": {"applications": ["los_2", "netbanking_1", "microbanking_1"]}}}', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-08-01 12:15:42', '2020-08-03 09:46:59');
INSERT INTO `user_access_details` (`id`, `user_identifier`, `access_details`, `user_details_id`, `created_time`, `updated_time`) VALUES ('48', '9feea7db-c6eb-4071-828a-ee9f40016dff', '{"accounts": ["*"], "accountMapping": {}}', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-08-01 12:17:03', '2020-08-01 12:17:03');
INSERT INTO `user_access_details` (`id`, `user_identifier`, `access_details`, `user_details_id`, `created_time`, `updated_time`) VALUES ('49', 'e8c4c018-4eb4-43da-b5b0-dc8409bacbe1', '{"accounts": ["d681ef13-d690-4917-jkhg-6c79b-1"], "accountMapping": {"d681ef13-d690-4917-jkhg-6c79b-1": {"applications": ["netbanking_1", "los_2", "microbanking_1", "enet_3", "3f2d43b8-2676-485b-b401-4dc5c2081619"]}}}', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-08-01 12:17:38', '2020-08-01 12:17:38');
INSERT INTO `user_access_details` (`id`, `user_identifier`, `access_details`, `user_details_id`, `created_time`, `updated_time`) VALUES ('54', 'f635c526-d84c-476d-bd7e-dec7182565ce', '{"accounts": ["d681ef13-d690-4917-jkhg-6c79b-1", "c681ef13-d690-4917-jkhg-6c79b-1", "j681ef13-d690-4917-jkhg-6c79b-1"], "accountMapping": {"c681ef13-d690-4917-jkhg-6c79b-1": {"applications": ["UPI", "OfBiz"]}, "d681ef13-d690-4917-jkhg-6c79b-1": {"applications": ["microbanking_1", "los_2"]}, "j681ef13-d690-4917-jkhg-6c79b-1": {"applications": ["Audit-Trigger-191-Test"]}}}', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-08-03 13:02:27', '2020-08-04 06:16:32');
INSERT INTO `user_access_details` (`id`, `user_identifier`, `access_details`, `user_details_id`, `created_time`, `updated_time`) VALUES ('55', '46ff60d2-f072-416a-8631-6069fa1fb6c3', '{"accounts": ["*"], "accountMapping": {}}', '9feea7db-c6eb-4071-828a-ee9f40016dff', '2020-08-04 08:04:53', '2020-08-04 08:06:21');

INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (2,'Application','Application type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (69,'TagType','Data details of tag types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (70,'ControllerType','Controller types based on installation','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (103,'SignalSeverity','Signal Severity Type','2020-05-11 00:00:00','2020-05-11 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);

INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (190,'String',69,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Plain text tag type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (191,'Application',70,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Application controller type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (192,'Services',70,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Service controller type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (295,'Severe',103,'2020-05-11 00:00:00','2020-05-11 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Signal Severity Type details.',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (296,'Default',103,'2020-05-11 00:00:00','2020-05-11 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Signal Severity Type details.',0,1);

INSERT INTO `tag_details` (`id`,`name`,`tag_type_id`,`is_predefined`,`ref_table`,`created_time`,`updated_time`,`account_id`,`user_details_id`,`ref_where_column_name`,`ref_select_column_name`) VALUES (1,'Controller',190,1,'controller','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1','identifier = :name and account_id = :accountId','id tagKey, identifier tagValue');

INSERT INTO `tag_mapping` (`id`,`tag_id`,`object_id`,`object_ref_table`,`tag_key`,`tag_value`,`created_time`,`updated_time`,`account_id`,`user_details_id`) VALUES (464,1,1,'controller','2','NB-Web-Service','2020-08-21 05:37:33','2020-08-21 05:37:33',2,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO `tag_mapping` (`id`,`tag_id`,`object_id`,`object_ref_table`,`tag_key`,`tag_value`,`created_time`,`updated_time`,`account_id`,`user_details_id`) VALUES (470,1,1,'controller','3','NB-User','2020-08-21 05:37:33','2020-08-21 05:37:33',2,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO `tag_mapping` (`id`,`tag_id`,`object_id`,`object_ref_table`,`tag_key`,`tag_value`,`created_time`,`updated_time`,`account_id`,`user_details_id`) VALUES (476,1,1,'controller','4','NB-DB-Service','2020-08-21 05:37:33','2020-08-21 05:37:33',2,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');

INSERT INTO `controller` (`id`,`name`,`identifier`,`account_id`,`user_details_id`,`created_time`,`updated_time`,`controller_type_id`,`monitor_enabled`,`status`) VALUES (2,'NB-Web-Service','NB-Web-Service',2,'7640123a-fbde-4fe5-9812-581cd1e3a9c1','2020-07-09 05:53:44','2020-07-09 05:53:44',192,1,1);

INSERT INTO `controller` (`id`,`name`,`identifier`,`account_id`,`user_details_id`,`created_time`,`updated_time`,`controller_type_id`,`monitor_enabled`,`status`) VALUES (1,'NetBanking','netbanking_1',2,'7640123a-fbde-4fe5-9812-581cd1e3a9c1','2020-08-21 05:37:32','2020-08-21 05:37:32',191,1,1);