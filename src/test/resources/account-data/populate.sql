INSERT INTO `account` (`id`,`name`,`created_time`,`updated_time`,`status`,`private_key`,`public_key`,`user_details_id`,`identifier`) VALUES (1,'Global','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'MIIBCQIBADAQBgcqhkjOPQIBBgUrgQQAJwSB8TCB7gIBAQRIAAWoEJHwA9hOkSIOEjRfjhLg+JM5F43Hy8TfQ0YXUIyV/3dMS+JnNf+mBS4RfTsfEb1i2uL6hn7cXU491iuLt54f1BLshQjfoAcGBSuBBAAnoYGVA4GSAAQBPxDDWJdKBdAjgQoC62jaS9YGt4ZIQNjswquEtORgAScGqobCarv5D6UTQuxae/GY4EFR+jXulYFNG96+TF6hNe2oSV/5K6AFzg4uGgoCwXD7PJS1E48Peh85aP7fNiKJaqbjw7LlmkFEAPZlhLBddQVxkj8zBU1aRDKodyviw8v4TCLjhqNRPnm1mIq9qSM=','MIGnMBAGByqGSM49AgEGBSuBBAAnA4GSAAQBPxDDWJdKBdAjgQoC62jaS9YGt4ZIQNjswquEtORgAScGqobCarv5D6UTQuxae/GY4EFR+jXulYFNG96+TF6hNe2oSV/5K6AFzg4uGgoCwXD7PJS1E48Peh85aP7fNiKJaqbjw7LlmkFEAPZlhLBddQVxkj8zBU1aRDKodyviw8v4TCLjhqNRPnm1mIq9qSM=','7640123a-fbde-4fe5-9812-581cd1e3a9c1','e573f852-5057-11e9-8fd2-b37b61e52317');
INSERT INTO `account` (`id`,`name`,`created_time`,`updated_time`,`status`,`private_key`,`public_key`,`user_details_id`,`identifier`) VALUES (2,'INDIA','2020-07-09 11:23:36','2020-07-09 11:23:36',1,'MIIBCQIBADAQBgcqhkjOPQIBBgUrgQQAJwSB8TCB7gIBAQRIAy3YO3JTGhESIDySsQVYDcSaa3UDIi9zqwJflFgAYKSrJSXh/tfbnnLwO9QT7vmuIsmaD2e4z8HtAAeRCn9EuigUUlHIlD71oAcGBSuBBAAnoYGVA4GSAAQD4/FrpQMwRqyqqcuahSXi/8EcJcBoIQ8LMvPi4cknK8d94ICHEdAic3TQ+mLEZrlPkeIFim/McLp3PpVaEvqffjCyp2/VXLsEFgydF0FbeWvxZtGOzL41HtyCXNQhNMuVC0O3sMpKSVZ1A35UQgcakuDQpM9TKEP81ZeEcTqejszE5aGF86ccIhJApryF+44=','MIGnMBAGByqGSM49AgEGBSuBBAAnA4GSAAQD4/FrpQMwRqyqqcuahSXi/8EcJcBoIQ8LMvPi4cknK8d94ICHEdAic3TQ+mLEZrlPkeIFim/McLp3PpVaEvqffjCyp2/VXLsEFgydF0FbeWvxZtGOzL41HtyCXNQhNMuVC0O3sMpKSVZ1A35UQgcakuDQpM9TKEP81ZeEcTqejszE5aGF86ccIhJApryF+44=','7640123a-fbde-4fe5-9812-581cd1e3a9c1','d681ef13-d690-4917-jkhg-6c79b-1');

INSERT INTO mst_roles (id, name, description, status, ui_visible, created_time, updated_time, user_details_id) VALUES (1, 'Super Admin', 'One who initially installs Heal, has complete access to all accounts per installation.', 1, 0, '2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_roles (id, name, description, status, ui_visible, created_time, updated_time, user_details_id) VALUES (2, 'Admin', 'One or more individual, having access to one or multiple Big features/application (read/write/edit access).', 1, 1, '2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_roles (id, name, description, status, ui_visible, created_time, updated_time, user_details_id) VALUES (3, 'User Manager', 'One or more individual, having access to only Admin_Users section in Control Center.', 1, 1, '2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_roles (id, name, description, status, ui_visible, created_time, updated_time, user_details_id) VALUES (4, 'User', 'One or more individual, who has access to Heal dashboard only. Access will be a combination of Big Feature and Application level.', 1, 1, '2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');

INSERT INTO mst_access_profiles(id, name, mst_role_id, account_id, is_custom, status, ui_visible, created_time, updated_time, user_details_id) VALUES (1, 'Super Admin', 1, 1, 0, 1, 0,'2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profiles(id, name, mst_role_id, account_id, is_custom, status, ui_visible, created_time, updated_time, user_details_id) VALUES (2, 'Heal Admin', 2, 1, 0, 1, 1,'2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profiles(id, name, mst_role_id, account_id, is_custom, status, ui_visible, created_time, updated_time, user_details_id) VALUES (3, 'Application Owner', 2, 1, 0, 1, 1,'2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profiles(id, name, mst_role_id, account_id, is_custom, status, ui_visible, created_time, updated_time, user_details_id) VALUES (4, 'User Manager', 3, 1, 0, 1, 1,'2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profiles(id, name, mst_role_id, account_id, is_custom, status, created_time, updated_time, user_details_id) VALUES (5, 'Application User', 4, 1, 0, 1,'2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');

INSERT INTO user_attributes (id, user_identifier, username, status, mst_access_profile_id, mst_role_id, created_time, updated_time, user_details_id) VALUES (1, '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 'appsoneadmin', 1, 1, 1,'2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO `user_attributes` (`id`, `user_identifier`, `contact_number`, `email_address`, `username`, `user_details_id`, `created_time`, `updated_time`, `status`, `is_timezone_mychoice`, `mst_access_profile_id`, `mst_role_id`) VALUES ('47', '11f40006-ccb6-4fab-8fc9-c296f317d175', '**********', '<EMAIL>', 'healadmin', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-08-01 17:44:35', '2020-08-03 15:13:10', '1', '0', '2', '2');
INSERT INTO `user_attributes` (`id`, `user_identifier`, `contact_number`, `email_address`, `username`, `user_details_id`, `created_time`, `updated_time`, `status`, `is_timezone_mychoice`, `mst_access_profile_id`, `mst_role_id`) VALUES ('48', 'f67bca59-7a8d-4d20-bc8b-c972fe007915', '**********', '<EMAIL>', 'appowner', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-08-01 17:45:43', '2020-08-03 15:17:00', '1', '0', '3', '2');
INSERT INTO `user_attributes` (`id`, `user_identifier`, `contact_number`, `email_address`, `username`, `user_details_id`, `created_time`, `updated_time`, `status`, `is_timezone_mychoice`, `mst_access_profile_id`, `mst_role_id`) VALUES ('49', '9feea7db-c6eb-4071-828a-ee9f40016dff', '**********', '<EMAIL>', 'usermanager', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-08-01 17:47:04', '2020-08-01 17:47:04', '1', '0', '4', '3');
INSERT INTO `user_attributes` (`id`, `user_identifier`, `contact_number`, `email_address`, `username`, `user_details_id`, `created_time`, `updated_time`, `status`, `is_timezone_mychoice`, `mst_access_profile_id`, `mst_role_id`) VALUES ('50', 'e8c4c018-4eb4-43da-b5b0-dc8409bacbe1', '**********', '<EMAIL>', 'appuser', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-08-01 17:47:38', '2020-08-01 17:47:38', '1', '0', '5', '4');
INSERT INTO `user_attributes` (`id`, `user_identifier`, `contact_number`, `email_address`, `username`, `user_details_id`, `created_time`, `updated_time`, `status`, `is_timezone_mychoice`, `mst_access_profile_id`, `mst_role_id`) VALUES ('55', 'f635c526-d84c-476d-bd7e-dec7182565ce', '**********', '<EMAIL>', 'Testing', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-08-03 18:32:28', '2020-08-04 11:46:33', '1', '0', '3', '2');
INSERT INTO `user_attributes` (`id`, `user_identifier`, `contact_number`, `email_address`, `username`, `user_details_id`, `created_time`, `updated_time`, `status`, `is_timezone_mychoice`, `mst_access_profile_id`, `mst_role_id`) VALUES ('56', '46ff60d2-f072-416a-8631-6069fa1fb6c3', '**********', '<EMAIL>', 'Test', '9feea7db-c6eb-4071-828a-ee9f40016dff', '2020-08-04 13:34:54', '2020-08-04 13:36:22', '0', '0', '4', '3');

INSERT INTO user_access_details (id, user_identifier, access_details,created_time,updated_time,user_details_id) VALUES (1, '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '{"accounts":["*"], "accountMapping": {}}','2020-07-05 00:00:00','2020-07-05 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO `user_access_details` (`id`, `user_identifier`, `access_details`, `user_details_id`, `created_time`, `updated_time`) VALUES ('46', '11f40006-ccb6-4fab-8fc9-c296f317d175', '{"accounts": ["d681ef13-d690-4917-jkhg-6c79b-1", "j681ef13-d690-4917-jkhg-6c79b-1"], "accountMapping": {"d681ef13-d690-4917-jkhg-6c79b-1": {"applications": ["*"]}, "j681ef13-d690-4917-jkhg-6c79b-1": {"applications": ["*"]}}}', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-08-01 12:14:34', '2020-08-03 09:43:10');
INSERT INTO `user_access_details` (`id`, `user_identifier`, `access_details`, `user_details_id`, `created_time`, `updated_time`) VALUES ('47', 'f67bca59-7a8d-4d20-bc8b-c972fe007915', '{"accounts": ["d681ef13-d690-4917-jkhg-6c79b-1"], "accountMapping": {"d681ef13-d690-4917-jkhg-6c79b-1": {"applications": ["los_2", "netbanking_1", "microbanking_1"]}}}', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-08-01 12:15:42', '2020-08-03 09:46:59');
INSERT INTO `user_access_details` (`id`, `user_identifier`, `access_details`, `user_details_id`, `created_time`, `updated_time`) VALUES ('48', '9feea7db-c6eb-4071-828a-ee9f40016dff', '{"accounts": ["*"], "accountMapping": {}}', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-08-01 12:17:03', '2020-08-01 12:17:03');
INSERT INTO `user_access_details` (`id`, `user_identifier`, `access_details`, `user_details_id`, `created_time`, `updated_time`) VALUES ('49', 'e8c4c018-4eb4-43da-b5b0-dc8409bacbe1', '{"accounts": ["d681ef13-d690-4917-jkhg-6c79b-1"], "accountMapping": {"d681ef13-d690-4917-jkhg-6c79b-1": {"applications": ["netbanking_1", "los_2", "microbanking_1", "enet_3", "3f2d43b8-2676-485b-b401-4dc5c2081619"]}}}', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-08-01 12:17:38', '2020-08-01 12:17:38');
INSERT INTO `user_access_details` (`id`, `user_identifier`, `access_details`, `user_details_id`, `created_time`, `updated_time`) VALUES ('54', 'f635c526-d84c-476d-bd7e-dec7182565ce', '{"accounts": ["d681ef13-d690-4917-jkhg-6c79b-1", "c681ef13-d690-4917-jkhg-6c79b-1", "j681ef13-d690-4917-jkhg-6c79b-1"], "accountMapping": {"c681ef13-d690-4917-jkhg-6c79b-1": {"applications": ["UPI", "OfBiz"]}, "d681ef13-d690-4917-jkhg-6c79b-1": {"applications": ["microbanking_1", "los_2"]}, "j681ef13-d690-4917-jkhg-6c79b-1": {"applications": ["Audit-Trigger-191-Test"]}}}', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-08-03 13:02:27', '2020-08-04 06:16:32');
INSERT INTO `user_access_details` (`id`, `user_identifier`, `access_details`, `user_details_id`, `created_time`, `updated_time`) VALUES ('55', '46ff60d2-f072-416a-8631-6069fa1fb6c3', '{"accounts": ["*"], "accountMapping": {}}', '9feea7db-c6eb-4071-828a-ee9f40016dff', '2020-08-04 08:04:53', '2020-08-04 08:06:21');

INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (1,'Agent','Agent type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (2,'Application','Application type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (3,'Attribute_Type','Attributes field type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (4,'Availability_DataType','Availability KPI datatype','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (5,'Cluster_Operation','Cluster operations','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (6,'Communication_Endpoint','Communication endpoints of the data server','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (7,'Core_DataType','Core KPI datatype','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (8,'Forensic_DataType','Forensic KPI datatype','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (9,'HTTPMethod','HTTP Method types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (10,'KPI','KPI type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (11,'IntegerKpiUnits','Integer type KPI units','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (12,'FloatKpiUnits','Float type KPI units','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (13,'TextKpiUnits','Text type KPI units','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (14,'Transaction','Transaction types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (15,'Transaction_Attributes','Transaction attribute types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (16,'Transaction_Respone_Type','Transaction response time types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (17,'Object_Type','Object types for tags','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (18,'Apache Protocol','Types of protocol for Apache HTTPD','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (19,'Connect With','Oracle connectivity type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (20,'JDBC_Parameter_Type','JDBC Parameter Type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (21,'SCRIPT_Parameter_Type','SSH Parameter Type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (22,'WMI_Parameter_Type','Shell Parameter Type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (23,'JMX_Parameter_Type','JMX Parameter Type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (24,'ConfigWatch_DataType','Config watch KPI datatype','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (25,'FileWatch_DataType','File watch KPI datatype','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (26,'QueryResult','Result type of JDBC queries','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (27,'PSAgentOperationMode','Operation modes for PSAgent','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (28,'ComponentAgentOperationMode','Operation modes for Component Agent','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (29,'ResponseOptions','Options for PSAgent response data','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (30,'ServerDetailProtocols','PSAgent supported protocols for data capture','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (31,'HTTPProxyProtocol','PSAgent proxy protocols','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (32,'HTTPDataParts','Transaction data parts for HTTP','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (33,'TCPDataParts','Transaction data parts for TCP','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (34,'QueryParam','Extractors for query parameters','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (35,'Header','Extractors for headers','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (36,'HTTPRequestBody','Extractors for httprequest body','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (37,'HTTPResponseBody','Extractors for httpresponse body','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (38,'TCPRequestBody','Extractors for tcprequest body','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (39,'TCPResponseBody','Extractors for tcpresponse body','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (40,'UIDashboardTypes','Various dashboard types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (41,'UIPodOperation','Operation for UI Pods','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (42,'MethodType','Method type for JIM transactions','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (43,'TransformationType','Transformation type for JIM transactions','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (44,'ResponseTimeType','Transaction response time type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (45,'CommonPlaceholders','Placeholders for sms/email notification','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (46,'DayOptions','Option for all days in a week','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (47,'Days','All days in a week','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (48,'Operations','Operations type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (49,'SMTP Security','SMTP Security','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (50,'TransactionStatus','Status of a transaction','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (51,'TransactionKPITypes','Various thresholds type for a transaction','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (52,'SMSGatewayProtocols','SMS Gateway Protocols','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (53,'HTTPSMSRequestMethods','HTTP SMS Request Parameter Types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (54,'SMSParameterTypes','SMS Parameter Types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (57,'SMSPlaceHolders','SMS Placeholders','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (58,'CorePlaceHolders','Placeholder for core kpi type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (59,'AvailabilityPlaceHolders','Placeholder for availability kpi type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (60,'FileWatchPlaceHolders','Placeholder for file watch kpi type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (61,'ConfigWatchPlaceHolders','Placeholder for config watch kpi type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (62,'TransactionPlaceHolders','Placeholder for transactions','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (63,'UserRegistrationType','User registration type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (64,'UserRegistrationTypePlaceHolders','Placeholder for user registration type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (65,'UserLockStatus','User lock status type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (66,'JMXAttributeDataType','JMX attribute type for different set of attribute values','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (67,'SecuredType','Security type for JPPF, value should be either TRUE or FALSE','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (68,'JPPFType','JPPF type for server and node','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (69,'TagType','Data details of tag types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (70,'ControllerType','Controller types based on installation','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (71,'AggregationType','Group KPI collation based on types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (72,'XPTFlowDropDown','KPIs for XPT flow level','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (73,'XPTStepDropDown','KPIs for XPT step level','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (74,'RuleType','Rule pattern types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (75,'PayloadType','HTTP Payload types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (76,'PairType','HTTP key value types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (77,'JIMExitType','JIM exit types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (78,'SupervisorType','Type of supervisors','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (79,'CommandExecType','Execution method to run commands','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (80,'CommandType','Command type for execution','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (81,'StandardType','Type for distinction between custom or OOB','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (82,'CommandOutputType','Type of output of the command','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (83,'DownloadType','Type of download files','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (84,'JIMExitFramework','Type of JIM exit frameworks','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (85,'ForensicExecType','Type of execution by forensics','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (86,'AppsoneComponents','List of Appsone components','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (87,'AgentDataType','Type of agents based on load/behaviour KPI type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (88,'ThresholdType','Type of thresholds for data points','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (89,'AvailabilityOperations','Operations type for availability','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (90,'JIMAgentMode','Type of modes for JIM Agent.','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (91,'TemplateType','Type of notification templates.','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (92,'MaintenanceCmds','Type of maintenance commands.','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (93,'ForensicCmds','Type of forenisc commands.','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (94,'DiscoveryCmds','Type of discovery commands.','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (95,'CannedCmds','Type of canned commands.','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (96,'ConfigurationCmds','Type of configuration commands.','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (97,'HealthCmds','Type of health commands.','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (98,'AgentOperationCmds','Type of agent operation commands.','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (99,'RollUpOperation','RollUp operations','2020-01-06 00:00:00','2020-01-06 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (100,'HealActionCmds','Type of HEAL action commands.','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (101,'Actions','Action Scripts','2020-03-18 00:00:00','2020-03-18 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (102,'NotificationType','Type of Notification','2020-05-11 00:00:00','2020-05-11 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (103,'SignalSeverity','Signal Severity Type','2020-05-11 00:00:00','2020-05-11 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (104,'SignalType','Signal Type','2020-05-11 00:00:00','2020-05-11 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (105,'MaintenanceType','Maintenance Type','2020-05-21 00:00:00','2020-05-21 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (106,'RecurringType','Recurring types for maintenance details','2020-07-05 00:00:00','2020-07-05 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);

INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (1,'ComponentAgent',1,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Component Agent is the agent to collect the KPI data for Components',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (2,'JIMAgent',1,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Java Intrusive Monitoring Agent is the agent to collect the KPI data for the Java Applications',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (3,'PSAgent',1,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Protocol Stack Agent is the agent to collect the KPI data for the Transactions',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (4,'NoOpAgent',1,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Dummy agent for no use',0,0);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (5,'SyntheticAgent',1,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agent for synthetic monitoring',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (6,'DotNet',2,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Dotnet based Application',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (7,'Finacle10',2,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Finacle_10 based Applications',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (8,'Finacle7',2,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Finacle_7 based Application',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (9,'Flexcube',2,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Flexcube based Application',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (10,'Java',2,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Java based Application',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (11,'WebServices',2,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Web services based Application',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (12,'TextBox',3,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Attribute field type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (13,'DropDown',3,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Attribute field type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (14,'CheckBox',3,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Attribute field type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (15,'Password',3,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Attribute field type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (16,'Integer',4,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Availability datatype',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (17,'Sum',5,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Sum the KPI values for Cluster Operation',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (18,'Average',5,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Average the KPI values for the Cluster Operation',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (19,'None',5,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Do nothing on the KPI values for the Cluster Operation',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (20,'GRPC',6,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GRPC will be used for the Communication Endpoint',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (21,'Float',7,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Float datatype for Core KPIs with Decimal values',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (22,'Integer',7,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Integer datatype for Core KPIs with Numeric values',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (23,'Text',7,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Text datatype for Core KPIs with Alphanumeric values',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (24,'Text',8,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Text datatype for Forensic KPIs with Alphanumeric values',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (25,'GET',9,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP GET Method type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (26,'POST',9,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP POST Method type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (27,'UPDATE',9,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP UPDATE Method type',0,0);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (28,'DELETE',9,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP DELETE Method type',0,0);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (29,'Availability',10,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI Type to collect the availability data',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (30,'Core',10,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI Type to collect the core data',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (31,'Forensic',10,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI Type to collect the forensic data',0,0);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (32,'ConfigWatch',10,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI Type to collect the configuration data',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (33,'FileWatch',10,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI Type to collect the data for file changes',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (34,'Bytes',11,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (35,'Count',11,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (36,'Gigabytes',11,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (37,'Kilobytes',11,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (38,'Megabytes',11,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (39,'Microseconds',11,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (40,'Milliseconds',11,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (41,'Minutes',11,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (42,'Seconds',11,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (43,'Percentage',12,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (44,'Text',13,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (45,'TCP',14,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'TCP Transaction type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (46,'HTTP',14,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP Transaction type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (47,'Body',15,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP Body Patterns',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (48,'Header',15,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP Header Patterns',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (49,'QueryParams',15,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP Query Parameters',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (50,'TCPData',15,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'TCP Data Patterns',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (51,'DC',16,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'DC Response Type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (52,'EUE',16,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'EUE Response Type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (53,'RENDER',16,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'RENDER Response Type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (54,'Application',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Application tag type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (55,'Component',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Components tag type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (56,'ComponentInstance',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Component Instances tag type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (57,'KPI',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPIs tag type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (58,'Cluster',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Clusters tag type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (59,'Producer',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Procuers tag type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (60,'Transaction',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Transactions tag type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (61,'Agents',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agents tag type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (62,'http',18,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP will be used for collecting Apache HTTP KPIs',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (63,'https',18,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTPS will be used for collecting Apache HTTP KPIs',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (64,'ServiceName',19,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'ServiceName will be used for collecting Oracle KPIs',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (65,'SID',19,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'SID will be used for collecting Oracle KPIs',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (66,'KEY_VALUE',20,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Key value pair argument for JDBC',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (67,'COMMANDLINE',21,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Command line argument for SSH',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (68,'STANDARDINPUT',21,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Standard input argument for SSH',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (69,'COMMANDLINE',22,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Command line argument for Shell',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (70,'KEY_VALUE',23,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Key value pair argument for JMX',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (71,'Float',24,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Float datatype for Config KPIs with Decimal values',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (72,'Integer',24,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Integer datatype for Config KPIs with Numeric values',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (73,'Text',24,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Text datatype for Config watch KPIs with Alphanumeric values',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (74,'Text',25,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Text datatype for File watch KPIs with Alphanumeric values',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (75,'NAMEVALUE',26,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Query result type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (76,'RESULTSET',26,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Query result type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (77,'Remote',27,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Online operation mode for PSAgent',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (78,'Local',27,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Offline operation mode for PSAgent',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (79,'Remote',28,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Online operation mode for Component Agent',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (80,'Local',28,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Offline operation mode for Component Agent',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (81,'ResponseHeader',29,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Response header option',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (82,'ResponseBody',29,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Response body option',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (83,'StatusLine',29,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'No response data',0,0);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (84,'HTTP',30,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Protocol for HTTP data',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (85,'HTTPS',30,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Protocol for HTTPS data',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (86,'TCP-DIR',30,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Protocol for TCP data',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (87,'FINICORE',30,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Protocol for Finacle data',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (88,'HTTP',31,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'PSAgent proxy protocol',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (89,'QueryParam',32,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP Query parameters',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (90,'Header',32,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP headers',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (91,'RequestBody',32,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP request body',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (92,'ResponseBody',32,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP response body',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (93,'RequestBody',33,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'TCP request body',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (94,'ResponseBody',33,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'TCP response body',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (95,'Query Param Extractor',34,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Query param extractor',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (96,'Grouped Regex Extractor',35,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Header extractor',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (97,'Grouped Regex Extractor',36,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP request body regex extractor',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (98,'Form Data Extractor',36,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP request body form extractor',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (99,'Grouped Regex Extractor',37,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP response body regex extractor',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (100,'Grouped Regex Extractor',38,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'TCP request body regex extractor',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (101,'Position And Length Extractor',38,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'TCP request body position length extractor',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (102,'Grouped Regex Extractor',39,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'TCP response body regex extractor',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (103,'Position And Length Extractor',39,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'TCP response body position length extractor',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (104,'Application Dashboard',40,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Dashboard for applications',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (105,'NOC Dashboard',40,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Dashboard for NOC',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (106,'JIM Dashboard',40,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Dashboard for JIM',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (107,'Synthetic Monitoring',40,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Dashboard for Synthetic Monitoring',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (108,'BVE',40,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Dashboard for BVE',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (109,'Default',41,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Default pod operation',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (110,'Custom',41,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Custom pod operation',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (111,'ENTRY',42,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Entry type method',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (112,'EXIT',42,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Exit type method',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (113,'GENERIC',43,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Generic transformation type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (114,'SERVLET',43,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Servlet transformation type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (115,'SQL',43,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Query transformation type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (116,'Transaction',10,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Transaction KPI Type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (117,'SeverityProfile',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Severity alert profile type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (118,'NotificationContentProfile',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Notification alert profile type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (119,'EscalationProfile',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Esacalation alert profile type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (120,'AlertProfile',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Alert profile',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (121,'DC',44,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'DC transaction time type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (122,'EUM',44,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'EUM transaction time type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (123,'BOTH',44,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Both transaction time type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (124,'{Account}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Account',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (125,'{Application}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Application',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (126,'{Severity}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Severity',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (127,'{ComponentInstance}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Component Instance',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (128,'{Component}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Component',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (129,'{ComponentType}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Component Type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (130,'{KPI}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for KPI',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (131,'{KPIType}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for KPI Type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (132,'{ActualValue}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Actual Value',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (133,'{ThresholdValue}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Threshold Value',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (134,'{Time}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Time',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (135,'{AlertID}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Alert Id',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (136,'{EscalationLevel}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Escalation Level',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (137,'{ViolationDetails}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Violation Details',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (138,'{KPI Group}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for KPI Group',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (139,'Daily',46,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'All days in a week',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (140,'Days',46,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Day in a week',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (141,'Monday',47,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'1st day of the week',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (142,'Tuesday',47,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'2nd day of the week',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (143,'Wednesday',47,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'3rd day of the week',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (144,'Thursday',47,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'4th day of the week',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (145,'Friday',47,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'5th day of the week',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (146,'Saturday',47,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'6th day of the week',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (147,'Sunday',47,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'7th day of the week',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (148,'TimeProfile',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Tags for coverage window',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (149,'greater than',48,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Greater than operation',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (150,'lesser than',48,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Less than operation',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (151,'NONE',49,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'SMTP security as NONE',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (152,'SSL',49,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'SMTP security as SSL',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (153,'TLS',49,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'SMTP security as TLS',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (154,'Good',50,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Good status of a transaction',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (155,'Slow',50,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Slow status of a transaction',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (156,'Fail',50,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Fail status of a transaction',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (157,'Unknown',50,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Unknown status of a transaction',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (158,'Timedout',50,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Timedout status of a transaction',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (159,'Slow Percentage',51,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Total volume threshold for transactions',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (160,'Fail Percentage',51,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Transaction status threshold for transactions',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (161,'HTTP',52,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP SMS Gateway Protocol',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (162,'TCP',52,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'TCP SMS Gateway Protocol',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (163,'GET',53,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP GET SMS Request Method',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (164,'POST',53,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP POST SMS Request Method',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (165,'QueryParameter',54,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Query parameter for SMS Request',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (166,'RequestParameter',54,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Request parameter for SMS Request',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (167,'TcpParameter',54,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'TCP parameter for SMS Request',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (168,'{MobileNumber}',57,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Mobile number placeholder for SMS',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (169,'{Message}',57,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'SMS content placeholder for SMS',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (170,'UserRegistration',63,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'User registration',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (171,'UserResetPassword',63,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Password reset for users',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (172,'{Username}',64,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for username',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (173,'{Password}',64,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for user password',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (174,'{Account}',64,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for user account',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (175,'Locked',65,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'User account is locked',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (176,'Unlocked',65,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'User account is unlocked',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (177,'GrpcSettings',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Grpc Settings',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (178,'KPIGroup',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI Group',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (179,'HolidayProfiles',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Holiday Profiles',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (180,'HTTPS',52,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTPS SMS Gateway Protocol',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (181,'DirectValue',66,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'DirectValue type for JMX attributes',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (182,'CompositeValue',66,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'CompositeValue type for JMX attributes',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (183,'Yes',67,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Security type TRUE for JPPF security enable',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (184,'No',67,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Security type FALSE for JPPF security enable',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (185,'Server',68,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'JPPF type is server',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (186,'Node',68,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'JPPF type is node',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (187,'ENV_PARAM',22,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Standard input argument for Shell',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (188,'LogForwarder',1,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Log forwarder agent',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (189,'KeyValue',69,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KeyValue tag type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (190,'String',69,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Plain text tag type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (191,'Application',70,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Application controller type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (192,'Services',70,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Service controller type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (193,'not between',48,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Less than minimum value and greater than maximum value',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (194,'Total Volume',51,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Total volume of transactions',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (195,'SingleValue',71,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Single value aggregation',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (196,'MultiValue',71,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Multi value aggregation',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (197,'None',71,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'No aggregation',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (198,'Transacted Value',72,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Transacted KPI',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (199,'Conversion',72,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Conversion KPI',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (200,'Business Error',72,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Business error KPI',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (201,'Arrival Rate',72,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Arrival rate KPI',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (202,'Response Time (ms)',73,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Response time KPI',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (203,'Pass throughs (%)',73,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Passthrough KPI',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (204,'OPTIONS',9,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP OPTIONS Method type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (205,'PUT',9,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP PUT Method type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (206,'Regex',74,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Regex rule type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (207,'Request Data',74,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Request data rule type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (208,'Form Data',75,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Form data http payload type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (209,'XML Data',75,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'XML data http payload type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (210,'JSON Data',75,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'JSON data http payload type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (211,'Cookie',76,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Cookie key value pair type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (212,'Query Parameters',76,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Query parameter key value pair type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (213,'HTTP Header',76,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP Header key value pair type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (214,'PayloadType',76,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Payload content type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (215,'JDBC',77,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'JDBC exit type for JIM',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (216,'HTTP',77,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP exit type for JIM',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (217,'Queue',77,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Queue exit type for JIM',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (218,'ForensicAgent',1,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agent for foresic actions',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (219,'WindowsSupervisor',78,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Windows based supervisor',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (220,'UnixSupervisor',78,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Unix based supervisor',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (221,'RestClient',85,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'REST client based command execution',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (222,'Script',85,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Script based command execution',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (223,'LongPolling',79,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Long polling based command execution',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (224,'Install',92,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Command to perform installation',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (225,'Upgrade',92,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Command to perform upgradeation',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (226,'UnInstall',92,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Command to perform uninstall',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (227,'Running',98,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Command to perform stop',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (228,'Stopped',98,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Command to perform start',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (229,'Restart',98,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Command to perform restart',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (230,'Execute',93,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Command to perform execute',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (231,'Custom',81,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Type to identify custom component',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (232,'OOB',81,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Type to identify OOB component',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (233,'Blob',82,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Output contents in blob',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (234,'File',82,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Output contents in a file',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (235,'PDF',83,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'PDF download type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (236,'CSV',83,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'CSV download type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (237,'JDBC',84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Oracle JIM exit framework',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (240,'JAX_WS_CLIENT',84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'JAX WS client JIM exit framework',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (241,'IBM_MQ',84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'IBM MQ JIM exit framework',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (242,'APACHE_HTTP_CLIENT1',84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Apache HTTP client JIM exit framework',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (243,'APACHE_HTTP_CLIENT2',84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Apache HTTP client JIM exit framework',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (244,'AXIS2_CLIENT',84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'AXIS2 client JIM exit framework',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (245,'JDBC_CONNECTION',84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'MSSQL JIM exit framework',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (246,'JMS',84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'JMS JIM exit framework',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (247,'Supervisor',86,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Appsone supervisor',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (248,'SupervisorController',86,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Appsone supervisor controller',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (249,'Workload',87,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agents for load KPIs',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (250,'Behaviour',87,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agents for behaviour KPIs',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (251,'WorkNBehaviour',87,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agent for both load and behaviour KPIs',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (252,'Others',87,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agent for other than load and behaviour KPIs',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (253,'Baseline',88,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Baseline threshold to be discovered by MLE',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (254,'Static',88,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Static threshold to be defined by users',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (255,'in between',48,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'In Between operation',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (256,'not equals',89,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Value is not equals.',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (257,'Auto',96,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agent will be running as Auto mode.',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (258,'Verbose',96,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agent will be running in Verbose mode.',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (259,'SwitchOff',96,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agent will be running in SwitchOff mode.',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (260,'Problem',91,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agent will be running as Auto mode.',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (261,'Early Warning',91,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agent will be running in Verbose mode.',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (262,'Anomaly',91,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agent will be running in SwitchOff mode.',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (263,'Realtime',88,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Realtime threshold to be discovered by MLE',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (264,'EJB_WEBLOGIC_REMOTE',84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'EJB Weblogic exit framework',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (265,'Weblogic_EJB',77,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'EJB Weblogic exit type for JIM',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (266,'EJB Data',74,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'EJB rule type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (267,'MYSQL_STATEMENT',84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'MySQL Statement JIM exit framework',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (268,'MYSQL_PREPARED_STATEMENT',84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'MySQL Prepared Statement JIM exit framework',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (269,'ORACLE',84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Oracle JIM exit framework',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (270,'MSSQL',84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'MSSQL JIM exit framework',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (271,'NEW-JDBC',77,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'JDBC exit type for JIM',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (272,'AgentOperations',80,'2019-12-10 00:00:00','2019-12-10 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agent operations type for command type.',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (273,'Maintenance',80,'2019-12-10 00:00:00','2019-12-10 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Maintenance type for command type.',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (274,'Forensic',80,'2019-12-10 00:00:00','2019-12-10 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Forensic type for command type.',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (275,'Discovery',80,'2019-12-10 00:00:00','2019-12-10 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Discovery type for command type.',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (276,'CannedCommands',80,'2019-12-10 00:00:00','2019-12-10 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Canned type for command type.',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (277,'Configuration',80,'2019-12-10 00:00:00','2019-12-10 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Configuration type for command type.',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (278,'Health',80,'2019-12-10 00:00:00','2019-12-10 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Health type for command type.',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (279,'SelfHeal',98,'2019-12-10 00:00:00','2019-12-10 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'SelfHeal for command type.',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (280,'Sum',99,'2020-01-06 00:00:00','2020-01-06 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Sum the KPI values for rollup Operation',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (281,'Average',99,'2020-01-06 00:00:00','2020-01-06 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Average the KPI values for the rollup Operation',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (282,'None',99,'2020-01-06 00:00:00','2020-01-06 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Do nothing on the KPI values for the rollup Operation',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (283,'Max',99,'2020-01-06 00:00:00','2020-01-06 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Max of KPI values for the rollup Operation',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (284,'Last',99,'2020-01-06 00:00:00','2020-01-06 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Last fo KPI values for the rollup Operation',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (285,'COMMAND_OPTIONS',21,'2020-01-24 00:00:00','2020-01-24 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Standard input argument for SSH',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (286,'COMMAND_OPTIONS',22,'2020-01-24 00:00:00','2020-01-24 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Command line argument for Shell',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (287,'Custom',1,'2020-01-06 00:00:00','2020-01-06 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agent for custom',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (288,'Execute',100,'2020-01-06 00:00:00','2020-01-06 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Command types for heal actions.',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (289,'Forensic Action',101,'2020-03-18 00:00:00','2020-03-18 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Forensic Action detail',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (290,'Heal Action',101,'2020-03-18 00:00:00','2020-03-18 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Heal Action detail',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (291,'Immediately',102,'2020-05-11 00:00:00','2020-05-11 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Notification Type details.',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (292,'Open for long',102,'2020-05-11 00:00:00','2020-05-11 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Notification Type details.',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (293,'Open for too long',102,'2020-05-11 00:00:00','2020-05-11 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Notification Type details.',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (294,'Off',102,'2020-05-11 00:00:00','2020-05-11 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Notification Type details.',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (295,'Severe',103,'2020-05-11 00:00:00','2020-05-11 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Signal Severity Type details.',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (296,'Default',103,'2020-05-11 00:00:00','2020-05-11 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Signal Severity Type details.',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (297,'Problem',104,'2020-05-11 00:00:00','2020-05-11 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Signal Type details.',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (298,'Early Warning',104,'2020-05-11 00:00:00','2020-05-11 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Signal Type details.',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (299,'Immediate',105,'2020-05-21 00:00:00','2020-05-21 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Maintenance Type details.',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (300,'Scheduled',105,'2020-05-21 00:00:00','2020-05-21 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Maintenance Type details.',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (301,'Recurring',105,'2020-05-21 00:00:00','2020-05-21 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Maintenance Type details.',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (302,'Workload',1,'2020-05-20 00:00:00','2020-05-20 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Workload agent',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (303,'Daily',106,'2020-07-05 00:00:00','2020-07-05 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Daily option in recurring type.',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (304,'Weekly',106,'2020-07-05 00:00:00','2020-07-05 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Weekly option in recurring type.',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (305,'Monthly',106,'2020-07-05 00:00:00','2020-07-05 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Monthly option in recurring type.',0,1);

INSERT INTO `tag_details` (`id`,`name`,`tag_type_id`,`is_predefined`,`ref_table`,`created_time`,`updated_time`,`account_id`,`user_details_id`,`ref_where_column_name`,`ref_select_column_name`) VALUES (1,'Controller',190,1,'controller','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1','identifier = :name and account_id = :accountId','id tagKey, identifier tagValue');

INSERT INTO `tag_mapping` (`id`,`tag_id`,`object_id`,`object_ref_table`,`tag_key`,`tag_value`,`created_time`,`updated_time`,`account_id`,`user_details_id`) VALUES (464,1,1,'controller','2','NB-Web-Service','2020-08-21 05:37:33','2020-08-21 05:37:33',2,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO `tag_mapping` (`id`,`tag_id`,`object_id`,`object_ref_table`,`tag_key`,`tag_value`,`created_time`,`updated_time`,`account_id`,`user_details_id`) VALUES (470,1,1,'controller','3','NB-User','2020-08-21 05:37:33','2020-08-21 05:37:33',2,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO `tag_mapping` (`id`,`tag_id`,`object_id`,`object_ref_table`,`tag_key`,`tag_value`,`created_time`,`updated_time`,`account_id`,`user_details_id`) VALUES (476,1,1,'controller','4','NB-DB-Service','2020-08-21 05:37:33','2020-08-21 05:37:33',2,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO `tag_mapping` (`id`,`tag_id`,`object_id`,`object_ref_table`,`tag_key`,`tag_value`,`created_time`,`updated_time`,`account_id`,`user_details_id`) VALUES (594,1,1,'comp_instance','2','NB-Web-Service','2020-07-09 05:54:02','2020-07-09 05:54:02',2,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO `tag_mapping` (`id`,`tag_id`,`object_id`,`object_ref_table`,`tag_key`,`tag_value`,`created_time`,`updated_time`,`account_id`,`user_details_id`) VALUES (595,1,1,'comp_instance','1','netbanking_1','2020-07-09 05:54:02','2020-07-09 05:54:02',2,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');

INSERT INTO `controller` (`id`,`name`,`identifier`,`account_id`,`user_details_id`,`created_time`,`updated_time`,`controller_type_id`,`monitor_enabled`,`status`) VALUES (1,'NetBanking','netbanking_1',2,'7640123a-fbde-4fe5-9812-581cd1e3a9c1','2020-08-21 05:37:32','2020-08-21 05:37:32',191,1,1);
INSERT INTO `controller` (`id`,`name`,`identifier`,`account_id`,`user_details_id`,`created_time`,`updated_time`,`controller_type_id`,`monitor_enabled`,`status`) VALUES (2,'NB-Web-Service','NB-Web-Service',2,'7640123a-fbde-4fe5-9812-581cd1e3a9c1','2020-07-09 05:53:44','2020-07-09 05:53:44',192,1,1);

INSERT INTO `mst_component_type` (`id`,`name`,`description`,`is_custom`,`status`,`created_time`,`updated_time`,`user_details_id`,`account_id`) VALUES (1,'Host','Host Server - Standard Component',0,1,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO `mst_component_type` (`id`,`name`,`description`,`is_custom`,`status`,`created_time`,`updated_time`,`user_details_id`,`account_id`) VALUES (2,'Web Server','Web Server - Standard Component',0,1,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO `mst_component_type` (`id`,`name`,`description`,`is_custom`,`status`,`created_time`,`updated_time`,`user_details_id`,`account_id`) VALUES (3,'Application Server','Application Server - Standard Component',0,1,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO `mst_component_type` (`id`,`name`,`description`,`is_custom`,`status`,`created_time`,`updated_time`,`user_details_id`,`account_id`) VALUES (4,'Database Server','Database Server - Standard Component',0,1,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO `mst_component_type` (`id`,`name`,`description`,`is_custom`,`status`,`created_time`,`updated_time`,`user_details_id`,`account_id`) VALUES (5,'Services','Services',0,1,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO `mst_component_type` (`id`,`name`,`description`,`is_custom`,`status`,`created_time`,`updated_time`,`user_details_id`,`account_id`) VALUES (6,'Workload','Workload type for transactions',0,1,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO `mst_component_type` (`id`,`name`,`description`,`is_custom`,`status`,`created_time`,`updated_time`,`user_details_id`,`account_id`) VALUES (7,'Message Queue','Message Queue',0,1,'2020-03-18 00:00:00','2020-03-18 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO `mst_component_type` (`id`,`name`,`description`,`is_custom`,`status`,`created_time`,`updated_time`,`user_details_id`,`account_id`) VALUES (8,'Pod','Pod',0,1,'2020-04-27 00:00:00','2020-04-27 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
