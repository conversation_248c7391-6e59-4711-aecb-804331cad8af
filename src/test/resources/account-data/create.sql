-- -----------------------------------------------------
-- Schema appsone
-- -----------------------------------------------------
-- CREATE SCHEMA IF NOT EXISTS `appsone` DEFAULT CHARACTER SET utf8 ;
drop SCHEMA IF EXISTS `appsone` ;
create SCHEMA `appsone`;
USE `appsone` ;

-- -----------------------------------------------------
-- DROP TABLES
-- -----------------------------------------------------
drop view if exists view_types;
drop view if exists view_application_service_mapping;
drop view if exists view_component_instance;
drop view if exists view_cluster_services;
drop table IF EXISTS `appsone`.`account` ;
drop table IF EXISTS `appsone`.`mst_roles` ;
drop table IF EXISTS `appsone`.`mst_access_profiles` ;
drop table IF EXISTS `appsone`.`user_attributes` ;
drop table IF EXISTS `appsone`.`user_access_details` ;
drop table IF EXISTS `appsone`.`mst_type`;
drop table IF EXISTS `appsone`.`mst_sub_type`;
drop table IF EXISTS `appsone`.`tag_details` ;
drop table IF EXISTS `appsone`.`tag_mapping` ;
drop table IF EXISTS `appsone`.`controller` ;
drop table IF EXISTS `appsone`.`comp_instance` ;
drop table IF EXISTS `appsone`.`component_cluster_mapping` ;
drop table IF EXISTS `appsone`.`mst_component_mapping` ;
drop table IF EXISTS `appsone`.`mst_common_version` ;
drop table IF EXISTS `appsone`.`mst_kpi_details` ;
drop table IF EXISTS `appsone`.`mst_producer_type` ;
drop table IF EXISTS `appsone`.`mst_producer` ;
drop table IF EXISTS `appsone`.`comp_instance_kpi_group_details` ;
drop table IF EXISTS `appsone`.`mst_kpi_group` ;
DROP TABLE IF EXISTS `appsone`.`mst_timezone` ;

-- -----------------------------------------------------
-- Table `appsone`.``account`.`
-- -----------------------------------------------------

create TABLE `appsone`.`account` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(128) NOT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `status` tinyint NOT NULL,
  `private_key` text,
  `public_key` text,
  `user_details_id` varchar(256) NOT NULL,
  `identifier` varchar(128) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `identifier_UNIQUE` (`identifier`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.``mst_roles`.`
-- -----------------------------------------------------

create TABLE `appsone`.`mst_roles` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(32) NOT NULL,
  `description` VARCHAR(256) NULL,
  `status` TINYINT NOT NULL,
  `ui_visible` TINYINT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `name_UNIQUE` (`name` ASC));

-- -----------------------------------------------------
-- Table `appsone`.``mst_access_profiles`.`
-- -----------------------------------------------------

create TABLE `appsone`.`mst_access_profiles` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(64) NOT NULL,
  `mst_role_id` INT NOT NULL,
  `account_id` INT NOT NULL,
  `is_custom` TINYINT NOT NULL,
  `status` TINYINT NOT NULL,
  `ui_visible` TINYINT NOT NULL DEFAULT 1,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  INDEX `fk_mst_access_profiles_1_idx` (`mst_role_id` ASC),
  CONSTRAINT `fk_mst_access_profiles_1`
    FOREIGN KEY (`mst_role_id`)
    REFERENCES `mst_roles` (`id`)
    ON delete NO ACTION
    ON update NO ACTION);

-- -----------------------------------------------------
-- Table `appsone`.``user_attributes`.`
-- -----------------------------------------------------

create TABLE `appsone`.`user_attributes` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `user_identifier` VARCHAR(256) NOT NULL,
  `contact_number` VARCHAR(64) NULL,
  `email_address` VARCHAR(256) NULL,
  `username` VARCHAR(256) NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `status` tinyint NOT NULL,
  `is_timezone_mychoice` tinyint NOT NULL DEFAULT 0,
  `mst_access_profile_id` INT NOT NULL,
  `mst_role_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  INDEX `fk_user_attributes_1_idx` (`mst_access_profile_id` ASC),
  CONSTRAINT `fk_user_attributes_1`
    FOREIGN KEY (`mst_access_profile_id`)
    REFERENCES `mst_access_profiles` (`id`)
    ON delete NO ACTION
    ON update NO ACTION);

-- -----------------------------------------------------
-- Table `appsone`.``user_access_details`.`
-- -----------------------------------------------------

create TABLE `appsone`.`user_access_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `user_identifier` VARCHAR(256) NOT NULL,
  `access_details` CLOB NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  PRIMARY KEY (`id`));

-- -----------------------------------------------------
-- Table `appsone`.``mst_type`.`
-- -----------------------------------------------------

create TABLE `appsone`.`mst_type` (
  `id` int NOT NULL AUTO_INCREMENT,
  `type` varchar(45) NOT NULL,
  `description` varchar(128) DEFAULT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `account_id` int NOT NULL,
  `status` tinyint NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `mst_type_account1_idx` (`account_id`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.``mst_sub_type`.`
-- -----------------------------------------------------

create TABLE `appsone`.`mst_sub_type` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(45) NOT NULL,
  `mst_type_id` int NOT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `account_id` int NOT NULL,
  `description` varchar(256) NOT NULL,
  `is_custom` tinyint NOT NULL DEFAULT '0',
  `status` tinyint NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `fk_mst_sub_type_mst_type_idx` (`mst_type_id`),
  KEY `mst_sub_type_account1_idx` (`account_id`),
  CONSTRAINT `fk_mst_sub_type_mst_type` FOREIGN KEY (`mst_type_id`) REFERENCES `mst_type` (`id`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.``view_types`.`
-- -----------------------------------------------------

create view appsone.view_types as
select t.type, t.id typeid, st.name, st.id subtypeid
from mst_type t, mst_sub_type st
where t.id = st.mst_type_id;

-- -----------------------------------------------------
-- Table `appsone`.`mst_kpi_details`
-- -----------------------------------------------------

CREATE TABLE `appsone`.`mst_kpi_group` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(32) NOT NULL,
  `description` varchar(256) NOT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `account_id` int NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `kpi_type_id` int NOT NULL,
  `discovery` tinyint DEFAULT NULL,
  `regex` varchar(64) DEFAULT NULL,
  `status` tinyint NOT NULL,
  `is_custom` tinyint NOT NULL,
  `identifier` varchar(64) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `indx_mst_kpi_group_account_id` (`account_id`),
  KEY `fk_mst_kpi_group_mst_sub_type1_idx` (`kpi_type_id`),
  CONSTRAINT `fk_mst_kpi_group_mst_sub_type1` FOREIGN KEY (`kpi_type_id`) REFERENCES `mst_sub_type` (`id`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`mst_kpi_details`
-- -----------------------------------------------------

CREATE TABLE `appsone`.`mst_kpi_details` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(128) NOT NULL,
  `description` mediumtext NOT NULL,
  `data_type` varchar(32) NOT NULL,
  `is_custom` tinyint(1) NOT NULL DEFAULT '0',
  `status` tinyint NOT NULL DEFAULT '1',
  `kpi_type_id` int NOT NULL,
  `measure_units` varchar(16) DEFAULT NULL,
  `cluster_operation` varchar(16) DEFAULT 'NONE',
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `account_id` int NOT NULL,
  `kpi_group_id` int NOT NULL DEFAULT '0',
  `identifier` varchar(128) NOT NULL,
  `value_type` varchar(45) DEFAULT 'SNAPSHOT',
  `rollup_operation` varchar(16) NOT NULL,
  `cluster_aggregation_type` int DEFAULT NULL,
  `instance_aggregation_type` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_mst_kpi_details_mst_sub_type1_idx` (`kpi_type_id`),
  KEY `mst_kpi_details_account1_idx` (`account_id`),
  KEY `indx_mst_kpi_details_account_id` (`kpi_group_id`),
  KEY `indx_mst_kpi_details_identifier` (`identifier`),
  CONSTRAINT `fk_mst_kpi_details_mst_sub_type1` FOREIGN KEY (`kpi_type_id`) REFERENCES `mst_sub_type` (`id`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`tag_details`
-- -----------------------------------------------------

create TABLE `appsone`.`tag_details` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(128) NOT NULL,
  `tag_type_id` int NOT NULL,
  `is_predefined` tinyint NOT NULL,
  `ref_table` varchar(64) DEFAULT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `account_id` int NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `ref_where_column_name` varchar(128) DEFAULT NULL,
  `ref_select_column_name` varchar(126) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_tag_details_tag_type_id_idx` (`tag_type_id`),
  KEY `fk_tag_details_account_id_idx` (`account_id`),
  CONSTRAINT `fk_tag_details_account_id` FOREIGN KEY (`account_id`) REFERENCES `account` (`id`),
  CONSTRAINT `fk_tag_details_tag_type_id` FOREIGN KEY (`tag_type_id`) REFERENCES `mst_sub_type` (`id`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`tag_mapping`
-- -----------------------------------------------------

create TABLE `appsone`.`tag_mapping` (
  `id` int NOT NULL AUTO_INCREMENT,
  `tag_id` int NOT NULL,
  `object_id` int NOT NULL,
  `object_ref_table` varchar(256) NOT NULL,
  `tag_key` varchar(256) NOT NULL,
  `tag_value` varchar(256) DEFAULT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `account_id` int NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_tag_mapping_account_id_idx` (`account_id`),
  KEY `fk_tag_mapping_tag_id_idx` (`tag_id`),
  CONSTRAINT `fk_tag_mapping_account_id` FOREIGN KEY (`account_id`) REFERENCES `account` (`id`),
  CONSTRAINT `fk_tag_mapping_tag_id` FOREIGN KEY (`tag_id`) REFERENCES `tag_details` (`id`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`controller`
-- -----------------------------------------------------

create TABLE `appsone`.`controller` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(128) NOT NULL,
  `identifier` varchar(128) NOT NULL,
  `account_id` int NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `controller_type_id` int NOT NULL,
  `monitor_enabled` tinyint NOT NULL DEFAULT '1',
  `status` tinyint NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `fk_application_account_id_idx` (`account_id`),
  CONSTRAINT `fk_application_account_id` FOREIGN KEY (`account_id`) REFERENCES `account` (`id`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`mst_component`
-- -----------------------------------------------------

CREATE TABLE `appsone`.`mst_component` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(64) NOT NULL,
  `is_custom` tinyint(1) DEFAULT '0',
  `status` tinyint DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `account_id` int NOT NULL DEFAULT '0',
  `description` varchar(256) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `mst_component_account1_idx` (`account_id`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`mst_component_type`
-- -----------------------------------------------------

CREATE TABLE `appsone`.`mst_component_type` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(64) NOT NULL,
  `description` varchar(256) DEFAULT NULL,
  `is_custom` tinyint(1) NOT NULL DEFAULT '0',
  `status` tinyint DEFAULT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `account_id` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `mst_component_type_account1_idx` (`account_id`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`comp_instance`
-- -----------------------------------------------------

CREATE TABLE `appsone`.`comp_instance` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(128) NOT NULL,
  `status` tinyint NOT NULL DEFAULT '1',
  `host_id` int DEFAULT NULL,
  `is_DR` tinyint DEFAULT NULL,
  `is_cluster` tinyint DEFAULT NULL,
  `mst_component_version_id` int DEFAULT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `account_id` int NOT NULL,
  `mst_component_id` int NOT NULL,
  `mst_component_type_id` int NOT NULL,
  `discovery` tinyint DEFAULT NULL,
  `host_address` varchar(256) DEFAULT NULL,
  `identifier` varchar(128) NOT NULL,
  `mst_common_version_id` int NOT NULL,
  `parent_instance_id` int DEFAULT NULL,
  `supervisor_id` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `comp_instance_account1_idx` (`account_id`),
  KEY `fk_comp_instance_mst_component1_idx` (`mst_component_id`),
  KEY `fk_comp_instance_mst_component_type1_idx` (`mst_component_type_id`),
  CONSTRAINT `fk_comp_instance_mst_component1` FOREIGN KEY (`mst_component_id`) REFERENCES `mst_component` (`id`),
  CONSTRAINT `fk_comp_instance_mst_component_type1` FOREIGN KEY (`mst_component_type_id`) REFERENCES `mst_component_type` (`id`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.``component_cluster_mapping`.`
-- -----------------------------------------------------

CREATE TABLE `appsone`.`component_cluster_mapping` (
  `id` int NOT NULL AUTO_INCREMENT,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `account_id` int NOT NULL,
  `comp_instance_id` int NOT NULL,
  `cluster_id` int NOT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_component_cluster_mapping_comp_instance1_idx` (`comp_instance_id`),
  KEY `fk_component_cluster_mapping_comp_instance2_idx` (`cluster_id`),
  CONSTRAINT `fk_component_cluster_mapping_comp_instance1` FOREIGN KEY (`comp_instance_id`) REFERENCES `comp_instance` (`id`),
  CONSTRAINT `fk_component_cluster_mapping_comp_instance2` FOREIGN KEY (`cluster_id`) REFERENCES `comp_instance` (`id`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.``mst_component_mapping`.`
-- -----------------------------------------------------

CREATE TABLE `appsone`.`mst_component_mapping` (
  `id` int NOT NULL AUTO_INCREMENT,
  `mst_component_type_id` int NOT NULL,
  `mst_component_id` int NOT NULL,
  `created_time` datetime NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `account_id` int NOT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_mst_component_mapping_mst_component_type1_idx` (`mst_component_type_id`),
  KEY `fk_mst_component_mapping_mst_component1_idx` (`mst_component_id`),
  CONSTRAINT `fk_mst_component_mapping_mst_component1` FOREIGN KEY (`mst_component_id`) REFERENCES `mst_component` (`id`),
  CONSTRAINT `fk_mst_component_mapping_mst_component_type1` FOREIGN KEY (`mst_component_type_id`) REFERENCES `mst_component_type` (`id`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.``mst_common_version`.`
-- -----------------------------------------------------

CREATE TABLE `appsone`.`mst_common_version` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(45) NOT NULL,
  `mst_component_id` int NOT NULL,
  `is_custom` tinyint(1) NOT NULL DEFAULT '0',
  `status` tinyint NOT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `account_id` int NOT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_mst_common_version_mst_component_idx` (`mst_component_id`),
  KEY `mst_common_version_account1_idx` (`account_id`),
  CONSTRAINT `fk_mst_common_version_mst_component` FOREIGN KEY (`mst_component_id`) REFERENCES `mst_component` (`id`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.``mst_common_version`.`
-- -----------------------------------------------------

CREATE TABLE `appsone`.`mst_component_version` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(128) NOT NULL,
  `is_custom` tinyint(1) NOT NULL,
  `status` tinyint NOT NULL,
  `version_from` smallint DEFAULT NULL,
  `version_to` smallint DEFAULT NULL,
  `mst_common_version_id` int NOT NULL,
  `mst_component_id` int NOT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `account_id` int NOT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_mst_component_version_mst_common_version1_idx` (`mst_common_version_id`),
  KEY `fk_mst_component_version_mst_component1_idx` (`mst_component_id`),
  KEY `mst_component_version_account1_idx` (`account_id`),
  CONSTRAINT `fk_mst_component_version_mst_common_version1` FOREIGN KEY (`mst_common_version_id`) REFERENCES `mst_common_version` (`id`),
  CONSTRAINT `fk_mst_component_version_mst_component1` FOREIGN KEY (`mst_component_id`) REFERENCES `mst_component` (`id`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.``view_application_service_mapping`.`
-- -----------------------------------------------------

create view `appsone`.`view_application_service_mapping` AS
select `a`.`id` AS `application_id`,
`a`.`name` AS `application_name`,
`a`.`identifier` AS `application_identifier`,
`s`.`id` AS `service_id`,
`s`.`name` AS `service_name`,
`s`.`identifier` AS `service_identifier`,
`tm`.`account_id` AS `account_id`
from ((`controller` `a` join `tag_mapping` `tm`) join `controller` `s`)
where ((`tm`.`tag_id` = 1) and (`a`.`account_id` = `tm`.`account_id`) and
(`tm`.`object_ref_table` = 'controller') and (`tm`.`object_id` = `a`.`id`) and
(`a`.`status` = 1) and (`a`.`controller_type_id` = 191) and
(`tm`.`account_id` = `s`.`account_id`) and (`tm`.`tag_key` = `s`.`id`) and
(`s`.`status` = 1) and (`s`.`controller_type_id` = 192));

-- -----------------------------------------------------
-- Table `appsone`.``view_cluster_services`.`
-- -----------------------------------------------------

create view `appsone`.`view_cluster_services` AS
select `c`.`id` AS `id`,
`c`.`name` AS `name`,
`c`.`identifier` AS `identifier`,
`c`.`host_id` AS `host_cluster_id`,
`c`.`mst_component_id` AS `mst_component_id`,
`c`.`mst_component_type_id` AS `mst_component_type_id`,
`c`.`mst_component_version_id` AS `mst_component_version_id`,
`cnt`.`id` AS `service_id`,
`cnt`.`name` AS `service_name`,
`cnt`.`identifier` AS `service_identifier`
from ((`comp_instance` `c` join `tag_mapping` `tm`) join `controller` `cnt`)
where ((`c`.`status` = 1) and (`c`.`is_cluster` = 1) and
(`c`.`account_id` = `tm`.`account_id`) and (`tm`.`tag_id` = 1) and
(`tm`.`object_ref_table` = 'comp_instance') and (`tm`.`object_id` = `c`.`id`) and
(`tm`.`tag_key` = `cnt`.`id`) and (`cnt`.`controller_type_id` = 192) and (`c`.`status` = 1));

-- -----------------------------------------------------
-- Table `appsone`.`view_components`.`
-- -----------------------------------------------------
create view `appsone`.`view_components` AS select `ct`.`id` AS `component_type_id`,`ct`.`name` AS `component_type_name`,`ct`.`status` AS `component_type_status`,`c`.`id` AS `component_id`,`c`.`name` AS `component_name`,`c`.`is_custom` AS `is_custom`,`c`.`status` AS `component_status`,`cv`.`id` AS `common_version_id`,`cv`.`name` AS `common_version_name`,`v`.`id` AS `component_version_id`,`v`.`name` AS `component_version_name`,`v`.`is_custom` AS `is_custom_version`,`v`.`status` AS `is_version_status`,`v`.`version_from` AS `version_from`,`v`.`version_to` AS `version_to` from ((((`mst_component_type` `ct` join `mst_component` `c`) join `mst_component_mapping` `cm`) join `mst_common_version` `cv`) join `mst_component_version` `v`) where ((`ct`.`id` = `cm`.`mst_component_type_id`) and (`cm`.`mst_component_id` = `c`.`id`) and (`c`.`id` = `v`.`mst_component_id`) and (`v`.`mst_common_version_id` = `cv`.`id`));

-- -----------------------------------------------------
-- Table `appsone`.`view_component_instance`.`
-- -----------------------------------------------------

create view `appsone`.`view_component_instance` AS select `c`.`id` AS `id`,`c`.`name` AS `name`,`c`.`status` AS `status`,`c`.`identifier` AS `identifier`,`c`.`host_id` AS `host_id`,(select `comp_instance`.`name` from `comp_instance` where (`comp_instance`.`id` = `c`.`host_id`)) AS `host_name`,`c`.`is_cluster` AS `is_cluster`,`c`.`account_id` AS `account_id`,`c`.`user_details_id` AS `user_details_id`,`c`.`discovery` AS `discovery`,`c`.`host_address` AS `host_address`,`c`.`mst_component_id` AS `mst_component_id`,`vc`.`component_name` AS `component_name`,`c`.`mst_component_type_id` AS `mst_component_type_id`,`vc`.`component_type_name` AS `component_type_name`,`c`.`mst_component_version_id` AS `mst_component_version_id`,`vc`.`component_version_name` AS `component_version_name`,`vc`.`common_version_id` AS `common_version_id`,`vc`.`common_version_name` AS `common_version_name`,`c`.`created_time` AS `created_time`,`c`.`updated_time` AS `updated_time`,`c`.`parent_instance_id` AS `parent_instance_id`,`c`.`supervisor_id` AS `supervisor_id` from (`comp_instance` `c` join `view_components` `vc`) where ((`c`.`mst_component_id` = `vc`.`component_id`) and (`c`.`mst_component_type_id` = `vc`.`component_type_id`) and (`c`.`mst_component_version_id` = `vc`.`component_version_id`));

-- -----------------------------------------------------
-- Table `appsone`.`mst_producer_type`.`
-- -----------------------------------------------------

CREATE TABLE `appsone`.`mst_producer_type` (
  `id` int NOT NULL AUTO_INCREMENT,
  `type` varchar(45) NOT NULL,
  `description` varchar(256) NOT NULL,
  `classname` varchar(128) DEFAULT NULL,
  `status` tinyint NOT NULL DEFAULT '1',
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `parameter_type_id` int DEFAULT NULL,
  `producer_table_name` varchar(64) DEFAULT NULL,
  `account_id` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_mst_producer_type_mst_type` (`parameter_type_id`),
  CONSTRAINT `fk_mst_producer_type_mst_type` FOREIGN KEY (`parameter_type_id`) REFERENCES `mst_type` (`id`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`mst_producer`.`
-- -----------------------------------------------------

CREATE TABLE `appsone`.`mst_producer` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(45) NOT NULL,
  `description` varchar(256) NOT NULL,
  `is_custom` tinyint(1) NOT NULL DEFAULT '1',
  `status` tinyint NOT NULL DEFAULT '1',
  `version` varchar(45) DEFAULT NULL,
  `is_deprecated` tinyint(1) NOT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `account_id` int NOT NULL,
  `mst_producer_type_id` int NOT NULL,
  `mst_sub_type_id` int NOT NULL,
  `is_kpi_group` tinyint DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `producer_account1_idx` (`account_id`),
  KEY `fk_mst_producer_mst_producer_type1_idx` (`mst_producer_type_id`),
  KEY `fk_mst_producer_mst_sub_type1_idx` (`mst_sub_type_id`),
  KEY `fk_mst_producer_name_idx` (`name`),
  CONSTRAINT `fk_mst_producer_kpi_type` FOREIGN KEY (`mst_sub_type_id`) REFERENCES `mst_sub_type` (`id`),
  CONSTRAINT `fk_mst_producer_mst_producer_type1` FOREIGN KEY (`mst_producer_type_id`) REFERENCES `mst_producer_type` (`id`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`mst_producer_kpi_mapping`.`
-- -----------------------------------------------------

CREATE TABLE `appsone`.`mst_producer_kpi_mapping` (
  `id` int NOT NULL AUTO_INCREMENT,
  `producer_id` int NOT NULL,
  `is_default` tinyint(1) NOT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `account_id` int NOT NULL,
  `mst_kpi_details_id` int NOT NULL,
  `mst_component_version_id` int NOT NULL,
  `mst_component_id` int NOT NULL,
  `mst_component_type_id` int NOT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_mst_producer_component_kpi_mapping_producer1_idx` (`producer_id`),
  KEY `mst_producer_kpi_mapping_account1_idx` (`account_id`),
  KEY `fk_mst_producer_kpi_mapping_mst_kpi_details1_idx` (`mst_kpi_details_id`),
  KEY `fk_mst_producer_kpi_mapping_mst_component_version1_idx` (`mst_component_version_id`),
  KEY `fk_mst_producer_kpi_mapping_component_id_idx` (`mst_component_id`),
  KEY `fk_mst_producer_kpi_mapping_component_type_id_idx` (`mst_component_type_id`),
  CONSTRAINT `fk_mst_producer_component_kpi_mapping_producer1` FOREIGN KEY (`producer_id`) REFERENCES `mst_producer` (`id`),
  CONSTRAINT `fk_mst_producer_kpi_mapping_component_id` FOREIGN KEY (`mst_component_id`) REFERENCES `mst_component` (`id`),
  CONSTRAINT `fk_mst_producer_kpi_mapping_component_type_id` FOREIGN KEY (`mst_component_type_id`) REFERENCES `mst_component_type` (`id`),
  CONSTRAINT `fk_mst_producer_kpi_mapping_mst_component_version1` FOREIGN KEY (`mst_component_version_id`) REFERENCES `mst_component_version` (`id`),
  CONSTRAINT `fk_mst_producer_kpi_mapping_mst_kpi_details1` FOREIGN KEY (`mst_kpi_details_id`) REFERENCES `mst_kpi_details` (`id`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`comp_instance_kpi_group_details`.`
-- -----------------------------------------------------

CREATE TABLE `appsone`.`comp_instance_kpi_group_details` (
  `id` int NOT NULL AUTO_INCREMENT,
  `attribute_value` varchar(255) NOT NULL,
  `status` tinyint NOT NULL DEFAULT '1',
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `comp_instance_id` int NOT NULL,
  `mst_producer_kpi_mapping_id` int NOT NULL,
  `collection_interval` int NOT NULL DEFAULT '60',
  `mst_kpi_details_id` int NOT NULL,
  `is_discovery` tinyint NOT NULL,
  `kpi_group_name` varchar(512) NOT NULL,
  `mst_kpi_group_id` int NOT NULL,
  `mst_producer_id` int NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `comp_inst_grp_name_UNIQUE` (`attribute_value`,`comp_instance_id`,`mst_kpi_details_id`),
  KEY `fk_comp_instance_kpi_group_details_comp_instance1_idx` (`comp_instance_id`),
  KEY `fk_comp_instance_kpi_group_details_mst_producer_kpi_mapping_idx` (`mst_producer_kpi_mapping_id`),
  KEY `fk_comp_instance_kpi_group_details_mst_kpi_details1_idx` (`mst_kpi_details_id`),
  KEY `fk_comp_instance_kpi_group_details_group_id_idx` (`mst_kpi_group_id`),
  KEY `fk_comp_instance_kpi_group_details_producer_id_idx` (`mst_producer_id`),
  CONSTRAINT `fk_comp_instance_kpi_group_details_comp_instance1` FOREIGN KEY (`comp_instance_id`) REFERENCES `comp_instance` (`id`),
  CONSTRAINT `fk_comp_instance_kpi_group_details_group_id` FOREIGN KEY (`mst_kpi_group_id`) REFERENCES `mst_kpi_group` (`id`),
  CONSTRAINT `fk_comp_instance_kpi_group_details_mst_kpi_details1` FOREIGN KEY (`mst_kpi_details_id`) REFERENCES `mst_kpi_details` (`id`),
  CONSTRAINT `fk_comp_instance_kpi_group_details_mst_producer_kpi_mapping1` FOREIGN KEY (`mst_producer_kpi_mapping_id`) REFERENCES `mst_producer_kpi_mapping` (`id`),
  CONSTRAINT `fk_comp_instance_kpi_group_details_producer_id` FOREIGN KEY (`mst_producer_id`) REFERENCES `mst_producer` (`id`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`mst_timezone`
-- -----------------------------------------------------

CREATE TABLE `appsone`.`mst_timezone` (
  `id` int NOT NULL AUTO_INCREMENT,
  `time_zone_id` varchar(128) NOT NULL,
  `timeoffset` int NOT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `account_id` int NOT NULL,
  `offset_name` varchar(64) NOT NULL,
  `status` tinyint NOT NULL DEFAULT '0',
  `abbreviation` varchar(128) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `mst_timezone_account1_idx` (`account_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
