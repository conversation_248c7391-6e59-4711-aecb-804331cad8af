-- master tables
DROP SCHEMA IF EXISTS `appsone` ;

-- -----------------------------------------------------
-- Schema appsone
-- -----------------------------------------------------
-- CREATE SCHEMA IF NOT EXISTS `appsone` DEFAULT CHARACTER SET utf8 ;
CREATE SCHEMA `appsone`;
USE `appsone` ;

-- -----------------------------------------------------
-- Table `appsone`.`mst_type`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`mst_type` ;

CREATE TABLE IF NOT EXISTS `appsone`.`mst_type` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `type` VARCHAR(45) NOT NULL,
  `description` VARCHAR(128) NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `status` TINYINT NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;

CREATE INDEX `mst_type_account1_idx` ON `appsone`.`mst_type` (`account_id` ASC);


-- -----------------------------------------------------
-- Table `appsone`.`mst_sub_type`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`mst_sub_type` ;

CREATE TABLE IF NOT EXISTS `appsone`.`mst_sub_type` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(45) NOT NULL,
  `mst_type_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `description` VARCHAR(256) NOT NULL,
  `is_custom` TINYINT NOT NULL DEFAULT 0,
  `status` TINYINT NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_sub_type_mst_type`
    FOREIGN KEY (`mst_type_id`)
    REFERENCES `appsone`.`mst_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_mst_sub_type_mst_type_idx` ON `appsone`.`mst_sub_type` (`mst_type_id` ASC);

CREATE INDEX `mst_sub_type_account1_idx` ON `appsone`.`mst_sub_type` (`account_id` ASC);

-- -----------------------------------------------------
-- Table `appsone`.`transaction`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`transaction` ;

CREATE TABLE IF NOT EXISTS `appsone`.`transaction` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `status` TINYINT NOT NULL DEFAULT 1,
  `audit_enabled` TINYINT(1) NOT NULL DEFAULT 0,
  `is_autoconfigured` TINYINT(1) NOT NULL DEFAULT 0,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `transaction_type_id` INT NOT NULL,
  `pattern_hashcode` VARCHAR(256) NOT NULL,
  `description` VARCHAR(256) NOT NULL,
  `identifier` VARCHAR(128) NOT NULL,
  `rule_id` INT NULL,
  `is_business_txn` TINYINT NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_transaction_mst_sub_type1`
    FOREIGN KEY (`transaction_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_transaction_mst_sub_type1_idx` ON `appsone`.`transaction` (`transaction_type_id` ASC);

CREATE UNIQUE INDEX `identifier_UNIQUE` ON `appsone`.`transaction` (`identifier` ASC);


-- -----------------------------------------------------
-- Table `appsone`.`sub_transactions`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`sub_transactions` ;

CREATE TABLE IF NOT EXISTS `appsone`.`sub_transactions` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `http_method` VARCHAR(8) NULL,
  `http_url` VARCHAR(128) NULL,
  `transaction_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_configured_transactions_transaction1`
    FOREIGN KEY (`transaction_id`)
    REFERENCES `appsone`.`transaction` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_sub_transactions_transaction1_idx` ON `appsone`.`sub_transactions` (`transaction_id` ASC);


-- -----------------------------------------------------
-- Table `appsone`.`mst_component`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`mst_component` ;

CREATE TABLE IF NOT EXISTS `appsone`.`mst_component` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(64) NOT NULL,
  `is_custom` TINYINT(1) NULL DEFAULT 0,
  `status` TINYINT NULL,
  `created_time` DATETIME NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL DEFAULT 0,
  `description` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;

CREATE INDEX `mst_component_account1_idx` ON `appsone`.`mst_component` (`account_id` ASC);


-- -----------------------------------------------------
-- Table `appsone`.`mst_component_type`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`mst_component_type` ;

CREATE TABLE IF NOT EXISTS `appsone`.`mst_component_type` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(64) NOT NULL,
  `description` VARCHAR(256) NULL,
  `is_custom` TINYINT(1) NOT NULL DEFAULT 0,
  `status` TINYINT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;

CREATE INDEX `mst_component_type_account1_idx` ON `appsone`.`mst_component_type` (`account_id` ASC);


-- -----------------------------------------------------
-- Table `appsone`.`comp_instance`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `comp_instance` ;

CREATE TABLE IF NOT EXISTS `appsone`.`comp_instance` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `status` TINYINT NOT NULL DEFAULT 1,
  `host_id` INT NULL,
  `is_DR` TINYINT NULL,
  `is_cluster` TINYINT NULL,
  `mst_component_version_id` INT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `mst_component_id` INT NOT NULL,
  `mst_component_type_id` INT NOT NULL,
  `discovery` TINYINT NULL,
  `host_address` VARCHAR(256) NULL,
  `identifier` VARCHAR(128) NOT NULL,
  `mst_common_version_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_comp_instance_mst_component1`
    FOREIGN KEY (`mst_component_id`)
    REFERENCES `appsone`.`mst_component` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_comp_instance_mst_component_type1`
    FOREIGN KEY (`mst_component_type_id`)
    REFERENCES `appsone`.`mst_component_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;



-- -----------------------------------------------------
-- Table `appsone`.`mst_common_attributes`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`mst_common_attributes` ;

CREATE TABLE IF NOT EXISTS `appsone`.`mst_common_attributes` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `attribute_name` VARCHAR(64) NOT NULL,
  `attribute_type` VARCHAR(64) NOT NULL,
  `is_custom` TINYINT(1) NOT NULL DEFAULT 0,
  `status` TINYINT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `name` VARCHAR(128) NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;

CREATE INDEX `mst_common_attributes_account1_details` ON `appsone`.`mst_common_attributes` (`account_id` ASC);


-- -----------------------------------------------------
-- Table `appsone`.`mst_common_version`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`mst_common_version` ;

CREATE TABLE IF NOT EXISTS `appsone`.`mst_common_version` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(45) NOT NULL,
  `mst_component_id` INT NOT NULL,
  `is_custom` TINYINT(1) NOT NULL DEFAULT 0,
  `status` TINYINT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_common_version_mst_component`
    FOREIGN KEY (`mst_component_id`)
    REFERENCES `appsone`.`mst_component` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_mst_common_version_mst_component_idx` ON `appsone`.`mst_common_version` (`mst_component_id` ASC);

CREATE INDEX `mst_common_version_account1_idx` ON `appsone`.`mst_common_version` (`account_id` ASC);


-- -----------------------------------------------------
-- Table `appsone`.`mst_component_attribute_mapping`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`mst_component_attribute_mapping` ;

CREATE TABLE IF NOT EXISTS `appsone`.`mst_component_attribute_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `mst_common_attributes_id` INT NOT NULL,
  `is_custom` TINYINT(1) NULL DEFAULT 0,
  `is_mandatory` TINYINT(1) NOT NULL,
  `default_value` VARCHAR(64) NULL,
  `mst_common_version_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `min_length` SMALLINT NULL,
  `max_length` INT NULL,
  `regex` VARCHAR(512) NULL,
  `error_message` VARCHAR(256) NULL,
  `mst_component_id` INT NOT NULL,
  `mst_component_type_id` INT NOT NULL,
  `is_ui_visible` TINYINT(1) NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_component_attribute_mapping_mst_common_attributes1`
    FOREIGN KEY (`mst_common_attributes_id`)
    REFERENCES `appsone`.`mst_common_attributes` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_component_attribute_mapping_mst_common_version1`
    FOREIGN KEY (`mst_common_version_id`)
    REFERENCES `appsone`.`mst_common_version` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_component_attribute_mapping_component_id`
    FOREIGN KEY (`mst_component_id`)
    REFERENCES `appsone`.`mst_component` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_component_attribute_mapping_component_type_id`
    FOREIGN KEY (`mst_component_type_id`)
    REFERENCES `appsone`.`mst_component_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`comp_instance_attribute_values`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`comp_instance_attribute_values` ;

CREATE TABLE IF NOT EXISTS `appsone`.`comp_instance_attribute_values` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `attribute_value` VARCHAR(256) NULL,
  `comp_instance_id` INT NOT NULL,
  `mst_component_attribute_mapping_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `mst_common_attributes_id` INT NOT NULL,
  `attribute_name` VARCHAR(45) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_comp_instance_config_details_comp_instance1`
    FOREIGN KEY (`comp_instance_id`)
    REFERENCES `appsone`.`comp_instance` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_comp_instance_attribute_values_mst_component_attribute_map1`
    FOREIGN KEY (`mst_component_attribute_mapping_id`)
    REFERENCES `appsone`.`mst_component_attribute_mapping` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_comp_instance_attribute_values_mst_common_attributes1`
    FOREIGN KEY (`mst_common_attributes_id`)
    REFERENCES `appsone`.`mst_common_attributes` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_comp_instance_config_details_comp_instance1_idx` ON `appsone`.`comp_instance_attribute_values` (`comp_instance_id` ASC);

CREATE INDEX `fk_comp_instance_attribute_values_mst_component_attribute_m_idx` ON `appsone`.`comp_instance_attribute_values` (`mst_component_attribute_mapping_id` ASC);

CREATE INDEX `fk_comp_instance_attribute_values_mst_common_attributes1_idx` ON `appsone`.`comp_instance_attribute_values` (`mst_common_attributes_id` ASC);


-- -----------------------------------------------------
-- Table `appsone`.`agent`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`agent` ;

CREATE TABLE IF NOT EXISTS `appsone`.`agent` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `unique_token` VARCHAR(128) NOT NULL,
  `name` VARCHAR(128) NOT NULL,
  `agent_type_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `status` TINYINT NOT NULL DEFAULT 1,
  `host_address` VARCHAR(128) NOT NULL,
  `mode` VARCHAR(45) NOT NULL,
  `description` VARCHAR(256) NOT NULL,
  `agent_data_type_id` INT NOT NULL DEFAULT 251,
  `comp_instance_id` INT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_agent_mst_sub_type1`
    FOREIGN KEY (`agent_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_agent_mst_sub_type1_idx` ON `appsone`.`agent` (`agent_type_id` ASC);

CREATE UNIQUE INDEX `unique_token_UNIQUE` ON `appsone`.`agent` (`unique_token` ASC);


-- -----------------------------------------------------
-- Table `appsone`.`server_type_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`server_type_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`server_type_details` (
  `id` INT NOT NULL,
  `vendor` VARCHAR(45) NULL,
  `server_type_id` INT NULL,
  `description` VARCHAR(45) NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`mst_kpi_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`mst_kpi_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`mst_kpi_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `description` MEDIUMTEXT NOT NULL,
  `data_type` VARCHAR(32) NOT NULL,
  `is_custom` TINYINT(1) NOT NULL DEFAULT 0,
  `status` TINYINT NOT NULL DEFAULT 1,
  `kpi_type_id` INT NOT NULL,
  `measure_units` VARCHAR(16) NULL,
  `cluster_operation` VARCHAR(16) NULL DEFAULT 'NONE',
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `kpi_group_id` INT NOT NULL DEFAULT 0,
  `alias_name` VARCHAR(128) NOT NULL,
  `value_type` VARCHAR(45) NULL DEFAULT 'SNAPSHOT',
  `rollup_operation` VARCHAR(16) NOT NULL,
  `cluster_aggregation_type` INT NULL,
  `instance_aggregation_type` INT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_kpi_details_mst_sub_type1`
    FOREIGN KEY (`kpi_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_mst_kpi_details_mst_sub_type1_idx` ON `appsone`.`mst_kpi_details` (`kpi_type_id` ASC);

CREATE INDEX `mst_kpi_details_account1_idx` ON `appsone`.`mst_kpi_details` (`account_id` ASC);

CREATE INDEX `indx_mst_kpi_details_account_id` ON `appsone`.`mst_kpi_details` (`kpi_group_id` ASC);


-- -----------------------------------------------------
-- Table `appsone`.`mst_timezone`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`mst_timezone` ;

CREATE TABLE IF NOT EXISTS `appsone`.`mst_timezone` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `time_zone_id` VARCHAR(128) NOT NULL,
  `timeoffset` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `offset_name` varchar(64) NOT NULL,
  `abbreviation` varchar(128) NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;

CREATE INDEX `mst_timezone_account1_idx` ON `appsone`.`mst_timezone` (`account_id` ASC);


-- -----------------------------------------------------
-- Table `appsone`.`agent_comp_instance_mapping`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`agent_comp_instance_mapping` ;

CREATE TABLE IF NOT EXISTS `appsone`.`agent_comp_instance_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `comp_instance_id` INT NOT NULL,
  `agent_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_server_agent_comp_instance1`
    FOREIGN KEY (`comp_instance_id`)
    REFERENCES `appsone`.`comp_instance` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_server_agent_agent1`
    FOREIGN KEY (`agent_id`)
    REFERENCES `appsone`.`agent` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_server_agent_comp_instance1_idx` ON `appsone`.`agent_comp_instance_mapping` (`comp_instance_id` ASC);

CREATE INDEX `fk_server_agent_agent1_idx` ON `appsone`.`agent_comp_instance_mapping` (`agent_id` ASC);


-- -----------------------------------------------------
-- Table `appsone`.`mst_kpi_group`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`mst_kpi_group` ;

CREATE TABLE IF NOT EXISTS `appsone`.`mst_kpi_group` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(32) NOT NULL,
  `description` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `account_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `kpi_type_id` INT NOT NULL,
  `discovery` TINYINT NULL,
  `regex` VARCHAR(64) NULL,
  `status` TINYINT NOT NULL,
  `is_custom` TINYINT NOT NULL,
  `alias_name` VARCHAR(64) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_kpi_group_mst_sub_type1`
    FOREIGN KEY (`kpi_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `indx_mst_kpi_group_account_id` ON `appsone`.`mst_kpi_group` (`account_id` ASC);

CREATE INDEX `fk_mst_kpi_group_mst_sub_type1_idx` ON `appsone`.`mst_kpi_group` (`kpi_type_id` ASC);


-- -----------------------------------------------------
-- Table `appsone`.`mst_component_version_kpi_mapping`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`mst_component_version_kpi_mapping` ;

CREATE TABLE IF NOT EXISTS `appsone`.`mst_component_version_kpi_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `mst_kpi_details_id` INT NOT NULL,
  `is_custom` TINYINT(1) NULL DEFAULT 0,
  `do_analytics` TINYINT(1) NULL DEFAULT 0,
  `mst_common_version_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `default_collection_interval` INT NOT NULL DEFAULT 60,
  `status` TINYINT NOT NULL,
  `mst_component_id` INT NOT NULL,
  `mst_component_type_id` INT NOT NULL,
  `default_operation_id` INT NULL,
  `default_threshold` DOUBLE NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_component_version_kpi_mapping_mst_kpi_details1`
    FOREIGN KEY (`mst_kpi_details_id`)
    REFERENCES `appsone`.`mst_kpi_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_component_version_kpi_mapping_mst_common_version1`
    FOREIGN KEY (`mst_common_version_id`)
    REFERENCES `appsone`.`mst_common_version` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_component_version_kpi_mapping_component_id`
    FOREIGN KEY (`mst_component_id`)
    REFERENCES `appsone`.`mst_component` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_component_version_kpi_mapping_component_type_id`
    FOREIGN KEY (`mst_component_type_id`)
    REFERENCES `appsone`.`mst_component_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_component_version_kpi_mapping_mst_kpi_details1_idx` ON `appsone`.`mst_component_version_kpi_mapping` (`mst_kpi_details_id` ASC);

CREATE INDEX `fk_mst_component_version_kpi_mapping_mst_common_version1_idx` ON `appsone`.`mst_component_version_kpi_mapping` (`mst_common_version_id` ASC);

CREATE INDEX `fk_mst_component_version_kpi_mapping_component_id_idx` ON `appsone`.`mst_component_version_kpi_mapping` (`mst_component_id` ASC);

CREATE INDEX `fk_mst_component_version_kpi_mapping_component_type_id_idx` ON `appsone`.`mst_component_version_kpi_mapping` (`mst_component_type_id` ASC);


-- -----------------------------------------------------
-- Table `appsone`.`mst_producer_type`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`mst_producer_type` ;

CREATE TABLE `mst_producer_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(45) NOT NULL,
  `description` varchar(256) NOT NULL,
  `classname` varchar(128) NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1',
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `account_id` int(11) DEFAULT NULL,
  `parameter_type_id` int(11) DEFAULT NULL,
  `producer_table_name` varchar(64) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_mst_producer_type_1_idx` (`parameter_type_id`),
  CONSTRAINT `fk_mst_producer_type_1` FOREIGN KEY (`parameter_type_id`) REFERENCES `mst_type` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8;


-- -----------------------------------------------------
-- Table `appsone`.`mst_producer`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`mst_producer` ;

CREATE TABLE IF NOT EXISTS `appsone`.`mst_producer` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(45) NOT NULL,
  `description` VARCHAR(256) NOT NULL,
  `is_custom` TINYINT(1) NOT NULL DEFAULT 1,
  `status` TINYINT NOT NULL DEFAULT 1,
  `version` VARCHAR(45) NULL,
  `is_deprecated` TINYINT(1) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `mst_producer_type_id` INT NOT NULL,
  `mst_sub_type_id` INT NOT NULL,
  `is_kpi_group` TINYINT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_producer_mst_producer_type1`
    FOREIGN KEY (`mst_producer_type_id`)
    REFERENCES `appsone`.`mst_producer_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_producer_kpi_type`
    FOREIGN KEY (`mst_sub_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`mst_component_version`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`mst_component_version` ;

CREATE TABLE IF NOT EXISTS `appsone`.`mst_component_version` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `is_custom` TINYINT(1) NOT NULL,
  `status` TINYINT NOT NULL,
  `version_from` SMALLINT NULL,
  `version_to` SMALLINT NULL,
  `mst_common_version_id` INT NOT NULL,
  `mst_component_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_component_version_mst_common_version1`
    FOREIGN KEY (`mst_common_version_id`)
    REFERENCES `appsone`.`mst_common_version` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_component_version_mst_component1`
    FOREIGN KEY (`mst_component_id`)
    REFERENCES `appsone`.`mst_component` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`mst_producer_kpi_mapping`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`mst_producer_kpi_mapping` ;

CREATE TABLE IF NOT EXISTS `appsone`.`mst_producer_kpi_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `producer_id` INT NOT NULL,
  `is_default` TINYINT(1) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `mst_kpi_details_id` INT NOT NULL,
  `mst_component_version_id` INT NOT NULL,
  `mst_component_id` INT NOT NULL,
  `mst_component_type_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_producer_component_kpi_mapping_producer1`
    FOREIGN KEY (`producer_id`)
    REFERENCES `appsone`.`mst_producer` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_producer_kpi_mapping_mst_kpi_details1`
    FOREIGN KEY (`mst_kpi_details_id`)
    REFERENCES `appsone`.`mst_kpi_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_producer_kpi_mapping_mst_component_version1`
    FOREIGN KEY (`mst_component_version_id`)
    REFERENCES `appsone`.`mst_component_version` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_producer_kpi_mapping_component_id`
    FOREIGN KEY (`mst_component_id`)
    REFERENCES `appsone`.`mst_component` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_producer_kpi_mapping_component_type_id`
    FOREIGN KEY (`mst_component_type_id`)
    REFERENCES `appsone`.`mst_component_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`comp_instance_kpi_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`comp_instance_kpi_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`comp_instance_kpi_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `comp_instance_id` INT NOT NULL,
  `mst_producer_kpi_mapping_id` INT NOT NULL,
  `collection_interval` INT NOT NULL,
  `status` TINYINT NOT NULL DEFAULT 1,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `mst_kpi_details_id` INT NOT NULL,
  `mst_producer_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_comp_instance_kpi_details_comp_instance1`
    FOREIGN KEY (`comp_instance_id`)
    REFERENCES `appsone`.`comp_instance` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_comp_instance_kpi_details_mst_producer_kpi_mapping1`
    FOREIGN KEY (`mst_producer_kpi_mapping_id`)
    REFERENCES `appsone`.`mst_producer_kpi_mapping` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_comp_instance_kpi_details_mst_kpi_details1`
    FOREIGN KEY (`mst_kpi_details_id`)
    REFERENCES `appsone`.`mst_kpi_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_comp_instance_kpi_details_producer_id`
    FOREIGN KEY (`mst_producer_id`)
    REFERENCES `appsone`.`mst_producer` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`server_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`server_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`server_details` (
  `Id` INT NOT NULL AUTO_INCREMENT,
  `interface` VARCHAR(45) NOT NULL,
  `physical_address` VARCHAR(256) NOT NULL,
  `lowerbound_port` INT NOT NULL,
  `status` TINYINT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `upperbound_port` INT NULL,
  `virtual_ip` TINYINT(1) NOT NULL,
  `virtual_host` VARCHAR(256) NULL,
  `protocol_id` INT NOT NULL,
  `key_file_path` VARCHAR(516) NULL,
  `key_file_password` VARCHAR(516) NULL,
  `response_header` TINYINT NOT NULL DEFAULT 0,
  `offset` INT NULL DEFAULT 0,
  `size` INT NULL DEFAULT 100,
  `response_body` TINYINT NOT NULL DEFAULT 0,
  `response_time_type_id` INT NOT NULL,
  PRIMARY KEY (`Id`),
  CONSTRAINT `fk_server_details_1`
    FOREIGN KEY (`response_time_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`agent_server_mapping`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`agent_server_mapping` ;

CREATE TABLE IF NOT EXISTS `appsone`.`agent_server_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `agent_id` INT NOT NULL,
  `server_details_Id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_agent_server_mapping_agent1`
    FOREIGN KEY (`agent_id`)
    REFERENCES `appsone`.`agent` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_agent_server_mapping_server_details1`
    FOREIGN KEY (`server_details_Id`)
    REFERENCES `appsone`.`server_details` (`Id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`audit_table`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`audit_table` ;

CREATE TABLE IF NOT EXISTS `appsone`.`audit_table` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `time` DATETIME NOT NULL,
  `table_name` VARCHAR(128) NOT NULL,
  `operation` VARCHAR(64) NOT NULL,
  `object_id` INT NOT NULL,
  `db_user` VARCHAR(256) NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`component_cluster_mapping`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`component_cluster_mapping` ;

CREATE TABLE IF NOT EXISTS `appsone`.`component_cluster_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `comp_instance_id` INT NOT NULL,
  `cluster_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_component_cluster_mapping_comp_instance1`
    FOREIGN KEY (`comp_instance_id`)
    REFERENCES `appsone`.`comp_instance` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_component_cluster_mapping_comp_instance2`
    FOREIGN KEY (`cluster_id`)
    REFERENCES `appsone`.`comp_instance` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`data_communication_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`data_communication_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`data_communication_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(64) NOT NULL,
  `protocol_id` INT NOT NULL,
  `host` VARCHAR(512) NOT NULL,
  `port` INT NOT NULL,
  `status` TINYINT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `description` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_data_communication_details_2`
    FOREIGN KEY (`protocol_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`component_agent`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`component_agent` ;

CREATE TABLE IF NOT EXISTS `appsone`.`component_agent` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `agent_id` INT NOT NULL,
  `timeout_multiplier` TINYINT NULL DEFAULT 2,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `config_operation_mode_id` INT NOT NULL,
  `data_operation_mode_id` INT NOT NULL,
  `data_communication_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_component_agent_agent1`
    FOREIGN KEY (`agent_id`)
    REFERENCES `appsone`.`agent` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_component_agent_2`
    FOREIGN KEY (`config_operation_mode_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_component_agent_3`
    FOREIGN KEY (`data_operation_mode_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_component_agent_4`
    FOREIGN KEY (`data_communication_id`)
    REFERENCES `appsone`.`data_communication_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`jim_agent`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`jim_agent` ;

CREATE TABLE IF NOT EXISTS `appsone`.`jim_agent` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `jvm_id` INT NOT NULL,
  `agent_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(128) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_jim_agent_agent1`
    FOREIGN KEY (`agent_id`)
    REFERENCES `appsone`.`agent` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_jim_agent_2`
    FOREIGN KEY (`jvm_id`)
    REFERENCES `appsone`.`comp_instance` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`comp_instance_kpi_group_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`comp_instance_kpi_group_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`comp_instance_kpi_group_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `attribute_value` VARCHAR(255) NOT NULL,
  `status` TINYINT NOT NULL DEFAULT 1,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `comp_instance_id` INT NOT NULL,
  `mst_producer_kpi_mapping_id` INT NOT NULL,
  `collection_interval` INT NOT NULL DEFAULT 60,
  `mst_kpi_details_id` INT NOT NULL,
  `is_discovery` TINYINT NOT NULL,
  `kpi_group_name` VARCHAR(512) NOT NULL,
  `mst_kpi_group_id` INT NOT NULL,
  `mst_producer_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_comp_instance_kpi_group_details_comp_instance1`
    FOREIGN KEY (`comp_instance_id`)
    REFERENCES `appsone`.`comp_instance` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_comp_instance_kpi_group_details_mst_producer_kpi_mapping1`
    FOREIGN KEY (`mst_producer_kpi_mapping_id`)
    REFERENCES `appsone`.`mst_producer_kpi_mapping` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_comp_instance_kpi_group_details_mst_kpi_details1`
    FOREIGN KEY (`mst_kpi_details_id`)
    REFERENCES `appsone`.`mst_kpi_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_comp_instance_kpi_group_details_group_id`
    FOREIGN KEY (`mst_kpi_group_id`)
    REFERENCES `appsone`.`mst_kpi_group` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_comp_instance_kpi_group_details_producer_id`
    FOREIGN KEY (`mst_producer_id`)
    REFERENCES `appsone`.`mst_producer` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`matcher_type`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`matcher_type` ;

CREATE TABLE IF NOT EXISTS `appsone`.`matcher_type` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(45) NOT NULL,
  `description` VARCHAR(128) NOT NULL,
  `status` TINYINT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL DEFAULT 0,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`matcher_type_attributes`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`matcher_type_attributes` ;

CREATE TABLE IF NOT EXISTS `appsone`.`matcher_type_attributes` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(45) NOT NULL,
  `is_mandatory` TINYINT NOT NULL DEFAULT 0,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `matcher_type_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_matcher_type_attributes_matcher_type1`
    FOREIGN KEY (`matcher_type_id`)
    REFERENCES `appsone`.`matcher_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`transaction_matcher_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`transaction_matcher_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`transaction_matcher_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `sub_transaction_id` INT NOT NULL,
  `transaction_attribute_id` INT NOT NULL,
  `attribute_1` VARCHAR(256) NOT NULL,
  `attribute_2` VARCHAR(256) NULL,
  `attribute_3` INT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_transaction_matcher_details_mst_sub_type1`
    FOREIGN KEY (`transaction_attribute_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_transaction_matcher_details_sub_transaction1`
    FOREIGN KEY (`sub_transaction_id`)
    REFERENCES `appsone`.`sub_transactions` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`account`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`account` ;

CREATE TABLE IF NOT EXISTS `appsone`.`account` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `status` TINYINT NOT NULL,
  `private_key` TEXT NULL,
  `public_key` TEXT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `identifier` VARCHAR(128) NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`transaction_threshold_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`transaction_threshold_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`transaction_threshold_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `transaction_id` INT NOT NULL,
  `transaction_kpi_type_id` INT NOT NULL,
  `operation_id` INT NOT NULL,
  `min_threshold` DOUBLE NOT NULL,
  `max_threshold` DOUBLE NOT NULL,
  `response_time_type_id` INT NULL,
  `start_time` DATETIME NOT NULL,
  `end_time` DATETIME NULL,
  `coverage_window_profile_id` INT NOT NULL,
  `account_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_transaction_threshold_details_3`
    FOREIGN KEY (`transaction_id`)
    REFERENCES `appsone`.`transaction` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_transaction_threshold_details_4`
    FOREIGN KEY (`transaction_kpi_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_transaction_threshold_details_5`
    FOREIGN KEY (`operation_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_transaction_threshold_details_7`
    FOREIGN KEY (`response_time_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_transaction_threshold_details_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`ssh_producer`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`ssh_producer` ;

CREATE TABLE IF NOT EXISTS `appsone`.`ssh_producer` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `mst_producer_id` INT NOT NULL,
  `script_name` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `signature` TEXT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_ssh_producer_mst_producer1`
    FOREIGN KEY (`mst_producer_id`)
    REFERENCES `appsone`.`mst_producer` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`jdbc_producer`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`jdbc_producer` ;

CREATE TABLE IF NOT EXISTS `appsone`.`jdbc_producer` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `mst_producer_id` INT NOT NULL,
  `query` TEXT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `driver` VARCHAR(64) NOT NULL,
  `url` VARCHAR(256) NOT NULL,
  `query_result` VARCHAR(45) NOT NULL,
  `is_query_encrypted` TINYINT NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_jdbc_producer_mst_producer1`
    FOREIGN KEY (`mst_producer_id`)
    REFERENCES `appsone`.`mst_producer` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`producer_parameters`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`producer_parameters` ;

CREATE TABLE IF NOT EXISTS `appsone`.`producer_parameters` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `mst_producer_id` INT NOT NULL,
  `parameter_type` VARCHAR(45) NULL,
  `parameter_name` VARCHAR(45) NOT NULL,
  `parameter_value` VARCHAR(512) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `parameter_order` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_producer_parameters_mst_producer1`
    FOREIGN KEY (`mst_producer_id`)
    REFERENCES `appsone`.`mst_producer` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`mst_common_attribute_type_values`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`mst_common_attribute_type_values` ;

CREATE TABLE IF NOT EXISTS `appsone`.`mst_common_attribute_type_values` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `created_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `mst_common_attributes_id` INT NOT NULL,
  `mst_type_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_common_attribute_type_values_mst_common_attributes1`
    FOREIGN KEY (`mst_common_attributes_id`)
    REFERENCES `appsone`.`mst_common_attributes` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_common_attribute_type_values_mst_type1`
    FOREIGN KEY (`mst_type_id`)
    REFERENCES `appsone`.`mst_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`jmx_producer`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`jmx_producer` ;

CREATE TABLE IF NOT EXISTS `appsone`.`jmx_producer` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `target_object_name` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `mst_producer_id` INT NOT NULL,
  `url` VARCHAR(256) NOT NULL,
  `attribute_data_type_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_jmx_producer_mst_producer1`
    FOREIGN KEY (`mst_producer_id`)
    REFERENCES `appsone`.`mst_producer` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_jmx_producer_attribute_data`
    FOREIGN KEY (`attribute_data_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`httpd_producer`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`httpd_producer` ;

CREATE TABLE IF NOT EXISTS `appsone`.`httpd_producer` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `status_url` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `mst_producer_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_http_producer_mst_producer1`
    FOREIGN KEY (`mst_producer_id`)
    REFERENCES `appsone`.`mst_producer` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`shell_producer`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`shell_producer` ;

CREATE TABLE IF NOT EXISTS `appsone`.`shell_producer` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `script_name` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `mst_producer_id` INT NOT NULL,
  `signature` TEXT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_shell_producer_mst_producer1`
    FOREIGN KEY (`mst_producer_id`)
    REFERENCES `appsone`.`mst_producer` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`was_producer`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`was_producer` ;

CREATE TABLE IF NOT EXISTS `appsone`.`was_producer` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `target_object_name` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `mst_producer_id` INT NOT NULL,
  `module` VARCHAR(45) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_was_producer_mst_producer1`
    FOREIGN KEY (`mst_producer_id`)
    REFERENCES `appsone`.`mst_producer` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`mst_component_mapping`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`mst_component_mapping` ;

CREATE TABLE IF NOT EXISTS `appsone`.`mst_component_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `mst_component_type_id` INT NOT NULL,
  `mst_component_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_component_mapping_mst_component_type1`
    FOREIGN KEY (`mst_component_type_id`)
    REFERENCES `appsone`.`mst_component_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_component_mapping_mst_component1`
    FOREIGN KEY (`mst_component_id`)
    REFERENCES `appsone`.`mst_component` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`agent_account_mapping`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`agent_account_mapping` ;

CREATE TABLE IF NOT EXISTS `appsone`.`agent_account_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `created_time` DATETIME NOT NULL,
  `agent_id` INT NOT NULL,
  `account_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_agent_account_mapping_agent1`
    FOREIGN KEY (`agent_id`)
    REFERENCES `appsone`.`agent` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_agent_account_mapping_account1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`notification_profile`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`notification_profile` ;

CREATE TABLE IF NOT EXISTS `appsone`.`notification_profile` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `email_flag` TINYINT(1) NOT NULL,
  `email_subject` TINYTEXT NOT NULL,
  `email_body` TEXT NOT NULL,
  `sms_flag` TINYINT(1) NOT NULL,
  `sms_content` VARCHAR(512) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `name` VARCHAR(64) NOT NULL,
  `description` VARCHAR(256) NOT NULL,
  `notification_type_id` INT NOT NULL,
  `status` TINYINT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_notification_profile_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_notification_profile_4`
    FOREIGN KEY (`notification_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`alert_profile`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`alert_profile` ;

CREATE TABLE IF NOT EXISTS `appsone`.`alert_profile` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `status` TINYINT NOT NULL,
  `profile_type_id` INT NOT NULL,
  `notification_profile_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `is_maintenance` TINYINT(1) NULL,
  `description` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_alert_profile_1`
    FOREIGN KEY (`profile_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_alert_profile_2`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_alert_profile_4`
    FOREIGN KEY (`notification_profile_id`)
    REFERENCES `appsone`.`notification_profile` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`coverage_window_profile`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`coverage_window_profile` ;

CREATE TABLE IF NOT EXISTS `appsone`.`coverage_window_profile` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `status` TINYINT NOT NULL,
  `day_option_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `description` VARCHAR(256) NOT NULL,
  `is_custom` TINYINT(1) NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_coverage_window_profile_1`
    FOREIGN KEY (`day_option_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_coverage_window_profile_2`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`coverage_window`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`coverage_window` ;

CREATE TABLE IF NOT EXISTS `appsone`.`coverage_window` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `day` VARCHAR(16) NOT NULL,
  `start_hour` SMALLINT NOT NULL,
  `start_minute` SMALLINT NOT NULL,
  `end_hour` SMALLINT NOT NULL,
  `end_minute` SMALLINT NOT NULL,
  `coverage_window_profile_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `is_business_hour` TINYINT NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_coverage_window_1`
    FOREIGN KEY (`coverage_window_profile_id`)
    REFERENCES `appsone`.`coverage_window_profile` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`severity_profile`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`severity_profile` ;

CREATE TABLE IF NOT EXISTS `appsone`.`severity_profile` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `status` TINYINT NOT NULL,
  `high_enable` TINYINT(1) NOT NULL,
  `high_persistence` INT NULL,
  `medium_enable` TINYINT(1) NOT NULL,
  `medium_persistence` INT NULL,
  `low_enable` TINYINT(1) NOT NULL,
  `low_persistence` INT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `description` VARCHAR(256) NOT NULL,
  `profile_type_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_severity_profile_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_severity_profile_4`
    FOREIGN KEY (`profile_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`escalation_profile`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`escalation_profile` ;

CREATE TABLE IF NOT EXISTS `appsone`.`escalation_profile` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `status` TINYINT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `description` VARCHAR(256) NOT NULL,
  `escalation_type_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_escalation_profile_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_escalation_profile_3`
    FOREIGN KEY (`escalation_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`escalation_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`escalation_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`escalation_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `level_name` VARCHAR(64) NOT NULL,
  `level_number` SMALLINT NOT NULL,
  `email_to` VARCHAR(512) NULL,
  `email_cc` VARCHAR(512) NULL,
  `email_bcc` VARCHAR(512) NULL,
  `sms_numbers` VARCHAR(512) NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `escalation_profile_id` INT NOT NULL,
  `high_suppression` INT NULL,
  `medium_suppression` INT NULL,
  `low_suppression` INT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_escalation_details_1`
    FOREIGN KEY (`escalation_profile_id`)
    REFERENCES `appsone`.`escalation_profile` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`comp_instance_kpi_threshold_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`comp_instance_kpi_threshold_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`comp_instance_kpi_threshold_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `comp_instance_id` INT NOT NULL,
  `kpi_id` INT NOT NULL,
  `operation_id` INT NOT NULL,
  `min_threshold` FLOAT NOT NULL,
  `max_threshold` FLOAT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `kpi_group_id` INT NULL,
  `kpi_group_value` VARCHAR(512) NULL,
  `coverage_window_profile_id` INT NOT NULL,
  `start_time` DATETIME NOT NULL,
  `end_time` DATETIME NULL,
  `account_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_comp_instance_kpi_threshold_3`
    FOREIGN KEY (`comp_instance_id`)
    REFERENCES `appsone`.`comp_instance` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_comp_instance_kpi_threshold_4`
    FOREIGN KEY (`kpi_id`)
    REFERENCES `appsone`.`mst_kpi_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`holiday_profile`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`holiday_profile` ;

CREATE TABLE IF NOT EXISTS `appsone`.`holiday_profile` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `description` VARCHAR(256) NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `status` TINYINT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_holiday_profile_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`alert_profile_mapping`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`alert_profile_mapping` ;

CREATE TABLE IF NOT EXISTS `appsone`.`alert_profile_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `coverage_window_profile_id` INT NOT NULL,
  `severity_profile_id` INT NULL,
  `escalation_profile_id` INT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `alert_profile_id` INT NOT NULL,
  `name` VARCHAR(64) NOT NULL,
  `holiday_profile_id` INT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_alert_profile_mapping_1`
    FOREIGN KEY (`escalation_profile_id`)
    REFERENCES `appsone`.`escalation_profile` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_alert_profile_mapping_2`
    FOREIGN KEY (`severity_profile_id`)
    REFERENCES `appsone`.`severity_profile` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_alert_profile_mapping_3`
    FOREIGN KEY (`coverage_window_profile_id`)
    REFERENCES `appsone`.`coverage_window_profile` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_alert_profile_mapping_6`
    FOREIGN KEY (`alert_profile_id`)
    REFERENCES `appsone`.`alert_profile` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_alert_profile_mapping_5`
    FOREIGN KEY (`holiday_profile_id`)
    REFERENCES `appsone`.`holiday_profile` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`protocol_stack_agent`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`protocol_stack_agent` ;

CREATE TABLE IF NOT EXISTS `appsone`.`protocol_stack_agent` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `config_operation_mode_id` INT NOT NULL,
  `data_operation_mode_id` INT NOT NULL,
  `created_time` DATETIME NULL,
  `updated_time` DATETIME NULL,
  `dbts` INT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `agent_id` INT NOT NULL,
  `data_flag_id` INT NOT NULL,
  `response_time_type_id` INT NOT NULL,
  `data_communication_id` INT NOT NULL,
  `http_proxy_id` INT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_protocol_stack_agent_3`
    FOREIGN KEY (`config_operation_mode_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_protocol_stack_agent_4`
    FOREIGN KEY (`data_operation_mode_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_protocol_stack_agent_6`
    FOREIGN KEY (`agent_id`)
    REFERENCES `appsone`.`agent` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_protocol_stack_agent_7`
    FOREIGN KEY (`data_flag_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_protocol_stack_agent_8`
    FOREIGN KEY (`response_time_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_protocol_stack_agent_2`
    FOREIGN KEY (`data_communication_id`)
    REFERENCES `appsone`.`data_communication_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_protocol_stack_agent_5`
    FOREIGN KEY (`http_proxy_id`)
    REFERENCES `appsone`.`data_communication_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`http_json_producer`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`http_json_producer` ;

CREATE TABLE IF NOT EXISTS `appsone`.`http_json_producer` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `mst_producer_id` INT NOT NULL,
  `json_url` VARCHAR(512) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_http_json_producer_2`
    FOREIGN KEY (`mst_producer_id`)
    REFERENCES `appsone`.`mst_producer` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`biz_value_extractor`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`biz_value_extractor` ;

CREATE TABLE IF NOT EXISTS `appsone`.`biz_value_extractor` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(64) NOT NULL,
  `regex` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `transaction_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_biz_value_extractor_1`
    FOREIGN KEY (`transaction_id`)
    REFERENCES `appsone`.`transaction` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`transaction_audit_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`transaction_audit_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`transaction_audit_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `transaction_id` INT NOT NULL,
  `data_type_id` INT NOT NULL,
  `extractor_name` VARCHAR(128) NOT NULL,
  `extractor_type_id` INT NOT NULL,
  `status` TINYINT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `attribute_1` VARCHAR(512) NOT NULL,
  `attribute_2` VARCHAR(512) NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_transaction_audit_details_1`
    FOREIGN KEY (`transaction_id`)
    REFERENCES `appsone`.`transaction` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_transaction_audit_details_2`
    FOREIGN KEY (`data_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_transaction_audit_details_4`
    FOREIGN KEY (`extractor_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`jim_transactions`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`jim_transactions` ;

CREATE TABLE IF NOT EXISTS `appsone`.`jim_transactions` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `method_name` VARCHAR(256) NOT NULL,
  `method_long_name` VARCHAR(750) NOT NULL,
  `method_type_id` INT NOT NULL,
  `transformation_type_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `status` TINYINT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_jim_transactions_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_jim_transactions_3`
    FOREIGN KEY (`method_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_jim_transactions_4`
    FOREIGN KEY (`transformation_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`jim_transaction_mapping`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`jim_transaction_mapping` ;

CREATE TABLE IF NOT EXISTS `appsone`.`jim_transaction_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `jim_transaction_id` INT NOT NULL,
  `agent_id` INT NOT NULL,
  `comp_instance_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_jim_transaction_mapping_1`
    FOREIGN KEY (`jim_transaction_id`)
    REFERENCES `appsone`.`jim_transactions` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_jim_transaction_mapping_2`
    FOREIGN KEY (`agent_id`)
    REFERENCES `appsone`.`agent` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_jim_transaction_mapping_3`
    FOREIGN KEY (`comp_instance_id`)
    REFERENCES `appsone`.`comp_instance` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`transaction_response_threshold`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`transaction_response_threshold` ;

CREATE TABLE IF NOT EXISTS `appsone`.`transaction_response_threshold` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `transaction_id` INT NOT NULL,
  `slow_threshold_value` DOUBLE NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `response_time_type_id` INT NOT NULL,
  `start_time` DATETIME NOT NULL,
  `end_time` DATETIME NULL,
  `account_id` INT NOT NULL,
  `coverage_window_profile_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_transaction_response_threshold_1`
    FOREIGN KEY (`response_time_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_transaction_response_threshold_3`
    FOREIGN KEY (`transaction_id`)
    REFERENCES `appsone`.`transaction` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`holiday_dates`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`holiday_dates` ;

CREATE TABLE IF NOT EXISTS `appsone`.`holiday_dates` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `holiday_date` DATE NOT NULL,
  `holiday_profile_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `update_time` DATETIME NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_holiday_dates_1`
    FOREIGN KEY (`holiday_profile_id`)
    REFERENCES `appsone`.`holiday_profile` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`jppf_producer`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`jppf_producer` ;

CREATE TABLE IF NOT EXISTS `appsone`.`jppf_producer` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `mst_producer_id` INT NOT NULL,
  `jppf_type_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_jppf_producer_mst_producer1`
    FOREIGN KEY (`mst_producer_id`)
    REFERENCES `appsone`.`mst_producer` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_jppf_producer_jppf_type1`
    FOREIGN KEY (`jppf_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`controller`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`controller` ;

CREATE TABLE IF NOT EXISTS `appsone`.`controller` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `alias_name` VARCHAR(128) NOT NULL,
  `account_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `controller_type_id` INT NOT NULL,
  `monitor_enabled` TINYINT NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_application_account_id`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`tag_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`tag_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`tag_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `tag_type_id` INT NOT NULL,
  `is_predefined` TINYINT NOT NULL,
  `ref_table` VARCHAR(64) NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `account_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `ref_where_column_name` VARCHAR(128) NULL,
  `ref_select_column_name` VARCHAR(126) NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_tag_details_tag_type_id`
    FOREIGN KEY (`tag_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_tag_details_account_id`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`tag_mapping`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`tag_mapping` ;

CREATE TABLE IF NOT EXISTS `appsone`.`tag_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `tag_id` INT NOT NULL,
  `object_id` INT NOT NULL,
  `object_ref_table` VARCHAR(256) NOT NULL,
  `tag_key` VARCHAR(256) NOT NULL,
  `tag_value` VARCHAR(256) NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `account_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_tag_mapping_tag_id`
    FOREIGN KEY (`tag_id`)
    REFERENCES `appsone`.`tag_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_tag_mapping_account_id`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`connection_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`connection_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`connection_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `source_id` INT NULL,
  `source_ref_object` VARCHAR(64) NULL,
  `destination_id` INT NULL,
  `destination_ref_object` VARCHAR(64) NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `account_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `is_discovery` TINYINT NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`application_threshold_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`application_threshold_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`application_threshold_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `application_id` INT NOT NULL,
  `transaction_kpi_type_id` INT NOT NULL,
  `operation_id` INT NOT NULL,
  `min_threshold` DOUBLE NOT NULL,
  `max_threshold` DOUBLE NOT NULL,
  `response_time_type_id` INT NULL,
  `start_time` DATETIME NOT NULL,
  `end_time` DATETIME NULL,
  `coverage_window_profile_id` INT NOT NULL,
  `account_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_application_threshold_details_3`
    FOREIGN KEY (`application_id`)
    REFERENCES `appsone`.`controller` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_application_threshold_details_4`
    FOREIGN KEY (`transaction_kpi_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_application_threshold_details_5`
    FOREIGN KEY (`operation_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_application_threshold_details_7`
    FOREIGN KEY (`response_time_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`component_kpi_threshold_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`component_kpi_threshold_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`component_kpi_threshold_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `mst_component_id` INT NOT NULL,
  `kpi_id` INT NOT NULL,
  `operation_id` INT NOT NULL,
  `min_threshold` FLOAT NOT NULL,
  `max_threshold` FLOAT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `kpi_group_id` INT NULL,
  `kpi_group_value` VARCHAR(512) NULL,
  `coverage_window_profile_id` INT NOT NULL,
  `start_time` DATETIME NOT NULL,
  `end_time` DATETIME NULL,
  `mst_common_version_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_component_kpi_threshold_3`
    FOREIGN KEY (`mst_component_id`)
    REFERENCES `appsone`.`mst_component` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_component_kpi_threshold_4`
    FOREIGN KEY (`kpi_id`)
    REFERENCES `appsone`.`mst_kpi_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_component_kpi_threshold_details_1`
    FOREIGN KEY (`mst_common_version_id`)
    REFERENCES `appsone`.`mst_common_version` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;




-- -----------------------------------------------------
-- Table `appsone`.`file_upload_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`file_upload_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`file_upload_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `file_name` VARCHAR(64) NOT NULL,
  `file_size` MEDIUMTEXT NOT NULL,
  `checksum` VARCHAR(64) NOT NULL,
  `file_location` VARCHAR(256) NOT NULL,
  `upload_by` VARCHAR(64) NOT NULL,
  `upload_time` DATETIME NOT NULL,
  `account_id` INT NOT NULL,
  `is_processing` TINYINT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_file_upload_details_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;




-- -----------------------------------------------------
-- Table `appsone`.`file_processed_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`file_processed_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`file_processed_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `file_name` VARCHAR(64) NOT NULL,
  `file_size` MEDIUMTEXT NOT NULL,
  `checksum` VARCHAR(64) NOT NULL,
  `file_location` VARCHAR(256) NOT NULL,
  `upload_by` VARCHAR(64) NOT NULL,
  `upload_time` DATETIME NOT NULL,
  `processed_by` VARCHAR(64) NOT NULL,
  `start_time` DATETIME NOT NULL,
  `end_time` DATETIME NULL,
  `status` VARCHAR(32) NOT NULL,
  `account_id` INT NOT NULL,
  `progress` VARCHAR(32) NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_file_processed_details_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;



-- -----------------------------------------------------
-- Table `appsone`.`file_summary_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`file_summary_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`file_summary_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `file_processed_id` INT NOT NULL,
  `key` VARCHAR(64) NOT NULL,
  `value` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NULL,
  `account_id` INT NOT NULL,
  `is_debug_logs` TINYINT NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_file_summary_details_1`
    FOREIGN KEY (`file_processed_id`)
    REFERENCES `appsone`.`file_processed_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;



-- -----------------------------------------------------
-- Table `appsone`.`flow_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`flow_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`flow_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `account_id` INT NOT NULL,
  `flow_name` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(64) NOT NULL,
  `identifier` VARCHAR(128) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_flow_details_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;




-- -----------------------------------------------------
-- Table `appsone`.`step_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`step_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`step_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `sequence_number` INT NOT NULL,
  `account_id` INT NOT NULL,
  `flow_id` INT NOT NULL,
  `user_details_id` VARCHAR(64) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `identifier` VARCHAR(64) NOT NULL,
  `monitor_enabled` TINYINT NOT NULL,
  `trace_enabled` TINYINT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_step_details_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_step_details_2`
    FOREIGN KEY (`flow_id`)
    REFERENCES `appsone`.`flow_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;



-- -----------------------------------------------------
-- Table `appsone`.`step_connection_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`step_connection_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`step_connection_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `source_id` INT NULL,
  `destination_id` INT NULL,
  `user_details_id` VARCHAR(64) NOT NULL,
  `account_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `is_discovery` TINYINT NOT NULL DEFAULT 0,
  `flow_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_step_connection_details_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`step_threshold_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`step_threshold_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`step_threshold_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `flow_id` INT NOT NULL,
  `kpi_name` VARCHAR(64) NOT NULL,
  `operation_id` INT NOT NULL,
  `min_threshold` FLOAT NOT NULL,
  `max_threshold` FLOAT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `coverage_window_profile_id` INT NOT NULL,
  `start_time` DATETIME NOT NULL,
  `end_time` DATETIME NULL,
  `step_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_step_threshold_details_2`
    FOREIGN KEY (`flow_id`)
    REFERENCES `appsone`.`flow_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_step_threshold_details_1`
    FOREIGN KEY (`step_id`)
    REFERENCES `appsone`.`step_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;




-- -----------------------------------------------------
-- Table `appsone`.`flow_segments`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`flow_segments` ;

CREATE TABLE IF NOT EXISTS `appsone`.`flow_segments` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `flow_id` INT NOT NULL,
  `segment_name` VARCHAR(64) NOT NULL,
  `attribute_lookup_name` VARCHAR(64) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(64) NOT NULL,
  `is_default_enabled` TINYINT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_flow_segments_1`
    FOREIGN KEY (`flow_id`)
    REFERENCES `appsone`.`flow_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;



-- -----------------------------------------------------
-- Table `appsone`.`flow_segment_lookup`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`flow_segment_lookup` ;

CREATE TABLE IF NOT EXISTS `appsone`.`flow_segment_lookup` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `flow_id` INT NOT NULL,
  `attribute_lookup_name` VARCHAR(64) NOT NULL,
  `data_value` VARCHAR(128) NOT NULL,
  `display_value` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_flow_segment_lookup_1`
    FOREIGN KEY (`flow_id`)
    REFERENCES `appsone`.`flow_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`step_stitch_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`step_stitch_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`step_stitch_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `step_id` INT NOT NULL,
  `stitch_from` VARCHAR(64) NOT NULL,
  `stitch_field` VARCHAR(64) NOT NULL,
  `user_details_id` VARCHAR(64) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `flow_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_step_stitch_details_1`
    FOREIGN KEY (`step_id`)
    REFERENCES `appsone`.`step_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_step_stitch_details_2`
    FOREIGN KEY (`flow_id`)
    REFERENCES `appsone`.`flow_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB DEFAULT CHARSET=utf8;




-- -----------------------------------------------------
-- Table `appsone`.`flow_threshold_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`flow_threshold_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`flow_threshold_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `flow_id` INT NOT NULL,
  `kpi_name` VARCHAR(64) NOT NULL,
  `operation_id` INT NOT NULL,
  `min_threshold` FLOAT NOT NULL,
  `max_threshold` FLOAT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `coverage_window_profile_id` INT NOT NULL,
  `start_time` DATETIME NOT NULL,
  `end_time` DATETIME NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_flow_threshold_details_1`
    FOREIGN KEY (`flow_id`)
    REFERENCES `appsone`.`flow_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;



-- -----------------------------------------------------
-- Table `appsone`.`step_threshold_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`step_threshold_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`step_threshold_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `flow_id` INT NOT NULL,
  `kpi_name` VARCHAR(64) NOT NULL,
  `operation_id` INT NOT NULL,
  `min_threshold` FLOAT NOT NULL,
  `max_threshold` FLOAT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `coverage_window_profile_id` INT NOT NULL,
  `start_time` DATETIME NOT NULL,
  `end_time` DATETIME NULL,
  `step_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_step_threshold_details_2`
    FOREIGN KEY (`flow_id`)
    REFERENCES `appsone`.`flow_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_step_threshold_details_1`
    FOREIGN KEY (`step_id`)
    REFERENCES `appsone`.`step_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;




-- -----------------------------------------------------
-- Table `appsone`.`flow_segment_threshold_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`flow_segment_threshold_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`flow_segment_threshold_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `flow_id` INT NOT NULL,
  `kpi_name` VARCHAR(64) NOT NULL,
  `operation_id` INT NOT NULL,
  `min_threshold` FLOAT NOT NULL,
  `max_threshold` FLOAT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `coverage_window_profile_id` INT NOT NULL,
  `start_time` DATETIME NOT NULL,
  `end_time` DATETIME NULL,
  `segment_name` VARCHAR(64) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_flow_segment_threshold_details_2`
    FOREIGN KEY (`flow_id`)
    REFERENCES `appsone`.`flow_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;



-- -----------------------------------------------------
-- Table `appsone`.`kiaros_data_store_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`kiaros_data_store_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`kiaros_data_store_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `field` VARCHAR(64) NOT NULL,
  `store` VARCHAR(64) NOT NULL,
  `type` VARCHAR(64) NOT NULL,
  `display_name` VARCHAR(64) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(64) NOT NULL,
  `is_spend_keystore` TINYINT NOT NULL,
  `flow_id` INT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`rules`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`rules` ;

CREATE TABLE IF NOT EXISTS `appsone`.`rules` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `account_id` INT NOT NULL,
  `is_enabled` TINYINT NOT NULL,
  `order` INT NOT NULL,
  `name` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(64) NOT NULL,
  `rule_type_id` INT NOT NULL,
  `is_default` TINYINT NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_rules_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_rules_2`
    FOREIGN KEY (`rule_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_rules_1_idx` ON `appsone`.`rules` (`account_id` ASC);

CREATE INDEX `fk_rules_2_idx` ON `appsone`.`rules` (`rule_type_id` ASC);


-- -----------------------------------------------------
-- Table `appsone`.`tcp_patterns`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`tcp_patterns` ;

CREATE TABLE IF NOT EXISTS `appsone`.`tcp_patterns` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `rule_id` INT NOT NULL,
  `account_id` INT NOT NULL,
  `initial_pattern` VARCHAR(256) NOT NULL,
  `length` INT NULL,
  `last_pattern` VARCHAR(256) NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(64) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_tcp_patterns_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_tcp_patterns_2`
    FOREIGN KEY (`rule_id`)
    REFERENCES `appsone`.`rules` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_tcp_patterns_1_idx` ON `appsone`.`tcp_patterns` (`account_id` ASC);

CREATE INDEX `fk_tcp_patterns_2_idx` ON `appsone`.`tcp_patterns` (`rule_id` ASC);


-- -----------------------------------------------------
-- Table `appsone`.`http_patterns`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`http_patterns` ;

CREATE TABLE IF NOT EXISTS `appsone`.`http_patterns` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `rule_id` INT NOT NULL,
  `account_id` INT NOT NULL,
  `http_method_type_id` INT NULL,
  `first_uri_segments` INT NULL,
  `last_uri_segments` INT NULL,
  `complete_uri` TINYINT NULL,
  `payload_type_id` INT NOT NULL,
  `complete_pattern` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(64) NOT NULL,
  `custom_segments` VARCHAR(256) NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_http_patterns_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_http_patterns_2`
    FOREIGN KEY (`rule_id`)
    REFERENCES `appsone`.`rules` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_http_patterns_4`
    FOREIGN KEY (`payload_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_http_patterns_1_idx` ON `appsone`.`http_patterns` (`account_id` ASC);

CREATE INDEX `fk_http_patterns_2_idx` ON `appsone`.`http_patterns` (`rule_id` ASC);

CREATE INDEX `fk_http_patterns_4_idx` ON `appsone`.`http_patterns` (`payload_type_id` ASC);


-- -----------------------------------------------------
-- Table `appsone`.`http_pair_data`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`http_pair_data` ;

CREATE TABLE IF NOT EXISTS `appsone`.`http_pair_data` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `rule_id` INT NOT NULL,
  `http_pattern_id` INT NOT NULL,
  `account_id` INT NOT NULL,
  `pair_type_id` INT NOT NULL,
  `pair_key` VARCHAR(256) NOT NULL,
  `pair_value` VARCHAR(256) NOT NULL DEFAULT '*',
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(64) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_http_pair_data_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_http_pair_data_2`
    FOREIGN KEY (`http_pattern_id`)
    REFERENCES `appsone`.`http_patterns` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_http_pair_data_3`
    FOREIGN KEY (`rule_id`)
    REFERENCES `appsone`.`rules` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_http_pair_data_4`
    FOREIGN KEY (`pair_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_http_pair_data_1_idx` ON `appsone`.`http_pair_data` (`account_id` ASC);

CREATE INDEX `fk_http_pair_data_2_idx` ON `appsone`.`http_pair_data` (`http_pattern_id` ASC);

CREATE INDEX `fk_http_pair_data_3_idx` ON `appsone`.`http_pair_data` (`rule_id` ASC);

CREATE INDEX `fk_http_pair_data_4_idx` ON `appsone`.`http_pair_data` (`pair_type_id` ASC);


-- -----------------------------------------------------
-- Table `appsone`.`mst_date_component_data`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`mst_date_component_data` ;

CREATE TABLE IF NOT EXISTS `appsone`.`mst_date_component_data` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(64) NOT NULL,
  `type` VARCHAR(64) NOT NULL,
  `value` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(64) NOT NULL,
  `collation_values` INT NOT NULL,
  `data_points` INT NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`mst_features`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`mst_features` ;

CREATE TABLE IF NOT EXISTS `appsone`.`mst_features` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(64) NOT NULL,
  `display_name` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(64) NOT NULL,
  `is_enabled` TINYINT NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`mst_refresh_conf_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`mst_refresh_conf_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`mst_refresh_conf_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `page_name` VARCHAR(256) NOT NULL,
  `refresh_enabled` TINYINT NOT NULL,
  `refresh_in_secs` INT NOT NULL DEFAULT 0,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`mst_jim_exit_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`mst_jim_exit_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`mst_jim_exit_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `jim_exit_type_id` INT NOT NULL,
  `param_display_name` VARCHAR(128) NOT NULL,
  `param_name` VARCHAR(128) NOT NULL,
  `param_data_type` VARCHAR(32) NOT NULL,
  `is_mandatory` TINYINT NOT NULL DEFAULT 0,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_jim_exit_details_1`
    FOREIGN KEY (`jim_exit_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_mst_jim_exit_details_1_idx` ON `appsone`.`mst_jim_exit_details` (`jim_exit_type_id` ASC);


-- -----------------------------------------------------
-- Table `appsone`.`mst_category_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`mst_category_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`mst_category_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `account_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(128) NOT NULL,
  `identifier` VARCHAR(128) NOT NULL,
  `status` TINYINT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_category_details_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;



-- -----------------------------------------------------
-- Table `appsone`.`forensics`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`forensics` ;

CREATE TABLE IF NOT EXISTS `appsone`.`forensics` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `standard_type_id` INT NOT NULL,
  `account_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(128) NOT NULL,
  `agent_type_id` INT NOT NULL,
  `identifier` VARCHAR(128) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_forensics_1`
    FOREIGN KEY (`standard_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`forensic_category_mapping`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`forensic_category_mapping` ;

CREATE TABLE IF NOT EXISTS `appsone`.`forensic_category_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `category_id` INT NOT NULL,
  `forensic_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(128) NOT NULL,
  `time_window_in_secs` INT NOT NULL DEFAULT 30,
  `forensic_exec_type_id` INT NOT NULL,
  `download_type_id` INT NOT NULL,
  `retries` INT NOT NULL,
  `ttl_in_secs` INT NOT NULL,
  `object_id` INT NOT NULL,
  `object_ref_table` VARCHAR(64) NOT NULL,
  `command_exec_type_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_forensic_category_mapping_1`
    FOREIGN KEY (`forensic_id`)
    REFERENCES `appsone`.`forensics` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_forensic_category_mapping_2`
    FOREIGN KEY (`category_id`)
    REFERENCES `appsone`.`mst_category_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_forensic_category_mapping_3`
    FOREIGN KEY (`forensic_exec_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_forensic_category_mapping_4`
    FOREIGN KEY (`download_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`forensic_http_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`forensic_http_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`forensic_http_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `forensic_category_id` INT NOT NULL,
  `url` VARCHAR(128) NOT NULL,
  `http_method` VARCHAR(128) NOT NULL,
  `protocol` varchar(64) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(128) NOT NULL,
  `time_out_in_mins` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_forensic_http_details_1`
    FOREIGN KEY (`forensic_category_id`)
    REFERENCES `appsone`.`forensic_category_mapping` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_forensic_http_details_1_idx` ON `appsone`.`forensic_http_details` (`forensic_category_id` ASC);


-- -----------------------------------------------------
-- Table `appsone`.`command_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`command_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`command_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `identifier` VARCHAR(128) NOT NULL,
  `command_type_id` INT NOT NULL,
  `command_name` VARCHAR(128) NOT NULL,
  `timeout_in_secs` INT NOT NULL,
  `output_type_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(128) NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`command_arguments`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`command_arguments` ;

CREATE TABLE IF NOT EXISTS `appsone`.`command_arguments` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `command_id` INT NOT NULL,
  `argument_key` VARCHAR(64) NOT NULL,
  `argument_value` VARCHAR(128) NOT NULL,
  `default_value` VARCHAR(128) NULL,
  `argument_value_type_id` INT NOT NULL,
  `argument_type_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `is_placeholder` TINYINT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_command_arguments_1`
    FOREIGN KEY (`command_id`)
    REFERENCES `appsone`.`command_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_command_arguments_2`
    FOREIGN KEY (`argument_value_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_command_arguments_3`
    FOREIGN KEY (`argument_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;



-- -----------------------------------------------------
-- Table `appsone`.`supervisor_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`supervisor_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`supervisor_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `host_box_name` VARCHAR(64) NULL,
  `identifier` VARCHAR(128) NOT NULL,
  `supervisor_type_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(128) NOT NULL,
  `version` VARCHAR(32) NOT NULL,
  `host_address` VARCHAR(64) NOT NULL,
  `status` VARCHAR(32) NULL,
  `account_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_supervisor_details_1`
    FOREIGN KEY (`supervisor_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_supervisor_details_2`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;



-- -----------------------------------------------------
-- Table `appsone`.`command_audit_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`command_audit_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`command_audit_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `supervisor_identifier` VARCHAR(128) NOT NULL,
  `agent_identifier` VARCHAR(128) NOT NULL,
  `command_timeout_in_secs` INT NOT NULL,
  `retries` INT NOT NULL,
  `trigger_time` DATETIME NOT NULL,
  `command_complete_time` DATETIME NOT NULL,
  `exit_status` VARCHAR(64) NOT NULL,
  `trigger_source` VARCHAR(64) NOT NULL,
  `user_details_id` VARCHAR(64) NOT NULL,
  `supr_ctrl_ttl_in_secs` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `violation_time` DATETIME NOT NULL,
  `command_job_id` VARCHAR(128) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_command_audit_details_3`
    FOREIGN KEY (`agent_identifier`)
    REFERENCES `appsone`.`agent` (`unique_token`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;



-- -----------------------------------------------------
-- Table `appsone`.`command_metadata_audit_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`command_metadata_audit_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`command_metadata_audit_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `command_audit_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `key` VARCHAR(256) NOT NULL,
  `value` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_command_metadata_audit_details_1`
    FOREIGN KEY (`command_audit_id`)
    REFERENCES `appsone`.`command_audit_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;



-- -----------------------------------------------------
-- Table `appsone`.`mst_jim_exit_type_mapping`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`mst_jim_exit_type_mapping` ;

CREATE TABLE IF NOT EXISTS `appsone`.`mst_jim_exit_type_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `mst_jim_exit_type_id` INT NOT NULL,
  `framework_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(128) NOT NULL,
  `account_id` INT NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`mst_error_codes`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`mst_error_codes` ;

CREATE TABLE IF NOT EXISTS `appsone`.`mst_error_codes` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `a1_component_type_id` INT NOT NULL,
  `error_code` VARCHAR(64) NOT NULL,
  `error_message` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(128) NOT NULL,
  `error_status` VARCHAR(64) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_error_codes_1`
    FOREIGN KEY (`a1_component_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `service_kpi_thresholds`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `service_kpi_thresholds` ;

CREATE TABLE IF NOT EXISTS `service_kpi_thresholds` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `account_id` INT NOT NULL,
  `service_id` INT NOT NULL,
  `kpi_id` INT NOT NULL,
  `applicable_to` VARCHAR(64) NOT NULL,
  `operation_type_id` INT NOT NULL,
  `min_threshold` VARCHAR(128) NULL,
  `max_threshold` VARCHAR(128) NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `kpi_attribute` VARCHAR(256) NOT NULL DEFAULT 'ALL',
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_service_kpi_thresholds_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_service_kpi_thresholds_2`
    FOREIGN KEY (`service_id`)
    REFERENCES `controller` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_service_kpi_thresholds_3`
    FOREIGN KEY (`kpi_id`)
    REFERENCES `mst_kpi_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_service_kpi_thresholds_4`
    FOREIGN KEY (`operation_type_id`)
    REFERENCES `mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;



-- -----------------------------------------------------
-- Table `event_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `event_details` ;

CREATE TABLE IF NOT EXISTS `event_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `transaction_id` INT NOT NULL,
  `event_type` VARCHAR(64) NOT NULL,
  `stitch_field` VARCHAR(128) NULL,
  `name` VARCHAR(128) NOT NULL,
  `identifier` VARCHAR(256) NOT NULL,
  `status` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_event_details_1`
    FOREIGN KEY (`transaction_id`)
    REFERENCES `transaction` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;



-- -----------------------------------------------------
-- Table `transaction_attributes`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `transaction_attributes` ;

CREATE TABLE IF NOT EXISTS `transaction_attributes` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `transaction_id` INT NOT NULL,
  `name` VARCHAR(128) NOT NULL,
  `identifier` VARCHAR(256) NOT NULL,
  `extraction_field` VARCHAR(128) NOT NULL,
  `is_segment` TINYINT NOT NULL,
  `is_business_status` TINYINT NOT NULL,
  `is_technical_status` TINYINT NOT NULL,
  `is_business_value` TINYINT NOT NULL,
  `is_search_index` TINYINT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `status` TINYINT NOT NULL,
  `descriptor` VARCHAR(128) NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_transaction_attributes_1`
    FOREIGN KEY (`transaction_id`)
    REFERENCES `transaction` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;




-- -----------------------------------------------------
-- Table `transaction_attribute_data`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `transaction_attribute_data` ;

CREATE TABLE IF NOT EXISTS `transaction_attribute_data` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `transaction_id` INT NOT NULL,
  `transaction_attribute_id` INT NOT NULL,
  `descriptor` VARCHAR(128) NOT NULL,
  `value` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_transaction_attribute_data_1`
    FOREIGN KEY (`transaction_id`)
    REFERENCES `transaction` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_transaction_attribute_data_2`
    FOREIGN KEY (`transaction_attribute_id`)
    REFERENCES `transaction_attributes` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;




-- -----------------------------------------------------
-- Table `email_templates`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `email_templates` ;

CREATE TABLE IF NOT EXISTS `email_templates` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `account_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `update_time` DATETIME NOT NULL,
  `subject` VARCHAR(1024) NOT NULL,
  `body` VARCHAR(4096) NOT NULL,
  `status` TINYINT NOT NULL DEFAULT 1,
  `name` VARCHAR(64) NOT NULL,
  `template_type_id` INT NOT NULL,
  `to_address` VARCHAR(516) NULL,
  `cc_address` VARCHAR(516) NULL,
  `bcc_address` VARCHAR(516) NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_email_templates_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_email_templates_2`
    FOREIGN KEY (`template_type_id`)
    REFERENCES `mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `sms_templates`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `sms_templates` ;

CREATE TABLE IF NOT EXISTS `sms_templates` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `account_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `sms_content` VARCHAR(512) NOT NULL,
  `status` TINYINT NOT NULL,
  `name` VARCHAR(64) NOT NULL,
  `template_type_id` INT NOT NULL,
  `mobile_numbers` VARCHAR(512) NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_sms_templates_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_sms_templates_2`
    FOREIGN KEY (`template_type_id`)
    REFERENCES `mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`service_configurations`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`service_configurations` ;

CREATE TABLE IF NOT EXISTS `appsone`.`service_configurations` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `service_id` INT NOT NULL,
  `account_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `start_collection_interval` INT NOT NULL,
  `end_collection_interval` INT NOT NULL,
  `sor_persistence` INT NOT NULL,
  `sor_suppression` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_service_configurations_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_service_configurations_2`
    FOREIGN KEY (`service_id`)
    REFERENCES `appsone`.`controller` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone`.`smtp_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`smtp_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`smtp_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `address` VARCHAR(256) NOT NULL,
  `port` INT NOT NULL,
  `username` VARCHAR(128) NOT NULL,
  `password` VARCHAR(128) NOT NULL,
  `security_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `account_id` INT NOT NULL,
  `from_recipient` VARCHAR(256) NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_smtp_details_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_smtp_details_2`
    FOREIGN KEY (`security_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`sms_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`sms_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`sms_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `address` VARCHAR(128) NOT NULL,
  `port` INT NOT NULL,
  `country_code` VARCHAR(32) NULL,
  `protocol_id` INT NOT NULL,
  `http_method` VARCHAR(16) NULL,
  `http_relative_url` VARCHAR(512) NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `account_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `post_data` MEDIUMTEXT NULL,
  `post_data_flag` TINYINT(1) NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_sms_details_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_sms_details_2`
    FOREIGN KEY (`protocol_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`sms_parameters`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`sms_parameters` ;

CREATE TABLE IF NOT EXISTS `appsone`.`sms_parameters` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `parameter_name` VARCHAR(128) NOT NULL,
  `parameter_value` VARCHAR(128) NOT NULL,
  `sms_details_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `parameter_type_id` INT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `default_value` VARCHAR(128) NULL,
  `is_placeholder` TINYINT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_sms_parameters_1`
    FOREIGN KEY (`sms_details_id`)
    REFERENCES `appsone`.`sms_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

DROP TABLE IF EXISTS `appsone`.`agent_mode_configuration`;
CREATE TABLE `appsone`.`agent_mode_configuration` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `service_id` INT NOT NULL,
  `agent_type_id` INT NOT NULL,
  `command_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `jvm_mem_util` VARCHAR(64) NOT NULL,
  `jvm_cpu_util` VARCHAR(64) NOT NULL,
  `auto_snapshot_for_failures` TINYINT NOT NULL,
  `account_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  INDEX `fk_agent_mode_configuration_1_idx` (`account_id` ASC),
  INDEX `fk_agent_mode_configuration_2_idx` (`service_id` ASC),
  INDEX `fk_agent_mode_configuration_3_idx` (`agent_type_id` ASC),
  INDEX `fk_agent_mode_configuration_4_idx` (`command_id` ASC),
  CONSTRAINT `fk_agent_mode_configuration_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_agent_mode_configuration_2`
    FOREIGN KEY (`service_id`)
    REFERENCES `controller` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_agent_mode_configuration_3`
    FOREIGN KEY (`agent_type_id`)
    REFERENCES `mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_agent_mode_configuration_4`
    FOREIGN KEY (`command_id`)
    REFERENCES `command_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION);

DROP TABLE IF EXISTS `appsone`.`snapshot_levels`;
CREATE TABLE `appsone`.`snapshot_levels` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `identifier` VARCHAR(128) NOT NULL,
  `command_mode` VARCHAR(64) NOT NULL,
  `duration_in_mins` INT NOT NULL,
  `no_of_snapshots` INT NOT NULL,
  `silent_window_in_minis` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`));

DROP TABLE IF EXISTS `appsone`.`mst_producer_attributes`;
CREATE TABLE `appsone`.`mst_producer_attributes` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `mst_producer_type_id` INT NOT NULL,
  `attribute_name` VARCHAR(64) NOT NULL,
  `is_custom` VARCHAR(64) NOT NULL,
  `status` VARCHAR(64) NOT NULL,
  `is_mandatory` TINYINT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(128) NOT NULL,
  `default_value` VARCHAR(256) NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_producer_attributes_mst_producer_type`
    FOREIGN KEY (`mst_producer_type_id`)
    REFERENCES `appsone`.`mst_producer_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION);
-- All types and subtypes(view_types)
DROP VIEW if exists view_types;
CREATE VIEW view_types AS
select t.type, t.id typeid, st.name, st.id subtypeid
from mst_type t, mst_sub_type st
where t.id = st.mst_type_id;

-- All component, component type, component version(view_components)
DROP VIEW if exists view_components;
CREATE VIEW view_components AS
select ct.id component_type_id,ct.name component_type_name, ct.status component_type_status, c.id component_id, c.name component_name,
c.is_custom, c.status component_status, cv.id common_version_id, cv.name common_version_name, v.id component_version_id,
v.name component_version_name, v.is_custom is_custom_version, v.status is_version_status, v.version_from, v.version_to
from mst_component_type ct, mst_component c, mst_component_mapping cm,
mst_common_version cv, mst_component_version v
where ct.id = cm.mst_component_type_id and cm.mst_component_id = c.id and c.id = v.mst_component_id
and v.mst_common_version_id=cv.id;

-- All component attributes for each component common version.(view_attributes)
DROP VIEW if exists view_attributes;
CREATE VIEW view_attributes AS
select cam.id mst_component_attribute_mapping_id, cv.id mst_common_version_id, cv.mst_component_id, cv.name mst_common_name, ca.id attribute_id, ca.attribute_name,
ca.is_custom, ca.status, cam.is_mandatory, cam.default_value, cam.is_ui_visible
from mst_common_attributes ca, mst_component_attribute_mapping cam,
mst_common_version cv
where ca.id = cam.mst_common_attributes_id and cam.mst_common_version_id = cv.id;


-- All kpi group and kpis of each group.(view_kpi_groups)
DROP VIEW if exists view_kpi_groups;
CREATE VIEW view_kpi_groups AS
select g.id groupid,g.name groupname,g.alias_name groupaliasname, g.discovery, g.is_custom, g.status groupstatus, k.id kpiid,
k.name kpiname, k.alias_name, k.value_type, g.account_id, k.status kpistatus, st.name kpitype, k.measure_units, k.cluster_operation,
k.data_type,k.rollup_operation,k.cluster_aggregation_type,k.instance_aggregation_type
from mst_kpi_group g, mst_kpi_details k, mst_sub_type st
where k.kpi_group_id = g.id and g.kpi_type_id = st.id;


-- All kpis without the group.(view_kpis)
DROP VIEW if exists view_kpis;
CREATE VIEW view_kpis AS
select k.id kpiid,
k.name kpi_name, k.alias_name, k.value_type, k.account_id, k.status kpistatus,
st.name kpi_type, k.is_custom, k.measure_units, k.cluster_operation, k.data_type,k.rollup_operation,
k.cluster_aggregation_type,k.instance_aggregation_type
from mst_kpi_details k, mst_sub_type st
where k.kpi_group_id = 0 and k.kpi_type_id = st.id;


-- All kpis.(view_all_kpis)
DROP VIEW if exists view_all_kpis;
CREATE VIEW view_all_kpis AS
select st.name kpi_type, g.id group_id,g.name group_name,g.alias_name group_alias_name, g.discovery, g.is_custom, g.status group_status, k.id kpiid,
k.name kpi_name, k.alias_name, k.value_type, g.account_id, k.status kpi_status, k.measure_units, k.cluster_operation,
k.data_type,k.rollup_operation,k.cluster_aggregation_type,k.instance_aggregation_type
from mst_kpi_group g right join mst_kpi_details k on k.kpi_group_id = g.id, mst_sub_type st where st.id = k.kpi_type_id;


-- All common version component and kpis.(view_common_version_kpis)
DROP VIEW if exists view_common_version_kpis;
CREATE VIEW view_common_version_kpis AS
select cv.mst_component_id, k.id kpi_id, k.name kpi_name, k.alias_name kpi_alias_name,
m.mst_common_version_id, k.kpi_group_id,k.kpi_type_id,
m.default_collection_interval, m.status, m.default_operation_id, m.default_threshold, k.account_id,
m.do_analytics
from mst_component_version_kpi_mapping m, mst_common_version cv,
mst_kpi_details k
where m.mst_common_version_id = cv.id and m.mst_kpi_details_id = k.id;

-- All producer details.(view_producers)
DROP VIEW if exists view_producers;
CREATE VIEW view_producers AS
select p.id producer_id, p.name producer_name, p.is_custom, p.status,
p.account_id, p.is_kpi_group, st.name kpi_type, pt.type producer_type, pt.classname
from mst_producer p, mst_sub_type st, mst_producer_type pt
where st.id = p.mst_sub_type_id and pt.id= p.mst_producer_type_id;


-- All producer and kpi details.(view_producer_kpis)
DROP VIEW if exists view_producer_kpis;
CREATE VIEW view_producer_kpis AS
select p.id producer_id, p.name producer_name, p.is_custom, p.status,
p.account_id, p.is_kpi_group, st.name kpi_type, pt.type producer_type, pt.classname, pkm.is_default,
pkm.mst_kpi_details_id, pkm.mst_component_version_id, k.name kpi_name, pkm.mst_component_id,
pkm.mst_component_type_id, pkm.id mst_producer_kpi_mapping_id
from mst_producer p, mst_sub_type st, mst_producer_type pt,
mst_producer_kpi_mapping pkm, mst_kpi_details k
where st.id = p.mst_sub_type_id and pt.id= p.mst_producer_type_id and pkm.producer_id = p.id
and pkm.mst_kpi_details_id = k.id;


-- All component instance.(view_component_instance)
DROP VIEW if exists view_component_instance;
CREATE VIEW view_component_instance AS
select c.id, c.name, c.status, c.identifier,
c.host_id,
(select name from comp_instance where id=c.host_id) host_name,
c.is_cluster,
(case when c.is_cluster = 0 then (select cluster_id from component_cluster_mapping where comp_instance_id=c.id and account_id=c.account_id) else null end) as cluster_id,
(case when c.is_cluster = 0 then (select ci.name from component_cluster_mapping ccm, comp_instance ci where ccm.comp_instance_id=c.id and ccm.cluster_id= ci.id and ccm.account_id=ci.account_id) else null end) as cluster_name,
(case when c.is_cluster = 0 then (select ci.identifier from component_cluster_mapping ccm, comp_instance ci where ccm.comp_instance_id=c.id and ccm.cluster_id= ci.id and ccm.account_id=ci.account_id) else null end) as cluster_identifier,
c.account_id,
c.user_details_id, c.discovery, c.host_address, c.mst_component_id, vc.component_name,
c.mst_component_type_id, vc.component_type_name, c.mst_component_version_id, vc.component_version_name,
vc.common_version_id, vc.common_version_name, c.created_time, c.updated_time
from comp_instance c, view_components vc
where c.mst_component_id=vc.component_id and c.mst_component_type_id=vc.component_type_id
and c.mst_component_version_id = vc.component_version_id;

-- All coverage window details.(view_coverage_window_profile_details)
DROP VIEW if exists view_coverage_window_profile_details;
CREATE VIEW view_coverage_window_profile_details AS
select cwp.id profile_id, cwp.name profile_name, cwp.day_option_id,st.name day_option_name, cwp.status, cwp.account_id, cw.day, cw.start_hour, cw.start_minute, cw.end_hour, cw.end_minute,cw.is_business_hour from coverage_window cw, coverage_window_profile cwp, mst_sub_type st where cw.coverage_window_profile_id=cwp.id and st.id = cwp.day_option_id;

-- All kpi with category mapping.
DROP VIEW if exists view_kpi_category_details;
CREATE VIEW view_kpi_category_details AS
select object_id kpi_id, c.id category_id, c.name, c.identifier category_identifier
from tag_mapping tm, mst_kpi_details kp, mst_category_details c
where tm.object_id = kp.id and tm.object_ref_table='mst_kpi_details' and tm.tag_key = c.id and c.status=1;


-- All component instance attributes data
DROP VIEW if exists view_comp_instance_attributes;
CREATE VIEW view_comp_instance_attributes AS
select c.id,c.attribute_value attributeValue,c.comp_instance_id compInstanceId,
c.mst_component_attribute_mapping_id mstComponentAttributeMappingId,
c.created_time createdTime,c.updated_time updatedTime,c.user_details_id userDetailsId,
c.mst_common_attributes_id mstCommonAttributesId,c.attribute_name attributeName,
m.is_mandatory isMandatory, a.name displayName,m.is_ui_visible as isUiVisible
from comp_instance_attribute_values c, mst_component_attribute_mapping m,
mst_common_attributes a
where
c.mst_common_attributes_id = m.mst_common_attributes_id and
c.mst_component_attribute_mapping_id = m.id
and m.mst_common_attributes_id = a.id;

-- All component instance kpis data
DROP VIEW if exists view_comp_instance_kpis;
CREATE VIEW view_comp_instance_kpis AS
select k.kpi_type, k.kpiid, k.alias_name kpi_identifier, k.kpi_name, c.id instance_id,
k.group_name, k.group_id, k.discovery, null attribute_value,c.id comp_instance_id
from comp_instance c, comp_instance_kpi_details ck, view_all_kpis k
where c.id = ck.comp_instance_id and ck.mst_kpi_details_id = k.kpiid
union
select k.kpi_type, k.kpiid, k.alias_name kpi_identifier, k.kpi_name, c.id instance_id,
k.group_name, k.group_id, k.discovery, ck.attribute_value,c.id comp_instance_id
from comp_instance c, comp_instance_kpi_group_details ck, view_all_kpis k
where c.id = ck.comp_instance_id and ck.mst_kpi_details_id = k.kpiid;

DROP VIEW if exists view_application_service_mapping;
CREATE VIEW view_application_service_mapping as SELECT a.id AS application_id, a.name AS application_name,
a.alias_name AS application_identifier, s.id AS service_id, s.name AS service_name,
s.alias_name AS service_identifier, tm.account_id
FROM controller a, tag_mapping tm, controller s
WHERE tm.tag_id = 1 AND a.account_id = tm.account_id AND tm.object_ref_table = 'controller' AND tm.object_id = a.id
AND a.controller_type_id = 191 AND tm.account_id = s.account_id AND tm.tag_key = s.id AND s.controller_type_id = 192;
