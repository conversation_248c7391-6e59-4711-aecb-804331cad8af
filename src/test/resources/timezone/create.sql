-- -----------------------------------------------------
-- Schema appsone
-- -----------------------------------------------------
-- CREATE SCHEMA IF NOT EXISTS `appsone` DEFAULT CHARACTER SET utf8 ;
DROP SCHEMA IF EXISTS `appsone` ;
CREATE SCHEMA `appsone`;
USE `appsone` ;

-- -----------------------------------------------------
-- DROP TABLES
-- -----------------------------------------------------
DROP VIEW if exists view_types;
DROP TABLE IF EXISTS `appsone`.`mst_timezone` ;
DROP TABLE IF EXISTS `appsone`.`mst_roles` ;
DROP TABLE IF EXISTS `appsone`.`mst_access_profiles` ;
DROP TABLE IF EXISTS `appsone`.`user_attributes` ;
DROP TABLE IF EXISTS `appsone`.`account` ;
DROP TABLE IF EXISTS `appsone`.`mst_type`;
DROP TABLE IF EXISTS `appsone`.`mst_sub_type`;
DROP TABLE IF EXISTS `appsone`.`tag_details` ;
DROP TABLE IF EXISTS `appsone`.`tag_mapping` ;
DROP TABLE IF EXISTS `appsone`.`controller` ;

-- -----------------------------------------------------
-- Table `appsone`.`mst_timezone`
-- -----------------------------------------------------

CREATE TABLE `appsone`.`mst_timezone` (
  `id` int NOT NULL AUTO_INCREMENT,
  `time_zone_id` varchar(128) NOT NULL,
  `timeoffset` int NOT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `account_id` int NOT NULL,
  `offset_name` varchar(64) NOT NULL,
  `status` tinyint NOT NULL DEFAULT '0',
  `abbreviation` varchar(128) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `mst_timezone_account1_idx` (`account_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- -----------------------------------------------------
-- Table `appsone`.`mst_roles`
-- -----------------------------------------------------

CREATE TABLE `appsone`.`mst_roles` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(32) NOT NULL,
  `description` varchar(256) DEFAULT NULL,
  `status` tinyint NOT NULL,
  `ui_visible` tinyint NOT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name_UNIQUE` (`name`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`mst_access_profiles`
-- -----------------------------------------------------

CREATE TABLE `appsone`.`mst_access_profiles` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(64) NOT NULL,
  `mst_role_id` int NOT NULL,
  `account_id` int NOT NULL,
  `is_custom` tinyint NOT NULL,
  `status` tinyint NOT NULL,
  `ui_visible` tinyint NOT NULL DEFAULT '1',
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_mst_access_profiles_1_idx` (`mst_role_id`),
  CONSTRAINT `fk_mst_access_profiles_1` FOREIGN KEY (`mst_role_id`) REFERENCES `mst_roles` (`id`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`user_attributes`
-- -----------------------------------------------------

CREATE TABLE `appsone`.`user_attributes` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_identifier` varchar(256) NOT NULL,
  `contact_number` varchar(64) DEFAULT NULL,
  `email_address` varchar(64) DEFAULT NULL,
  `username` varchar(256) NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `status` tinyint NOT NULL,
  `is_timezone_mychoice` tinyint NOT NULL DEFAULT '0',
  `mst_access_profile_id` int NOT NULL,
  `mst_role_id` int NOT NULL,
  `is_notifications_timezone_mychoice` tinyint NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `fk_user_attributes_1_idx` (`mst_access_profile_id`),
  CONSTRAINT `fk_user_attributes_1` FOREIGN KEY (`mst_access_profile_id`) REFERENCES `mst_access_profiles` (`id`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.``account`.`
-- -----------------------------------------------------

CREATE TABLE `appsone`.`account` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(128) NOT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `status` tinyint NOT NULL,
  `private_key` text,
  `public_key` text,
  `user_details_id` varchar(256) NOT NULL,
  `identifier` varchar(128) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `identifier_UNIQUE` (`identifier`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.``mst_type`.`
-- -----------------------------------------------------

CREATE TABLE `appsone`.`mst_type` (
  `id` int NOT NULL AUTO_INCREMENT,
  `type` varchar(45) NOT NULL,
  `description` varchar(128) DEFAULT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `account_id` int NOT NULL,
  `status` tinyint NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `mst_type_account1_idx` (`account_id`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.``mst_sub_type`.`
-- -----------------------------------------------------

CREATE TABLE `appsone`.`mst_sub_type` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(45) NOT NULL,
  `mst_type_id` int NOT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `account_id` int NOT NULL,
  `description` varchar(256) NOT NULL,
  `is_custom` tinyint NOT NULL DEFAULT '0',
  `status` tinyint NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `fk_mst_sub_type_mst_type_idx` (`mst_type_id`),
  KEY `mst_sub_type_account1_idx` (`account_id`),
  CONSTRAINT `fk_mst_sub_type_mst_type` FOREIGN KEY (`mst_type_id`) REFERENCES `mst_type` (`id`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.``view_types`.`
-- -----------------------------------------------------

CREATE VIEW appsone.view_types AS
select t.type, t.id typeid, st.name, st.id subtypeid
from mst_type t, mst_sub_type st
where t.id = st.mst_type_id;

-- -----------------------------------------------------
-- Table `appsone`.`tag_details`
-- -----------------------------------------------------

CREATE TABLE `appsone`.`tag_details` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(128) NOT NULL,
  `tag_type_id` int NOT NULL,
  `is_predefined` tinyint NOT NULL,
  `ref_table` varchar(64) DEFAULT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `account_id` int NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `ref_where_column_name` varchar(128) DEFAULT NULL,
  `ref_select_column_name` varchar(126) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_tag_details_tag_type_id_idx` (`tag_type_id`),
  KEY `fk_tag_details_account_id_idx` (`account_id`),
  CONSTRAINT `fk_tag_details_account_id` FOREIGN KEY (`account_id`) REFERENCES `account` (`id`),
  CONSTRAINT `fk_tag_details_tag_type_id` FOREIGN KEY (`tag_type_id`) REFERENCES `mst_sub_type` (`id`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`tag_mapping`
-- -----------------------------------------------------

CREATE TABLE `appsone`.`tag_mapping` (
  `id` int NOT NULL AUTO_INCREMENT,
  `tag_id` int NOT NULL,
  `object_id` int NOT NULL,
  `object_ref_table` varchar(256) NOT NULL,
  `tag_key` varchar(256) NOT NULL,
  `tag_value` varchar(256) DEFAULT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `account_id` int NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_tag_mapping_account_id_idx` (`account_id`),
  KEY `fk_tag_mapping_tag_id_idx` (`tag_id`),
  CONSTRAINT `fk_tag_mapping_account_id` FOREIGN KEY (`account_id`) REFERENCES `account` (`id`),
  CONSTRAINT `fk_tag_mapping_tag_id` FOREIGN KEY (`tag_id`) REFERENCES `tag_details` (`id`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`controller`
-- -----------------------------------------------------

CREATE TABLE `appsone`.`controller` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(128) NOT NULL,
  `identifier` varchar(128) NOT NULL,
  `account_id` int NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `controller_type_id` int NOT NULL,
  `monitor_enabled` tinyint NOT NULL DEFAULT '1',
  `status` tinyint NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `fk_application_account_id_idx` (`account_id`),
  CONSTRAINT `fk_application_account_id` FOREIGN KEY (`account_id`) REFERENCES `account` (`id`)
) ENGINE=InnoDB;



