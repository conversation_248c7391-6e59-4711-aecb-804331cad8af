INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (1,'(GMT-12:00) International Date Line West',-********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT-12:00',1,'DST - Dateline Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (2,'(GMT-11:00) Midway Island, Samoa',-********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT-11:00',0,'SMO - Samoa Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (3,'(GMT-10:00) Hawaii',-********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT-10:00',0,'HAST - Hawaii-Aleutian Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (4,'(GMT-09:00) Alaska',-********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT-09:00',0,'AKST - Alaska Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (5,'(GMT-08:00) Pacific Time (US and Canada); Tijuana',-********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT-08:00',0,'PST - Pacific Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (6,'(GMT-07:00) Mountain Time (US and Canada)',-********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT-07:00',0,'MST - Mountain Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (7,'(GMT-07:00) Chihuahua, La Paz, Mazatlan',-********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT-07:00',0,'MSTM - Mountain Standard Time (Mexico)');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (8,'(GMT-07:00) Arizona',-********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT-07:00',0,'USMST - US Mountain Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (9,'(GMT-06:00) Central Time (US and Canada',-********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT-06:00',0,'CST - Central Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (10,'(GMT-06:00) Saskatchewan',-********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT-06:00',0,'SSK - Saskatchewan Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (11,'(GMT-06:00) Guadalajara, Mexico City, Monterrey',-********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT-06:00',0,'CSTM - Central Standard Time (Mexico)');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (12,'(GMT-06:00) Central America',-********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT-06:00',0,'CAST - Central America Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (13,'(GMT-05:00) Eastern Time (US and Canada)',-********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT-05:00',0,'EST - Eastern Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (14,'(GMT-05:00) Indiana (East)',-********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT-05:00',0,'USEST - US Eastern Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (15,'(GMT-05:00) Bogota, Lima, Quito',-********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT-05:00',0,'SAPST - SA Pacific Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (16,'(GMT-04:00) Atlantic Time (Canada)',-********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT-04:00',0,'AST - Atlantic Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (17,'(GMT-04:00) Caracas, La Paz',-********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT-04:00',0,'SAWST - SA Western Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (18,'(GMT-04:00) Santiago',-********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT-04:00',0,'PSA - Pacific South America Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (19,'(GMT-03:30) Newfoundland and Labrador',-********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT-03:30',0,'NLT - Newfoundland Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (20,'(GMT-03:00) Brasilia',-********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT-03:00',0,'ESAST - South America Eastern Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (21,'(GMT-03:00) Buenos Aires, Georgetown',-********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT-03:00',0,'ART - Argentina Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (22,'(GMT-03:00) Greenland',-********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT-03:00',0,'CGT - Greenland Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (23,'(GMT-02:00) Mid-Atlantic',-7200000,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT-02:00',0,'MAST - Mid-Atlantic Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (24,'(GMT-01:00) Azores',-3600000,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT-01:00',0,'AZOST - Azores Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (25,'(GMT-01:00) Cape Verde Islands',-3600000,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT-01:00',0,'CVT - Cape Verde Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (26,'(GMT) Greenwich Mean Time: Dublin, Edinburgh, Lisbon, London',0,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT',0,'GMT - Greenwich Mean Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (27,'(GMT) Casablanca, Monrovia',0,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT',0,'MRC - Morocco Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (28,'(GMT+01:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague',3600000,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+01:00',0,'CET - Central Europe Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (29,'(GMT+01:00) Sarajevo, Skopje, Warsaw, Zagreb',3600000,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+01:00',0,'CEST - Central European Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (30,'(GMT+01:00) Brussels, Copenhagen, Madrid, Paris',3600000,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+01:00',0,'ROM - Romance Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (31,'(GMT+01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna',3600000,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+01:00',0,'WET - Western European Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (32,'(GMT+01:00) West Central Africa',3600000,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+01:00',0,'WCAST - Western Central Africa Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (33,'(GMT+02:00) Bucharest',7200000,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+02:00',0,'GTB - GTB Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (34,'(GMT+02:00) Cairo',7200000,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+02:00',0,'EGY - Egypt Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (35,'(GMT+02:00) Helsinki, Kiev, Riga, Sofia, Tallinn, Vilnius',7200000,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+02:00',0,'FLE - FLE Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (36,'(GMT+02:00) Athens, Istanbul, Minsk',7200000,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+02:00',0,'EET - Eastern European Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (37,'(GMT+02:00) Jerusalem',7200000,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+02:00',0,'ISRAEL - Israel Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (38,'(GMT+02:00) Harare, Pretoria',7200000,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+02:00',0,'SAST - South Africa Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (39,'(GMT+03:00) Moscow, St. Petersburg, Volgograd',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+03:00',0,'MSK - Moscow Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (40,'(GMT+03:00) Kuwait, Riyadh',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+03:00',0,'ARAB - Arab Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (41,'(GMT+03:00) Nairobi',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+03:00',0,'EAT - East Africa Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (42,'(GMT+03:00) Baghdad',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+03:00',0,'ARABIC - Arabic Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (43,'(GMT+03:30) Tehran',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+03:30',0,'IRST - Iran Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (44,'(GMT+04:00) Abu Dhabi, Muscat',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+04:00',0,'ARABIA - Arabian Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (45,'(GMT+04:00) Baku, Tbilisi, Yerevan',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+04:00',0,'AZT - Azerbaijan Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (46,'(GMT+04:30) Kabul',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+04:30',0,'AFT - Afghanistan Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (47,'(GMT+05:00) Ekaterinburg',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+05:00',0,'EKB - Ekaterinburg Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (48,'(GMT+05:00) Islamabad, Karachi, Tashkent',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+05:00',0,'PAK - Pakistan Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (49,'(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+05:30',1,'IST - India Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (50,'(GMT+05:45) Kathmandu',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+05:45',0,'NPT - Nepal Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (51,'(GMT+06:00) Astana, Dhaka',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+06:00',0,'CAT - Central Asia Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (52,'(GMT+06:00) Sri Jayawardenepura',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+06:00',0,'SLST - Sri Lanka Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (53,'(GMT+06:00) Almaty, Novosibirsk',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+06:00',0,'NCAST - North Central Asia Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (54,'(GMT+06:30) Yangon Rangoon',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+06:30',0,'MMT - Myanmar Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (55,'(GMT+07:00) Bangkok, Hanoi, Jakarta',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+07:00',0,'SEA - SE Asia Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (56,'(GMT+07:00) Krasnoyarsk',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+07:00',0,'NAST - North Asia Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (57,'(GMT+08:00) Beijing, Chongqing, Hong Kong SAR, Urumqi',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+08:00',0,'CHN - China Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (58,'(GMT+08:00) Kuala Lumpur, Singapore',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+08:00',0,'SST - Singapore Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (59,'(GMT+08:00) Taipei',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+08:00',0,'TWT - Taiwan Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (60,'(GMT+08:00) Perth',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+08:00',0,'AWST - Australian Western Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (61,'(GMT+08:00) Irkutsk, Ulaanbaatar',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+08:00',0,'NAEST - North Asia East Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (62,'(GMT+09:00) Seoul',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+09:00',0,'KST - Korea Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (63,'(GMT+09:00) Osaka, Sapporo, Tokyo',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+09:00',0,'JST - Japan Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (64,'(GMT+09:00) Yakutsk',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+09:00',0,'YAKT - Yakutsk Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (65,'(GMT+09:30) Darwin',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+09:30',0,'ACST - Australian Central Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (66,'(GMT+09:30) Adelaide',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+09:30',0,'ACDT - Australian Central Daylight Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (67,'(GMT+10:00) Canberra, Melbourne, Sydney',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+10:00',0,'AEDT - Australian Eastern Daylight Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (68,'(GMT+10:00) Brisbane',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+10:00',0,'AEST - Australian Eastern Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (69,'(GMT+10:00) Hobart',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+10:00',0,'TSM - Tasmania Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (70,'(GMT+10:00) Vladivostok',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+10:00',0,'VVS - Vladivostok Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (71,'(GMT+10:00) Guam, Port Moresby',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+10:00',0,'WPT - West Pacific Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (72,'(GMT+11:00) Magadan, Solomon Islands, New Caledonia',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+11:00',0,'CPST - Central Pacific Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (73,'(GMT+12:00) Fiji Islands, Kamchatka, Marshall Islands',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+12:00',0,'FJT - Fiji Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (74,'(GMT+12:00) Auckland, Wellington',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+12:00',0,'NZST - New Zealand Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (75,'(GMT+13:00) Nuku alofa',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+13:00',0,'TON - Tonga Standard Time');
INSERT INTO `mst_timezone` (`id`,`time_zone_id`,`timeoffset`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`offset_name`,`status`,`abbreviation`) VALUES (76,'(GMT+14:00) Kiritimati Island',********,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GMT+14:00',0,'LINT - Line Islands Time (Standard Time)');

INSERT INTO `mst_roles` (`id`,`name`,`description`,`status`,`ui_visible`,`created_time`,`updated_time`,`user_details_id`) VALUES (1,'Super Admin','One who initially installs Heal, has complete access to all accounts per installation.',1,0,'2020-07-05 00:00:00','2020-07-05 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO `mst_roles` (`id`,`name`,`description`,`status`,`ui_visible`,`created_time`,`updated_time`,`user_details_id`) VALUES (2,'Admin','One or more individual, having access to one or multiple Big features/application (read/write/edit access).',1,1,'2020-07-05 00:00:00','2020-07-05 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO `mst_roles` (`id`,`name`,`description`,`status`,`ui_visible`,`created_time`,`updated_time`,`user_details_id`) VALUES (3,'User Manager','One or more individual, having access to only Admin_Users section in Control Center.',1,1,'2020-07-05 00:00:00','2020-07-05 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO `mst_roles` (`id`,`name`,`description`,`status`,`ui_visible`,`created_time`,`updated_time`,`user_details_id`) VALUES (4,'User','One or more individual, who has access to Heal dashboard only. Access will be a combination of Big Feature and Application level.',1,1,'2020-07-05 00:00:00','2020-07-05 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1');

INSERT INTO `mst_access_profiles` (`id`,`name`,`mst_role_id`,`account_id`,`is_custom`,`status`,`ui_visible`,`created_time`,`updated_time`,`user_details_id`) VALUES (1,'Super Admin',1,1,0,1,0,'2020-07-05 00:00:00','2020-07-05 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO `mst_access_profiles` (`id`,`name`,`mst_role_id`,`account_id`,`is_custom`,`status`,`ui_visible`,`created_time`,`updated_time`,`user_details_id`) VALUES (2,'Heal Admin',2,1,0,1,1,'2020-07-05 00:00:00','2020-07-05 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO `mst_access_profiles` (`id`,`name`,`mst_role_id`,`account_id`,`is_custom`,`status`,`ui_visible`,`created_time`,`updated_time`,`user_details_id`) VALUES (3,'Application Owner',2,1,0,1,1,'2020-07-05 00:00:00','2020-07-05 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO `mst_access_profiles` (`id`,`name`,`mst_role_id`,`account_id`,`is_custom`,`status`,`ui_visible`,`created_time`,`updated_time`,`user_details_id`) VALUES (4,'User Manager',3,1,0,1,1,'2020-07-05 00:00:00','2020-07-05 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO `mst_access_profiles` (`id`,`name`,`mst_role_id`,`account_id`,`is_custom`,`status`,`ui_visible`,`created_time`,`updated_time`,`user_details_id`) VALUES (5,'Application User',4,1,0,1,1,'2020-07-05 00:00:00','2020-07-05 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1');

INSERT INTO `user_attributes` (`id`,`user_identifier`,`contact_number`,`email_address`,`username`,`user_details_id`,`created_time`,`updated_time`,`status`,`is_timezone_mychoice`,`mst_access_profile_id`,`mst_role_id`,`is_notifications_timezone_mychoice`) VALUES (1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1',NULL,NULL,'appsoneadmin','7640123a-fbde-4fe5-9812-581cd1e3a9c1','2020-07-15 13:53:50','2020-07-15 13:53:50',1,1,1,1,1);

INSERT INTO `account` (`id`,`name`,`created_time`,`updated_time`,`status`,`private_key`,`public_key`,`user_details_id`,`identifier`) VALUES (1,'Global','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'MIIBCQIBADAQBgcqhkjOPQIBBgUrgQQAJwSB8TCB7gIBAQRIAAWoEJHwA9hOkSIOEjRfjhLg+JM5F43Hy8TfQ0YXUIyV/3dMS+JnNf+mBS4RfTsfEb1i2uL6hn7cXU491iuLt54f1BLshQjfoAcGBSuBBAAnoYGVA4GSAAQBPxDDWJdKBdAjgQoC62jaS9YGt4ZIQNjswquEtORgAScGqobCarv5D6UTQuxae/GY4EFR+jXulYFNG96+TF6hNe2oSV/5K6AFzg4uGgoCwXD7PJS1E48Peh85aP7fNiKJaqbjw7LlmkFEAPZlhLBddQVxkj8zBU1aRDKodyviw8v4TCLjhqNRPnm1mIq9qSM=','MIGnMBAGByqGSM49AgEGBSuBBAAnA4GSAAQBPxDDWJdKBdAjgQoC62jaS9YGt4ZIQNjswquEtORgAScGqobCarv5D6UTQuxae/GY4EFR+jXulYFNG96+TF6hNe2oSV/5K6AFzg4uGgoCwXD7PJS1E48Peh85aP7fNiKJaqbjw7LlmkFEAPZlhLBddQVxkj8zBU1aRDKodyviw8v4TCLjhqNRPnm1mIq9qSM=','7640123a-fbde-4fe5-9812-581cd1e3a9c1','e573f852-5057-11e9-8fd2-b37b61e52317');
INSERT INTO `account` (`id`,`name`,`created_time`,`updated_time`,`status`,`private_key`,`public_key`,`user_details_id`,`identifier`) VALUES (2,'INDIA','2020-07-09 11:23:36','2020-07-09 11:23:36',1,'MIIBCQIBADAQBgcqhkjOPQIBBgUrgQQAJwSB8TCB7gIBAQRIAy3YO3JTGhESIDySsQVYDcSaa3UDIi9zqwJflFgAYKSrJSXh/tfbnnLwO9QT7vmuIsmaD2e4z8HtAAeRCn9EuigUUlHIlD71oAcGBSuBBAAnoYGVA4GSAAQD4/FrpQMwRqyqqcuahSXi/8EcJcBoIQ8LMvPi4cknK8d94ICHEdAic3TQ+mLEZrlPkeIFim/McLp3PpVaEvqffjCyp2/VXLsEFgydF0FbeWvxZtGOzL41HtyCXNQhNMuVC0O3sMpKSVZ1A35UQgcakuDQpM9TKEP81ZeEcTqejszE5aGF86ccIhJApryF+44=','MIGnMBAGByqGSM49AgEGBSuBBAAnA4GSAAQD4/FrpQMwRqyqqcuahSXi/8EcJcBoIQ8LMvPi4cknK8d94ICHEdAic3TQ+mLEZrlPkeIFim/McLp3PpVaEvqffjCyp2/VXLsEFgydF0FbeWvxZtGOzL41HtyCXNQhNMuVC0O3sMpKSVZ1A35UQgcakuDQpM9TKEP81ZeEcTqejszE5aGF86ccIhJApryF+44=','7640123a-fbde-4fe5-9812-581cd1e3a9c1','d681ef13-d690-4917-jkhg-6c79b-1');

INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (69,'TagType','Data details of tag types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO `mst_type` (`id`,`type`,`description`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`status`) VALUES (70,'ControllerType','Controller types based on installation','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);

INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (190,'String',69,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Plain text tag type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (191,'Application',70,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Application controller type',0,1);
INSERT INTO `mst_sub_type` (`id`,`name`,`mst_type_id`,`created_time`,`updated_time`,`user_details_id`,`account_id`,`description`,`is_custom`,`status`) VALUES (192,'Services',70,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Service controller type',0,1);

INSERT INTO `controller` (`id`,`name`,`identifier`,`account_id`,`user_details_id`,`created_time`,`updated_time`,`controller_type_id`,`monitor_enabled`,`status`) VALUES (2,'NB-Web-Service','NB-Web-Service',2,'7640123a-fbde-4fe5-9812-581cd1e3a9c1','2020-07-09 05:53:44','2020-07-09 05:53:44',192,1,1);

INSERT INTO `tag_details` (`id`,`name`,`tag_type_id`,`is_predefined`,`ref_table`,`created_time`,`updated_time`,`account_id`,`user_details_id`,`ref_where_column_name`,`ref_select_column_name`) VALUES (2,'Timezone',190,1,'mst_timezone','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1','(time_zone_id = :name or id =:name) and account_id = 1','id tagKey, timeoffset tagValue');

INSERT INTO `tag_mapping` (`id`,`tag_id`,`object_id`,`object_ref_table`,`tag_key`,`tag_value`,`created_time`,`updated_time`,`account_id`,`user_details_id`) VALUES (453,2,2,'account','49','********','2020-07-09 05:53:36','2020-07-09 05:53:36',2,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO `tag_mapping` (`id`,`tag_id`,`object_id`,`object_ref_table`,`tag_key`,`tag_value`,`created_time`,`updated_time`,`account_id`,`user_details_id`) VALUES (1780,2,2,'controller','49','********','2020-08-24 07:29:07','2020-08-24 07:29:07',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO `tag_mapping` (`id`,`tag_id`,`object_id`,`object_ref_table`,`tag_key`,`tag_value`,`created_time`,`updated_time`,`account_id`,`user_details_id`) VALUES (8428,2,2,'controller','49','********','2020-09-23 06:59:05','2020-09-23 06:59:05',2,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO `tag_mapping` (`id`,`tag_id`,`object_id`,`object_ref_table`,`tag_key`,`tag_value`,`created_time`,`updated_time`,`account_id`,`user_details_id`) VALUES (1946,2,1,'user_attributes','49','********','2020-08-25 12:58:12','2020-08-26 12:32:31',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');




