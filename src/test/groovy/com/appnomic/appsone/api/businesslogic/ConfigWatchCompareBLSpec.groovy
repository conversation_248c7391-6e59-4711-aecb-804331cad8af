package com.appnomic.appsone.api.businesslogic

import com.appnomic.appsone.api.beans.UserAccountBean
import com.appnomic.appsone.api.beans.WatcherKpiRawBean
import com.appnomic.appsone.api.common.Constants
import com.appnomic.appsone.api.custom.exceptions.ClientException
import com.appnomic.appsone.api.manager.MySQLConnectionManager
import com.appnomic.appsone.api.pojo.*
import com.appnomic.appsone.api.pojo.request.UtilityBean
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class ConfigWatchCompareBLSpec extends Specification {
    Request request = Spy(Request.class)
    Response response = Spy(Response.class)
    RequestObject requestObject = Spy(RequestObject.class)

    ConfigWatchCompareBL bl = Spy(ConfigWatchCompareBL.class)


    @Shared
    Account account = new Account()

    String AUTH_TOKEN = "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"

    @Shared
    UserAccountBean userAccountBean = new UserAccountBean()

    @Shared
    String oldH2URL

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setDbi(null)

        oldH2URL = MySQLConnectionManager.INSTANCE.getH2url()

        String H2URL = "jdbc:h2:mem:appsone;DB_CLOSE_DELAY=-1;MODE=MySQL;INIT=RUNSCRIPT FROM './src/test/resources/config-watch/create.sql'\\;" +
                "RUNSCRIPT FROM './src/test/resources/config-watch/populate.sql'"

        MySQLConnectionManager.INSTANCE.setH2URL(H2URL)
        println "H2URL: " + MySQLConnectionManager.INSTANCE.getH2url()
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(true)
        MySQLConnectionManager.INSTANCE.getHandle()
        account.accountId = 2
        account.identifier = "d681ef13-d690-4917-jkhg-6c79b-1"
        account.accountName= "INDIA"
        account.updatedBy = "7640123a-fbde-4fe5-9812-581cd1e3a9c1"
        Thread.sleep(5000)
    }

    def cleanupSpec() {
        MySQLConnectionManager.INSTANCE.setDbi(null)
        MySQLConnectionManager.INSTANCE.setH2URL(oldH2URL)
        MySQLConnectionManager.INSTANCE.getHandle()
    }

    def "Client validation: no exception"(){
        setup:
        Map<String, String> headers = new HashMap<>()
        headers.put(Constants.AUTHORIZATION_HEADER, AUTH_TOKEN)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.REQUEST_PARAM_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-12")

        Map<String,String[]> queryParams = new HashMap<>()

        String[] instanceIds = ["[26,27]"]
        String[] time = ["[*************,*************]"]

        queryParams.put(Constants.REQUEST_PARAM_INSTANCE_IDS_LIST, instanceIds)
        queryParams.put(Constants.REQUEST_PARAM_DATE_LIST, time)

        requestObject.setHeaders(headers)
        requestObject.setParams(params)
        requestObject.setQueryParams(queryParams)

        when:
        UtilityBean<ConfigWatchCompareRequest> data = bl.clientValidation(requestObject)

        then:
        noExceptionThrown()
    }

    def "Client validation exception: AUTH_TOKEN"(){
        setup:
        Map<String, String> headers = new HashMap<>()
        headers.put(Constants.AUTHORIZATION_HEADER, null)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.REQUEST_PARAM_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-12")

        Map<String,String[]> queryParams = new HashMap<>()

        String[] instanceIds = ["[26,27]"]
        String[] time = ["[*************,*************]"]

        queryParams.put(Constants.REQUEST_PARAM_INSTANCE_IDS_LIST, instanceIds)
        queryParams.put(Constants.REQUEST_PARAM_DATE_LIST, time)

        requestObject.setHeaders(headers)
        requestObject.setParams(params)
        requestObject.setQueryParams(queryParams)

        when:
        UtilityBean<ConfigWatchCompareRequest> data = bl.clientValidation(requestObject)

        then:
        thrown(ClientException)
    }

    def "Client validation exception: REQUEST_PARAM_IDENTIFIER"(){
        setup:
        Map<String, String> headers = new HashMap<>()
        headers.put(Constants.AUTHORIZATION_HEADER, AUTH_TOKEN)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.REQUEST_PARAM_IDENTIFIER, null)

        Map<String,String[]> queryParams = new HashMap<>()

        String[] instanceIds = ["[26,27]"]
        String[] time = ["[*************,*************]"]

        queryParams.put(Constants.REQUEST_PARAM_INSTANCE_IDS_LIST, instanceIds)
        queryParams.put(Constants.REQUEST_PARAM_DATE_LIST, time)

        requestObject.setHeaders(headers)
        requestObject.setParams(params)
        requestObject.setQueryParams(queryParams)

        when:
        UtilityBean<ConfigWatchCompareRequest> data = bl.clientValidation(requestObject)

        then:
        thrown(ClientException)
    }

    def "Client validation exception: REQUEST_PARAM_INSTANCE_IDS_LIST"(){
        setup:
        Map<String, String> headers = new HashMap<>()
        headers.put(Constants.AUTHORIZATION_HEADER, AUTH_TOKEN)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.REQUEST_PARAM_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-12")

        Map<String,String[]> queryParams = new HashMap<>()

        String[] instanceIds = ["[abc,cde]"]
        String[] time = ["[*************,*************]"]

        queryParams.put(Constants.REQUEST_PARAM_INSTANCE_IDS_LIST, instanceIds)
        queryParams.put(Constants.REQUEST_PARAM_DATE_LIST, time)

        requestObject.setHeaders(headers)
        requestObject.setParams(params)
        requestObject.setQueryParams(queryParams)

        when:
        UtilityBean<ConfigWatchCompareRequest> data = bl.clientValidation(requestObject)

        then:
        thrown(ClientException)
    }

    def "Client validation exception: REQUEST_PARAM_DATE_LIST"(){
        setup:
        Map<String, String> headers = new HashMap<>()
        headers.put(Constants.AUTHORIZATION_HEADER, AUTH_TOKEN)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.REQUEST_PARAM_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-12")

        Map<String,String[]> queryParams = new HashMap<>()

        String[] instanceIds = ["[26,27]"]
        String[] time = [""]

        queryParams.put(Constants.REQUEST_PARAM_INSTANCE_IDS_LIST, instanceIds)
        queryParams.put(Constants.REQUEST_PARAM_DATE_LIST, time)

        requestObject.setHeaders(headers)
        requestObject.setParams(params)
        requestObject.setQueryParams(queryParams)

        when:
        UtilityBean<ConfigWatchCompareRequest> data = bl.clientValidation(requestObject)

        then:
        thrown(ClientException)
    }

    def "Client validation: INSTANCE_COUNT_EXCEEDED"(){
        setup:
        Map<String, String> headers = new HashMap<>()
        headers.put(Constants.AUTHORIZATION_HEADER, AUTH_TOKEN)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.REQUEST_PARAM_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-12")

        Map<String,String[]> queryParams = new HashMap<>()

        String[] instanceIds = ["[26,27,28,29,30]"]
        String[] time = ["[*************,*************]"]

        queryParams.put(Constants.REQUEST_PARAM_INSTANCE_IDS_LIST, instanceIds)
        queryParams.put(Constants.REQUEST_PARAM_DATE_LIST, time)

        requestObject.setHeaders(headers)
        requestObject.setParams(params)
        requestObject.setQueryParams(queryParams)

        when:
        UtilityBean<ConfigWatchCompareRequest> data = bl.clientValidation(requestObject)

        then:
        thrown(ClientException)
    }

    def "Client validation: DATE_COUNT_EXCEEDED"(){
        setup:
        Map<String, String> headers = new HashMap<>()
        headers.put(Constants.AUTHORIZATION_HEADER, AUTH_TOKEN)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.REQUEST_PARAM_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-12")

        Map<String,String[]> queryParams = new HashMap<>()

        String[] instanceIds = ["[26,27,28,29]"]
        String[] time = ["[*************,*************,*************,*************,*************]"]

        queryParams.put(Constants.REQUEST_PARAM_INSTANCE_IDS_LIST, instanceIds)
        queryParams.put(Constants.REQUEST_PARAM_DATE_LIST, time)

        requestObject.setHeaders(headers)
        requestObject.setParams(params)
        requestObject.setQueryParams(queryParams)

        when:
        UtilityBean<ConfigWatchCompareRequest> data = bl.clientValidation(requestObject)

        then:
        thrown(ClientException)
    }

    def "Server validation: no exception"(){
        setup:
        userAccountBean.setAccount(account)

        List<TimeRequest> list = new ArrayList<>()
        list.add(TimeRequest.builder()
                    .fromTime(*************)
                    .toTime(*************)
                    .build())

        UtilityBean<Integer> utilityBean = UtilityBean.<ConfigWatchCompareRequest>builder()
                .authToken(AUTH_TOKEN)
                .accountIdString("d681ef13-d690-4917-jkhg-6c79b-12")
                .requestPayloadObject(ConfigWatchCompareRequest.builder()
                        .instanceIds(Arrays.asList(26,27))
                        .dates(list)
                        .build())
                .build()
        bl.getCommonServerValidation(_ as String, _ as String) >> userAccountBean

        when:
        UtilityBean<ConfigWatchCompareRequest> data = bl.serverValidation(utilityBean)

        then:
        noExceptionThrown()

        then:
        data.getAccount().getId()==2
    }

    private WatcherKpiRawBean watcherKpisRaw(){
        return WatcherKpiRawBean.builder()
            .accountId("d681ef13-d690-4917-jkhg-6c79b-12")
            .instanceId("SOLARIS_LOS_HOST_110_Inst_1")
            .kpiId(32)
            .kpiAttributeName("/opt/appnomic/ConfigData/agent_config.properties")
            .time(*************)
            .groupId("KEY_VALUE")
            .kpiType("ConfigWatch")
            .kpiValue(kpiValue1())
            .lastUpdatedTime(null)
        .build()
    }

    private Map<String, String> kpiValue1(){
        Map<String, String> map = new HashMap<>()
        map.put("datacollector.collectors.instancespool.cache.spec.maximumSize", "2500")
        map.put("datacollector.collectors.threadpool.maximum.size", "1000")
        map.put("datacollector.collectors.threadpool.minimum.size", "270")
        map.put("datacollector.configserviceurl", "https://localhost:9120/CollectionServiceConfig.pdff, datacollector.dataserviceurl=https://localhost:9120/CollectionServiceDataa")
        return map;
    }
}
