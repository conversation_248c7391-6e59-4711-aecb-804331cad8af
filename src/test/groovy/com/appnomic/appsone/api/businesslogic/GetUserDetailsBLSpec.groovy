package com.appnomic.appsone.api.businesslogic

import com.appnomic.appsone.api.beans.UserAccountBean
import com.appnomic.appsone.api.beans.UserTimezoneRequestData
import com.appnomic.appsone.api.common.Constants
import com.appnomic.appsone.api.custom.exceptions.ClientException
import com.appnomic.appsone.api.custom.exceptions.ServerException
import com.appnomic.appsone.api.dao.mysql.entity.UserDetailsBean
import com.appnomic.appsone.api.manager.MySQLConnectionManager
import com.appnomic.appsone.api.pojo.Account
import com.appnomic.appsone.api.pojo.RequestObject
import com.appnomic.appsone.api.pojo.UserTimezonePojo
import com.appnomic.appsone.api.pojo.request.UtilityBean
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class GetUserDetailsBLSpec extends Specification {
    Request request = Spy(Request.class)
    Response response = Spy(Response.class)
    RequestObject requestObject = Spy(RequestObject.class)
    GetUserDetailsBL bl = Spy(GetUserDetailsBL.class)

    @Shared
    Account account = new Account()

    String AUTH_TOKEN = "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"

    @Shared
    UserAccountBean userAccountBean = new UserAccountBean()

    @Shared
    String oldH2URL

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setDbi(null)

        oldH2URL = MySQLConnectionManager.INSTANCE.getH2url()

        String H2URL = "jdbc:h2:mem:appsone;DB_CLOSE_DELAY=-1;MODE=MySQL;INIT=RUNSCRIPT FROM './src/test/resources/timezone/create.sql'\\;" +
                "RUNSCRIPT FROM './src/test/resources/timezone/populate.sql'"

        MySQLConnectionManager.INSTANCE.setH2URL(H2URL)
        println "H2URL: " + MySQLConnectionManager.INSTANCE.getH2url()
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(true)
        MySQLConnectionManager.INSTANCE.getHandle()
        account.accountId = 2
        account.identifier = "d681ef13-d690-4917-jkhg-6c79b-1"
        account.accountName= "INDIA"
        account.updatedBy = "7640123a-fbde-4fe5-9812-581cd1e3a9c1"
        Thread.sleep(10000)
    }

    def cleanupSpec() {
        MySQLConnectionManager.INSTANCE.setDbi(null)
        MySQLConnectionManager.INSTANCE.setH2URL(oldH2URL)
        MySQLConnectionManager.INSTANCE.getHandle()
    }

    def "Client validation- Get user details: no exception"(){
        setup:
        Map<String, String> params = new HashMap<>()
        params.put(Constants.REQUEST_PARAM_USER_ID, "7640123a-fbde-4fe5-9812-581cd1e3a9c1")
        Map<String, String> headers = new HashMap<>()
        headers.put(Constants.AUTHORIZATION_HEADER.toLowerCase(), AUTH_TOKEN)
        requestObject.setHeaders(headers)
        requestObject.setParams(params)

        when:
        bl.clientValidation(requestObject)

        then:
        noExceptionThrown()
    }

    def "Client validation- Get user details: exception- userIdentifier"(){
        setup:
        Map<String, String> params = new HashMap<>()
        params.put(Constants.REQUEST_PARAM_USER_ID, null)
        Map<String, String> headers = new HashMap<>()
        headers.put(Constants.AUTHORIZATION_HEADER.toLowerCase(), AUTH_TOKEN)
        requestObject.setHeaders(headers)
        requestObject.setParams(params)

        when:
        bl.clientValidation(requestObject)

        then:
        thrown(ClientException)
    }

    def "Server validation- Get user details: no exception"(){
        setup:

        bl.getUserDetails(_ as String) >>  getUserDetails()
        UtilityBean<String> utilityBean = UtilityBean.<String>builder()
                .authToken(AUTH_TOKEN)
                .requestPayloadObject("appsoneadmin").build()

        when:
        bl.serverValidation(utilityBean)

        then:
        noExceptionThrown()
    }

    def "Server validation- Get user details: exception- userIdentifier"(){
        setup:

        bl.getUserDetails(_ as String) >>  getUserDetails()
        UtilityBean<String> utilityBean = UtilityBean.<String>builder()
                .authToken(AUTH_TOKEN)
                .requestPayloadObject("").build()

        when:
        bl.serverValidation(utilityBean)

        then:
        thrown(ServerException)
    }

    def "Process Data- Get user details: no exception"(){
        setup:

        bl.getUserDetails(_ as String) >>  getUserDetails()
        UserTimezonePojo expectedResult = expectedResult()
        UtilityBean<String> utilityBean = UtilityBean.<String>builder()
                .authToken(AUTH_TOKEN)
                .requestPayloadObject("appsoneadmin").build()

        when:
        UserTimezoneRequestData configData = bl.serverValidation(utilityBean)
        UserTimezonePojo response = bl.processData(configData)

        then:
        noExceptionThrown()

        then:
        response == expectedResult
    }

    private static UserDetailsBean getUserDetails(){
        UserDetailsBean userDetailsBean = new UserDetailsBean()
        userDetailsBean.setId(1)
        userDetailsBean.setUserIdentifier("7640123a-fbde-4fe5-9812-581cd1e3a9c1")
        userDetailsBean.setStatus(1)
        userDetailsBean.setCreatedBy('7640123a-fbde-4fe5-9812-581cd1e3a9c1')
        userDetailsBean.setUserName("appsoneadmin")
        return userDetailsBean
    }

    private static UserTimezonePojo expectedResult(){
        UserTimezonePojo userTimezonePojo = new UserTimezonePojo()
        userTimezonePojo.setIsNotificationsTimezoneMychoice(0)
        userTimezonePojo.setIsTimezoneMychoice(0)
        userTimezonePojo.setTimezoneId(49)
        return userTimezonePojo
    }
}
