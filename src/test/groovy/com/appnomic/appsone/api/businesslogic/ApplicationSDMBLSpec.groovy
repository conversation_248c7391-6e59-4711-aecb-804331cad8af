package com.appnomic.appsone.api.businesslogic

import com.appnomic.appsone.api.pojo.*
import com.appnomic.appsone.api.pojo.request.UtilityBean
import spock.lang.Shared
import spock.lang.Specification

class ApplicationSDMBLSpec extends Specification{

    ApplicationSDMBL applicationSDMBL = Spy(ApplicationSDMBL.class)

    @Shared
    Account account = new Account()

    def setupSpec() {
        account.accountId = 2
        account.identifier = "d681ef13-d690-4917-jkhg-6c79b-1"
        account.accountName= "INDIA"
        account.updatedBy = "7640123a-fbde-4fe5-9812-581cd1e3a9c1"
        Thread.sleep(5000)
    }

    private Set<SignalData> getSignalsAccountMock(){
        Set<SignalData> signalDataSet = new HashSet<>()

        Set<String> strings = ["serv2","serv3","serv4","serv0"]

        for(int i = 0; i<5; i++){
            SignalData signalData = new SignalData()
            signalData.setId("1")
            signalData.setCurrentStatus("OPEN")
            signalData.setEntryServiceId("serv1")
            signalData.setAffectedServices(strings)
            signalDataSet.add(signalData)
        }

        return signalDataSet
    }

    private List<TopologyDetailsResponse.Nodes> getNodesMock(){
        List<TopologyDetailsResponse.Nodes> nodes = new ArrayList<>()
        for(int i=0;i<5;i++){
            TopologyDetailsResponse.Nodes node = new TopologyDetailsResponse.Nodes()
            node.setIdentifier("serv"+i)
            nodes.add(node)
        }
        return nodes
    }

    def "getEventStatus"(){
        setup:

        Controller controller = new Controller()
        controller.setAppId("1")

        ApplicationSDMRequestBean applicationSDMRequestBean = ApplicationSDMRequestBean.builder()
                                                                                .controller(controller)
                                                                                .account(account)
                                                                                .fromTime(*************)
                                                                                .toTime(*************)
                                                                                .build()

        UtilityBean utilityBean = UtilityBean.builder()
                .applicationIdString("1")
                .account(account)
                .fromTime(*************)
                .toTime(*************)
                .controllerBean(controller)
                .accountIdString(account.getIdentifier())
                .build()

        applicationSDMBL.getApplicationSignals(utilityBean) >> getSignalsAccountMock()

        when:
        List<TopologyDetailsResponse.Nodes> nodes = applicationSDMBL.getEventStatus(getNodesMock(),applicationSDMRequestBean)

        then:
        nodes.size() == 5
        nodes.get(0).behaviorEventCount == 1
        nodes.get(0).workloadEventCount == -1
    }

}
