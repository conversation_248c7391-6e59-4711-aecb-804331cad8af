package com.appnomic.appsone.api.businesslogic

import com.appnomic.appsone.api.common.Constants
import com.appnomic.appsone.api.common.LoggerTags
import com.appnomic.appsone.api.common.UIMessages
import com.appnomic.appsone.api.custom.exceptions.ClientException
import com.appnomic.appsone.api.pojo.RequestObject
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class InstanceCategoryDetailsBLTest extends Specification{
    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            return "eyJhbGciOiJSUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.eyJqdGkiOiI5MzU2Y2NiZC03YmNjLTRkMjktYjY5MS00ZmY3ZjNmZjIyNzUiLCJleHAiOjE1NjIzMzA3ODAsIm5iZiI6MCwiaWF0IjoxNTYyMzI5ODgwLCJpc3MiOiJodHRwOi8vMTkyLjE2OC4xMy4xNjc6OTA4MC9hdXRoL3JlYWxtcy9tYXN0ZXIiLCJzdWIiOiI3NjQwMTIzYS1mYmRlLTRmZTUtOTgxMi01ODFjZDFlM2E5YzEiLCJ0eXAiOiJCZWFyZXIiLCJhenAiOiJhZG1pbi1jbGkiLCJhdXRoX3RpbWUiOjAsInNlc3Npb25fc3RhdGUiOiJhOTFmNjYyMC1iYmU5LTQ5ZmUtYTZiYy0wNjIzNjc3YzU0YWIiLCJhY3IiOiIxIiwic2NvcGUiOiJwcm9maWxlIGVtYWlsIiwiZW1haWxfdmVyaWZpZWQiOmZhbHNlLCJwcmVmZXJyZWRfdXNlcm5hbWUiOiJhcHBzb25lYWRtaW4ifQ.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        }
    }

    class InvalidDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }
    DummyRequest request = Spy(DummyRequest.class)

    @Shared
    Map<String, String[]> queryMapData = new HashMap<>()
    @Shared
    Map<String, String> parameters = new HashMap<>()
    @Shared
    Set<String> header = new HashSet<>()

    String[] t1=["*************"]
    String[] t2=["*************"]
    String[] t3=["159898089889ts7240800000"]
    String[] t4=[""]
    String[] t5 = [null]

    def "client validation invalid account-identifier"() {
        given:
        header.add(Constants.AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()
        
        parameters.put(Constants.ACCOUNT_IDENTIFIER, "")
        parameters.put(Constants.REQUEST_PARAM_SERVICE_ID,"2")
        parameters.put(Constants.REQUEST_PARAM_CLUSTER_ID,"1")
        parameters.put(Constants.REQUEST_PARAM_COM_INSTANCE_ID, "12")
        queryMapData.put("fromTime", t1)
        queryMapData.put("toTime", t2)
        request.queryMap() >> queryMapData
        request.params() >> parameters
        request.body() >> ""
        when:
        def RequestObject = new RequestObject(request)
        new InstanceCategoryDetailsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "Account Identifier should not be empty."
    }
    def "client validation null service id"() {
        given:
        header.add(Constants.AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()
        
        parameters.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-1")
        parameters.put(Constants.REQUEST_PARAM_SERVICE_ID,"")
        parameters.put(Constants.REQUEST_PARAM_CLUSTER_ID,"1")
        parameters.put(Constants.REQUEST_PARAM_COM_INSTANCE_ID, "12")
        queryMapData.put("fromTime", t1)
        queryMapData.put("toTime", t2)
        request.queryMap() >> queryMapData
        request.params() >> parameters
        request.body() >> ""
        when:
        def RequestObject = new RequestObject(request)
        new InstanceCategoryDetailsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "Invalid service id provided."
    }
    def "client validation not number service id"() {
        given:
        header.add(Constants.AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()

        parameters.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-1")
        parameters.put(Constants.REQUEST_PARAM_SERVICE_ID,"xyz")
        parameters.put(Constants.REQUEST_PARAM_CLUSTER_ID,"1")
        parameters.put(Constants.REQUEST_PARAM_COM_INSTANCE_ID, "12")
        queryMapData.put("fromTime", t1)
        queryMapData.put("toTime", t2)
        request.queryMap() >> queryMapData
        request.params() >> parameters
        request.body() >> ""
        when:
        def RequestObject = new RequestObject(request)
        new InstanceCategoryDetailsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "Parsing of value not possible."
    }
    def "client validation service id is 0"() {
        given:
        header.add(Constants.AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-1")
        parameters.put(Constants.REQUEST_PARAM_SERVICE_ID,"0")
        parameters.put(Constants.REQUEST_PARAM_CLUSTER_ID,"1")
        parameters.put(Constants.REQUEST_PARAM_COM_INSTANCE_ID, "12")
        queryMapData.put("fromTime", t1)
        queryMapData.put("toTime", t2)
        request.queryMap() >> queryMapData
        request.params() >> parameters
        request.body() >> ""
        when:
        def RequestObject = new RequestObject(request)
        new InstanceCategoryDetailsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "Invalid service id provided."
    }
    def "client validation invalid auth token"() {
        given:
        InvalidDummyRequest invalidDummyRequest = Spy(InvalidDummyRequest.class)
        Set<String> headers = new HashSet<>()
        invalidDummyRequest.headers() >> headers.toSet()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-1")
        parameters.put(Constants.REQUEST_PARAM_SERVICE_ID,"12")
        parameters.put(Constants.REQUEST_PARAM_CLUSTER_ID,"1")
        parameters.put(Constants.REQUEST_PARAM_COM_INSTANCE_ID, "12")
        queryMapData.put("fromTime", t1)
        queryMapData.put("toTime", t2)
        invalidDummyRequest.queryMap() >> queryMapData
        invalidDummyRequest.params() >> parameters
        invalidDummyRequest.body() >> ""
        when:
        def RequestObject = new RequestObject(invalidDummyRequest)
        new InstanceCategoryDetailsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "Invalid auth token received"
    }
    def "client validation failure: clusterId null"() {
        given:
        header.add(Constants.AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()
        
        parameters.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-1")
        parameters.put(Constants.REQUEST_PARAM_SERVICE_ID,"12")
        parameters.put(Constants.REQUEST_PARAM_CLUSTER_ID,"")
        parameters.put(Constants.REQUEST_PARAM_COM_INSTANCE_ID, "12")
        queryMapData.put("fromTime", t1)
        queryMapData.put("toTime", t2)
        request.queryMap() >> queryMapData
        request.params() >> parameters
        request.body() >> ""
        when:
        def RequestObject = new RequestObject(request)
        new InstanceCategoryDetailsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "Invalid cluster id provided."
    }
    def "client validation failure: cluster Id not a number"() {
        given:
        header.add(Constants.AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()

        parameters.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-1")
        parameters.put(Constants.REQUEST_PARAM_SERVICE_ID,"12")
        parameters.put(Constants.REQUEST_PARAM_CLUSTER_ID,"xyz")
        parameters.put(Constants.REQUEST_PARAM_COM_INSTANCE_ID, "10")
        queryMapData.put("fromTime", t1)
        queryMapData.put("toTime", t2)
        request.queryMap() >> queryMapData
        request.params() >> parameters
        request.body() >> ""
        when:
        def RequestObject = new RequestObject(request)
        new InstanceCategoryDetailsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "Parsing of value not possible."
    }
    def "client validation failure: cluster Id is 0"() {
        given:
        header.add(Constants.AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()

        parameters.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-1")
        parameters.put(Constants.REQUEST_PARAM_SERVICE_ID,"12")
        parameters.put(Constants.REQUEST_PARAM_CLUSTER_ID,"0")
        parameters.put(Constants.REQUEST_PARAM_COM_INSTANCE_ID, "10")
        queryMapData.put("fromTime", t1)
        queryMapData.put("toTime", t2)
        request.queryMap() >> queryMapData
        request.params() >> parameters
        request.body() >> ""
        when:
        def RequestObject = new RequestObject(request)
        new InstanceCategoryDetailsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == UIMessages.INVALID_CLUSTER_ID
    }
    def "client validation failure: instance Id null"() {
        given:
        header.add(Constants.AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()

        parameters.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-1")
        parameters.put(Constants.REQUEST_PARAM_SERVICE_ID,"12")
        parameters.put(Constants.REQUEST_PARAM_CLUSTER_ID,"8")
        parameters.put(Constants.REQUEST_PARAM_COM_INSTANCE_ID, "")
        queryMapData.put("fromTime", t1)
        queryMapData.put("toTime", t2)
        request.queryMap() >> queryMapData
        request.params() >> parameters
        request.body() >> ""
        when:
        def RequestObject = new RequestObject(request)
        new InstanceCategoryDetailsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == UIMessages.ERROR_INVALID_COMP_INSTANCE_ID
    }
    def "client validation failure: instance Id not a number"() {
        given:
        header.add(Constants.AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()

        parameters.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-1")
        parameters.put(Constants.REQUEST_PARAM_SERVICE_ID,"12")
        parameters.put(Constants.REQUEST_PARAM_CLUSTER_ID,"8")
        parameters.put(Constants.REQUEST_PARAM_COM_INSTANCE_ID, "xyz")
        queryMapData.put("fromTime", t1)
        queryMapData.put("toTime", t2)
        request.queryMap() >> queryMapData
        request.params() >> parameters
        request.body() >> ""
        when:
        def RequestObject = new RequestObject(request)
        new InstanceCategoryDetailsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "Parsing of value not possible."
    }
    def "client validation failure: instance Id is 0"() {
        given:
        header.add(Constants.AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()

        parameters.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-1")
        parameters.put(Constants.REQUEST_PARAM_SERVICE_ID,"12")
        parameters.put(Constants.REQUEST_PARAM_CLUSTER_ID,"8")
        parameters.put(Constants.REQUEST_PARAM_COM_INSTANCE_ID, "0")
        queryMapData.put("fromTime", t1)
        queryMapData.put("toTime", t2)
        request.queryMap() >> queryMapData
        request.params() >> parameters
        request.body() >> ""
        when:
        def RequestObject = new RequestObject(request)
        new InstanceCategoryDetailsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == UIMessages.ERROR_INVALID_COMP_INSTANCE_ID
    }
    def "client validation failure: from time is empty string"() {
        given:
        header.add(Constants.AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()

        parameters.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-1")
        parameters.put(Constants.REQUEST_PARAM_SERVICE_ID,"12")
        parameters.put(Constants.REQUEST_PARAM_CLUSTER_ID,"1")
        parameters.put(Constants.REQUEST_PARAM_COM_INSTANCE_ID, "12")
        queryMapData.put("fromTime",t4 )
        queryMapData.put("toTime", t2)
        request.queryMap() >> queryMapData
        request.params() >> parameters
        request.body() >> ""
        when:
        def RequestObject = new RequestObject(request)
        new InstanceCategoryDetailsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "invalid fromTime provided."
    }
    def "client validation failure: from time is null"() {
        given:
        header.add(Constants.AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()

        parameters.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-1")
        parameters.put(Constants.REQUEST_PARAM_SERVICE_ID,"12")
        parameters.put(Constants.REQUEST_PARAM_CLUSTER_ID,"1")
        parameters.put(Constants.REQUEST_PARAM_COM_INSTANCE_ID, "12")
        queryMapData.put("fromTime",t5 )
        queryMapData.put("toTime", t2)
        request.queryMap() >> queryMapData
        request.params() >> parameters
        request.body() >> ""
        when:
        def RequestObject = new RequestObject(request)
        new InstanceCategoryDetailsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == LoggerTags.TAG_INVALID_FROM_TO_TIME
    }
    def "client validation failure: to time is empty string"() {
        given:
        header.add(Constants.AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()

        parameters.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-1")
        parameters.put(Constants.REQUEST_PARAM_SERVICE_ID,"12")
        parameters.put(Constants.REQUEST_PARAM_CLUSTER_ID,"1")
        parameters.put(Constants.REQUEST_PARAM_COM_INSTANCE_ID, "12")
        queryMapData.put("fromTime",t2 )
        queryMapData.put("toTime", t4)
        request.queryMap() >> queryMapData
        request.params() >> parameters
        request.body() >> ""
        when:
        def RequestObject = new RequestObject(request)
        new InstanceCategoryDetailsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "invalid toTime provided."
    }
    def "client validation failure: to time is null"() {
        given:
        header.add(Constants.AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()

        parameters.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-1")
        parameters.put(Constants.REQUEST_PARAM_SERVICE_ID,"12")
        parameters.put(Constants.REQUEST_PARAM_CLUSTER_ID,"1")
        parameters.put(Constants.REQUEST_PARAM_COM_INSTANCE_ID, "12")
        queryMapData.put("fromTime",t2 )
        queryMapData.put("toTime", t5)
        request.queryMap() >> queryMapData
        request.params() >> parameters
        request.body() >> ""
        when:
        def RequestObject = new RequestObject(request)
        new InstanceCategoryDetailsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == LoggerTags.TAG_INVALID_FROM_TO_TIME
    }
    def "client validation failure: from time is greater than to time"() {
        given:
        header.add(Constants.AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()

        parameters.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-1")
        parameters.put(Constants.REQUEST_PARAM_SERVICE_ID,"12")
        parameters.put(Constants.REQUEST_PARAM_CLUSTER_ID,"1")
        parameters.put(Constants.REQUEST_PARAM_COM_INSTANCE_ID, "12")
        queryMapData.put("fromTime",t2 )
        queryMapData.put("toTime", t1)
        request.queryMap() >> queryMapData
        request.params() >> parameters
        request.body() >> ""
        when:
        def RequestObject = new RequestObject(request)
        new InstanceCategoryDetailsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == LoggerTags.TAG_INVALID_FROM_TO_TIME
    }
    def "client validation success"() {
        given:
        header.add(Constants.AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()

        parameters.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-1")
        parameters.put(Constants.REQUEST_PARAM_SERVICE_ID,"12")
        parameters.put(Constants.REQUEST_PARAM_CLUSTER_ID,"1")
        parameters.put(Constants.REQUEST_PARAM_COM_INSTANCE_ID, "12")
        queryMapData.put("fromTime", t1)
        queryMapData.put("toTime", t2)
        request.queryMap() >> queryMapData
        request.params() >> parameters
        request.body() >> ""
        when:
        def RequestObject = new RequestObject(request)
        new InstanceCategoryDetailsBL().clientValidation(RequestObject)
        then:
        noExceptionThrown()
    }
}
