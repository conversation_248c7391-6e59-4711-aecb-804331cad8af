package com.appnomic.appsone.api.businesslogic

import com.appnomic.appsone.api.beans.Thresholds
import spock.lang.Specification

class KpiDataBLSpec extends Specification {

    def "mergeInstanceServiceThresholdMap: merge instance and service Sor AttributeKpiThresholdMap"() {

        setup:
        Thresholds threshold = new Thresholds()
        threshold.setMinThreshold(0);
        threshold.setMaxThreshold(1);
        threshold.setOperationList("greater than")
        Map<String, Thresholds> instanceSorAttributeKpiThresholdMap = new HashMap<>()
        instanceSorAttributeKpiThresholdMap.put("dm-1", threshold)

        Map<String, Thresholds> serviceSorAttributeKpiThresholdMap = new HashMap<>()
        Thresholds thresholds = new Thresholds()
        thresholds.setMinThreshold(0)
        thresholds.setMaxThreshold(2)
        thresholds.setOperationList("greater than ")
        serviceSorAttributeKpiThresholdMap.put("ALL", thresholds)

        Set<String> groupAttributesList = new HashSet<>();
        groupAttributesList.add("dm-1")
        groupAttributesList.add("dm-2")
        groupAttributesList.add("sda")
        groupAttributesList.add("dm-0")

        when:
        Map<String, Thresholds> sorAttributeKpiThresholdMap = new KpiDataBL().mergeInstanceServiceThresholdMap(instanceSorAttributeKpiThresholdMap, serviceSorAttributeKpiThresholdMap, groupAttributesList)


        then:
        println "Success"
    }
}
