package com.appnomic.appsone.api.businesslogic

import com.appnomic.appsone.api.beans.UserAccountBean
import com.appnomic.appsone.api.common.Constants
import com.appnomic.appsone.api.common.UIMessages
import com.appnomic.appsone.api.custom.exceptions.ClientException
import com.appnomic.appsone.api.dao.opensearch.SignalSearchRepo
import com.appnomic.appsone.api.manager.MySQLConnectionManager
import com.appnomic.appsone.api.pojo.Account
import com.appnomic.appsone.api.pojo.ApplicationHealthRequestPojo
import com.appnomic.appsone.api.pojo.RequestObject
import com.appnomic.appsone.api.pojo.request.UtilityBean
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

import java.text.MessageFormat

import static com.appnomic.appsone.api.common.LoggerTags.TAG_INVALID_FROM_TO_TIME

class ApplicationHealthBLSpec  extends Specification  {

    Request request = Spy(Request.class)
    Response response = Spy(Response.class)
    RequestObject requestObject = Spy(RequestObject.class)

    ApplicationHealthBL bl = Spy(ApplicationHealthBL.class)
    SignalSearchRepo signalRepo = new SignalSearchRepo()
    SignalSearchRepo signalSearchRepo = Spy(signalRepo)

    Set<String> services = ["NB-Web-Service"]
    def serviceIdList = [0]

    @Shared
    Account account = new Account()

    String AUTH_TOKEN = "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"

    @Shared
    UserAccountBean userAccountBean = new UserAccountBean()

    @Shared
    String oldH2URL

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setDbi(null)

        oldH2URL = MySQLConnectionManager.INSTANCE.getH2url()

        String H2URL = "jdbc:h2:mem:appsone;DB_CLOSE_DELAY=-1;MODE=MySQL;INIT=RUNSCRIPT FROM './src/test/resources/signals/create.sql'\\;" +
                "RUNSCRIPT FROM './src/test/resources/signals/populate.sql'"

        MySQLConnectionManager.INSTANCE.setH2URL(H2URL)
        println "H2URL: " + MySQLConnectionManager.INSTANCE.getH2url()
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(true)
        MySQLConnectionManager.INSTANCE.getHandle()
        account.accountId = 2
        account.identifier = "d681ef13-d690-4917-jkhg-6c79b-1"
        account.accountName= "INDIA"
        account.updatedBy = "7640123a-fbde-4fe5-9812-581cd1e3a9c1"
        Thread.sleep(5000)
    }

    def cleanupSpec() {
        MySQLConnectionManager.INSTANCE.setDbi(null)
        MySQLConnectionManager.INSTANCE.setH2URL(oldH2URL)
        MySQLConnectionManager.INSTANCE.getHandle()
    }

    def "Client validation: no exception"(){
        setup:
        Map<String, String> headers = new HashMap<>()
        headers.put(Constants.AUTHORIZATION_HEADER.toLowerCase(), AUTH_TOKEN)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.REQUEST_PARAM_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-12")

        Map<String,String[]> queryParams = new HashMap<>()
        String[] fromTime = ["*************"]
        String[] toTime = ["*************"]
        queryParams.put(Constants.REQUEST_PARAM_FROM_TIME, fromTime)
        queryParams.put(Constants.REQUEST_PARAM_TO_TIME, toTime)

        requestObject.setHeaders(headers)
        requestObject.setParams(params)
        requestObject.setQueryParams(queryParams)

        when:
        UtilityBean<ApplicationHealthRequestPojo> data = bl.clientValidation(requestObject)

        then:
        noExceptionThrown()

        then:
        data.getRequestPayloadObject().getActualFromTime() == Long.parseLong("*************")
    }

    def "Client validation: exception- identifier"(){
        setup:
        Map<String, String> headers = new HashMap<>()
        headers.put(Constants.AUTHORIZATION_HEADER.toLowerCase(), AUTH_TOKEN)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.REQUEST_PARAM_IDENTIFIER, null)

        Map<String,String[]> queryParams = new HashMap<>()
        String[] fromTime = ["*************"]
        String[] toTime = ["*************"]
        queryParams.put(Constants.REQUEST_PARAM_FROM_TIME, fromTime)
        queryParams.put(Constants.REQUEST_PARAM_TO_TIME, toTime)

        requestObject.setHeaders(headers)
        requestObject.setParams(params)
        requestObject.setQueryParams(queryParams)

        when:
        bl.clientValidation(requestObject)

        then:
        final e = thrown(ClientException)
        e.getSimpleMessage() == "ClientValidationException :: "+ MessageFormat.format(UIMessages.ACCOUNT_EMPTY, Constants.REQUEST_PARAM_IDENTIFIER, null)
    }

    def "Client validation: exception- fromTime"(){
        setup:
        Map<String, String> headers = new HashMap<>()
        headers.put(Constants.AUTHORIZATION_HEADER.toLowerCase(), AUTH_TOKEN)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.REQUEST_PARAM_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-12")

        Map<String,String[]> queryParams = new HashMap<>()
        String[] fromTime = ["abc"]
        String[] toTime = ["*************"]
        queryParams.put(Constants.REQUEST_PARAM_FROM_TIME, fromTime)
        queryParams.put(Constants.REQUEST_PARAM_TO_TIME, toTime)

        requestObject.setHeaders(headers)
        requestObject.setParams(params)
        requestObject.setQueryParams(queryParams)

        when:
        bl.clientValidation(requestObject)

        then:
        final e = thrown(ClientException)
        e.getSimpleMessage() == "ClientValidationException :: Error occurred while converting the fromTime [abc]. Reason: For input string: \"abc\""
    }

    def "Client validation: exception- toTime"(){
        setup:
        Map<String, String> headers = new HashMap<>()
        headers.put(Constants.AUTHORIZATION_HEADER.toLowerCase(), AUTH_TOKEN)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.REQUEST_PARAM_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-12")

        Map<String,String[]> queryParams = new HashMap<>()
        String[] fromTime = ["*************"]
        String[] toTime = ["abc"]
        queryParams.put(Constants.REQUEST_PARAM_FROM_TIME, fromTime)
        queryParams.put(Constants.REQUEST_PARAM_TO_TIME, toTime)

        requestObject.setHeaders(headers)
        requestObject.setParams(params)
        requestObject.setQueryParams(queryParams)

        when:
        bl.clientValidation(requestObject)

        then:
        final e = thrown(ClientException)
        e.getSimpleMessage() == "ClientValidationException :: Error occurred while converting the toTime [abc]. Reason: For input string: \"abc\""
    }

    def "Client validation: exception- fromTime<toTime"(){
        setup:
        Map<String, String> headers = new HashMap<>()
        headers.put(Constants.AUTHORIZATION_HEADER.toLowerCase(), AUTH_TOKEN)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.REQUEST_PARAM_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-12")

        Map<String,String[]> queryParams = new HashMap<>()
        String[] fromTime = ["*************"]
        String[] toTime = ["*************"]
        queryParams.put(Constants.REQUEST_PARAM_FROM_TIME, fromTime)
        queryParams.put(Constants.REQUEST_PARAM_TO_TIME, toTime)

        requestObject.setHeaders(headers)
        requestObject.setParams(params)
        requestObject.setQueryParams(queryParams)

        when:
        bl.clientValidation(requestObject)

        then:
        final e = thrown(ClientException)
        e.getSimpleMessage() == "ClientValidationException :: "+ TAG_INVALID_FROM_TO_TIME
    }

    def "Client validation: SIGNAL_CLOSE_WINDOW_TIME"(){
        setup:
        Map<String, String> headers = new HashMap<>()
        headers.put(Constants.AUTHORIZATION_HEADER.toLowerCase(), AUTH_TOKEN)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.REQUEST_PARAM_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-12")

        Map<String,String[]> queryParams = new HashMap<>()
        String[] fromTime = ["*************"]
        String[] toTime = ["*************"]
        queryParams.put(Constants.REQUEST_PARAM_FROM_TIME, fromTime)
        queryParams.put(Constants.REQUEST_PARAM_TO_TIME, toTime)

        requestObject.setHeaders(headers)
        requestObject.setParams(params)
        requestObject.setQueryParams(queryParams)

        when:
        UtilityBean<ApplicationHealthRequestPojo> data = bl.clientValidation(requestObject)

        then:
        noExceptionThrown()

        then:
        data.getAccountIdString() == "d681ef13-d690-4917-jkhg-6c79b-12"
    }

    def "Server validation: no exception"(){
        setup:
        userAccountBean.setAccount(account)
        ApplicationHealthRequestPojo pojo = new ApplicationHealthRequestPojo()
        pojo.setFromTime(*************)
        pojo.setToTime(*************)

        UtilityBean<ApplicationHealthRequestPojo> utilityBean = UtilityBean.<ApplicationHealthRequestPojo>builder()
                .accountIdString("d681ef13-d690-4917-jkhg-6c79b-12")
                .requestPayloadObject(pojo)
                .authToken(AUTH_TOKEN)
                .build()
        bl.ValidationUtils.commonServerValidation(_ as String, _ as String) >> userAccountBean

        when:
        UtilityBean<ApplicationHealthRequestPojo> data = bl.serverValidation(utilityBean)

        then:
        noExceptionThrown()

        then:
        data.getAccountIdString()=="d681ef13-d690-4917-jkhg-6c79b-12"
    }

}


