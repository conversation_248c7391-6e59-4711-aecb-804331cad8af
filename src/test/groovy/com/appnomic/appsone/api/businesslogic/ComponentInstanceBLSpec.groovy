package com.appnomic.appsone.api.businesslogic

import com.appnomic.appsone.api.beans.UserAccountBean
import com.appnomic.appsone.api.common.Constants
import com.appnomic.appsone.api.custom.exceptions.ClientException
import com.appnomic.appsone.api.manager.MySQLConnectionManager
import com.appnomic.appsone.api.pojo.Account
import com.appnomic.appsone.api.pojo.ComponentInstancesResponse
import com.appnomic.appsone.api.pojo.RequestObject
import com.appnomic.appsone.api.pojo.request.UtilityBean
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class ComponentInstanceBLSpec extends Specification {
    Request request = Spy(Request.class)
    Response response = Spy(Response.class)
    RequestObject requestObject = Spy(RequestObject.class)

    ComponentInstanceBL bl = Spy(ComponentInstanceBL.class)

    @Shared
    Account account = new Account()

    String AUTH_TOKEN = "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"

    @Shared
    UserAccountBean userAccountBean = new UserAccountBean()

    @Shared
    String oldH2URL

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setDbi(null)

        oldH2URL = MySQLConnectionManager.INSTANCE.getH2url()

        String H2URL = "jdbc:h2:mem:appsone;DB_CLOSE_DELAY=-1;MODE=MySQL;INIT=RUNSCRIPT FROM './src/test/resources/config-watch/create.sql'\\;" +
                "RUNSCRIPT FROM './src/test/resources/config-watch/populate.sql'"

        MySQLConnectionManager.INSTANCE.setH2URL(H2URL)
        println "H2URL: " + MySQLConnectionManager.INSTANCE.getH2url()
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(true)
        MySQLConnectionManager.INSTANCE.getHandle()
        account.accountId = 2
        account.identifier = "d681ef13-d690-4917-jkhg-6c79b-1"
        account.accountName= "INDIA"
        account.updatedBy = "7640123a-fbde-4fe5-9812-581cd1e3a9c1"
        Thread.sleep(5000)
    }

    def cleanupSpec() {
        MySQLConnectionManager.INSTANCE.setDbi(null)
        MySQLConnectionManager.INSTANCE.setH2URL(oldH2URL)
        MySQLConnectionManager.INSTANCE.getHandle()
    }

    def "Client validation: no exception"(){
        setup:
        Map<String, String> headers = new HashMap<>()
        headers.put(Constants.AUTHORIZATION_HEADER, AUTH_TOKEN)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.REQUEST_PARAM_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-12")
        params.put(Constants.REQUEST_PARAM_APPLICATION_ID, "1")
        params.put(Constants.REQUEST_PARAM_COMPONENT_ID, "9")

        Map<String,String[]> queryParams = new HashMap<>()

        requestObject.setHeaders(headers)
        requestObject.setParams(params)
        requestObject.setQueryParams(queryParams)

        when:
        UtilityBean<Integer> data = bl.clientValidation(requestObject)

        then:
        noExceptionThrown()

        then:
        data.getRequestPayloadObject() == 9
    }

    def "Client validation exception: AUTHORIZATION_HEADER"(){
        setup:
        Map<String, String> headers = new HashMap<>()
        headers.put(Constants.AUTHORIZATION_HEADER, null)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.REQUEST_PARAM_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-12")
        params.put(Constants.REQUEST_PARAM_APPLICATION_ID, "1")
        params.put(Constants.REQUEST_PARAM_COMPONENT_ID, "9")

        Map<String,String[]> queryParams = new HashMap<>()

        requestObject.setHeaders(headers)
        requestObject.setParams(params)
        requestObject.setQueryParams(queryParams)

        when:
        UtilityBean<Integer> data = bl.clientValidation(requestObject)

        then:
        thrown(ClientException)
    }

    def "Client validation exception: REQUEST_PARAM_IDENTIFIER"(){
        setup:
        Map<String, String> headers = new HashMap<>()
        headers.put(Constants.AUTHORIZATION_HEADER, AUTH_TOKEN)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.REQUEST_PARAM_IDENTIFIER, null)
        params.put(Constants.REQUEST_PARAM_APPLICATION_ID, "1")
        params.put(Constants.REQUEST_PARAM_COMPONENT_ID, "9")

        Map<String,String[]> queryParams = new HashMap<>()

        requestObject.setHeaders(headers)
        requestObject.setParams(params)
        requestObject.setQueryParams(queryParams)

        when:
        UtilityBean<Integer> data = bl.clientValidation(requestObject)

        then:
        thrown(ClientException)
    }

    def "Client validation exception: REQUEST_PARAM_APPLICATION_ID"(){
        setup:
        Map<String, String> headers = new HashMap<>()
        headers.put(Constants.AUTHORIZATION_HEADER, AUTH_TOKEN)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.REQUEST_PARAM_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-12")
        params.put(Constants.REQUEST_PARAM_APPLICATION_ID, null)
        params.put(Constants.REQUEST_PARAM_COMPONENT_ID, "9")

        Map<String,String[]> queryParams = new HashMap<>()

        requestObject.setHeaders(headers)
        requestObject.setParams(params)
        requestObject.setQueryParams(queryParams)

        when:
        UtilityBean<Integer> data = bl.clientValidation(requestObject)

        then:
        thrown(ClientException)
    }

    def "Client validation exception: REQUEST_PARAM_APPLICATION_ID - Number Format"(){
        setup:
        Map<String, String> headers = new HashMap<>()
        headers.put(Constants.AUTHORIZATION_HEADER, AUTH_TOKEN)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.REQUEST_PARAM_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-12")
        params.put(Constants.REQUEST_PARAM_APPLICATION_ID, "abc")
        params.put(Constants.REQUEST_PARAM_COMPONENT_ID, "9")

        Map<String,String[]> queryParams = new HashMap<>()

        requestObject.setHeaders(headers)
        requestObject.setParams(params)
        requestObject.setQueryParams(queryParams)

        when:
        UtilityBean<Integer> data = bl.clientValidation(requestObject)

        then:
        thrown(ClientException)
    }

    def "Client validation exception: REQUEST_PARAM_COMPONENT_ID"(){
        setup:
        Map<String, String> headers = new HashMap<>()
        headers.put(Constants.AUTHORIZATION_HEADER, AUTH_TOKEN)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.REQUEST_PARAM_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-12")
        params.put(Constants.REQUEST_PARAM_APPLICATION_ID, "1")
        params.put(Constants.REQUEST_PARAM_COMPONENT_ID, null)

        Map<String,String[]> queryParams = new HashMap<>()

        requestObject.setHeaders(headers)
        requestObject.setParams(params)
        requestObject.setQueryParams(queryParams)

        when:
        UtilityBean<Integer> data = bl.clientValidation(requestObject)

        then:
        thrown(ClientException)
    }

    def "Client validation exception: REQUEST_PARAM_COMPONENT_ID - Number Format"(){
        setup:
        Map<String, String> headers = new HashMap<>()
        headers.put(Constants.AUTHORIZATION_HEADER, AUTH_TOKEN)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.REQUEST_PARAM_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-12")
        params.put(Constants.REQUEST_PARAM_APPLICATION_ID, "1")
        params.put(Constants.REQUEST_PARAM_COMPONENT_ID, "abc")

        Map<String,String[]> queryParams = new HashMap<>()

        requestObject.setHeaders(headers)
        requestObject.setParams(params)
        requestObject.setQueryParams(queryParams)

        when:
        UtilityBean<Integer> data = bl.clientValidation(requestObject)

        then:
        thrown(ClientException)
    }

    def "Server validation: no exception"(){
        setup:
        userAccountBean.setAccount(account)

        UtilityBean<Integer> utilityBean = UtilityBean.<Integer>builder()
                .authToken(AUTH_TOKEN)
                .accountIdString("d681ef13-d690-4917-jkhg-6c79b-12")
                .applicationId(1)
                .requestPayloadObject(9)
                .build()
        bl.getCommonServerValidation(_ as String, _ as String) >> userAccountBean

        when:
        UtilityBean<Integer> data = bl.serverValidation(utilityBean)

        then:
        noExceptionThrown()

        then:
        data.getRequestPayloadObject()==9
    }

    def "Process Data: no exception"(){
        setup:
        userAccountBean.setAccount(account)

        UtilityBean<Integer> utilityBean = UtilityBean.<Integer>builder()
                .authToken(AUTH_TOKEN)
                .accountIdString("d681ef13-d690-4917-jkhg-6c79b-12")
                .applicationId(1)
                .requestPayloadObject(3)
                .build()
        bl.getCommonServerValidation(_ as String, _ as String) >> userAccountBean
        UtilityBean<Integer> configData = bl.serverValidation(utilityBean)
        List<ComponentInstancesResponse> expectedResponse = expectedResult()

        when:
        List<ComponentInstancesResponse> result = bl.processData(configData)

        then:
        noExceptionThrown()

        then:
        result == expectedResponse
    }

    private List<ComponentInstancesResponse> expectedResult(){
        List<ComponentInstancesResponse> data = new ArrayList<>()

        ComponentInstancesResponse res1 = new ComponentInstancesResponse()
        res1.setId(26)
        res1.setName("RHEL_NB_Web_Host_154_Inst_1")
        res1.setServiceId(2)
        res1.setServiceName("NB-Web-Service")

        ComponentInstancesResponse res2 = new ComponentInstancesResponse()
        res2.setId(27)
        res2.setName("RHEL_NB_Web_Host_171_Inst_1")
        res2.setServiceId(2)
        res2.setServiceName("NB-Web-Service")

        data.add(res1)
        data.add(res2)
        return data
    }
}
