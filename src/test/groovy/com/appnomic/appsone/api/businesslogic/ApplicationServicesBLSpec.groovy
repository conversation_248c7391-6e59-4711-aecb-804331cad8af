package com.appnomic.appsone.api.businesslogic

import com.appnomic.appsone.api.beans.UserAccountBean
import com.appnomic.appsone.api.common.Constants
import com.appnomic.appsone.api.custom.exceptions.ClientException
import com.appnomic.appsone.api.custom.exceptions.ServerException
import com.appnomic.appsone.api.manager.MySQLConnectionManager
import com.appnomic.appsone.api.pojo.Account
import com.appnomic.appsone.api.pojo.RequestObject
import com.appnomic.appsone.api.pojo.request.UtilityBean
import spock.lang.Shared
import spock.lang.Specification

class ApplicationServicesBLSpec extends Specification{
    RequestObject requestObject = Spy(RequestObject.class)
    ApplicationServicesBL bl = Spy(ApplicationServicesBL.class)

    @Shared
    Account account = new Account()

    String AUTH_TOKEN = "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"

    @Shared
    UserAccountBean userAccountBean = new UserAccountBean()

    @Shared
    String oldH2URL

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setDbi(null)

        oldH2URL = MySQLConnectionManager.INSTANCE.getH2url()

        String H2URL = "jdbc:h2:mem:appsone;DB_CLOSE_DELAY=-1;MODE=MySQL;INIT=RUNSCRIPT FROM './src/test/resources/account-data/create.sql'\\;" +
                "RUNSCRIPT FROM './src/test/resources/account-data/populate.sql'"

        MySQLConnectionManager.INSTANCE.setH2URL(H2URL)
        println "H2URL: " + MySQLConnectionManager.INSTANCE.getH2url()
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(true)
        MySQLConnectionManager.INSTANCE.getHandle()
        account.accountId = 2
        account.identifier = "d681ef13-d690-4917-jkhg-6c79b-1"
        account.accountName= "INDIA"
        account.updatedBy = "7640123a-fbde-4fe5-9812-581cd1e3a9c1"
        Thread.sleep(5000)
    }

    def cleanupSpec() {
        MySQLConnectionManager.INSTANCE.setDbi(null)
        MySQLConnectionManager.INSTANCE.setH2URL(oldH2URL)
        MySQLConnectionManager.INSTANCE.getHandle()
    }

    def "Client validation: no exception"() {
        setup:
        Map<String, String> headers = new HashMap<>()
        headers.put(Constants.AUTHORIZATION_HEADER, AUTH_TOKEN)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.REQUEST_PARAM_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-12")
        params.put(Constants.REQUEST_PARAM_APPLICATION_ID, "85")

        requestObject.setHeaders(headers)
        requestObject.setParams(params)

        when:
        UtilityBean<Object> data = bl.clientValidation(requestObject)

        then:
        noExceptionThrown()
    }

    def "Client validation Exception: AUTH_TOKEN "() {
        setup:
        Map<String, String> headers = new HashMap<>()
        headers.put(Constants.AUTHORIZATION_HEADER, null)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.REQUEST_PARAM_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-12")
        params.put(Constants.REQUEST_PARAM_APPLICATION_ID, "85")

        requestObject.setHeaders(headers)
        requestObject.setParams(params)

        when:
        UtilityBean<Object> data = bl.clientValidation(requestObject)

        then:
        thrown(ClientException)
    }

    def "Client validation Exception: IDENTIFIER "() {
        setup:
        Map<String, String> headers = new HashMap<>()
        headers.put(Constants.AUTHORIZATION_HEADER, AUTH_TOKEN)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.REQUEST_PARAM_IDENTIFIER, null)
        params.put(Constants.REQUEST_PARAM_APPLICATION_ID, "85")

        requestObject.setHeaders(headers)
        requestObject.setParams(params)

        when:
        UtilityBean<Object> data = bl.clientValidation(requestObject)

        then:
        thrown(ClientException)
    }

    def "Client validation Exception: Application Id "() {
        setup:
        Map<String, String> headers = new HashMap<>()
        headers.put(Constants.AUTHORIZATION_HEADER, AUTH_TOKEN)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.REQUEST_PARAM_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-12")
        params.put(Constants.REQUEST_PARAM_APPLICATION_ID, null)

        requestObject.setHeaders(headers)
        requestObject.setParams(params)

        when:
        UtilityBean<Object> data = bl.clientValidation(requestObject)

        then:
        thrown(ClientException)
    }

    def "Client validation Exception Number Format: Application Id "() {
        setup:
        Map<String, String> headers = new HashMap<>()
        headers.put(Constants.AUTHORIZATION_HEADER, AUTH_TOKEN)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.REQUEST_PARAM_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-12")
        params.put(Constants.REQUEST_PARAM_APPLICATION_ID, "abc")

        requestObject.setHeaders(headers)
        requestObject.setParams(params)

        when:
        UtilityBean<Object> data = bl.clientValidation(requestObject)

        then:
        thrown(ClientException)
    }

    def "Server validation: no exception"(){
        setup:
        userAccountBean.setAccount(account)
        UtilityBean<Object> utilityBean = UtilityBean.builder().authToken(AUTH_TOKEN)
                .accountIdString("d681ef13-d690-4917-jkhg-6c79b-12")
                .applicationIdString("2")
                .applicationId(2)
                .serviceIdString("1")
                .serviceId(1)
                .build()
        bl.getCommonServerValidation(_ as String, _ as String) >> userAccountBean

        when:
        bl.serverValidation(utilityBean)

        then:
        noExceptionThrown()
    }

    def "Server validation exception: User Account bean null"(){
        setup:
        userAccountBean.setAccount(account)
        UtilityBean<Object> utilityBean = UtilityBean.builder().authToken(null)
                .accountIdString("d681ef13-d690-4917-jkhg-6c79b-12")
                .applicationIdString("2")
                .applicationId(2)
                .serviceIdString("1")
                .serviceId(1)
                .build()
        bl.getCommonServerValidation(_ as String, _ as String) >> userAccountBean

        when:
        bl.serverValidation(utilityBean)

        then:
        thrown(ServerException)
    }

    def "Process Data: no exception"(){
        setup:
        userAccountBean.setAccount(account)
        UtilityBean<Object> utilityBean = UtilityBean.builder().authToken(AUTH_TOKEN)
                .accountIdString("d681ef13-d690-4917-jkhg-6c79b-12")
                .applicationIdString("2")
                .applicationId(2)
                .serviceIdString("1")
                .serviceId(1)
                .build()
        bl.getCommonServerValidation(_ as String, _ as String) >> userAccountBean

        when:
        bl.serverValidation(utilityBean)
        bl.processData(utilityBean)

        then:
        noExceptionThrown()
    }
}
