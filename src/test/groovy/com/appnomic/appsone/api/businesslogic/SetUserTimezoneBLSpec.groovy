package com.appnomic.appsone.api.businesslogic

import com.appnomic.appsone.api.beans.UserAccountBean
import com.appnomic.appsone.api.beans.UserTimezoneRequestData
import com.appnomic.appsone.api.businesslogic.SetUserTimezoneBL
import com.appnomic.appsone.api.common.Constants
import com.appnomic.appsone.api.custom.exceptions.ClientException
import com.appnomic.appsone.api.custom.exceptions.ServerException
import com.appnomic.appsone.api.dao.mysql.entity.UserDetailsBean
import com.appnomic.appsone.api.manager.MySQLConnectionManager
import com.appnomic.appsone.api.pojo.Account
import com.appnomic.appsone.api.pojo.RequestObject
import com.appnomic.appsone.api.pojo.UserTimezonePojo
import com.appnomic.appsone.api.pojo.request.UtilityBean
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class SetUserTimezoneBLSpec extends Specification {
    Request request = Spy(Request.class)
    Response response = Spy(Response.class)
    RequestObject requestObject = Spy(RequestObject.class)
    SetUserTimezoneBL bl = Spy(SetUserTimezoneBL.class)

    @Shared
    Account account = new Account()

    String AUTH_TOKEN = "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"

    @Shared
    UserAccountBean userAccountBean = new UserAccountBean();

    @Shared
    String oldH2URL

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setDbi(null)

        oldH2URL = MySQLConnectionManager.INSTANCE.getH2url()

        String H2URL = "jdbc:h2:mem:appsone;DB_CLOSE_DELAY=-1;MODE=MySQL;INIT=RUNSCRIPT FROM './src/test/resources/timezone/create.sql'\\;" +
                "RUNSCRIPT FROM './src/test/resources/timezone/populate.sql'"

        MySQLConnectionManager.INSTANCE.setH2URL(H2URL)
        println "H2URL: " + MySQLConnectionManager.INSTANCE.getH2url()
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(true)
        MySQLConnectionManager.INSTANCE.getHandle()
        account.accountId = 2
        account.identifier = "d681ef13-d690-4917-jkhg-6c79b-1"
        account.accountName= "INDIA"
        account.updatedBy = "7640123a-fbde-4fe5-9812-581cd1e3a9c1"
        Thread.sleep(10000)
    }

    def cleanupSpec() {
        MySQLConnectionManager.INSTANCE.setDbi(null)
        MySQLConnectionManager.INSTANCE.setH2URL(oldH2URL)
        MySQLConnectionManager.INSTANCE.getHandle()
    }

    def "Client validation: Set user timezone"(){
        setup:
        Map<String, String> params = new HashMap<>()
        params.put(Constants.REQUEST_PARAM_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-12")
        params.put(Constants.REQUEST_PARAM_USERNAME, "appsoneadmin")
        Map<String, String> headers = new HashMap<>()
        requestObject.setHeaders(headers)
        requestObject.setParams(params)
        requestObject.setBody("{\"timezoneId\":49,\"isTimezoneMychoice\":1,\"isNotificationsTimezoneMychoice\":1}")

        when:
        UtilityBean<UserTimezoneRequestData> utilityBean = bl.clientValidation(requestObject)

        then:
        noExceptionThrown()

        then:
        utilityBean.getRequestPayloadObject().getUsername() == "appsoneadmin"
    }

    def "Client validation: Invalid user identifier"(){
        setup:
        Map<String, String> params = new HashMap<>()
        params.put(Constants.REQUEST_PARAM_USERNAME, null)
        Map<String, String> headers = new HashMap<>()
        requestObject.setHeaders(headers)
        requestObject.setParams(params)
        requestObject.setBody("{\"timezoneId\":49,\"isTimezoneMychoice\":1,\"isNotificationsTimezoneMychoice\":1}")

        when:
        UtilityBean<UserTimezoneRequestData> utilityBean = bl.clientValidation(requestObject)

        then:
        final e = thrown(ClientException)
        e.getMessage()== "Invalid input parameters provided. Param name::username, value:null"
    }

    def "Client validation: Invalid request body"(){
        setup:
        Map<String, String> params = new HashMap<>()
        params.put(Constants.REQUEST_PARAM_USERNAME, "appsoneadmin")
        Map<String, String> headers = new HashMap<>()
        requestObject.setHeaders(headers)
        requestObject.setParams(params)
        requestObject.setBody("")

        when:
        UtilityBean<UserTimezoneRequestData> utilityBean = bl.clientValidation(requestObject)

        then:
        final e = thrown(ClientException)
        e.getSimpleMessage()== "ClientValidationException :: Invalid data in request body"
    }

    def "Server validation: No exception"(){
        setup:
        UserTimezoneRequestData requestData = new UserTimezoneRequestData()
        UserTimezonePojo userTimezonePojo = new UserTimezonePojo()
        userTimezonePojo.isTimezoneMychoice=1
        userTimezonePojo.isNotificationsTimezoneMychoice=1
        userTimezonePojo.timezoneId=1
        requestData.setUserTimezonePojo(userTimezonePojo)
        requestData.setUsername("appsoneadmin")
        bl.getUserDetails(_ as String) >>  getUserDetails()

        UtilityBean<UserTimezoneRequestData> utilityBean = UtilityBean.<UserTimezoneRequestData>builder()
                .authToken(AUTH_TOKEN)
                .requestPayloadObject(requestData).build()

        userAccountBean.setAccount(account)

        when:
        bl.serverValidation(utilityBean)

        then:
        noExceptionThrown()
    }

    def "Server validation: Exception"(){
        setup:
        UserTimezoneRequestData requestData = new UserTimezoneRequestData()
        UserTimezonePojo userTimezonePojo = new UserTimezonePojo()
        userTimezonePojo.isTimezoneMychoice=1
        userTimezonePojo.isNotificationsTimezoneMychoice=1
        userTimezonePojo.timezoneId=1
        requestData.setUserTimezonePojo(userTimezonePojo)
        requestData.setUsername(null)
        bl.getUserDetails(_ as String) >>  getUserDetails()

        UtilityBean<UserTimezoneRequestData> utilityBean = UtilityBean.<UserTimezoneRequestData>builder()
                .authToken(AUTH_TOKEN)
                .requestPayloadObject(requestData).build()

        userAccountBean.setAccount(account)

        when:
        bl.serverValidation(utilityBean)

        then:
        final e = thrown(ServerException)
        e.getSimpleMessage() == "ServerValidationException :: Invalid input parameters provided. Param name::username, value:null"
    }

    def "Insert user timezone: No Exception"(){
        setup:
        UserTimezoneRequestData requestData = new UserTimezoneRequestData()
        UserTimezonePojo userTimezonePojo = new UserTimezonePojo()
        userTimezonePojo.isTimezoneMychoice=1
        userTimezonePojo.isNotificationsTimezoneMychoice=1
        userTimezonePojo.timezoneId=1
        requestData.setUserTimezonePojo(userTimezonePojo)
        requestData.setUsername("appsoneadmin")
        requestData.setUserDetailsBean(getUserDetails())
        bl.getUserDetails(_ as String) >>  getUserDetails()


        UtilityBean<UserTimezoneRequestData> utilityBean = UtilityBean.<UserTimezoneRequestData>builder()
                .authToken(AUTH_TOKEN)
                .requestPayloadObject(requestData).build()

        userAccountBean.setAccount(account)

        when:
        UserTimezoneRequestData configData = bl.serverValidation(utilityBean)
        bl.processData(configData)

        then:
        noExceptionThrown()
    }

    def "Update user timezone: No Exception"(){
        setup:
        UserTimezoneRequestData requestData = new UserTimezoneRequestData()
        UserTimezonePojo userTimezonePojo = new UserTimezonePojo()
        userTimezonePojo.isTimezoneMychoice=1
        userTimezonePojo.isNotificationsTimezoneMychoice=1
        userTimezonePojo.timezoneId=1
        requestData.setUserTimezonePojo(userTimezonePojo)
        requestData.setUsername("appsoneadmin")
        requestData.setUserDetailsBean(getUserDetails())
        bl.getUserDetails(_ as String) >>  getUserDetails()

        UtilityBean<UserTimezoneRequestData> utilityBean = UtilityBean.<UserTimezoneRequestData>builder()
                .authToken(AUTH_TOKEN)
                .requestPayloadObject(requestData).build()

        userAccountBean.setAccount(account)

        when:
        UserTimezoneRequestData configData = bl.serverValidation(utilityBean)
        bl.processData(configData)

        then:
        noExceptionThrown()
    }

    private UserDetailsBean getUserDetails(){
        UserDetailsBean userDetailsBean = new UserDetailsBean()
        userDetailsBean.setId(1)
        userDetailsBean.setUserIdentifier("7640123a-fbde-4fe5-9812-581cd1e3a9c1")
        userDetailsBean.setStatus(1)
        userDetailsBean.setCreatedBy('7640123a-fbde-4fe5-9812-581cd1e3a9c1')
        userDetailsBean.setUserName("appsoneadmin")
        return userDetailsBean
    }

}
