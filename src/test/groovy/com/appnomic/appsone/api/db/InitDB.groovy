package com.appnomic.appsone.api.db

import groovy.sql.Sql

class InitDB {
    /*private static Boolean initialized = false
    private static final String defaultDatabaseName = "apmcommon"

    static def init()  {
        if(!initialized)    {
            initialized = true
            Sql.newInstance("jdbc:h2:mem:"+defaultDatabaseName+";DB_CLOSE_DELAY=-1;MODE=MySQL;INIT=RUNSCRIPT FROM './src/test/resources/create.sql'\\;RUNSCRIPT FROM './src/test/resources/populate.sql'", "appsone", "App\$0ne", "org.h2.Driver")
        }
    }*/
}
