package com.appnomic.appsone.api.db

import com.appnomic.appsone.api.manager.MySQLConnectionManager
import org.junit.BeforeClass
import org.junit.Test
import org.skife.jdbi.v2.Handle
import org.skife.jdbi.v2.util.StringMapper

class DBConnectionTest  {

    /*@BeforeClass
    static void initDb()  {
        InitDB.init()
    }*/

    @Test
    void testMysqlConnection()   {
        /*Handle handle = MySQLConnectionManager.getTestInstance().getHandle().open()
        assert (handle != null)
        String version = handle.createQuery("select username from DISCOVERED_HOST_DETAILS")
                .map(StringMapper.FIRST).first()
        System.out.println("DB version: "+version)*/
    }

}
