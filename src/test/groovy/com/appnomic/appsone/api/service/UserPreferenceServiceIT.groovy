package com.appnomic.appsone.api.service

import com.appnomic.appsone.api.common.GenericResponse
import com.appnomic.appsone.api.pojo.UserPreferencePojo
import spark.Request
import spark.Response
import spock.lang.Specification

class UserPreferenceServiceIT extends Specification {

    Request request = Spy(Request.class)
    Response response = new DummyResponse();
    UserPreferenceService service = new UserPreferenceService();

    def "validate get user preference list api endpoint"() {

        setup:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"

        when:
        GenericResponse<List<UserPreferencePojo>> result = service.getPreferenceList(request, response)


        then:
        result != null

        then:
        List<UserPreferencePojo> data = result.data
        data != null
    }


    def "validate add user preference api endpoint"() {

        setup:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> "{\n" +
                "  \"tagKey\": \"GroovyTest\",\n" +
                "  \"tagValue\": \"TestValue\"\n" +
                "}"
        when:
        GenericResponse<Integer> result = service.addPreference(request, response)

        then:
        result != null

        then:
        Integer data = result.data
        data != 0
    }

    def "validate update user preference api endpoint"() {

        setup:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":preferenceId") >> "234"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> "{\n" +
                "  \"tagKey\": \"GroovyTest\",\n" +
                "  \"tagValue\": \"TestValue\"\n" +
                "}"


        when:
        GenericResponse<Integer> result = service.updatePreference(request, response)

        then:
        result != null

        then:
        Integer data = result.data
        data == null
    }

    class DummyResponse extends Response {
        int status;

        void status(int i) {
            status = i;
        }
    }

}