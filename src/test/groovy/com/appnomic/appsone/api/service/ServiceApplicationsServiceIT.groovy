package com.appnomic.appsone.api.service

import com.appnomic.appsone.api.common.Constants
import com.appnomic.appsone.api.common.GenericResponse
import com.appnomic.appsone.api.manager.MySQLConnectionManager
import com.appnomic.appsone.api.pojo.Application
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class ServiceApplicationsServiceIT extends Specification {

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            return KeycloakConnectionManager.getAccessToken()
        }
    }

    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)

    @Shared
    Set<String> header = new HashSet<>()
    Map<String, String> parameters = new HashMap<>()
    String identifier = "d681ef13-d690-4917-jkhg-6c79b-1"
    def service = new ServiceApplicationsService()

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        KeyCloakAuthService.init()
    }

    def "testGetServiceApplications"() {

        given:
        header.add(Constants.AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        parameters.put(Constants.REQUEST_PARAM_SERVICE_ID, "18")
        request.params() >> parameters
        request.body() >> "{}"
        response.status(200)

        when:
        GenericResponse<List<Application>> genericResponse = service.getServiceApplications(request, response)

        then:
        response.status == 200
    }
}
