package com.appnomic.appsone.api.service

import com.appnomic.appsone.api.common.Constants
import com.appnomic.appsone.api.common.GenericResponse
import com.appnomic.appsone.api.manager.MySQLConnectionManager
import com.appnomic.appsone.api.pojo.UIData
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Specification

import java.text.ParseException
import java.text.SimpleDateFormat

class KpiDataServiceIT extends Specification {

    class DummyResponse extends Response {
        int status

        @Override
        void status(int i) {
            status = i
        }

        @Override
        int status() {
            return this.status
        }
    }

    Request request = Spy(Request.class)
    DummyResponse response = Spy(DummyResponse.class)

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def "GET Core KPI Data current date"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        String[] fromTime = [getTimeInMs("25-08-2022 17:00:00")];
        String[] toTime = [getTimeInMs("25-08-2022 17:30:00")];
//        String[] type = [ "123" ];
        Map<String, String[]> queryMap = new HashMap<>();
        queryMap.put("fromTime", fromTime)
        queryMap.put("toTime", toTime)
//        queryMap.put("type", type)
        request.queryMap() >> queryMap
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "heal_health")
        parameters.put(":serviceId", "19")
        parameters.put(":instanceId", "9")
        parameters.put(":kpi_id", "9")
        parameters.put(":group_id", "0")
        request.params() >> parameters
        request.body() >> ""
        when:
        GenericResponse<UIData> res = new KpiDataService().fetchKpiDataService(request, response)

        then:
        response.getStatus() == 200
        res.getResponseStatus() == "SUCCESS"
    }

    def "GET Core KPI Data Old date"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        String[] fromTime = [getTimeInMs("10-08-2022 17:00:00")];
        String[] toTime = [getTimeInMs("10-08-2022 21:00:00")];
//        String[] type = [ "123" ];
        Map<String, String[]> queryMap = new HashMap<>();
        queryMap.put("fromTime", fromTime)
        queryMap.put("toTime", toTime)
//        queryMap.put("type", type)
        request.queryMap() >> queryMap
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "heal_health")
        parameters.put(":serviceId", "19")
//        parameters.put(":instanceId", "9")
        parameters.put(":instanceId", "22")
//        parameters.put(":kpi_id", "9")
        parameters.put(":kpi_id", "1079")
        parameters.put(":group_id", "0")
        parameters.put(":group_id", "71")
        request.params() >> parameters
        request.body() >> ""
        when:
        GenericResponse<UIData> res = new KpiDataService().fetchKpiDataService(request, response)

        then:
        response.getStatus() == 200
        res.getResponseStatus() == "SUCCESS"
    }

    static long getTimeInMs(String time) {
        //Specifying the pattern of input date and time
        SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy hh:mm:ss");
        String dateString = time;
        try {
            //formatting the dateString to convert it into a Date
            Date date = sdf.parse(dateString);
            return date.getTime();

//            Calendar calendar = Calendar.getInstance();
//            //Setting the Calendar date and time to the given date and time
//            calendar.setTime(date);
//            System.out.println("Given Time in milliseconds : "+calendar.getTimeInMillis());
//            return calendar.getTimeInMillis()
        } catch (ParseException e) {
            e.printStackTrace();
        }
    }

}
