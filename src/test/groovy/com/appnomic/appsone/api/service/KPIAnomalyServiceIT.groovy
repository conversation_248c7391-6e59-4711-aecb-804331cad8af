package com.appnomic.appsone.api.service

import com.appnomic.appsone.api.common.GenericResponse
import com.appnomic.appsone.api.pojo.KpiNames
import spark.Request
import spark.Response
import spock.lang.Specification

class KPIAnomalyServiceIT extends Specification {

    Request request = Spy(Request.class)
    Response response = new DummyResponse();

    class DummyResponse extends Response {
        int status;

        void status(int i) {
            status = i;
        }
    }

    def "GetSortedKpiList description test"() {

        setup:

        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":serviceId") >> 11
        request.params(":instanceId") >> 7
        request.params(":categoryId") >> 1
        request.queryParams("fromTime") >> 0
        request.queryParams("toTime") >> 0

        when:
        GenericResponse res = new KPIAnomalyService().getSortedKpiList(request, response)

        then:
        res.data != null
        List<KpiNames> list = res.data as List<KpiNames>
        list.get(0).description != null
        list.get(0).description.trim().length() > 0
    }
}
