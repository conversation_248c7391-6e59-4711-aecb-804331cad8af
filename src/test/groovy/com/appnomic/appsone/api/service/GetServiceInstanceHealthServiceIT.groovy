package com.appnomic.appsone.api.service

import com.appnomic.appsone.api.common.Constants
import com.appnomic.appsone.api.common.GenericResponse
import com.appnomic.appsone.api.common.UIMessages
import com.appnomic.appsone.api.manager.MySQLConnectionManager
import com.appnomic.appsone.api.pojo.ServiceInstanceHealthDetails
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class GetServiceInstanceHealthServiceIT extends Specification {
    GetServiceInstanceHealthService getServiceInstanceHealthService = new GetServiceInstanceHealthService()

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            return "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GwUKGNFwHlmzGkqZekiLxl55XnpYzIYDap2CR5-ZdFOfzxP0_DVO5AQK0B4yk5rp2FE7LKV_Q6aN7vUUuJDRCHghMfL28EryBqwfWjDb6p-GB1YVBcYZWBQz7_X3Y6XWHkKE1Wwt8wCIH2dPxS6jZc7zskKBGX7KTt3pftdM_fTYBu1skr_5KggHokkizQn46DmIfPi7eFkVBweuQEo-3FP1e2XohsXjCgZC79_zOyULF_MfRbaiytVzt2LIeMWvyj9j4rShWsKL-97YU7TplWsQc2AJ1M93MiYG3LJHQ_0-gna1Sziwvo8SyYTlGCZv5giWJeofex7o69ibjMhhVw"
        }
    }

    class InvalidDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }

    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)

    @Shared
    Set<String> header = new HashSet<>()
    Map<String, String> parameters = new HashMap<>()
    String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1"
    String serviceId = "2"

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def "GetServiceInstanceHealth"() {
        given:
        header.add(Constants.AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier)
        parameters.put(Constants.REQUEST_PARAM_SERVICE_ID, serviceId)
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse genericResponse = getServiceInstanceHealthService.getServiceInstanceHealth(request, response)

        then:
        response.status == Constants.SUCCESS_STATUS_CODE
        genericResponse.getMessage() == UIMessages.SERVICE_INSTANCE_HEALTH_GET_SUCCESS

        then:
        genericResponse != null && genericResponse.data != null
        List<ServiceInstanceHealthDetails> serviceInstanceHealthDetails = genericResponse.data
        serviceInstanceHealthDetails.parallelStream().anyMatch({ c -> c.getName() == "RHEL_NB_Web_Host_105_Inst_1-DR" })
        serviceInstanceHealthDetails.parallelStream().anyMatch({ c -> c.getAvailabilityStatus() == 0 })
    }

    def "GetServiceInstanceHealth : invalid authorization token"() {
        given:
        InvalidDummyRequest request = Spy(InvalidDummyRequest.class)
        header.add(Constants.AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier)
        parameters.put(Constants.REQUEST_PARAM_SERVICE_ID, serviceId)
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse genericResponse = getServiceInstanceHealthService.getServiceInstanceHealth(request, response)

        then:
        response.status == 400
        genericResponse.getMessage() == UIMessages.AUTH_KEY_INVALID
    }

    def "GetServiceInstanceHealth : invalid account"() {
        given:
        header.add(Constants.AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, "")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse genericResponse = getServiceInstanceHealthService.getServiceInstanceHealth(request, response)

        then:
        response.status == 400
        genericResponse.getMessage() == UIMessages.ACCOUNT_IDENTIFIER_NULL_EMPTY
    }

    def "GetServiceInstanceHealth : service ID with zero"() {
        given:
        header.add(Constants.AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier)
        parameters.put(Constants.REQUEST_PARAM_SERVICE_ID, "0")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse genericResponse = getServiceInstanceHealthService.getServiceInstanceHealth(request, response)

        then:
        response.status == 400
        genericResponse.getMessage() == "Service ID should be a greater than zero."
    }

    def "GetServiceInstanceHealth : service ID with null or empty"() {
        given:
        header.add(Constants.AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier)
        parameters.put(Constants.REQUEST_PARAM_SERVICE_ID, "")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse genericResponse = getServiceInstanceHealthService.getServiceInstanceHealth(request, response)

        then:
        response.status == 400
        genericResponse.getMessage() == "Service ID is null or empty."
    }
}
