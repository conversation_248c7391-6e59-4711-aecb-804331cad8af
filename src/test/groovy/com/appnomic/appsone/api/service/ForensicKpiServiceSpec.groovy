package com.appnomic.appsone.api.service

import com.appnomic.appsone.api.pojo.Account
import spock.lang.Specification

class ForensicKpiServiceSpec extends Specification{

    ForensicKpiService kpiService = new ForensicKpiService()
    Account account = Spy(Account.class)

    def "validate getDateString method for GMT"(){

        given:
        account.getTimezoneMilli() >> 0

        expect: "return date string in dd MMM yyyy format in given timezone"
        result == kpiService.getDateString(date, account)

        where:
        date           | result
        *************  | "30 Dec 2019"
        *************  | "17 Dec 2019"
        *************  | "17 May 2018"
        *************  | "02 Oct 2019"
    }

    def "validate getHour method for GMT"(){

        given:
        account.getTimezoneMilli() >> 0

        expect: "return date string in HH:00 format in given timezone"
        result == kpiService.getHour(date, account)

        where:
        date           | result
        *************  | "06:00"
        *************  | "13:00"
        *************  | "20:00"
        *************  | "03:00"
    }

    def "validate getDateString method for IST"(){

        given:
        account.getTimezoneMilli() >> ********

        expect: "return date string in dd MMM yyyy format in given timezone"
        result == kpiService.getDateString(date, account)

        where:
        date           | result
        *************  | "30 Dec 2019"
        *************  | "17 Dec 2019"
        *************  | "18 May 2018"
        *************  | "02 Oct 2019"
    }

    def "validate getHour method for IST"(){

        given:
        account.getTimezoneMilli() >> ********

        expect: "return date string in HH:00 format in given timezone"
        result == kpiService.getHour(date, account)

        where:
        date           | result
        *************  | "12:00"
        *************  | "18:00"
        *************  | "01:00"
        *************  | "09:00"
    }

}
