package com.appnomic.appsone.api.service


import com.appnomic.appsone.api.common.Constants
import com.appnomic.appsone.api.manager.MySQLConnectionManager
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class InstanceCategoryDetailsServiceIT extends Specification{
    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            return KeycloakConnectionManager.getAccessToken()
        }
    }

    class InvalidDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }

    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)
    @Shared
    Map<String, String[]> queryMapData = new HashMap<>()
    @Shared
    Map<String, String> parameters = new HashMap<>()
    @Shared
    Set<String> header = new HashSet<>()
    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(false)
        KeyCloakAuthService.init()
    }
    String[] t1=["*************"]
    String[] t2=["*************"]
    def "Instance categories api Failure: Account identifier null"() {
        given:
        header.add(Constants.AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()

        parameters.put(Constants.ACCOUNT_IDENTIFIER, "")
        parameters.put(Constants.REQUEST_PARAM_SERVICE_ID,"2")
        parameters.put(Constants.REQUEST_PARAM_CLUSTER_ID,"1")
        parameters.put(Constants.REQUEST_PARAM_COM_INSTANCE_ID, "12")
        queryMapData.put("fromTime", t1)
        queryMapData.put("toTime", t2)
        request.queryMap() >> queryMapData
        request.params() >> parameters
        request.body() >> ""
        when:
        def res = new InstanceCategoryDetailsService().getInstanceCategoryDetails(request, response)
        then:
        response.getStatus() == 400
        res.getMessage() == "Error while fetching instance categories"
    }
    def "Instance categories api Failure: Account identifier invalid"() {
        given:
        header.add(Constants.AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()

        parameters.put(Constants.ACCOUNT_IDENTIFIER, "qa-d681ef13-d690-4917-jkhg-6c79b-10")
        parameters.put(Constants.REQUEST_PARAM_SERVICE_ID,"2")
        parameters.put(Constants.REQUEST_PARAM_CLUSTER_ID,"1")
        parameters.put(Constants.REQUEST_PARAM_COM_INSTANCE_ID, "12")
        queryMapData.put("fromTime", t1)
        queryMapData.put("toTime", t2)
        request.queryMap() >> queryMapData
        request.params() >> parameters
        request.body() >> ""
        when:
        def res = new InstanceCategoryDetailsService().getInstanceCategoryDetails(request, response)
        then:
        response.getStatus() == 400
        res.getMessage() == "Error while fetching instance categories"
    }
    def "Transaction count api Failure: User token identifier invalid"() {
        given:
        InvalidDummyRequest invalidRequest = Spy(InvalidDummyRequest.class)
        header.add(Constants.AUTHORIZATION_HEADER)
        invalidRequest.headers() >> header.toSet()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, "qa-d681ef13-d690-4917-jkhg-6c79b-10")
        parameters.put(Constants.REQUEST_PARAM_SERVICE_ID,"2")
        parameters.put(Constants.REQUEST_PARAM_CLUSTER_ID,"1")
        parameters.put(Constants.REQUEST_PARAM_COM_INSTANCE_ID, "12")
        queryMapData.put("fromTime", t1)
        queryMapData.put("toTime", t2)
        invalidRequest.queryMap() >> queryMapData
        invalidRequest.params() >> parameters
        invalidRequest.body() >> ""
        when:
        def res = new InstanceCategoryDetailsService().getInstanceCategoryDetails(invalidRequest, response)
        then:
        response.getStatus() == 400
        res.getMessage() == "Error while fetching instance categories"
    }
    def "Instance categories api Failure: service id invalid"() {
        given:
        header.add(Constants.AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()

        parameters.put(Constants.ACCOUNT_IDENTIFIER, "qa-d681ef13-d690-4917-jkhg-6c79b-1")
        parameters.put(Constants.REQUEST_PARAM_SERVICE_ID,"110")
        parameters.put(Constants.REQUEST_PARAM_CLUSTER_ID,"1")
        parameters.put(Constants.REQUEST_PARAM_COM_INSTANCE_ID, "12")
        queryMapData.put("fromTime", t1)
        queryMapData.put("toTime", t2)
        request.queryMap() >> queryMapData
        request.params() >> parameters
        request.body() >> ""
        when:
        def res = new InstanceCategoryDetailsService().getInstanceCategoryDetails(request, response)
        then:
        response.getStatus() == 400
        res.getMessage() == "Error while fetching instance categories"
    }

    def "Instance categories api Failure: cluster id invalid"() {
        given:
        header.add(Constants.AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()

        parameters.put(Constants.ACCOUNT_IDENTIFIER, "qa-d681ef13-d690-4917-jkhg-6c79b-1")
        parameters.put(Constants.REQUEST_PARAM_SERVICE_ID,"10")
        parameters.put(Constants.REQUEST_PARAM_CLUSTER_ID,"110")
        parameters.put(Constants.REQUEST_PARAM_COM_INSTANCE_ID, "12")
        queryMapData.put("fromTime", t1)
        queryMapData.put("toTime", t2)
        request.queryMap() >> queryMapData
        request.params() >> parameters
        request.body() >> ""
        when:
        def res = new InstanceCategoryDetailsService().getInstanceCategoryDetails(request, response)
        then:
        response.getStatus() == 400
        res.getMessage() == "Error while fetching instance categories"
    }
    def "Instance categories api Failure: instance id invalid"() {
        given:
        header.add(Constants.AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()

        parameters.put(Constants.ACCOUNT_IDENTIFIER, "qa-d681ef13-d690-4917-jkhg-6c79b-1")
        parameters.put(Constants.REQUEST_PARAM_SERVICE_ID,"10")
        parameters.put(Constants.REQUEST_PARAM_CLUSTER_ID,"10")
        parameters.put(Constants.REQUEST_PARAM_COM_INSTANCE_ID, "12")
        queryMapData.put("fromTime", t1)
        queryMapData.put("toTime", t2)
        request.queryMap() >> queryMapData
        request.params() >> parameters
        request.body() >> ""
        when:
        def res = new InstanceCategoryDetailsService().getInstanceCategoryDetails(request, response)
        then:
        response.getStatus() == 400
        res.getMessage() == "Error while fetching instance categories"
    }

    def "Instance categories api Failure: instance id success"() {
        given:
        header.add(Constants.AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()

        parameters.put(Constants.ACCOUNT_IDENTIFIER, "qa-d681ef13-d690-4917-jkhg-6c79b-1")
        parameters.put(Constants.REQUEST_PARAM_SERVICE_ID, "10")
        parameters.put(Constants.REQUEST_PARAM_CLUSTER_ID, "10")
        parameters.put(Constants.REQUEST_PARAM_COM_INSTANCE_ID, "10")
        queryMapData.put("fromTime", t1)
        queryMapData.put("toTime", t2)
        request.queryMap() >> queryMapData
        request.params() >> parameters
        request.body() >> ""
        when:
        def res = new InstanceCategoryDetailsService().getInstanceCategoryDetails(request, response)
        then:
        response.getStatus() == 200
        res.getMessage() == Constants.SUCCESS_MESSAGE
        def result = res.getData()
            .parallelStream()
                .filter({ it -> it.eventCount > 0 })
                .findAny()
        result != null
    }
}
