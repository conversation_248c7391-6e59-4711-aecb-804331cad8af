package com.appnomic.appsone.api.service

import com.appnomic.appsone.api.pojo.AvailabilityKpi
import com.appnomic.appsone.api.pojo.AvailabilityKpiResponse
import spark.Request
import spark.Response
import spock.lang.Specification

class AnomalyServiceIT extends Specification{

    Request request = Spy(Request.class)
    Response response = new DummyResponse();
    KPIAnomalyService service = new KPIAnomalyService();

    def "validate availability kpi and anomaly api"(){

        setup:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":instanceId") >> "4"
        request.queryParams("fromTime") >> 1571043600000
        request.queryParams("toTime") >> 1571050800000

        when:
        AvailabilityKpiResponse result = service.getAvailabilityKpiList(request, response)


        then:
        result != null

        then:
        List<AvailabilityKpi> data = result.data
        data != null
    }

    class DummyResponse extends Response {
        int status;
        void status(int i) {
            status = i;
        }
    }

}
