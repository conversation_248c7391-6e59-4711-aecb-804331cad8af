package com.appnomic.appsone.api.service

import com.appnomic.appsone.api.common.UIMessages
import com.appnomic.appsone.api.util.StringUtils
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Specification

import static com.appnomic.appsone.api.common.Constants.*

class GetForensicNotificationConfigurationsServiceIT extends Specification {


    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            String authToken = KeycloakConnectionManager.getAccessToken()
            if (StringUtils.isEmpty(authToken))
                return "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCS" +
                        "jhzMnVhazFUUWd3In0.eyJleHAiOjE2MjU4MjM1MjEsImlhdCI6MTYyNTgyMDgyMSwianRpIjoiNmFlNGVjMDEtZDI5N" +
                        "i00ZTQ4LWI2NTQtNzY4Y2YxY2M0NGQxIiwiaXNzIjoiaHR0cHM6Ly8xOTIuMTY4LjEzLjQ0Ojg0NDMvYXV0aC9yZWFsb" +
                        "XMvbWFzdGVyIiwic3ViIjoiNzY0MDEyM2EtZmJkZS00ZmU1LTk4MTItNTgxY2QxZTNhOWMxIiwidHlwIjoiQmVhcmVyI" +
                        "iwiYXpwIjoiYWRtaW4tY2xpIiwic2Vzc2lvbl9zdGF0ZSI6IjkxNjBiOWQyLWI5NjctNGZlOS1hZWFmLTI5N2Q5YTBjO" +
                        "GRlNyIsImFjciI6IjEiLCJhbGxvd2VkLW9yaWdpbnMiOlsiKiJdLCJzY29wZSI6InByb2ZpbGUgZW1haWwiLCJlbWFpb" +
                        "F92ZXJpZmllZCI6ZmFsc2UsInByZWZlcnJlZF91c2VybmFtZSI6ImFwcHNvbmVhZG1pbiIsImVtYWlsIjoiYXBwc29uZW" +
                        "FkbWluLmtAYXBwbm9taWMuY29tIn0.FmNEY7oOraBuukI4ORBzK4TOkdRI9LBKQMiSzJhjhFQ7LIOih5SyoioHKfOI_Kh" +
                        "PaklnCTQSTlBSO7teYTo1M48I0AEOjCf7MMGzIxwm78UrPcJlg_VuNcoHIM2XaPiZxUcKZHVIsC33OuX0uPBf2TKt8GyC" +
                        "nQQoC9CiKHT3HKVG1koDbL-wRCL4hG2_LHQq5z3LqO9u_Cq5QUbJvFE2N2fTt6j8bkKIWK0FtC4CsNCKTtB5fX8m7YhfD" +
                        "2pXy0I-7i-B1fZFnM7DE0uFRScvp4CoPQyHZ11UWAdhPdOewffO9FDpEXfqhYyX195NJSDVfqkrR1Kx1XU-VVirxegD8Q"
            return authToken
        }
    }

    class InvalidTokenDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }

    class NullTokenDummyRequest extends Request {

        @Override
        String headers(String header) {
            return null
        }
    }

    class EmptyTokenDummyRequest extends Request {

        @Override
        String headers(String header) {
            return ""
        }
    }

    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)

    Set<String> header = new HashSet<>()
    Map<String, String> parameters = new HashMap<>()
    Map<String, String[]> queryMap = new HashMap<>()
    String identifier = "d681ef13-d690-4917-jkhg-6c79b-1"
    String userId = "7640123a-fbde-4fe5-9812-581cd1e3a9c1"
    GetForensicNotificationConfigurationsService service = new GetForensicNotificationConfigurationsService()

    def setupSpec() {
        KeyCloakAuthService.init()
    }


    def "GetForensicNotificationConfigurations"() {

        given:
        header.add(AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(REQUEST_PARAM_USER_ID, userId)
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        def res = service.getForensicNotificationConfigurations(request, response)

        then:
        response.status == SUCCESS_STATUS_CODE
        res.getData() != null
    }

    def "GetForensicNotificationConfigurations : Auth token NULL"() {
        setup:
        NullTokenDummyRequest nullDummyRequest = Spy(NullTokenDummyRequest.class)
        header.add(AUTHORIZATION_HEADER)
        nullDummyRequest.headers() >> header.toSet()
        nullDummyRequest.queryMap() >> queryMap
        parameters.put(REQUEST_PARAM_USER_ID, userId)
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        nullDummyRequest.params() >> parameters
        nullDummyRequest.body() >> ""


        when:
        def res = service.getForensicNotificationConfigurations(nullDummyRequest, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(MESSAGE_INVALID_AUTH_TOKEN)
    }

    def "GetForensicNotificationConfigurations : Auth token Empty"() {
        setup:
        EmptyTokenDummyRequest emptyDummyRequest = Spy(EmptyTokenDummyRequest.class)
        header.add(AUTHORIZATION_HEADER)
        emptyDummyRequest.headers() >> header.toSet()
        emptyDummyRequest.queryMap() >> queryMap
        parameters.put(REQUEST_PARAM_USER_ID, userId)
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        emptyDummyRequest.params() >> parameters
        emptyDummyRequest.body() >> ""


        when:
        def res = service.getForensicNotificationConfigurations(emptyDummyRequest, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(MESSAGE_INVALID_AUTH_TOKEN)
    }

    def "GetForensicNotificationConfigurations : Account Identifier NULL"() {
        setup:
        header.add(AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(REQUEST_PARAM_USER_ID, userId)
        parameters.put(ACCOUNT_IDENTIFIER, null)
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.getForensicNotificationConfigurations(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.ACCOUNT_EMPTY)
    }

    def "GetForensicNotificationConfigurations : Account Identifier Empty"() {
        setup:
        header.add(AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(REQUEST_PARAM_USER_ID, userId)
        parameters.put(ACCOUNT_IDENTIFIER, "")
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.getForensicNotificationConfigurations(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.ACCOUNT_EMPTY)
    }

    def "GetForensicNotificationConfigurations : User Id NULL"() {
        setup:
        header.add(AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(REQUEST_PARAM_USER_ID, null)
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.getForensicNotificationConfigurations(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("userId should not be NULL or empty.")
    }

    def "GetForensicNotificationConfigurations : User Id Empty"() {
        setup:
        header.add(AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(REQUEST_PARAM_USER_ID, " ")
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.getForensicNotificationConfigurations(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("userId should not be NULL or empty.")
    }

    def "GetForensicNotificationConfigurations : Auth token Invalid"() {
        setup:
        InvalidTokenDummyRequest invalidDummyRequest = Spy(InvalidTokenDummyRequest.class)
        header.add(AUTHORIZATION_HEADER)
        invalidDummyRequest.headers() >> header.toSet()
        invalidDummyRequest.queryMap() >> queryMap
        parameters.put(REQUEST_PARAM_USER_ID, userId)
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        invalidDummyRequest.params() >> parameters
        invalidDummyRequest.body() >> ""


        when:
        def res = service.getForensicNotificationConfigurations(invalidDummyRequest, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(MESSAGE_INVALID_AUTH_TOKEN)
    }

    def "GetForensicNotificationConfigurations : Account Identifier Invalid"() {
        setup:
        header.add(AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(REQUEST_PARAM_USER_ID, userId)
        parameters.put(ACCOUNT_IDENTIFIER, "invalid-account-identifier")
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.getForensicNotificationConfigurations(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(MESSAGE_INVALID_ACCOUNT)
    }

    def "GetForensicNotificationConfigurations : User Id Invalid"() {
        setup:
        header.add(AUTHORIZATION_HEADER)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(REQUEST_PARAM_USER_ID, "invalid-user-id")
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.getForensicNotificationConfigurations(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.USER_NOT_EXISTS)
    }
}
