package com.appnomic.appsone.api.service.mysql

import com.appnomic.appsone.api.common.Constants
import com.appnomic.appsone.api.common.GenericResponse
import com.appnomic.appsone.api.common.StatusResponse
import com.appnomic.appsone.api.pojo.Account
import com.appnomic.appsone.api.pojo.RowDetails
import com.appnomic.appsone.api.pojo.TransactionConfigData
import com.appnomic.appsone.api.pojo.TxnAndGroupBean
import com.appnomic.appsone.api.pojo.request.TransactionConfigRequest
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class TransactionConfigServiceSpec extends Specification {
    Request request = Spy(Request.class)
    Response response = new DummyResponse()
    TransactionConfigService transactionConfigService = Spy(TransactionConfigService.class)
    TransactionConfigRequest transactionConfigRequest = Spy(TransactionConfigRequest.class)
    @Shared
    Account account = new Account()
    @Shared
    TxnAndGroupBean transaction = new TxnAndGroupBean()
    @Shared
    RowDetails rowDetails = new RowDetails()

    def setupSpec() {
        account.setAccountId(1)
        account.setIdentifier("test")
        transaction.setTxnId(1)
        transaction.setTxnName("Txn-1")
        rowDetails.setName("TestName")
        rowDetails.setValue("TestValue")
        rowDetails.setUrl("TestUrl")
    }

    def "GetTransactionConfig"() {
        setup:
        request.params(":identifier") >> "test"
        request.params(":transaction_id") >> "1"
        transactionConfigRequest.setResponse(response)
        transactionConfigRequest.getAccountData() >> account
        transactionConfigRequest.getTransactionData() >> transaction
        transactionConfigService.setTransactionConfigRequest(transactionConfigRequest)
        transactionConfigService.getAttributes(transaction.getTxnId()) >> Arrays.asList(rowDetails)

        when:
        GenericResponse res = transactionConfigService.getTransactionConfig()

        then:
        res.getData() != null
        res.getMessage().equals(Constants.MESSAGE_SUCCESS)
        res.getResponseStatus().equals(StatusResponse.SUCCESS.toString())
    }

    def "GetTransactionConfig with invalid account id"() {
        setup:
        request.params(":identifier") >> "test"
        request.params(":transaction_id") >> "1"
        transactionConfigRequest.setResponse(response)
        transactionConfigRequest.getAccountData() >> null
        transactionConfigRequest.getTransactionData() >> transaction
        transactionConfigService.setTransactionConfigRequest(transactionConfigRequest)
        transactionConfigService.getAttributes(transaction.getTxnId()) >> Arrays.asList(rowDetails)

        when:
        GenericResponse res = transactionConfigService.getTransactionConfig()

        then:
        res.getData() == null
        res.getResponseStatus().equals(StatusResponse.FAILURE.toString())
        res.getMessage().equals(Constants.MESSAGE_INVALID_ACCOUNT)
    }

    def "GetTransactionConfig with invalid transaction id"() {
        setup:
        request.params(":identifier") >> "test"
        request.params(":transaction_id") >> "1"
        transactionConfigRequest.setResponse(response)
        transactionConfigRequest.getAccountData() >> account
        transactionConfigRequest.getTransactionData() >> null
        transactionConfigService.setTransactionConfigRequest(transactionConfigRequest)
        transactionConfigService.getAttributes(transaction.getTxnId()) >> Arrays.asList(rowDetails)

        when:
        GenericResponse res = transactionConfigService.getTransactionConfig()

        then:
        res.getData() == null
        res.getResponseStatus().equals(StatusResponse.FAILURE.toString())
        res.getMessage().equals(Constants.MESSAGE_INVALID_TRANSACTION)
    }

    def "GetTransactionConfig with invalid transaction attributes"() {
        setup:
        request.params(":identifier") >> "test"
        request.params(":transaction_id") >> "1"
        transactionConfigRequest.setResponse(response)
        transactionConfigRequest.getAccountData() >> account
        transactionConfigRequest.getTransactionData() >> transaction
        transactionConfigService.setTransactionConfigRequest(transactionConfigRequest)
        transactionConfigService.getAttributes(transaction.getTxnId()) >> null

        when:
        GenericResponse<TransactionConfigData> res = transactionConfigService.getTransactionConfig()
        TransactionConfigData resData = (TransactionConfigData)res.getData()

        then:
        resData.getPattern().size() == 0
        res.getResponseStatus().equals(StatusResponse.SUCCESS.toString())
    }

    def "GetTransactionConfig with internal error"() {
        setup:
        request.params(":identifier") >> "test"
        request.params(":transaction_id") >> "1"
        transactionConfigRequest.setResponse(response)
        transactionConfigRequest.getAccountData() >> account
        transactionConfigRequest.getTransactionData() >> transaction
        transactionConfigService.setTransactionConfigRequest(transactionConfigRequest)
        transactionConfigService.getAttributes(transaction.getTxnId()) >> Arrays.asList(rowDetails)
        transactionConfigRequest.validateAndPopulate() >> { throw new Exception("Test") }

        when:
        GenericResponse<TransactionConfigData> res = transactionConfigService.getTransactionConfig()

        then:
        res.getResponseStatus().equals(StatusResponse.FAILURE.toString())
        res.getMessage().equals(Constants.MESSAGE_INTERNAL_ERROR)
    }

    class DummyResponse extends Response {
        void status(int i) {
            // do nothing
        }
    }
}
