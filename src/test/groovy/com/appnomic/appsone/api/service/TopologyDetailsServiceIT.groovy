package com.appnomic.appsone.api.service

import com.appnomic.appsone.api.pojo.TopologyDetailsResponse
import spark.Request
import spock.lang.Specification

class TopologyDetailsServiceIT extends Specification {
    Request request = Spy(Request.class)

    def "GetTopologyDetails user invalid"() {

        setup:

        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.queryParams("applicationId") >> null

        request.headers("Authorization") >> "abc"

        when:
        TopologyDetailsResponse response = TopologyDetailsService.getTopologyDetails(request)

        then:
        response != null

        then:
        response.responseStatus == "FAILURE"

    }

    def "GetTopologyDetails user admin"() {

        setup:

        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.queryParams("applicationId") >> null

        request.headers("Authorization") >> "eyJhbGciOiJSUzUxMiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICI3TWFqem5objVlcXM4ZThfMW80djZ5cXVDZFdUQUdKWVNpRE1hejk5dTFvIn0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.N9gA9TmxYzXYfpVonezQR2twCJeUyar2tPY9N5z3vl6HBH7IhcytQXBviI8cMIHgd-JSUAaahOAOLmPWTScUFrCErkN-Ez6sWcMx1YeuphO8DOzniaMtlXl5oSmIWIr6-0ErST_xWNwcuj8BXMfWj-LqaDqdZMw6bgB9c7TfkYSBGFFGIxczwa0iX4UXqfZAMZKNBQMoC0ML4hOqXV_fszP-PzBrhiYnTvTznzER3y4T54qBqD5nHwT58VBgaUoBm3HJ58oU90oEjeMNGyE9i-QLIgEg0Zf1cm1NQ4EfvykVSlvchujH8k6AREGttrQCseo87DjZQHPukxa0eckRig"

        when:
        TopologyDetailsResponse response = TopologyDetailsService.getTopologyDetails(request)

        then:
        response != null

        then:
        response.responseStatus == "SUCCESS"
        response.data != null

        then:
        TopologyDetailsResponse.TopologyDetails result = response.data as TopologyDetailsResponse.TopologyDetails
        result.nodes.parallelStream().noneMatch({ n -> !n.userAccessible })

    }

    def "GetTopologyDetails user inaccessible account "() {

        setup:

        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-3"
        request.queryParams("applicationId") >> null

        request.headers("Authorization") >> "eyJhbGciOiJSUzUxMiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICI3TWFqem5objVlcXM4ZThfMW80djZ5cXVDZFdUQUdKWVNpRE1hejk5dTFvIn0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ZTIv0CK9qJWEkLVEoyxnYoAsYQ_MfpimsuF4ac0mSA1CjXxSH-DKpf25EYHWmM6dHMjn75l0ba1Y9HV90pbNhx1b3lisWmChQwnCttAEVTa_aRrY-lPn2ZXs8qaIU-dkdsGLhytH1TCqGs-kpYoU0p3dO9XfvEKd5iPr2dhDWz20NVwlHIwaBBM3MSaomOUuEjKjZcXtJyo5_R0zOGgisSIIYczZo2BHvqDXq9vndb9Owru0DAPfVAEFj58i2nAZ9D0BMtbsr5R2Aqp0kA5TusI2My-rp8Zm1IicJFs9EsQlxTKHX6FOUHCA9edgN30slz85SEv3hfGsMILTe7YqYQ"

        when:
        TopologyDetailsResponse response = TopologyDetailsService.getTopologyDetails(request)

        then:
        response != null

        then:
        response.responseStatus == "FAILURE"
    }

    def "GetTopologyDetails user "() {

        setup:

        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.queryParams("applicationId") >> null

        request.headers("Authorization") >> "eyJhbGciOiJSUzUxMiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICI3TWFqem5objVlcXM4ZThfMW80djZ5cXVDZFdUQUdKWVNpRE1hejk5dTFvIn0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ZTIv0CK9qJWEkLVEoyxnYoAsYQ_MfpimsuF4ac0mSA1CjXxSH-DKpf25EYHWmM6dHMjn75l0ba1Y9HV90pbNhx1b3lisWmChQwnCttAEVTa_aRrY-lPn2ZXs8qaIU-dkdsGLhytH1TCqGs-kpYoU0p3dO9XfvEKd5iPr2dhDWz20NVwlHIwaBBM3MSaomOUuEjKjZcXtJyo5_R0zOGgisSIIYczZo2BHvqDXq9vndb9Owru0DAPfVAEFj58i2nAZ9D0BMtbsr5R2Aqp0kA5TusI2My-rp8Zm1IicJFs9EsQlxTKHX6FOUHCA9edgN30slz85SEv3hfGsMILTe7YqYQ"


        when:
        TopologyDetailsResponse response = TopologyDetailsService.getTopologyDetails(request)

        then:
        response != null

        then:
        response.responseStatus == "SUCCESS"
        response.data != null

        then:
        TopologyDetailsResponse.TopologyDetails result = response.data as TopologyDetailsResponse.TopologyDetails
        !result.nodes.get(6).userAccessible
    }
}
