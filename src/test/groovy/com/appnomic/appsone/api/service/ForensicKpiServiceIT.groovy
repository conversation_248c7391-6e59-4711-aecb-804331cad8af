package com.appnomic.appsone.api.service

import com.appnomic.appsone.api.common.GenericResponse
import com.appnomic.appsone.api.pojo.ForensicKpiDataResponse
import spark.Request
import spark.Response
import spock.lang.Specification

class ForensicKpiServiceIT extends Specification{

    Request request = Spy(Request.class)
    Response response = new DummyResponse()

    ForensicKpiService kpiService = new ForensicKpiService()
    String accountIdentifier = "d681ef13-d690-4917-jkhg-6c79b-5"

    def "validation of getKpiTimeSeries api with correct input"(){
        given:
        request.params(":identifier") >> accountIdentifier
        request.params(":comp_instance_id") >> 1
        request.queryParams("fromTime") >> *************
        request.queryParams("toTime") >> *************

        when:
        GenericResponse genericResponse = kpiService.getKpiTimeSeries(request, response)

        then:
        genericResponse != null
        response.status() == 200

        then:
        Map<String, TreeMap<String, List<Long>>> map = genericResponse.getData()
        map != null
    }


    def "validation of getForensicKpiTopNSQL api with correct input"(){
        given:
        request.params(":identifier") >> accountIdentifier
        request.params(":comp_instance_id") >> 1
        request.queryParams("time") >> *************

        when:
        GenericResponse genericResponse = kpiService.getForensicKpiTopNSQL(request, response)

        then:
        genericResponse != null
        response.status() == 200

        then:
        ForensicKpiDataResponse dataResponse = genericResponse.getData()
        dataResponse != null
    }


    class DummyResponse extends Response {
        int status
        void status(int i) {
            status = i
        }

        int status(){
            return status
        }
    }
}
