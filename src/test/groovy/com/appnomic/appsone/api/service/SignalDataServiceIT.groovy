package com.appnomic.appsone.api.service

import com.appnomic.appsone.api.common.GenericResponse
import com.appnomic.appsone.api.pojo.SignalData
import spark.Request
import spark.Response
import spock.lang.Specification

class SignalDataServiceIT extends Specification{
    Response response = new DummyResponse()
    Request request = Spy(Request.class)
    SignalDataService signalDataService = new SignalDataService()

    def "validate problem list"() {
        setup:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":serviceId") >> "13"
        request.queryParams("fromTime") >> "1586664000000"
        request.queryParams("toTime") >> "1586850879000"
        request.queryParams("problemId") >> null
        request.queryParams("type") >> null

        when:
        GenericResponse<Set<SignalData>> response = signalDataService.getSignalList(request, response)

        then:
        response != null

        then:
        response.data != null
    }

    class DummyResponse extends Response {
        int status
        void status(int i) {
            status = i
        }
    }

}
