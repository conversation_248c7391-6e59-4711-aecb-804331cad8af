package com.appnomic.appsone.api.service

import com.appnomic.appsone.api.common.GenericResponse
import com.appnomic.appsone.api.pojo.RCAPathResponseObject
import spark.Request
import spock.lang.Specification

class RCAPathServiceIT extends Specification {
    Request request = Spy(Request.class)

    def "GetRCAPathDetails user invalid"() {
        setup:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":signalId") >> "E-2-1-4-********"

        request.headers("Authorization") >> "abc"
        request.queryParams("type") >> "W"

        when:
        GenericResponse response = new RCAPathService().getRCAPathDetails(request)

        then:
        response != null

        then:
        response.responseStatus == "FAILURE"
    }

    def "GetRCAPathDetails user inaccessible account "() {

        setup:

        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-2"
        request.params(":signalId") >> "E-2-1-4-********"

        request.headers("Authorization") >> "eyJhbGciOiJSUzUxMiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICI3TWFqem5objVlcXM4ZThfMW80djZ5cXVDZFdUQUdKWVNpRE1hejk5dTFvIn0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ZTIv0CK9qJWEkLVEoyxnYoAsYQ_MfpimsuF4ac0mSA1CjXxSH-DKpf25EYHWmM6dHMjn75l0ba1Y9HV90pbNhx1b3lisWmChQwnCttAEVTa_aRrY-lPn2ZXs8qaIU-dkdsGLhytH1TCqGs-kpYoU0p3dO9XfvEKd5iPr2dhDWz20NVwlHIwaBBM3MSaomOUuEjKjZcXtJyo5_R0zOGgisSIIYczZo2BHvqDXq9vndb9Owru0DAPfVAEFj58i2nAZ9D0BMtbsr5R2Aqp0kA5TusI2My-rp8Zm1IicJFs9EsQlxTKHX6FOUHCA9edgN30slz85SEv3hfGsMILTe7YqYQ"
        request.queryParams("type") >> "W"

        when:
        GenericResponse response = new RCAPathService().getRCAPathDetails(request)

        then:
        response != null

        then:
        response.responseStatus == "FAILURE"
    }

    def "GetRCAPathDetails admin"() {

        setup:

        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":signalId") >> "E-2-1-4-********"

        request.headers("Authorization") >> "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        request.queryParams("type") >> "W"

        when:
        GenericResponse response = new RCAPathService().getRCAPathDetails(request)

        then:
        response != null

        then:
        response.responseStatus == "SUCCESS"
        RCAPathResponseObject.ResponseData data = response.data

        then:
        data.rcaPaths.parallelStream().noneMatch({ t -> t.nodes.parallelStream()
                .anyMatch({ n -> !n.userAccessible }) })
    }

    def "GetRCAPathDetails user"() {

        setup:

        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":signalId") >> "E-2-1-4-********"

        request.headers("Authorization") >> "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        request.queryParams("type") >> "W"

        when:
        GenericResponse response = new RCAPathService().getRCAPathDetails(request)

        then:
        response != null

        then:
        response.responseStatus == "SUCCESS"
        RCAPathResponseObject.ResponseData data = response.data

        then:
        !data.rcaPaths.get(0).nodes.get(5).userAccessible
    }




}
