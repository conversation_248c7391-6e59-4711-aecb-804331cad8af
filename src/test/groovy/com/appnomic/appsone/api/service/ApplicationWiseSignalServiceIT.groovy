package com.appnomic.appsone.api.service

import com.appnomic.appsone.api.common.Constants
import com.appnomic.appsone.api.common.GenericResponse
import com.appnomic.appsone.api.pojo.SignalData
import spark.Request
import spark.Response
import spock.lang.Specification

class ApplicationWiseSignalServiceIT extends Specification{
    Request request = Spy(Request.class)
    Response response = new DummyResponse()
    ApplicationWiseSignalsService applicationWiseSignalsService = Spy(ApplicationWiseSignalsService.class)
    class DummyResponse extends Response {
        int status
        void status(int i) {
            status = i
        }
    }
    def "get signals based on time range"(){

        setup:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":applicationId") >> "1"
        request.queryParams("fromTime") >> "1586664000000"
        request.queryParams("toTime") >> "1586850879000"
        request.queryParams("status") >> null
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.f4vVXGo3h3l6IP1Y9OXPR-UWYpY2SgIS9o-uAjzA5wjrpCrh-LmQrvqs-Apo0LSbkfvbSYKLVWGwAW16VrCH2eyx25NW64kgEs6PP9AovJM9uL6Nw_BzRi5dO_BwtFnT2NlC2yGC7fmE47oYkC9c8-wPp3IuPtrXvaJ1CfxPlqHbTYWJTGI4tPzCYTRA1ppCwqLFOw_0eod_p3z-RLSFoyw5MWlKFy1PjvvNrGYRY-HFOriDvjIuT7lOkAxnOPVm3cL6rJSKgC4Ur3kOejGtpUjy0S_KRL1-BmZMbRaGa7Y7CD0b6ZJFgNVhYgcvBxmDEAdOL8SnCYzSDiHKBbd8tw"
        when:
        GenericResponse<Set<SignalData>> data = applicationWiseSignalsService.getApplicationSignals(request, response)

        then:
        data.getResponseStatus()== Constants.MESSAGE_SUCCESS

        then:
        data.getData().size() == 0

    }
    def "get assigned application to user filtering with current application LOS-webserver"(){

        setup:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":applicationId") >> "9"
        request.queryParams("fromTime") >> "1586664000000"
        request.queryParams("toTime") >> "1586850879000"
        request.queryParams("status") >> null
        request.headers("Authorization") >> "eyJhbGciOiJSUzUxMiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICI3TWFqem5objVlcXM4ZThfMW80djZ5cXVDZFdUQUdKWVNpRE1hejk5dTFvIn0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.IKFEZJAagf1taFL7LWltll1RV3B_N_pH1xvOVLjqfKbkz1nkcH9fNS4L8CN1k6Q9Q6EhmtTHSNof2e-rrl99CIj_O9fmk3guy4cJNzFe5vqwP9WsK-koP1nqCTE66v8btGyiHs1zTxptmn98AmRqAjXDRKSjeFrMbd5z3woYHPsbS-Ec4v4OSXs9FQdANAeIMxbi7S_gw8OkaIb7Xe0OcpMvAhgWDS7pP04ILT77Vvhj3aFyeZ3NXHIWqkgGG1o2z89u0rVKfjUhiEP3mbCL8eyMSUbyalL1Kp3ATa2sc-UhjQy7Zi8_xLNr-51xbB_e7mp5auUwpnu2S6qaRR04IQ"
        when:
        GenericResponse<Set<SignalData>> data = applicationWiseSignalsService.getApplicationSignals(request, response)

        then:
        data.getResponseStatus()== Constants.MESSAGE_SUCCESS

        then:
        data.getData().size() == 0

    }
    def "get signals based on time range with application not exist"(){

        setup:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":applicationId") >> "999"
        request.queryParams("fromTime") >> "1586664000000"
        request.queryParams("toTime") >> "1586850879000"
        request.queryParams("status") >> null
        request.headers("Authorization") >> "eyJhbGciOiJSUzUxMiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICI3TWFqem5objVlcXM4ZThfMW80djZ5cXVDZFdUQUdKWVNpRE1hejk5dTFvIn0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.IKFEZJAagf1taFL7LWltll1RV3B_N_pH1xvOVLjqfKbkz1nkcH9fNS4L8CN1k6Q9Q6EhmtTHSNof2e-rrl99CIj_O9fmk3guy4cJNzFe5vqwP9WsK-koP1nqCTE66v8btGyiHs1zTxptmn98AmRqAjXDRKSjeFrMbd5z3woYHPsbS-Ec4v4OSXs9FQdANAeIMxbi7S_gw8OkaIb7Xe0OcpMvAhgWDS7pP04ILT77Vvhj3aFyeZ3NXHIWqkgGG1o2z89u0rVKfjUhiEP3mbCL8eyMSUbyalL1Kp3ATa2sc-UhjQy7Zi8_xLNr-51xbB_e7mp5auUwpnu2S6qaRR04IQ"
        when:
        GenericResponse<Set<SignalData>> data = applicationWiseSignalsService.getApplicationSignals(request, response)

        then:
        data.getResponseStatus()== Constants.MESSAGE_FAILURE


    }
}
