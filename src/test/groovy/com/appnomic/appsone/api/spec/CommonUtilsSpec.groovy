package com.appnomic.appsone.api.spec


import com.appnomic.appsone.api.util.CommonUtils
import spock.lang.Specification

/**
 * <AUTHOR> : 16/01/20
 */
class CommonUtilsSpec extends Specification {
    def "get thresholds for greater than operation"() {
        given:
        def thresholds = [lower:12.0d, upper:0.0d]
        def operationType = "greater than"

        when:
        thresholds = CommonUtils.getThresholdsForOperationLevel(operationType, thresholds, 'upper', 'lower')

        then:
        thresholds != null
        thresholds.get('upper') == 12.0
        thresholds.get('lower') == 0.0
    }

    def "get thresholds for lesser than operation"() {
        given:
        def thresholds = [lower:12.0d, upper:0.0d]
        def operationType = "lesser than"

        when:
        thresholds = CommonUtils.getThresholdsForOperationLevel(operationType, thresholds, 'upper', 'lower')

        then:
        thresholds != null
        thresholds.get('upper') == -1.0
        thresholds.get('lower') == 12.0
    }

    def "get thresholds for in between operation"() {
        given:
        def thresholds = [lower:12.0d, upper:34.0d]
        def operationType = "in between"

        when:
        thresholds = CommonUtils.getThresholdsForOperationLevel(operationType, thresholds, 'upper', 'lower')

        then:
        thresholds != null
        thresholds.get('upper') == 34.0
        thresholds.get('lower') == 12.0
    }

    def "get thresholds for in between operation with invalid data"() {
        given:
        def thresholds = [lower:34.0d, upper:12.0d]
        def operationType = "in between"

        when:
        thresholds = CommonUtils.getThresholdsForOperationLevel(operationType, thresholds, 'upper', 'lower')

        then:
        thresholds != null
        thresholds.get('upper') == 34.0
        thresholds.get('lower') == 12.0
    }

    def "changePrecision - Check Integer"() {
        given:
        String value = "123"
        int precision = 2

        when:
        String changePrecision = CommonUtils.changePrecision(value, precision)

        then:
        changePrecision == "123.00"
    }

    def "changePrecision - Check Float"() {
        given:
        String value = "123.456"
        int precision = 2

        when:
        String changePrecision = CommonUtils.changePrecision(value, precision)

        then:
        changePrecision == "123.46"
    }

    def "changePrecision - Check string"() {
        given:
        String value = "ab12cd"
        int precision = 2

        when:
        String changePrecision = CommonUtils.changePrecision(value, precision)

        then:
        changePrecision == "ab12cd"
    }

    def "changePrecision - Check empty"() {
        given:
        String value = ""
        int precision = 2

        when:
        String changePrecision = CommonUtils.changePrecision(value, precision)

        then:
        changePrecision == ""
    }
}
