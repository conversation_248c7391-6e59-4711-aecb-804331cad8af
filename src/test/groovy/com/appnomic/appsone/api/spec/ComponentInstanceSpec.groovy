package com.appnomic.appsone.api.spec

import com.appnomic.appsone.api.Main
import com.appnomic.appsone.api.cache.MasterCache
import com.appnomic.appsone.api.db.InitDB
import groovyx.net.http.RESTClient

/**
 * <AUTHOR> : 18/1/19
 */
class ComponentInstanceSpec extends spock.lang.Specification{
    /*static {
        Main.main("haveToRunTestCases")
        InitDB.init()
        MasterCache.getInstance().loadMasterDataForCache()
    }
    def "get master component data"() {
        when:
        def restClient = new RESTClient("http://localhost:8996")
        restClient.ignoreSSLIssues()
        response = restClient.post(path : '/v1.0/ui/componentInstance',
                body : '[' +
                        '  {' +
                        '    "name": "name",' +
                        '    "status": 1,' +
                        '    "isCluster": 0,' +
                        '    "mstComponentVersion": "1.0",' +
                        '    "accountId": "String",' +
                        '    "mstComponentName": "AIX",' +
                        '    "mstComponentType": "Web Server",' +
                        '    "groupKpi": [' +
                        '      {' +
                        '        "name": "LogForwarder",' +
                        '        "attributes": [' +
                        '          "nollPointer",' +
                        '          "streamClosed"' +
                        '        ]' +
                        '      }' +
                        '    ],' +
                        '    "discovery": 1,' +
                        '    "attributes": [' +
                        '      {' +
                        '        "name": "hostAddress",' +
                        '        "value": "********"' +
                        '      },' +
                        '      {' +
                        '        "name": "monitorPort",' +
                        '        "value": "9150"' +
                        '      }' +
                        '    ]' +
                        '  }' +
                        ']',
                requestContentType: groovyx.net.http.ContentType.JSON)
        println response

        then: "data successfully posted"
        response.status == 200
    }*/
}
