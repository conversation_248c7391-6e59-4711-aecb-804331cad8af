package com.appnomic.appsone.api.spec

import com.appnomic.appsone.api.Main
import com.appnomic.appsone.api.cache.MasterCache
import com.appnomic.appsone.api.db.InitDB
import groovyx.net.http.RESTClient
import org.junit.BeforeClass
import org.junit.Test
import spock.lang.Specification

/**
 * @author: Sonu Parekaden
 */

class TransactionAddSpec extends Specification   {
    /*static {
        Main.main("haveToRunTestCases")
        InitDB.init()
        MasterCache.getInstance().loadMasterDataForCache()
    }

    @Test
    def "add transaction" ()   {
        when:
            def restClient = new RESTClient("http://localhost:8996")
            restClient.ignoreSSLIssues()
            response = restClient.post(path : '/v1.0/api/3/addTransaction',
            body : '[ ' +
                    ' {' +
                    '    "txnGrp": "ACCESS_LOG",' +
                    '    "txnType": "HTTP",' +
                    '    "tags": [' +
                    '     {' +
                    '        "name": "Controller",' +
                    '        "value": "BranchTransactions_INT",' +
                    '        "subTypeName" : "Application"' +
                    '      }    ' +
                    '],    ' +
                    '    "txnName": "ACCOUNT_ID",' +
                    '    "txnIdentifier": null,' +
                    '    "description": "ACCOUNT_ID",' +
                    '    "isAuditEnabled": false,' +
                    '    "isAutoConfigured": false,' +
                    '    "isRawEnabled": false,' +
                    '    "txnThresholds": [],' +
                    '    "subTransactions": [    ' +
                    '{ ' +
                    '               "httpTxnConfig": {' +
                    '                    "type": "GET",' +
                    '                    "urlPattern": "/finbranch/arjspmorph/INFENG/search_accountId.jsp",' +
                    '                    "queryParam": null,' +
                    '                    "headerPattern": null,' +
                    '                    "bodyPattern": null' +
                    '                },' +
                    '                "tcpTxnConfig": null' +
                    '            }' +
                    '    ],    ' +
                    '"bizValueExtractorList": [],' +
                    '    "txnAuditDetails": []}]',
                    requestContentType: groovyx.net.http.ContentType.JSON)

        println(response)

        then: "Data successfully posted"
        response.status == 200
    }*/
}
