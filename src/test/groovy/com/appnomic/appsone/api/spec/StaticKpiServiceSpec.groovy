package com.appnomic.appsone.api.spec


import spock.lang.Specification

/**
 * <AUTHOR> : 30/12/19
 */
class StaticKpiServiceSpec extends Specification{
    /*def times = new ArrayList<>([*************,1577689260000,1577689320000,1577689380000,1577689440000,1577689500000,1577689560000,1577689620000,1577689680000,1577689740000,1577689800000,1577689860000,1577689920000,1577689980000,1577690040000,1577690100000,1577690160000,1577690220000,1577690280000,1577690340000,1577690400000,1577690460000,1577690520000,1577690580000,1577690640000,1577690700000,1577690760000,1577690820000,1577690880000,1577690940000,*************,1577691060000,1577691120000,1577691180000,*************,1577691300000,*************,1577691420000,1577691480000,*************,1577691600000,1577691660000,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************])

    def "static kpi threshold if only one threshold data is found between given time range for greater than operation"() {
        given:
        List<String> serviceIdentifier=new ArrayList<>()
        serviceIdentifier.add("NB-DB-Service")
        def account = Spy(Account.class)
        account.setAccountId(1)
        account.setIdentifier("abc")

        def staticKpiService = Spy(StaticKpiService.class)
        def staticThresholdDataList = new ArrayList<>()
        def thresholds = [MIN: 5.0d]
        staticThresholdDataList.add(getServiceKpiThreshold("greater than", thresholds, *************))

        staticKpiService.getInstanceLevelStaticThresholdFromCassandra(*_) >> staticThresholdDataList
        AggregationLevel aggregationLevel = AggregationLevel.MINUTELY
        def kpiData = Spy(KpiData.class)

        when:
        staticKpiService.getStaticThresholds(account, 1, *************, *************, times, aggregationLevel, kpiData, serviceIdentifier,
                "instances", "ALL", "DummyInstanceIdentifier")

        then:
        kpiData != null
        kpiData.staticRange.maxThreshold.size() == 60
        kpiData.staticRange.minThreshold.size() == 60
        kpiData.staticRange.maxThreshold.size() == 60
        kpiData.staticRange.minThreshold.get(33) == null
        kpiData.staticRange.maxThreshold.get(33) == 5.0
        kpiData.staticRange.maxThreshold.get(0) == null
        kpiData.staticRange.minThreshold.get(0) == null
        kpiData.operationList.size() == 60
    }

    def "static kpi threshold if multiple threshold data are found between given time range for greater than operation"() {
        given:
        def account = Spy(Account.class)
        account.setAccountId(1)
        account.setIdentifier("abc")
        List<String> serviceIdentifier=new ArrayList<>()
        serviceIdentifier.add("NB-DB-Service")
        def staticKpiService = Spy(StaticKpiService.class)
        def staticThresholdDataList = new ArrayList<>()
        def thresholds = [MIN: 5.0d]
        staticThresholdDataList.add(getServiceKpiThreshold("greater than", thresholds, *************))
        thresholds = [MIN: 15.0d]
        staticThresholdDataList.add(getServiceKpiThreshold("greater than", thresholds, *************))
        thresholds = [MIN: 25.0d]
        staticThresholdDataList.add(getServiceKpiThreshold("greater than", thresholds, *************))

        staticKpiService.getInstanceLevelStaticThresholdFromCassandra(*_) >> staticThresholdDataList
        AggregationLevel aggregationLevel = AggregationLevel.MINUTELY
        def kpiData = Spy(KpiData.class)

        when:
        staticKpiService.getStaticThresholds(account, 1, *************, *************, times, aggregationLevel, kpiData, serviceIdentifier,
                "instances", "ALL", "DummyInstanceIdentifier")

        then:
        kpiData != null
        kpiData.staticRange.maxThreshold.size() == 60
        kpiData.staticRange.minThreshold.size() == 60
        kpiData.staticRange.maxThreshold.size() == 60
        kpiData.staticRange.minThreshold.get(33) == null
        kpiData.staticRange.maxThreshold.get(33) == 5.0
        kpiData.staticRange.maxThreshold.get(38) == 15.0
        kpiData.staticRange.maxThreshold.get(52) == 25.0
        kpiData.staticRange.minThreshold.get(0) == null
        kpiData.staticRange.maxThreshold.get(0) == null
        kpiData.operationList.size() == 60
    }

    def "static kpi threshold if no any threshold data is found between given time range for greater than operation"() {
        given:
        def account = Spy(Account.class)
        account.setAccountId(1)
        account.setIdentifier("abc")
        List<String> serviceIdentifier=new ArrayList<>()
        serviceIdentifier.add("NB-DB-Service")
        def staticKpiService = Spy(StaticKpiService.class)

        staticKpiService.getInstanceLevelStaticThresholdFromCassandra(*_) >> null
        AggregationLevel aggregationLevel = AggregationLevel.MINUTELY
        def kpiData = Spy(KpiData.class)
        when:
        staticKpiService.getStaticThresholds(account, 1, *************, *************, times, aggregationLevel, kpiData, serviceIdentifier,
                "instances", "ALL", "DummyInstanceIdentifier")

        then:
        kpiData != null
        kpiData.staticRange != null
        kpiData.operationList != null
    }

    def "static kpi threshold if only one threshold data is found between given time range for lesser than operation"() {
        given:
        def account = Spy(Account.class)
        account.setAccountId(1)
        account.setIdentifier("abc")
        List<String> serviceIdentifier=new ArrayList<>()
        serviceIdentifier.add("NB-DB-Service")
        def staticKpiService = Spy(StaticKpiService.class)
        def staticThresholdDataList = new ArrayList<>()
        def thresholds = [MIN: 75.0d]


        staticThresholdDataList.add(getServiceKpiThreshold("lesser than", thresholds, *************))

        staticKpiService.getInstanceLevelStaticThresholdFromCassandra(*_) >> staticThresholdDataList
        AggregationLevel aggregationLevel = AggregationLevel.MINUTELY
        def kpiData = Spy(KpiData.class)

        when:
        staticKpiService.getStaticThresholds(account, 1, *************, *************, times, aggregationLevel, kpiData, serviceIdentifier,
                "instances", "ALL", "DummyInstanceIdentifier")

        then:
        kpiData != null
        kpiData.staticRange.maxThreshold.size() == 60
        kpiData.staticRange.minThreshold.size() == 60
        kpiData.staticRange.maxThreshold.size() == 60
        kpiData.staticRange.maxThreshold.get(33) == null
        kpiData.staticRange.minThreshold.get(33) == 75.0
        kpiData.staticRange.maxThreshold.get(0) == null
        kpiData.staticRange.minThreshold.get(0) == null
        kpiData.operationList.size() == 60
    }

    def "static kpi threshold if multiple threshold data are found between given time range for lesser than operation"() {
        given:
        def account = Spy(Account.class)
        account.setAccountId(1)
        account.setIdentifier("abc")
        List<String> serviceIdentifier=new ArrayList<>()
        serviceIdentifier.add("NB-DB-Service")
        def staticKpiService = Spy(StaticKpiService.class)
        def staticThresholdDataList = new ArrayList<>()
        def thresholds = [MIN: 65.0d]
        staticThresholdDataList.add(getServiceKpiThreshold("lesser than", thresholds, *************))
        thresholds = [MIN: 75.0d]
        staticThresholdDataList.add(getServiceKpiThreshold("lesser than", thresholds, *************))
        thresholds = [MIN: 85.0d]
        staticThresholdDataList.add(getServiceKpiThreshold("lesser than", thresholds, *************))

        staticKpiService.getInstanceLevelStaticThresholdFromCassandra(*_) >> staticThresholdDataList
        AggregationLevel aggregationLevel = AggregationLevel.MINUTELY
        def kpiData = Spy(KpiData.class)
        when:
        staticKpiService.getStaticThresholds(account, 1, *************, *************, times, aggregationLevel, kpiData, serviceIdentifier,
                "instances", "ALL", "DummyInstanceIdentifier")

        then:
        kpiData != null
        kpiData.staticRange.maxThreshold.size() == 60
        kpiData.staticRange.minThreshold.size() == 60
        kpiData.staticRange.maxThreshold.size() == 60
        kpiData.staticRange.maxThreshold.get(33) == null
        kpiData.staticRange.minThreshold.get(33) == 65.0
        kpiData.staticRange.minThreshold.get(38) == 75.0
        kpiData.staticRange.minThreshold.get(52) == 85.0
        kpiData.staticRange.maxThreshold.get(0) == null
        kpiData.staticRange.minThreshold.get(0) == null
        kpiData.operationList.size() == 60
    }

    def "static kpi threshold if no any threshold data is configured between given time range for lesser than operation"() {
        given:
        def account = Spy(Account.class)
        account.setAccountId(1)
        account.setIdentifier("abc")
        List<String> serviceIdentifier=new ArrayList<>()
        serviceIdentifier.add("NB-DB-Service")
        def staticKpiService = Spy(StaticKpiService.class)
        def staticThresholdDataList = new ArrayList<>()
        def thresholds = [MIN: 65.0d]
        staticThresholdDataList.add(getServiceKpiThreshold("lesser than", thresholds, *************))

        staticKpiService.getInstanceLevelStaticThresholdFromCassandra(*_) >> staticThresholdDataList
        AggregationLevel aggregationLevel = AggregationLevel.MINUTELY
        def kpiData = Spy(KpiData.class)
        when:
        staticKpiService.getStaticThresholds(account, 1, *************, *************, times, aggregationLevel, kpiData, serviceIdentifier,
                "instances", "ALL", "DummyInstanceIdentifier")

        then:
        kpiData != null
        kpiData.staticRange != null
    }

    def "static kpi threshold if threshold data is found before the specified time range"() {
        given:
        def account = Spy(Account.class)
        account.setAccountId(1)
        account.setIdentifier("abc")
        List<String> serviceIdentifier=new ArrayList<>()
        serviceIdentifier.add("NB-DB-Service")
        def staticKpiService = Spy(StaticKpiService.class)
        def staticThresholdDataList = new ArrayList<>()
        def thresholds = [MIN: 65.0d]
        staticThresholdDataList.add(getServiceKpiThreshold("lesser than", thresholds, *************,*************))
        thresholds = [MIN: 75.0d]
        staticThresholdDataList.add(getServiceKpiThreshold("lesser than", thresholds, *************,*************))
        thresholds = [MIN: 85.0d]
        staticThresholdDataList.add(getServiceKpiThreshold("lesser than", thresholds, *************,*************))

        staticKpiService.getInstanceLevelStaticThresholdFromCassandra(*_) >> staticThresholdDataList
        AggregationLevel aggregationLevel = AggregationLevel.MINUTELY
        def kpiData = Spy(KpiData.class)
        when:
        staticKpiService.getStaticThresholds(account, 1, *************, *************, times, aggregationLevel, kpiData, serviceIdentifier,
                "instances", "ALL", "DummyInstanceIdentifier")

        then:
        kpiData != null
        kpiData.staticRange.maxThreshold.size() == 60
        kpiData.staticRange.minThreshold.size() == 60
        kpiData.staticRange.maxThreshold.size() == 60
        kpiData.staticRange.maxThreshold.get(33) == null
        kpiData.staticRange.minThreshold.get(0) == null
        kpiData.operationList.size() == 60
    }

    def "static kpi threshold if threshold data is found after the specified time range"() {
        given:
        def account = Spy(Account.class)
        account.setAccountId(1)
        account.setIdentifier("abc")
        List<String> serviceIdentifier=new ArrayList<>()
        serviceIdentifier.add("NB-DB-Service")
        def staticKpiService = Spy(StaticKpiService.class)
        def staticThresholdDataList = new ArrayList<>()
        def thresholds = [MIN: 65.0d]
        staticThresholdDataList.add(getServiceKpiThreshold("lesser than", thresholds, *************,*************))
        thresholds = [MIN: 75.0d]
        staticThresholdDataList.add(getServiceKpiThreshold("lesser than", thresholds, *************,*************))
        thresholds = [MIN: 85.0d]
        staticThresholdDataList.add(getServiceKpiThreshold("lesser than", thresholds, *************))

        staticKpiService.getInstanceLevelStaticThresholdFromCassandra(*_) >> staticThresholdDataList
        AggregationLevel aggregationLevel = AggregationLevel.MINUTELY
        def kpiData = Spy(KpiData.class)
        when:
        staticKpiService.getStaticThresholds(account, 1, *************, *************, times, aggregationLevel, kpiData, serviceIdentifier,
                "instances", "ALL", "DummyInstanceIdentifier")

        then:
        kpiData != null
        kpiData.staticRange.maxThreshold.size() == 60
        kpiData.staticRange.minThreshold.size() == 60
        kpiData.staticRange.maxThreshold.size() == 60
        kpiData.staticRange.maxThreshold.get(33) == null
        kpiData.staticRange.minThreshold.get(33) == null
        kpiData.staticRange.minThreshold.get(38) == null
        kpiData.staticRange.minThreshold.get(52) == null
        kpiData.staticRange.maxThreshold.get(0) == null
        kpiData.staticRange.minThreshold.get(0) == null
        kpiData.operationList.size() == 60
    }

    def "static kpi threshold 1"() {
        given:
        def account = Spy(Account.class)
        account.setAccountId(1)
        account.setIdentifier("abc")
        List<String> serviceIdentifier=new ArrayList<>()
        serviceIdentifier.add("NB-DB-Service")
        def staticKpiService = Spy(StaticKpiService.class)
        def staticThresholdDataList = new ArrayList<>()
        def thresholds = [MIN: 65.0d]
        staticThresholdDataList.add(getServiceKpiThreshold("lesser than", thresholds, *************,*************))
        thresholds = [MIN: 85.0d]
        staticThresholdDataList.add(getServiceKpiThreshold("greater than", thresholds, *************))

        staticKpiService.getInstanceLevelStaticThresholdFromCassandra(*_) >> staticThresholdDataList
        AggregationLevel aggregationLevel = AggregationLevel.MINUTELY
        def kpiData = Spy(KpiData.class)
        when:
        staticKpiService.getStaticThresholds(account, 1, *************, *************, times, aggregationLevel, kpiData, serviceIdentifier,
                "instances", "ALL", "DummyInstanceIdentifier")

        then:
        kpiData != null
        kpiData.staticRange.maxThreshold.size() == 60
        kpiData.staticRange.minThreshold.size() == 60
        kpiData.staticRange.maxThreshold.size() == 60
        kpiData.staticRange.maxThreshold.get(33) == null
        kpiData.staticRange.minThreshold.get(33) == 65
        kpiData.staticRange.minThreshold.get(38) == null
        kpiData.staticRange.maxThreshold.get(52) == 85
        kpiData.staticRange.maxThreshold.get(0) == null
        kpiData.staticRange.minThreshold.get(0) == null
        kpiData.operationList.size() == 60
    }

    def "static kpi threshold 2"() {
        given:
        def account = Spy(Account.class)
        account.setAccountId(1)
        account.setIdentifier("abc")
        List<String> serviceIdentifier=new ArrayList<>()
        serviceIdentifier.add("NB-DB-Service")
        def staticKpiService = Spy(StaticKpiService.class)
        def staticThresholdDataList = new ArrayList<>()
        def thresholds = [MIN: 65.0d]
        staticThresholdDataList.add(getServiceKpiThreshold("lesser than", thresholds, *************,*************))
        thresholds = [MIN: 85.0d]
        staticThresholdDataList.add(getServiceKpiThreshold("greater than", thresholds, *************, *************))

        staticKpiService.getInstanceLevelStaticThresholdFromCassandra(*_) >> staticThresholdDataList
        AggregationLevel aggregationLevel = AggregationLevel.MINUTELY
        def kpiData = Spy(KpiData.class)
        when:
        staticKpiService.getStaticThresholds(account, 1, *************, *************, times, aggregationLevel, kpiData, serviceIdentifier,
                "instances", "ALL", "DummyInstanceIdentifier")

        then:
        kpiData != null
        kpiData.staticRange.maxThreshold.size() == 60
        kpiData.staticRange.minThreshold.size() == 60
        kpiData.staticRange.maxThreshold.size() == 60
        kpiData.staticRange.maxThreshold.get(33) == null
        kpiData.staticRange.minThreshold.get(33) == 65
        kpiData.staticRange.minThreshold.get(38) == null
        kpiData.staticRange.maxThreshold.get(46) == 85
        kpiData.staticRange.maxThreshold.get(52) == null
        kpiData.staticRange.maxThreshold.get(0) == null
        kpiData.staticRange.minThreshold.get(0) == null
        kpiData.operationList.size() == 60
    }

    def "static kpi threshold 3"() {
        given:
        def account = Spy(Account.class)
        account.setAccountId(1)
        account.setIdentifier("abc")
        List<String> serviceIdentifier=new ArrayList<>()
        serviceIdentifier.add("NB-DB-Service")
        def staticKpiService = Spy(StaticKpiService.class)
        def staticThresholdDataList = new ArrayList<>()
        def thresholds = [MIN: 75.0d]
        staticThresholdDataList.add(getServiceKpiThreshold("lesser than", thresholds, *************,*************))
        thresholds = [MIN: 85.0d]
        staticThresholdDataList.add(getServiceKpiThreshold("greater than", thresholds, *************, *************))

        staticKpiService.getInstanceLevelStaticThresholdFromCassandra(*_) >> staticThresholdDataList
        AggregationLevel aggregationLevel = AggregationLevel.MINUTELY
        def kpiData = Spy(KpiData.class)
        when:
        staticKpiService.getStaticThresholds(account, 1, *************, *************, times, aggregationLevel, kpiData, serviceIdentifier,
                "instances", "ALL", "DummyInstanceIdentifier")

        then:
        kpiData != null
        kpiData.staticRange.maxThreshold.size() == 60
        kpiData.staticRange.minThreshold.size() == 60
        kpiData.staticRange.maxThreshold.size() == 60
        kpiData.staticRange.minThreshold.get(36) == 75
        kpiData.staticRange.maxThreshold.get(37) == null
        kpiData.staticRange.minThreshold.get(38) == 75
        kpiData.staticRange.maxThreshold.get(46) == 85
        kpiData.staticRange.maxThreshold.get(52) == null
        kpiData.staticRange.maxThreshold.get(0) == null
        kpiData.staticRange.minThreshold.get(0) == null
        kpiData.operationList.size() == 60
    }

    def getServiceKpiThreshold(def operationType, def thresholds, def thresholdUpdateTime,def thresholdEndTime=0) {
        def staticThresholdData = Spy(StaticThresholdData.class)
        staticThresholdData.thresholds = thresholds
        staticThresholdData.operationType = operationType
        staticThresholdData.thresholdUpdateTime = thresholdUpdateTime
        staticThresholdData.thresholdEndTime = thresholdEndTime
        return staticThresholdData
    }*/
}
