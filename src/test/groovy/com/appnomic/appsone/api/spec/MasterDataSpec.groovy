package com.appnomic.appsone.api.spec

import com.appnomic.appsone.api.cache.MasterCache
import com.appnomic.appsone.api.db.InitDB
import com.appnomic.appsone.api.manager.MySQLConnectionManager

/**
 * <AUTHOR> : 18/1/19
 */
class MasterDataSpec extends spock.lang.Specification{
    /*static {
        InitDB.init()
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
    }

    def "get master component data"() {
        when:
        MasterCache.getInstance().loadMasterDataForCache()
        then: "total data should available in cache"
        5 == MasterCache.getInstance().getMstComponentSize()
    }

    def "get master component type data"() {
        when:
        MasterCache.getInstance().loadMasterDataForCache()
        then: "total data should available in cache"
        5 == MasterCache.getInstance().getMstComponentTypeSize()
    }*/
}
