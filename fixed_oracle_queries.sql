-- Fixed Query 1: Adjusted ratio threshold
select /*+RULE*/ count(1) as DB_SEQUENCE_CYCLIC 
from dba_sequences 
where cycle_flag='Y' 
and sequence_owner not in ('SYSTEM','SYS','DBSNMP') 
and (last_number-min_value)/(max_value-min_value) > 0.1;

-- Fixed Query 2: Simplified version without v$active_session_history
SELECT u.username as PHYSICALREADS_USER_ID,
       a.sql_id AS PHYSICALREADS_SQL_ID,
       substr(a.sql_text,1,50) AS PHYSICALREADS_SQL_TEXT,
       a.executions AS PHYSICALREADS_EXECUTIONS,
       a.disk_reads AS PHYSICALREADS_DISK_READS,
       a.buffer_gets AS PHYSICALREADS_BUFFER_GETS
FROM v\$sqlarea a, dba_users u
WHERE a.parsing_user_id = u.user_id
AND a.executions > 0
AND a.parsing_schema_name NOT IN ('SYS','PERFSTAT','DBSNMP')
AND rownum <= 10;
